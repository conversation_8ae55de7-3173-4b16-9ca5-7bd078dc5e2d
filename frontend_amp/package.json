{"name": "frontend_amp", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@fontsource/roboto": "^5.0.13", "@mui/icons-material": "^5.16.7", "@mui/joy": "^5.0.0-beta.48", "@mui/material": "^5.16.7", "@mui/styled-engine-sc": "^6.0.0-alpha.18", "@mui/x-charts": "^7.17.0", "@mui/x-charts-pro": "^7.0.0-beta.1", "@mui/x-date-pickers": "^7.16.0", "@react-spring/web": "^9.7.4", "@reduxjs/toolkit": "^2.2.7", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.5", "bootstrap": "^5.3.3", "dayjs": "^1.11.13", "dompurify": "^3.2.5", "jquery": "^3.7.1", "js-cookie": "^3.0.5", "popper.js": "^1.16.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-pro-sidebar": "^1.1.0", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-router": "^7.1.4", "react-router-dom": "^6.26.0", "react-scripts": "5.0.1", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "styled-components": "^6.1.12", "swiper": "^11.2.6", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/preset-react": "^7.24.7", "babel-loader": "^9.1.3", "css-loader": "^7.1.2", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.0", "img-loader": "^4.0.0", "mini-css-extract-plugin": "^2.9.0", "resolve-url-loader": "^5.0.0", "sass": "^1.77.8", "sass-loader": "^16.0.0", "style-loader": "^4.0.0", "url-loader": "^4.1.1", "webpack": "^5.93.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4"}}