import React from 'react';
import {
  Routes,
  Route,
  Navigate,
  BrowserRouter
} from 'react-router-dom';
import SigninScene from './scenes/SigninScene';
import OrderScene from './scenes/OrderScene';
import DashboardScene from './scenes/DashboardScene';
import { useAuth } from './context/AuthContext';
import PrivateRoute from './router/ProtectRoute';
import DashboardProvider from './context/DashboardContext';
import ReceivedScene from './scenes/ReceivedScene';
import ReturnScene from './scenes/ReturnScene';
import VendorScene from './scenes/VendorScene';
import ReportScene from './scenes/ReportScene';
import MyProductScene from './scenes/MyProcductScene';
import BudgetScene from './scenes/BudgetScene';
import InsuranceScene from './scenes/InsuranceScene';
import PatientExperienceScene from './scenes/PatientExperienceScene';
import InventoryScene from './scenes/InventoryScene';
import { Box, Fab } from '@mui/material';
import Header from './Components/Header';
import SideBar from './Components/SideBar';
import AlertUser from './Components/AlertUser';
import BadgeNumber from './Components/BadgeNumber';
import LoaderAllPage from './Components/LoaderAllPage';
import UserListScene from './scenes/UserListScene';
import ChatIcon from '@mui/icons-material/Chat';
import NotesScene from './scenes/NotesScene';
import UserManagerScene from './scenes/UserManagerScene';
import BlogsScene from './scenes/BlogsScene';
import BlogByIdScene from './scenes/BlogByIdScene';
import { useTheme } from './context/ThemeContext';
import BlogPreview from './Components/blogfeedback/BlogPreview';
import HeaderWithSidebar from './Components/HeaderWrapper';
import { useSidebar } from './Components/SideBar';
import SupplyHubScene from './scenes/SupplyHubScene';


const RouterComponent = () => {
  const theme = useTheme()
  const user = useAuth();
  const { sidebarCollapsed, isMobile } = useSidebar();
  
  return (
    <DashboardProvider>
      {!user.token ? (
        <>
          <Routes>
            <Route exact path='/' element={<SigninScene/>}/>
            <Route exact path='/signin' element={<SigninScene/>}/>
            <Route path="*" element={<Navigate to="/signin" replace />} />
          </Routes>
        </>
      ) : (
        user.isLoading ? (
          <LoaderAllPage/>
        ) : (
          <Box
            sx={{
              display: 'flex',
              flexDirection: {
                lg:'column',
                md:'column',
                xs: 'column',
                sm: 'row' 
              },
              // minHeight: '100vh',
              width:'100%',
              height:'100%',
              overflow:'auto',
              position:'absolute',
              // backgroundColor:this.props.theme.theme.background
              // overflow: 'hidden', // Prevent overall page scrolling issues


            }}
          >
            <Header />
            <Box
              sx={{
                width:'100%',
                height:'100%',
                display:'flex',
                position:'relative'
                // flexDirection:'row'
              }}
            >
              <SideBar /> 
              {/* Sidebar */}
              <Box
                  position={'relative'}
                  sx={{
                    width:'100%',
                    height:'100%',
                    backgroundColor:theme.selectTheme.background_2,
                    // overflow: "auto",
                  }}
                > 
                  <main>
                    <AlertUser />
                    <Routes>
                      <Route element={<PrivateRoute />}>
                        <Route exact path='/dashboard' element={<DashboardScene/>}/>
                        <Route exact path='/order' element={<OrderScene/>}/>
                        <Route exact path='/received' element={<ReceivedScene/>}/>
                        <Route exact path='/inventory' element={<InventoryScene/>}/>
                        <Route exact path='/return' element={<ReturnScene/>}/>
                        <Route exact path='/notes' element={<NotesScene/>}/>
                        <Route exact path='/vendor-list' element={<VendorScene/>}/>
                        <Route exact path='/report' element={<ReportScene/>}/>
                        <Route exact path='/myproducts' element={<MyProductScene/>}/>
                        <Route exact path='/budget' element={<BudgetScene/>}/>
                        <Route exact path='/insurance' element={<InsuranceScene/>}/>
                        <Route exact path='/patient-experience' element={<PatientExperienceScene/>}/>
                        <Route exact path='/user-list' element={<UserListScene/>}/>
                        <Route exact path='/blogs' element={<BlogsScene/>}/>
                        <Route exact path='/blogs-by-id/:id/' element={<BlogByIdScene/>}  />
                        <Route exact path='/blogs/preview/:id' element={<BlogPreview/>}  />
                        <Route exact path='/supply-hub' element={<SupplyHubScene/>}/>
                        {/* <Route exact path='/user-manager' element={<UserManagerScene/>}/> */}
                      </Route>
                      <Route path="*" element={<Navigate to="/order" replace />} />
                    </Routes>
                  </main>
              </Box>
            </Box>
          </Box>
        )
      )}
    </DashboardProvider>
  );
};

export default RouterComponent;
