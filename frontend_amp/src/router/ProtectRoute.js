import React from "react";
import { Navigate, Outlet } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const PrivateRoute = () => {
  const auth = useAuth();
  
  // If we have a token, allow access to the route
  // The token validation will happen in the background via the useEffect in AuthContext
  if (auth.token) {
    return <Outlet />;
  }
  
  // No token, redirect to signin
  return <Navigate to="/signin" />;
};

export default PrivateRoute;
