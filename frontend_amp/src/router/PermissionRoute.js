import React, { useEffect, useState } from "react";
import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
// import { useAuth } from "../hooks/AuthProvider";

const PermissionRoute = () => {
    const location = useLocation();
    const user = useAuth();
    const [checked,setChecked]=useState(false)
    const menuItem = [
      {
          label: 'Procurement Hub',
          items: [
              { link: 'order', name: 'Order Sheet' },
              { link: 'received', name: 'Received'},
              { link: 'inventory', name: 'Inventory'},
              { link: 'return', name: 'Return'},
              { link: 'notes', name: 'Notes'},
              {link: 'vendor-list',name: 'Vendor List'},
              { link: 'report', name: 'Report'},
              { link: 'myproducts', name: 'My Products'},
              { link: 'budget', name: 'Budget' },
          ],
      },
      {
          label: 'Insurance Verification',
          items: [
              {
                  link: 'insurance',
                  name: 'Insurance Verification',
                  tag:'Insurance_Verification'
              },
          ],
      },
      {
          label: 'Patient Experience',
          items: [
              {
                  link: 'patient-experience',
                  name: 'Patient Experience',
                  tag:'Patient_Experience'
              },
          ],
      },
    ];
    useEffect(() => {
        if (user.user){
            const current_path=location.pathname.split("/")[1]
            let findgroup=""
            menuItem.map((item, index)=>{
                item.items.map((menuItem,item_index)=>{
                    // console.log(menuItem.link)
                    // console.log(current_path)
                    if(menuItem.link==current_path){
                        findgroup = menuItem.tag
                        return
                    }
                })
            })
            console.log(findgroup)
            console.log(user.user.groups)
            console.log(user.user.groups.includes(findgroup))
            if(user.user.groups.includes(findgroup)){
                setChecked(true)
            }
        }else{
            setChecked(true)
            return
        }
        
    }, [location])
    if (checked){

    }else{
        return <Navigate to="/order" />;
    }
  return <Outlet />;
};

export default PermissionRoute;
