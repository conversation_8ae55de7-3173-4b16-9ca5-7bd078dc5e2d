# Ecwid API Integration Documentation

## Overview

This project integrates with the Ecwid REST API to provide product search functionality in the OrderAdd component. The integration replaces the previous widget-based approach with a more controlled and customizable API-based solution.

## Features

- **Real-time Product Search**: Search products as you type with debounced API calls
- **Product Selection**: Click to select products and auto-populate order form fields
- **Rich Product Display**: Shows product images, prices, SKU, and stock status
- **Theme Integration**: Matches your app's theme and styling
- **Error Handling**: Graceful error handling with user-friendly messages
- **Rate Limiting**: Built-in rate limiting to respect API limits
- **Console Logging**: Detailed logging for debugging and monitoring

## Setup Instructions

### 1. Get Your Ecwid API Token

#### Method 1: Create a New App (Recommended)
1. Log in to your Ecwid Control Panel
2. Go to **Apps** → **My Apps**
3. Click **"Develop an app"** or **"Create app"**
4. Fill in app details:
   - **App Name**: "My Store Integration" (any name)
   - **Description**: "Product search integration"
   - **App Type**: "Private app" (for your store only)
5. Set permissions:
   - ✅ **"Read catalog"** (to search products)
   - ✅ **"Read store profile"** (to get store info)
6. Copy the **Access Token** (this is what you need)

#### Method 2: REST API Settings
1. Go to **Settings** → **General** → **API**
2. Enable **REST API** if not already enabled
3. Copy the **Private token** or **Secret token**

#### Method 3: Use the Built-in Test Tool
1. Open your OrderAdd modal
2. Click the **"Test API"** button in the top-right
3. Follow the instructions in the test tool
4. Test your store access and API token

### 2. Configure the API Token

#### Option A: Environment Variable (Recommended for Production)
Create a `.env` file in your project root:
```bash
REACT_APP_ECWID_TOKEN=your_secret_token_here
```

#### Option B: Direct Configuration (Development Only)
Edit `frontend_amp/src/config/ecwid.js`:
```javascript
export const ECWID_CONFIG = {
  // ... other config
  ACCESS_TOKEN: 'your_secret_token_here',
  // ... rest of config
};
```

### 3. Verify Store ID

Make sure the store ID in `frontend_amp/src/config/ecwid.js` matches your store:
```javascript
export const ECWID_CONFIG = {
  STORE_ID: '31447282', // Replace with your actual store ID
  // ... rest of config
};
```

## File Structure

```
frontend_amp/src/
├── config/
│   └── ecwid.js                 # Ecwid configuration
├── services/
│   └── EcwidService.js          # API service layer
├── Components/
│   ├── EcwidProductSearch.js    # Product search component
│   └── OrderAdd.js              # Updated order form with API integration
└── docs/
    └── ECWID_API_INTEGRATION.md # This documentation
```

## Components

### EcwidService
- **Location**: `frontend_amp/src/services/EcwidService.js`
- **Purpose**: Handles all API interactions with Ecwid
- **Features**: Rate limiting, error handling, data formatting

### EcwidProductSearch
- **Location**: `frontend_amp/src/Components/EcwidProductSearch.js`
- **Purpose**: Product search UI component
- **Features**: Real-time search, product selection, theme integration

### OrderAdd (Updated)
- **Location**: `frontend_amp/src/Components/OrderAdd.js`
- **Purpose**: Order creation form with integrated product search
- **Features**: Auto-population of product data, visual product display

## API Endpoints Used

1. **Search Products**: `GET /api/v3/{storeId}/products`
   - Used for: Product search functionality
   - Parameters: keyword, limit, offset, sortBy, enabled

2. **Get Product**: `GET /api/v3/{storeId}/products/{productId}`
   - Used for: Getting detailed product information
   - Parameters: productId

3. **Store Profile**: `GET /api/v3/{storeId}/profile`
   - Used for: Store information and validation

## Configuration Options

Edit `frontend_amp/src/config/ecwid.js` to customize:

- **Search Settings**: Default limits, sort options, debounce timing
- **Display Settings**: Thumbnail sizes, description length, stock display
- **Rate Limiting**: Request limits and time windows
- **Error Messages**: Custom error messages for different scenarios

## Usage Example

```javascript
import EcwidProductSearch from './EcwidProductSearch';

// In your component
<EcwidProductSearch
  theme={this.props.theme}
  initialValue={this.state.product}
  onProductSelect={(product) => {
    console.log('Selected product:', product);
    // Handle product selection
  }}
/>
```

## Console Logging

The integration provides detailed console logging:

- **🔍 Search Operations**: Search queries and results
- **🎯 Product Selection**: When products are selected
- **📦 Product Information**: Detailed product data
- **⚠️ Errors**: API errors and handling

## Error Handling

The system handles various error scenarios:

- **Rate Limiting**: Automatic retry with user feedback
- **Authentication**: Clear messages for token issues
- **Network Errors**: Graceful degradation
- **Invalid Data**: Validation and sanitization

## Performance Considerations

- **Debounced Search**: Prevents excessive API calls
- **Rate Limiting**: Respects Ecwid's API limits (600 requests/minute)
- **Caching**: Consider implementing caching for frequently accessed data
- **Pagination**: Large result sets are paginated

## Troubleshooting

### Common Issues

1. **No Search Results**
   - Check API token configuration
   - Verify store ID is correct
   - Check network connectivity

2. **Authentication Errors**
   - Verify API token is valid
   - Check token permissions in Ecwid Control Panel

3. **Rate Limiting**
   - Reduce search frequency
   - Implement caching if needed

### Debug Mode

Enable detailed logging by opening browser console and monitoring:
- Search operations
- API responses
- Error messages

## Security Notes

- **Never commit API tokens** to version control
- **Use environment variables** for production deployments
- **Implement proper CORS** if needed
- **Monitor API usage** to prevent abuse

## Future Enhancements

Potential improvements:
- **Caching Layer**: Redis or local storage caching
- **Advanced Filters**: Category, price range, brand filters
- **Bulk Operations**: Multiple product selection
- **Offline Support**: Local storage fallback
- **Analytics**: Search analytics and optimization
