import axios from 'axios';
import { API_URL } from '../config';

// Create a function that takes the token as a parameter
export const chatService = {
  // Get all conversations for the current user
  getConversations: async (token) => {
    try {
      const response = await axios.get(`${API_URL}/chat/conversations`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching conversations:', error);
      throw error;
    }
  },

  // Get messages for a specific conversation
  getMessages: async (conversationId, token) => {
    try {
      const response = await axios.get(`${API_URL}/chat/conversations/${conversationId}/messages`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }
  },

  // Send a message in a conversation
  sendMessage: async (conversationId, content, token) => {
    try {
      const response = await axios.post(
        `${API_URL}/chat/conversations/${conversationId}/messages`, 
        { content },
        { 
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  },

  // Create a new conversation with a user
  createConversation: async (userId, token) => {
    try {
      const response = await axios.post(
        `${API_URL}/chat/conversations`, 
        { user_id: userId },
        { 
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  },

  // Get admin users for starting a conversation
  getAdminUsers: async (token) => {
    try {
      const response = await axios.get(`${API_URL}/chat/users/admins`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching admin users:', error);
      throw error;
    }
  }
};

export default chatService;
