// Get the authentication token from storage
export const getToken = () => {
  const tokenFromSessionStorage = sessionStorage.getItem("amp.amedicalpartner.com.token");
  const tokenFromLocalStorage = localStorage.getItem("amp.amedicalpartner.com.token");
  
  // Try session storage first, then local storage
  let token = tokenFromSessionStorage ? JSON.parse(tokenFromSessionStorage) : 
              tokenFromLocalStorage ? JSON.parse(tokenFromLocalStorage) : '';
              
  return token;
};

// Create authorization header with Bearer token
export const getAuthHeader = () => {
  const token = getToken();
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
};

// Check if user is authenticated
export const isAuthenticated = () => {
  const token = getToken();
  return !!token;
};

// Log out user by clearing tokens
export const logout = () => {
  sessionStorage.removeItem("amp.amedicalpartner.com.token");
  localStorage.removeItem("amp.amedicalpartner.com.token");
  sessionStorage.removeItem("amp.amedicalpartner.com.userinfo");
  localStorage.removeItem("amp.amedicalpartner.com.userinfo");
};