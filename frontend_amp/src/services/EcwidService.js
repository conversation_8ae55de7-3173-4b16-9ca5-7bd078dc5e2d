/**
 * Ecwid API Service
 * Handles all interactions with the Ecwid REST API
 */

import { ECWID_CONFIG, getApiUrl, getApiHeaders, validateSearchParams } from '../config/ecwid';

class EcwidService {
  constructor() {
    this.storeId = ECWID_CONFIG.STORE_ID;
    this.baseUrl = ECWID_CONFIG.API_BASE_URL;
    this.token = ECWID_CONFIG.ACCESS_TOKEN;

    // Rate limiting
    this.requestCount = 0;
    this.requestWindow = ECWID_CONFIG.RATE_LIMIT.WINDOW_MS;
    this.maxRequests = ECWID_CONFIG.RATE_LIMIT.MAX_REQUESTS;
    this.lastReset = Date.now();
  }

  /**
   * Set the API access token
   * @param {string} token - Ecwid API access token
   */
  setToken(token) {
    this.token = token;
  }

  /**
   * Test API access without authentication (public endpoints)
   * This can help verify your store ID is correct
   */
  async testPublicAccess() {
    try {
      // Try to access store profile (some data is public)
      const url = `${this.baseUrl}/${this.storeId}/profile`;
      const response = await fetch(url);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Store found:', {
          storeName: data.generalInfo?.storeTitle || 'Unknown',
          storeId: this.storeId,
          storeUrl: data.generalInfo?.storeUrl || 'Unknown'
        });
        return { success: true, data };
      } else {
        console.log('❌ Store access failed:', response.status, response.statusText);
        return { success: false, error: `HTTP ${response.status}: ${response.statusText}` };
      }
    } catch (error) {
      console.log('❌ Network error:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check rate limiting
   */
  checkRateLimit() {
    const now = Date.now();
    if (now - this.lastReset > this.requestWindow) {
      this.requestCount = 0;
      this.lastReset = now;
    }
    
    if (this.requestCount >= this.maxRequests) {
      throw new Error('Rate limit exceeded. Please wait before making more requests.');
    }
    
    this.requestCount++;
  }

  /**
   * Make authenticated API request
   * @param {string} endpoint - API endpoint
   * @param {object} options - Fetch options
   */
  async makeRequest(endpoint, options = {}) {
    this.checkRateLimit();

    const url = getApiUrl(endpoint);
    const headers = {
      ...getApiHeaders(),
      ...options.headers
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API Error ${response.status}: ${errorData.message || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Ecwid API Request Failed:', error);
      throw error;
    }
  }

  /**
   * Search products with various filters
   * @param {object} params - Search parameters
   */
  async searchProducts(params = {}) {
    const validatedParams = validateSearchParams(params);
    const queryParams = new URLSearchParams();

    // Add search parameters
    Object.entries(validatedParams).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        queryParams.append(key, value);
      }
    });

    const endpoint = `${ECWID_CONFIG.ENDPOINTS.PRODUCTS}?${queryParams.toString()}`;
    return await this.makeRequest(endpoint);
  }

  /**
   * Search products by keyword
   * @param {string} keyword - Search term
   * @param {object} options - Additional search options
   */
  async searchProductsByKeyword(keyword, options = {}) {
    const params = {
      keyword: keyword,
      limit: options.limit || ECWID_CONFIG.SEARCH.DEFAULT_LIMIT,
      offset: options.offset || 0,
      sortBy: options.sortBy || ECWID_CONFIG.SEARCH.DEFAULT_SORT,
      enabled: !ECWID_CONFIG.DISPLAY.SHOW_DISABLED, // Only get enabled products unless configured otherwise
      ...options
    };

    return await this.searchProducts(params);
  }

  /**
   * Get product details by ID
   * @param {number} productId - Product ID
   */
  async getProduct(productId) {
    const endpoint = `/products/${productId}`;
    return await this.makeRequest(endpoint);
  }

  /**
   * Get product by SKU
   * @param {string} sku - Product SKU
   */
  async getProductBySku(sku) {
    const params = { sku };
    const result = await this.searchProducts(params);
    return result.items && result.items.length > 0 ? result.items[0] : null;
  }

  /**
   * Get store profile information
   */
  async getStoreProfile() {
    const endpoint = '/profile';
    return await this.makeRequest(endpoint);
  }

  /**
   * Search products with advanced filters
   * @param {object} filters - Advanced filter options
   */
  async advancedProductSearch(filters) {
    const params = {
      keyword: filters.keyword || '',
      category: filters.categoryId || '',
      priceFrom: filters.minPrice || '',
      priceTo: filters.maxPrice || '',
      inStock: filters.inStockOnly || '',
      enabled: true,
      limit: filters.limit || 50,
      offset: filters.offset || 0,
      sortBy: filters.sortBy || 'RELEVANCE'
    };

    return await this.searchProducts(params);
  }

  /**
   * Get product categories
   */
  async getCategories() {
    const endpoint = '/categories';
    return await this.makeRequest(endpoint);
  }

  /**
   * Format product data for display
   * @param {object} product - Raw product data from API
   */
  formatProductForDisplay(product) {
    return {
      id: product.id,
      name: product.name,
      sku: product.sku || '',
      price: product.price || 0,
      formattedPrice: product.defaultDisplayedPriceFormatted || '',
      description: product.description || '',
      imageUrl: product.imageUrl || product.thumbnailUrl || '',
      inStock: product.inStock,
      unlimited: product.unlimited,
      quantity: product.quantity || 0,
      url: product.url || '',
      categories: product.categories || [],
      attributes: product.attributes || [],
      variations: product.combinations || [],
      weight: product.weight || 0,
      dimensions: product.dimensions || {},
      enabled: product.enabled
    };
  }

  /**
   * Extract search suggestions from products
   * @param {array} products - Array of products
   */
  extractSearchSuggestions(products) {
    return products.map(product => ({
      id: product.id,
      name: product.name,
      sku: product.sku,
      price: product.defaultDisplayedPriceFormatted,
      imageUrl: product.thumbnailUrl,
      inStock: product.inStock,
      description: product.description?.substring(0, 100) + '...' || ''
    }));
  }

  /**
   * Handle API errors gracefully
   * @param {Error} error - Error object
   */
  handleApiError(error) {
    console.error('Ecwid API Error:', error);
    
    if (error.message.includes('Rate limit')) {
      return {
        success: false,
        error: 'Too many requests. Please wait a moment and try again.',
        type: 'RATE_LIMIT'
      };
    }
    
    if (error.message.includes('401')) {
      return {
        success: false,
        error: 'Authentication failed. Please check your API token.',
        type: 'AUTH_ERROR'
      };
    }
    
    if (error.message.includes('404')) {
      return {
        success: false,
        error: 'Resource not found.',
        type: 'NOT_FOUND'
      };
    }
    
    return {
      success: false,
      error: 'An unexpected error occurred. Please try again.',
      type: 'UNKNOWN'
    };
  }
}

// Create singleton instance
const ecwidService = new EcwidService();

export default ecwidService;
