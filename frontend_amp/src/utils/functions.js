

export const formatDate = (format = 'yyyy-mm-dd', time = null) => {
    let date;
    // console.log("time",time)
    date = time === null ? new Date() : new Date(time);
    if (isNaN(date.getTime())) return '';

    const year = date.getUTCFullYear();
    const month = String(date.getUTCMonth()).padStart(2, '0');
    const day = String(date.getUTCDate()).padStart(2, '0');
    // console.log("date",year,month,day)

    const monthNames = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
    ];
    const monthName = monthNames[date.getUTCMonth()];

    const monthAbbs = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
    ];
    const monthAbb = monthAbbs[date.getUTCMonth()];

    const formattedDate = format
        .replace('yyyy', year.toString())
        .replace('yy', year.toString().substr(-2))
        .replace('dd', day)
        .replace('Mmm', monthName)
        .replace('Amm', monthAbb)
        .replace('mm', month);

    return formattedDate;
};

export const formatDate1 = (dateString: string) => {
    const [year, month, day] = dateString.split('-');
    return `${month}/${day}/${year}`;
};

export const orderStatusFn = (type = null) => {
    if (type == 'received') {
        return [
            {
                name: 'Incoming',
                value: 'incoming',
                color: 'success',
                style: 'green',
            },
            {
                name: 'In-Progress',
                value: 'in_progress',
                color: 'info',
                style: 'blue',
            },
            {
                name: 'Confirmed',
                value: 'confirmed',
                color: 'indigo',
                style: 'sky',
            },
            {
                name: 'Partially Delivered',
                value: 'partially_delivered',
                color: 'purple',
                style: 'purple',
            },
            {
                name: 'Delivered',
                value: 'delivered',
                color: 'gray',
                style: 'gray',
            },
            {
                name: 'In Storage',
                value: 'in_storage',
                color: 'black',
                style: 'black',
            },
            {
                name: 'Back Order',
                value: 'back_order',
                color: 'warning',
                style: 'orange',
            },
            {
                name: 'Returned',
                value: 'returned',
                color: 'red',
                style: 'red',
            },
        ];
    } else if (type == 'return') {
    } else if (type == 'user') {
        return [
            {
                name: 'Incoming',
                value: 'incoming',
                color: 'success',
                style: 'green',
            },
        ];
    } else {
        const data = {
            incoming: {
                name: 'Incoming',
                value: 'incoming',
                color: 'success',
                style: 'green',
            },
            in_progress: {
                name: 'In-Progress',
                value: 'in_progress',
                color: 'info',
                style: 'blue',
            },
            confirmed: {
                name: 'Confirmed',
                value: 'confirmed',
                color: 'indigo',
                style: 'sky',
            },
            partially_delivered: {
                name: 'Partially Delivered',
                value: 'partially_delivered',
                color: 'purple',
                style: 'purple',
            },
            delivered: {
                name: 'Delivered',
                value: 'delivered',
                color: 'gray',
                style: 'gray',
            },
            in_storage: {
                name: 'In Storage',
                value: 'in_storage',
                color: 'black',
                style: 'black',
            },
            back_order: {
                name: 'Back Order',
                value: 'back_order',
                color: 'warning',
                style: 'orange',
            },
            returned: {
                name: 'Returned',
                value: 'returned',
                color: 'red',
                style: 'red',
            },
            partially_returned: {
                name: 'Partially Returned',
                value: 'partially_returned',
                color: 'red',
                style: 'red',
            },
            not_available: {
                name: 'Not Available',
                value: 'not_available',
                color: 'red',
                style: 'red',
            }
        };
        return data[type] ? data[type] : Object.values(data);
    }
};

export const checkboxClassFn = (color: string) => {
    return `text-${color} focus:ring-${color} dark:focus:ring-${color} focus:ring-2 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600 rounded bg-gray-100 border-gray-300`;
};

export const inputClassFn = () => {
    return 'block px-2.5 pb-2.5 pt-4 w-full text-sm text-gray-900 bg-transparent rounded-lg border-1 border-gray-300 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-blue-500 focus:outline-none focus:ring-0 focus:border-blue-600 peer';
};
export const lableClassFn = () => {
    return 'absolute text-sm text-gray-500 dark:text-gray-400 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white dark:bg-gray-900 px-2 peer-focus:px-2 peer-focus:text-blue-600 peer-focus:dark:text-blue-500 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1';
};
export const inputTdClass = () => {
    return 'text-gray-900 text-sm dark:text-white bg-transparent border-0 focus:border-0 focus:ring-0 pl-1 p-0 d-block w-full';
};
export const tdClass = () => {
    return 'px-1 border border-gray-200 dark:border-gray-600 text-center py-2';
};
export const funRowGenerate = (rows: any, keys: any, number = 10) => {
    for (let i = 0; i < number; i++) {
        rows.push(keys);
    }

    let idCounter = 1; // Initialize a counter for IDs

    return rows.map((item: any, index: number) => ({
        // id: idCounter++,
        ...item,
    }));
};
export const areAllValuesEmpty = (obj: any) => {
    for (const key in obj) {
        if (
            obj.hasOwnProperty(key) &&
            typeof obj[key] === 'string' &&
            obj[key] !== ''
        ) {
            return false;
        }
    }
    return true;
};
export const maskeMonyFun2 = (
  event: string | number | bigint | React.ChangeEvent<HTMLInputElement>
) => {
  const formatNumber = (value: string) => {
    const floatValue = parseFloat(value);
    return isNaN(floatValue) ? '' : floatValue.toFixed(2);
  };

  if (
    typeof event === 'string' ||
    typeof event === 'number' ||
    typeof event === 'bigint'
  ) {
    return formatNumber(event.toString()).replace(
      /(\d)(?=(\d{3})+(?!\d))/g,
      '$1,'
    );
  } else {
    const inputValue = event.target.value.replace(/\D/g, '');
    const formattedValue = inputValue.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');

    event.target.value = formatNumber(formattedValue);
  }
};

export const maskeMonyFun3 = (
  event: string | number | bigint | React.ChangeEvent<HTMLInputElement>
) => {
  if (
    typeof event === 'string' ||
    typeof event === 'number' ||
    typeof event === 'bigint'
  ) {
    const numericValue = event.toString().replace(/[^\d.]/g, '');

    const [integerPart, decimalPart] = numericValue.split('.');
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    const formattedDecimal = decimalPart ? `.${decimalPart.slice(0, 2)}` : '.00';

    return formattedInteger + formattedDecimal;
  } else {
    const inputValue = event.target.value;
    const numericValue = inputValue.replace(/[^\d.]/g, '');

    const [integerPart, decimalPart] = numericValue.split('.');
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    const formattedDecimal = decimalPart ? `.${decimalPart.slice(0, 2)}` : '.00';

    event.target.value = formattedInteger + formattedDecimal;
  }
};

export const maskeMonyFun = (
  input: string | number | bigint | React.ChangeEvent<HTMLInputElement>
): string => {
  let inputValue: string;

  if (typeof input === 'string') {
    inputValue = input;
  } else if (typeof input === 'number' || typeof input === 'bigint') {
    inputValue = input.toString();
  } else {
    inputValue = input.target.value;
  }

  const numericValue = inputValue.replace(/[^\d.]/g, '');

  const [integerPart, decimalPart] = numericValue.split('.');
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  const formattedDecimal = decimalPart ? `.${decimalPart.slice(0, 2)}` : '.00';

  return formattedInteger + formattedDecimal;
};




export function diff_months_count( endDate) {
    let startDate=new Date()
    if(endDate == null){
        return false
    }else{
        var months;
        var d1 = new Date(startDate);
        var d2 = new Date(endDate);
        months = (d2.getFullYear() - d1.getFullYear()) * 12;
        months -= d1.getMonth();
        months += d2.getMonth();
        
        // console.log(months)
        return months < 6 ? true : false;
    }
}

