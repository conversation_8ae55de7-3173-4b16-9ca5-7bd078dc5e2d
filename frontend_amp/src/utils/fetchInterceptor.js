// Create a wrapper around fetch to handle token expiration and network errors
export const fetchWithAuth = async (url, options = {}) => {
  // Get token from storage
  const token = sessionStorage.getItem("amp.amedicalpartner.com.token") || 
                localStorage.getItem("amp.amedicalpartner.com.token");
  
  // Parse token if it exists
  const parsedToken = token ? JSON.parse(token) : "";
  
  // Set default headers with Authorization if token exists
  const headers = {
    ...options.headers,
    ...(parsedToken && { 'Authorization': `Bearer ${parsedToken}` })
  };
  
  try {
    // Add timeout to prevent indefinite waiting
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout
    
    // Make the fetch request
    const response = await fetch(url, {
      ...options,
      headers,
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    // Handle 401 Unauthorized (expired token)
    if (response.status === 401) {
      console.log('Token expired, logging out');
      
      // Clear token and user data
      sessionStorage.removeItem("amp.amedicalpartner.com.token");
      localStorage.removeItem("amp.amedicalpartner.com.token");
      sessionStorage.removeItem("amp.amedicalpartner.com.selectuser");
      localStorage.removeItem("amp.amedicalpartner.com.userinfo");
      
      // Only redirect if we're not already on the signin page
      if (!window.location.pathname.includes('/signin')) {
        // Use history API instead of directly changing location to avoid hash issues
        if (window.history && window.history.pushState) {
          window.history.pushState({}, '', '/signin?tokenExpired=true');
          // Force a page refresh to ensure clean state
          window.location.reload();
        } else {
          // Fallback for older browsers
          window.location.href = '/signin?tokenExpired=true';
        }
      }
      
      // Throw an error to stop further processing
      throw new Error('Token expired');
    }
    
    return response;
  } catch (error) {
    // Handle abort errors (timeouts)
    if (error.name === 'AbortError') {
      console.error('Request timed out');
      throw new Error('Request timed out. Please try again.');
    }
    
    // Handle network errors
    if (error.name === 'TypeError' && error.message.includes('network')) {
      console.error('Network error:', error);
      // Don't redirect for network errors
      throw new Error('Network error. Please check your connection.');
    }
    
    // Re-throw other errors
    throw error;
  }
};

export default fetchWithAuth;
