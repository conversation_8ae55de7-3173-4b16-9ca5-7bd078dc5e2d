import React, { Component } from 'react';
import {
  Box,
  <PERSON>ton,
  TextField,
  Typography,
  Alert,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  CircularProgress,
  IconButton,
  InputAdornment,
  Tooltip
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import ecwidService from '../services/EcwidService';

class EcwidApiTest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      testToken: '',
      testResults: [],
      loading: false,
      storeInfo: null,
      showToken: false
    };

    this.testPublicAccess = this.testPublicAccess.bind(this);
    this.testWithToken = this.testWithToken.bind(this);
    this.testProductSearch = this.testProductSearch.bind(this);
  }

  async testPublicAccess() {
    this.setState({ loading: true });
    this.addTestResult('🔍 Testing public store access...');

    try {
      const result = await ecwidService.testPublicAccess();
      
      if (result.success) {
        this.addTestResult('✅ Store access successful!');
        this.addTestResult(`📊 Store: ${result.data.generalInfo?.storeTitle || 'Unknown'}`);
        this.addTestResult(`🆔 Store ID: ${ecwidService.storeId}`);
        this.setState({ storeInfo: result.data });
      } else {
        this.addTestResult(`❌ Store access failed: ${result.error}`);
        this.addTestResult('💡 Check if your store ID is correct in the config');
      }
    } catch (error) {
      this.addTestResult(`❌ Error: ${error.message}`);
    }

    this.setState({ loading: false });
  }

  async testWithToken() {
    if (!this.state.testToken.trim()) {
      this.addTestResult('❌ Please enter an API token to test');
      return;
    }

    this.setState({ loading: true });
    this.addTestResult('🔑 Testing API token...');

    try {
      // Set the token temporarily
      const originalToken = ecwidService.token;
      ecwidService.setToken(this.state.testToken);

      // Test authenticated endpoint
      const profile = await ecwidService.getStoreProfile();
      
      this.addTestResult('✅ API token is valid!');
      this.addTestResult(`📊 Store: ${profile.generalInfo?.storeTitle || 'Unknown'}`);
      this.addTestResult(`📧 Email: ${profile.account?.accountEmail || 'Unknown'}`);
      
      // Restore original token
      ecwidService.setToken(originalToken);
      
    } catch (error) {
      this.addTestResult(`❌ Token test failed: ${error.message}`);
      this.addTestResult('💡 Check if your API token is correct and has proper permissions');
      
      // Restore original token
      ecwidService.setToken(ecwidService.token);
    }

    this.setState({ loading: false });
  }

  async testProductSearch() {
    if (!this.state.testToken.trim()) {
      this.addTestResult('❌ Please enter an API token to test product search');
      return;
    }

    this.setState({ loading: true });
    this.addTestResult('🔍 Testing product search...');

    try {
      // Set the token temporarily
      const originalToken = ecwidService.token;
      ecwidService.setToken(this.state.testToken);

      // Test product search
      const results = await ecwidService.searchProductsByKeyword('test', { limit: 5 });
      
      this.addTestResult(`✅ Product search successful!`);
      this.addTestResult(`📦 Found ${results.total} total products`);
      this.addTestResult(`📋 Showing ${results.count} products`);
      
      if (results.items && results.items.length > 0) {
        this.addTestResult('🛍️ Sample products:');
        results.items.slice(0, 3).forEach((product, index) => {
          this.addTestResult(`  ${index + 1}. ${product.name} (${product.sku || 'No SKU'})`);
        });
      }
      
      // Restore original token
      ecwidService.setToken(originalToken);
      
    } catch (error) {
      this.addTestResult(`❌ Product search failed: ${error.message}`);
      this.addTestResult('💡 Make sure your token has "Read catalog" permissions');
      
      // Restore original token
      ecwidService.setToken(ecwidService.token);
    }

    this.setState({ loading: false });
  }

  addTestResult(message) {
    this.setState(prevState => ({
      testResults: [...prevState.testResults, {
        id: Date.now() + Math.random(),
        message,
        timestamp: new Date().toLocaleTimeString()
      }]
    }));
  }

  clearResults() {
    this.setState({ testResults: [] });
  }

  render() {
    const { testToken, testResults, loading, showToken } = this.state;

    return (
      <Box sx={{ maxWidth: 800, margin: '0 auto', p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Ecwid API Test Tool
        </Typography>
        
        <Typography variant="body1" color="text.secondary" paragraph>
          Use this tool to test your Ecwid API access and get your API token.
        </Typography>

        {/* Store ID Info */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Current Configuration
            </Typography>
            <Typography variant="body2">
              <strong>Store ID:</strong> {ecwidService.storeId}
            </Typography>
            <Typography variant="body2">
              <strong>API Base URL:</strong> {ecwidService.baseUrl}
            </Typography>
          </CardContent>
        </Card>

        {/* Test Buttons */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              API Tests
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
              <Button 
                variant="outlined" 
                onClick={this.testPublicAccess}
                disabled={loading}
              >
                Test Store Access
              </Button>
              
              <Button 
                variant="outlined" 
                color="warning"
                onClick={() => this.clearResults()}
                disabled={loading}
              >
                Clear Results
              </Button>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Typography variant="subtitle1" gutterBottom>
              API Token Testing
            </Typography>
            
            <TextField
              fullWidth
              label="Enter your API token here"
              placeholder="secret_xxxxxxxxxxxxxxxxxx"
              value={testToken}
              onChange={(e) => this.setState({ testToken: e.target.value })}
              sx={{ mb: 1 }}
              type={this.state.showToken ? "text" : "password"}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <Tooltip title={this.state.showToken ? "Hide token" : "Show token"}>
                      <IconButton
                        aria-label="toggle token visibility"
                        onClick={() => this.setState({ showToken: !this.state.showToken })}
                        edge="end"
                      >
                        {this.state.showToken ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </Tooltip>
                  </InputAdornment>
                ),
              }}
              helperText="Click the eye icon to show/hide your token"
            />
            
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Button 
                variant="contained" 
                onClick={this.testWithToken}
                disabled={loading || !testToken.trim()}
              >
                Test API Token
              </Button>
              
              <Button 
                variant="contained" 
                color="secondary"
                onClick={this.testProductSearch}
                disabled={loading || !testToken.trim()}
              >
                Test Product Search
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Results */}
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Test Results
              </Typography>
              {loading && <CircularProgress size={20} sx={{ ml: 2 }} />}
            </Box>
            
            {testResults.length === 0 ? (
              <Typography color="text.secondary">
                No tests run yet. Click a test button above to start.
              </Typography>
            ) : (
              <List dense>
                {testResults.map((result) => (
                  <ListItem key={result.id} sx={{ py: 0.5 }}>
                    <ListItemText
                      primary={result.message}
                      secondary={result.timestamp}
                      primaryTypographyProps={{
                        fontFamily: 'monospace',
                        fontSize: '0.9rem'
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </CardContent>
        </Card>

        {/* Instructions */}
        <Alert severity="info" sx={{ mt: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            How to get your API token:
          </Typography>
          <Typography variant="body2" component="div">
            1. Go to your Ecwid Control Panel<br/>
            2. Navigate to <strong>Apps → My Apps</strong><br/>
            3. Click <strong>"Develop an app"</strong> or <strong>"Create app"</strong><br/>
            4. Set permissions: <strong>"Read catalog"</strong> and <strong>"Read store profile"</strong><br/>
            5. Copy the <strong>Access Token</strong> and paste it above
          </Typography>
        </Alert>
      </Box>
    );
  }
}

export default EcwidApiTest;
