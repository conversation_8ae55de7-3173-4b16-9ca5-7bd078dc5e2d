import React, { Component, useEffect, useState } from 'react'
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import { Avatar, Badge, Box, IconButton, Skeleton, TextField, Typography } from '@mui/material';
import { green, grey, pink, red, yellow } from '@mui/material/colors';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import NotificationsIcon from '@mui/icons-material/Notifications';
import { redirect, replace, useLocation, useNavigate } from 'react-router-dom';
import { useDashboard } from '../context/DashboardContext';
import { useSidebar } from './SideBar'; // Import the sidebar context

const CustomOption = ({ innerProps, label, data }) => (
    <Box
        display={'flex'}
        {...innerProps}
        // className='flex items-center cursor-pointer hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700 mx-1 rounded-lg text-gray-900 dark:text-white'
    >
        <Avatar
            src={data.image}
            alt={label}
            height={100}
            width={100}
            className='w-8 h-8 mx-2 rounded-full'
        />
        <span>{label}</span>
        {data.status ?
            (
                <span
                    style={{
                        width:'16px',
                        height:'16px',
                        marginLeft:'auto',
                        marginRight:2,
                        padding:1,
                        backgroundColor:green[600],
                        borderRadius:'50%',
                        border:2,
                    }}                    
                ></span>
            ):(
                <span
                    style={{
                        width:'16px',
                        height:'16px',
                        marginLeft:'auto',
                        marginRight:2,
                        padding:1,
                        backgroundColor:green[600],
                        borderRadius:'50%',
                        border:2,
                    }}                    
                ></span>
        )}
    </Box>
);
const ComboboxWithStatus = () => {
    const navigate = useNavigate()
    const location = useLocation()
    const dashboard = useDashboard()
    const user = useAuth()
    const [index, setIndex] = useState(null)
    const [selectedOption, setSelectedOption] = useState(null)
    const [notificationData, setNotificationData] = useState(user.notificationData)
    const [userNotification, setUserNotification] = useState({})
    const theme = useTheme()
    const { sidebarCollapsed } = useSidebar() // Get sidebar state
    
    useEffect(() => {
        if(user.selectUser){
            {Object.keys(user.userList).map((userIndex) => {
                    if (user.userList[userIndex].id==user.selectUser.id){
                        setIndex(userIndex)
                    }
                })
            }
        }
    }, [user.selectUser])
    
    useEffect(() => {
        async function fetchData() {
            // You can await here
            const response = await user.getUserListAction(user.token)
            
            // ...
        }
        fetchData();
        // await user.getUserListAction(user.token)
    },[user.users])
    
    useEffect(()=>{
        let userListNotif={}
        Object.keys(user.notificationData).map((notification,index)=>{
            const item=user.notificationData[notification]
            if (item.unread){
                if(!userListNotif.hasOwnProperty(item.recipient.id)){
                    userListNotif[item.recipient.id]={
                        quantity:0,
                        notificationId:[]
                    }
                }
                const uselistnotif=userListNotif[item.recipient.id]
                uselistnotif.quantity=uselistnotif.quantity+1
                uselistnotif.notificationId.push(item.id)
                userListNotif[item.recipient.id]=uselistnotif
                // userListNotif[item.recipient.id]=quantity+1
            }
        })
        setUserNotification(userListNotif)
    },[user.notificationData])
    
    const handleReadNotification = async (item) => {
        let notificationDat={
            admin_id:null,
        }
        notificationDat.admin_id=item.id
        let response=await user.readSingleUserNotifications(user.token,notificationDat)
        if(response.status!=200){
        }else{
          if(response.msg=="success"){
            // setNotificationData(await user.notificationData)
          }else{
    
          }
        }
    }
    
    const handleSelectedOption = (event,key) => {
        const {
            target: { value},
        } = event;
        handleReadNotification(user.userList[value])
        user.setSelectUser(user.userList[value])
        setIndex(value)
        setSelectedOption(typeof value === 'string' ? value.split(',') : value)
        dashboard.initialAllState(user.token,user.userList[value].id)
        // navigate(0)
    };

    return (
        <>
            <Box
                sx={{
                    display: 'block',
                    padding: 0,
                    width: '100%',
                    marginTop: 1,
                    background: theme.selectTheme.background,
                    // Adjust width based on sidebar state
                    maxWidth: sidebarCollapsed ? 'calc(100% - 10px)' : '100%',
                    transition: 'max-width 0.3s ease-in-out',
                }}
            >
                <FormControl 
                    size='small'
                    fullWidth
                    sx={{
                        borderColor: theme.selectTheme.fontColor,
                        backgroundColor: theme.selectTheme.background,
                        color: theme.selectTheme.fontColor,
                        // Ensure the form control adjusts with sidebar
                        width: '100%',
                        '& .MuiOutlinedInput-root': {
                            width: '100%',
                        }
                    }}
                >
                    {!user.userList ? (
                        <>
                            <Typography component="div" variant={'h3'}>
                                <Skeleton /> 
                            </Typography>
                        </>
                    ) : (
                    <>
                        <InputLabel 
                            shrink
                            variant="outlined"
                            id="demo-simple-select-label"
                            sx={{
                                color: theme.selectTheme.fontColor
                            }}
                        >Practice</InputLabel>
                        <Select
                            size='medium'
                            value={index}
                            label='Practice'
                            placeholder='Practice'
                            onChange={handleSelectedOption}
                            MenuProps={{ 
                                MenuListProps: { 
                                    disablePadding: true,
                                    sx: {
                                        "&& .Mui-selected": {
                                            backgroundColor: theme.selectTheme.sidebarSelected,
                                            '&:hover': {backgroundColor: theme.selectTheme.selectHover},
                                        }
                                    }
                                },
                                // Ensure menu opens in the right position
                                anchorOrigin: {
                                    vertical: 'bottom',
                                    horizontal: 'left',
                                },
                                transformOrigin: {
                                    vertical: 'top',
                                    horizontal: 'left',
                                },
                                // Ensure menu width matches the select width
                                PaperProps: {
                                    style: {
                                        width: 'auto',
                                        minWidth: '200px',
                                    },
                                },
                            }}
                            sx={{
                                display: 'flex',
                                flexDirection: 'row',
                                backgroundColor: theme.selectTheme.background,
                                color: theme.selectTheme.fontColor,
                                '& .MuiSelect-select': {
                                    width: '100%',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    // Adjust padding for better text display
                                    padding: '8px 14px',
                                    // Ensure text doesn't overflow
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                },
                                "&:hover": {
                                    "&& fieldset": {
                                        border: "3px solid green"
                                    }
                                }
                            }}
                            style={{
                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',
                                // Ensure the select is responsive
                                width: '100%',
                            }}
                        >
                            {Object.keys(user.userList).map((userIndex) => (
                                <MenuItem
                                    key={user.userList[userIndex].id}
                                    value={userIndex}
                                    sx={{
                                        '&:hover': {backgroundColor: theme.selectTheme.selectHover},
                                        backgroundColor: theme.selectTheme.background_2,
                                        color: theme.selectTheme.fontColor,
                                        display: 'flex',
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                        // Ensure text is visible
                                        padding: '8px 14px',
                                    }}
                                >   
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                            color: theme.selectTheme.fontColor,
                                        }}
                                    >
                                        <Typography 
                                            sx={{
                                                textAlign: 'left',
                                                justifyContent: 'center',
                                                alignContent: 'center',
                                                alignItems: 'center',
                                                // Ensure text doesn't get cut off
                                                whiteSpace: 'nowrap',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                maxWidth: '150px',
                                            }}
                                        >
                                            {user.userList[userIndex].first_name}
                                        </Typography>
                                    </Box>
                                    <Box
                                        sx={{
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            color: theme.selectTheme.fontColor,
                                            marginLeft: '8px', // Add some spacing
                                        }}
                                    >   
                                        <IconButton size="small">
                                            <Badge
                                                sx={{
                                                    color: theme.selectTheme.iconColor
                                                }}
                                                anchorOrigin={{
                                                    vertical: 'bottom',
                                                    horizontal: 'left',
                                                }} 
                                                badgeContent={userNotification.hasOwnProperty(user.userList[userIndex].id) ? userNotification[user.userList[userIndex].id].quantity : 0} 
                                                color="success"
                                            >
                                                <NotificationsIcon color={theme.selectTheme.iconColor} fontSize="small" />
                                            </Badge>
                                        </IconButton>
                                    </Box>
                                </MenuItem>
                            ))}
                        </Select>
                    </>
                    )}
                </FormControl>
            </Box>
        </>
    )
}

export default ComboboxWithStatus;
