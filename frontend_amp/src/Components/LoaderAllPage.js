
import { Box, Card, CircularProgress } from '@mui/material';
import React, { useState, useEffect } from 'react';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';

const LoaderAllPage = () => {
    const [isLoading, setIsLoading] = useState(true);
    const theme=useTheme()
    const user = useAuth();
    // const theme=useTheme()

    // Simulate loading for 3 seconds
    useEffect(() => {
        const timer = setTimeout(() => {
            setIsLoading(false);
        }, 3000);

        return () => {
            clearTimeout(timer);
        };
    }, [user.isLoading]);
    return (
        <>
        <Box
            sx={{
                width:"100%",
                minHeight:theme.windowDimensions.height,
                justifyContent:"center",
                alignItems:"center"
            }}
        >            
            <Card
                sx={{
                    display:'flex',
                    position:'absolute',
                    top:'80px',
                    left:(theme.windowDimensions.width/5),
                    width:'60%',
                    height:"45%",
                    padding:4,
                    textAlign:'center',
                    border:'gray',
                    shadow:true,
                    alignItems:'center',
                    justifyContent:'center'

                }}
                // className='w-[60%] p-4 text-center border border-gray-200 rounded-lg shadow sm:p-8 dark:border-gray-700'
            >
                <CircularProgress />
        </Card>
        </Box>

        </>
    );
};
export default LoaderAllPage;
