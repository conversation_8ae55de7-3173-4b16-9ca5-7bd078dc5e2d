
import React, { Component } from 'react'
import { Card,Box, IconButton, TextField, Typography, Modal } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { ColorButton } from '../ColorButton';

export default class DeliveredQty extends Component {
    constructor(props){
        super(props)
        this.state={
            delivered_qty:0
        }
    }
    handleSubmit(){
        this.props.DeliveredQty(this.state.delivered_qty)
    }
  render() {
    return (
      <Modal
      disablePortal
      disableEnforceFocus
      disableAutoFocus
      open
      aria-labelledby="server-modal-title"
      aria-describedby="server-modal-description"
      sx={{
        display: 'flex',
        p: 1,
        alignItems: 'center',
        justifyContent: 'center',
      }}
      // onClose={this.props.onClose}
      // container={() => rootRef.current}
    >
      <Card
        sx={{
            width:'20%',
            padding:4,
            textAlign:'center',
            padding:2,
            // border:'gray',
            // shadow:true
        }}
      >
        <Box
          sx={(theme) => ({
            position: 'relative',
            bgcolor: 'background.paper',
            // boxShadow: theme.shadows[5],
          })}
        >
          <Box
            sx={{
                width: '100%',
                height: '100%',
                display: 'flex',
                flexDirection:'column',
                textAlign:'left',
                gap:2,
            }}
          >
            <Box
              sx={{
                display:'flex',
                flexDirection:'row',
                justifyContent:'space-between'
              }}
            >
              <Typography
                // width={'90%'}
              >
              </Typography>
              <IconButton
                onClick={() => {
                  this.props.onClose()
                }}
              >
                <CloseIcon  fontSize="small"/>
              </IconButton>
            </Box>
            <Box>
              <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                Delivered QTY 
              </Typography>
            </Box>
            <Box
                sx={{
                width: '100%',                                           
                }}
            >
              <TextField
                  fullWidth
                  size='small'
                  id='Delivered QTY'
                  label="Delivered QTY"
                  placeholder='Delivered QTY'
                  required
                  type='text'
                  value={this.state.delivered_qty}
                  onChange={(e) =>{
                    this.setState({
                        delivered_qty:e.target.value
                    })
                    }
                  }
              />
            </Box>
            <Box
               sx={{
                width: '100%',                                           
              }}
            >
            < ColorButton 
                variant="contained"
                sx={{
                  marginRight:1
                }}
                onClick={() =>{
                    this.handleSubmit()
                }}   
                >
                    Submit
              </ColorButton>
              <ColorButton 
                    variant="contained"
                    onClick={() => {
                      this.props.onClose(true)
                    }}
                >
                    Close
              </ColorButton>
            </Box>
          </Box>
        </Box>
      </Card>
    </Modal>
    )
  }
}