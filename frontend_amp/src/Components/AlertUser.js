import React, { Component } from 'react'
import { formatDate } from '../utils/functions'
import { Box, Divider, Skeleton, Typography } from '@mui/material'
import authHook from '../HOK/AuthHook'
import { compose } from 'redux'
import themeHook from '../HOK/ThemeHook'

class AlertUser extends Component {
    constructor(props){
        super(props)
        this.state={
            theme:props.theme,
            auth:props.auth,
            isLoading:true,
            // username:props.auth.user.username|null,
        }
    }
    async componentDidMount(){
        const auth=this.props.auth
        this.setState({
            auth:this.props.auth
        })
        let response=await this.props.auth.getUserInfoAction(auth.token)
        if(await response.status!=200){

        }else{
            if(response.msg=="success"){
                this.setState({
                    isLoading:false
                })
            }else{

            }
        }
    }
   
  render() {
    return (
      <>
        <Box
            sx={{
                display:'flex',
                justifyContent:'space-between',
                paddingTop:2,
                paddingLeft:5,
                paddingRight:5,
                paddingBottom:2,
                height:'64px',
                backgroundColor:this.props.theme.selectTheme.background,
                color: this.props.theme.selectTheme.fontColor
                // borderBottom:'1px solid #e5e7eb'
            }}
        >
            <Box
                sx={{
                    display:'flex'
                }}
            >
                <Typography 
                    sx={{
                        marginRight:'5px',
                        opacity:'70%',
                        fontWeight:700,
                        fontSize:'20px'
                    }}
                >
                    Hi 
                </Typography>
                {this.state.isLoading?(
                    <Skeleton variant="text" sx={{ fontSize: '1rem',width:'100px' }} animation="wave" />
                ):(
                <Typography 
                    sx={{
                        opacity:'90%',
                        fontWeight:700,
                        fontSize:'20px'
                    }}
                >
                    {this.props.auth.user.first_name||''},
                    {/* {this.props.auth.user.username}, */}
                </Typography>
                )}
                <span className='text-slate-500 dark:text-slate-200 text-opacity-90 dark:text-opacity-60 text-[20px] font-bold'>
                    {/* {session && session.user.name}, */}
                </span>
            </Box>
            <div className='grow '>
                <span className='text-slate-500 dark:text-slate-200 text-opacity-70  dark:text-opacity-50 text-[25px] font-bold'>
                    Welcome to
                </span>
                <span className='text-slate-500 dark:text-slate-200 text-[25px] font-bold'>
                    {' '}
                    AMP Extension Hub
                </span>
            </div>
            <div className='grow text-right'>
                {' '}
                <span className='text-slate-500 dark:text-slate-200 text-opacity-70 text-[20px] font-bold'>
                    {formatDate('Amm-dd-yyyy')}
                </span>
            </div>
        </Box>
        <Divider />
      </>
    )
  }
}
export default  compose(
    themeHook,
    authHook
)(AlertUser)