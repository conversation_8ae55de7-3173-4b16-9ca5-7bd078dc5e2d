import React, { Component, useEffect, useState } from 'react'
import PropTypes from 'prop-types'
import { Box, Button, Grid, Typography } from '@mui/material';
import {yellow,indigo, purple, grey, lime, green, pink,common } from '@mui/material/colors';
import {alpha} from '@mui/material';
import { useDashboard } from '../context/DashboardContext';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
//amber, blue, blueGrey, brown, common, cyan, deepOrange, deepPurple, green, grey, indigo, lightBlue, lightGreen, lime, orange, pink, purple, red, teal, yellow)
const BadgeNumber =()=>{
    const [isloading,setIsLoading]=useState(true);
    const [statusTemplate,setStatusTemplate]=useState({
                back_order: { value: 0, name: 'BackOrder', color: yellow },
                incoming: { value: 0, name: 'Incoming', color: green },
                in_progress: { value: 0, name: 'In-Progress', color: pink },
                confirmed: { value: 0, name: 'Confirmed', color: indigo },
                    // partially_delivered: {
            //     value: 0,
            //     name: 'PartiallyDelivered',
            //     color: purple,
            // },
            // delivered: { value: 0, name: 'Delivered', color: grey },
            // in_storage: { value: 0, name: 'InStorage', color: lime },
            // returned: { value: 0, name: 'Returned', color: grey },
            }
        )
    const dashboard=useDashboard();
    const user=useAuth();
    const theme=useTheme();
    const [userSelection,setUserSelection]=useState(user.selectUser.id)
    useEffect(() => {
        if(user.selectUser){
            if (userSelection!=user.selectUser.id){
                setStatusTemplate(
                    {
                        back_order: { value: 0, name: 'BackOrder', color: yellow },
                        incoming: { value: 0, name: 'Incoming', color: green },
                        in_progress: { value: 0, name: 'In-Progress', color: pink },
                        confirmed: { value: 0, name: 'Confirmed', color: indigo },
                            // partially_delivered: {
                    //     value: 0,
                    //     name: 'PartiallyDelivered',
                    //     color: purple,
                    // },
                    // delivered: { value: 0, name: 'Delivered', color: grey },
                    // in_storage: { value: 0, name: 'InStorage', color: lime },
                    // returned: { value: 0, name: 'Returned', color: grey },
                    }
                )
                setUserSelection(user.selectUser.id)
            }
        }
    }, [user.selectUser])
    useEffect(() => {
        if (dashboard.ordersTableData.length > 0) {
            const ordersTableData = dashboard.ordersTableData;

            // Create a local copy of the statusTemplate
            const updatedStatusTemplate = { ...statusTemplate };

            // Iterate over the orders and update the local copy
            Object.keys(ordersTableData).forEach((key) => {
                const item = ordersTableData[key];
                const status = item.status;

                if (updatedStatusTemplate.hasOwnProperty(status)) {
                    updatedStatusTemplate[status].value += 1; // Increment the value
                }
            });

            // Update the state once after the loop
            setStatusTemplate(updatedStatusTemplate);
            setIsLoading(false);
        }
    }, [dashboard.ordersTableData]);

    useEffect(() => {
        console.log("Updated Status Template:", statusTemplate);
    }, [statusTemplate]);

    return isloading?(
        <>
        </>
        ):(
            <>
                <Box
                    sx={{
                        // color:purple.A400,
                        width:'100%',
                        padding:2,
                        paddingBottom:1,
                        borderBottom:'1px solid #e5e7eb',
                        backgroundColor:theme.selectTheme.background_2,
                        color: theme.selectTheme.fontColor

                    }}
                >
                    <Box
                        sx={{
                            width:'100%',
                            display:'flex',
                            flexDirection:'row',
                            justifyContent: "space-between",
                            alignItems: "center",
                            paddingTop:2,
                            paddingBottom:2
                        }}
                    >
                            {Object.keys(statusTemplate).map((statusKey, index)=>{
                                const item = statusTemplate[statusKey];
                                return (
                                    <Box
                                        sx={{
                                            display:'block',
                                            textAlign:'center'
                                        }} 
                                        className='grow mx-3 text-center' 
                                        key={index}>
                                        <Typography
                                            sx={{
                                                
                                            }} 
                                            // className='text-xl font-semibold dark:text-white'
                                            >
                                            {item.value}
                                        </Typography>
                                        <Box
                                            sx={{
                                                margin:'auto',
                                                background:alpha(item.color.A400,0.7),
                                                borderRadius:28,
                                                // px:4,
                                            }}
                                            size='sm'
                                        >
                                            <Typography 
                                                variant='p'
                                                sx={{
                                                    color:'black',
                                                    padding:1
                                                }}         
                                            >
                                                {item.name}
                                            </Typography>
                                        </Box>
                                    </Box>
                                );
                                })
                            }
                        {/* </Grid> */}
                    </Box>
                </Box>
            </>
        )
}
export default BadgeNumber;