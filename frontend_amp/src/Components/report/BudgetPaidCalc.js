import React, { useEffect, useState } from 'react'
import { Box, Typography, FormControl, Select, MenuItem, InputLabel, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from '@mui/material';
import { alpha } from '@mui/material';
import { green, pink, yellow, blue } from '@mui/material/colors'
import { useAuth } from '../../context/AuthContext';
import { useDashboard } from '../../context/DashboardContext';
import { useTheme } from '../../context/ThemeContext';
const BudgetPaidCalc = () => {
    const dashboard = useDashboard();
    const user = useAuth();
    const theme = useTheme();
    const [thisMonth, setThisMonth] = useState(parseInt(new Date().getUTCMonth()+1));
    const [thisYear, setThisYear] = useState(parseInt(new Date().getUTCFullYear()));
    const [isloading, setIsLoading] = useState(true);
    const [userSelection, setUserSelection] = useState(0);
    const [calcPaidTemlate, setCalcPaidTemplate] = useState(0);
    const [selectedYear, setSelectedYear] = useState(null);
    const [selectedMonth, setSelectedMonth] = useState(null);
    const [yearSummaries, setYearSummaries] = useState({});
    const [availableYears, setAvailableYears] = useState([]);

    // Initialize template for monthly data
    const initialTemplate = {
        1:{would_paid: { value: 0.00, name: 'Would\'ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
        2:{would_paid: { value: 0.00, name: 'Would\'ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
        3:{would_paid: { value: 0.00, name: 'Would\'ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
        4:{would_paid: { value: 0.00, name: 'Would\'ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
        5:{would_paid: { value: 0.00, name: 'Would\'ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
        6:{would_paid: { value: 0.00, name: 'Would\'ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
        7:{would_paid: { value: 0.00, name: 'Would\'ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
        8:{would_paid: { value: 0.00, name: 'Would\'ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
        9:{would_paid: { value: 0.00, name: 'Would\'ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
        10:{would_paid: { value: 0.00, name: 'Would\'ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
        11:{would_paid: { value: 0.00, name: 'Would\'ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
        12:{would_paid: { value: 0.00, name: 'Would\'ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
    };
    
    const [paidTemplate, setPaidTemplate] = useState(initialTemplate);

    // Handle year selection change
    const handleYearChange = (event) => {
        setSelectedYear(event.target.value);
        setSelectedMonth(null); // Reset month when year changes
    };

    // Handle month selection change
    const handleMonthChange = (event) => {
        setSelectedMonth(event.target.value);
    };

    // Reset data when user changes
    useEffect(() => {
        if(user.selectUser){
            if (userSelection !== user.selectUser.id){
                // Reset all data with a deep copy of the initial template
                setPaidTemplate(JSON.parse(JSON.stringify(initialTemplate)));
                setYearSummaries({});
                setCalcPaidTemplate(0);
                setUserSelection(user.selectUser.id);
                setIsLoading(true);
                setSelectedYear(null);
                setSelectedMonth(null);
            }
        }
    }, [user.selectUser]);

    // Calculate data from report table
    useEffect(() => {
        if(calcPaidTemlate === 0){
            if(dashboard.summaryReportTableData){
                const reportTableData = dashboard.summaryReportTableData;
                // Create new objects to avoid mutation issues
                const newPaidTemplate = JSON.parse(JSON.stringify(initialTemplate));
                const newYearSummaries = {};
                const years = [];
                
                // Process data by year and month
                Object.keys(reportTableData).forEach(year => {
                    const yearInt = parseInt(year);
                    years.push(yearInt);
                    
                    // Initialize year summary if it doesn't exist
                    if (!newYearSummaries[yearInt]) {
                        newYearSummaries[yearInt] = {
                            would_paid: 0,
                            paid: 0,
                            saved: 0,
                            count: 0,
                            qty: 0
                        };
                    }
                    
                    Object.keys(reportTableData[year]).forEach(month => {
                        const monthInt = parseInt(month);
                        
                        if (reportTableData[year][month].would_paid !== undefined) {
                            // Direct summary data format
                            newYearSummaries[yearInt].would_paid += reportTableData[year][month].would_paid;
                            newYearSummaries[yearInt].paid += reportTableData[year][month].paid;
                            newYearSummaries[yearInt].saved += reportTableData[year][month].saved;
                            newYearSummaries[yearInt].count += reportTableData[year][month].count_report || 1;
                            newYearSummaries[yearInt].qty += reportTableData[year][month].qty || 0;
                            
                            if (yearInt === thisYear && newPaidTemplate[monthInt]) {
                                newPaidTemplate[monthInt].would_paid.value = reportTableData[year][month].would_paid;
                                newPaidTemplate[monthInt].paid.value = reportTableData[year][month].paid;
                                newPaidTemplate[monthInt].saved.value = reportTableData[year][month].saved;
                            }
                        } else {
                            // Original format with individual reports
                            let monthCount = 0;
                            let monthQty = 0;
                            
                            Object.keys(reportTableData[year][month]).forEach(report => {
                                const item = reportTableData[year][month][report];
                                monthCount++;
                                monthQty += item.qty || 0;
                                
                                // Update monthly data for current year
                                if (yearInt === thisYear && newPaidTemplate[monthInt]) {
                                    newPaidTemplate[monthInt].would_paid.value += (item.qty * item.would_paid);
                                    newPaidTemplate[monthInt].paid.value += (item.qty * item.paid);
                                    newPaidTemplate[monthInt].saved.value += parseFloat((item.qty * item.saved).toFixed(2));
                                }
                                
                                // Update year summary
                                newYearSummaries[yearInt].would_paid += (item.qty * item.would_paid);
                                newYearSummaries[yearInt].paid += (item.qty * item.paid);
                                newYearSummaries[yearInt].saved += parseFloat((item.qty * item.saved).toFixed(2));
                            });
                            
                            newYearSummaries[yearInt].count += monthCount;
                            newYearSummaries[yearInt].qty += monthQty;
                        }
                    });
                });
                
                setPaidTemplate(newPaidTemplate);
                setYearSummaries(newYearSummaries);
                setAvailableYears(years.sort((a, b) => b - a)); // Sort years in descending order
                setSelectedYear(thisYear);
                setCalcPaidTemplate(1);
                setIsLoading(false);
            }
        }
    }, [dashboard.summaryReportTableData, dashboard.reportTableData, calcPaidTemlate, thisYear]);

    // Get data for display based on selections
    const getDisplayData = () => {
        if (!selectedYear) return [];
        
        if (!selectedMonth) {
            // Year summary - sum all months
            const yearData = yearSummaries[selectedYear] || { would_paid: 0, paid: 0, saved: 0, count: 0, qty: 0 };
            return [
                { 
                    name: "Would've Paid", 
                    value: yearData.would_paid || 0, 
                    color: yellow 
                },
                { 
                    name: "Paid", 
                    value: yearData.paid || 0, 
                    color: green 
                },
                { 
                    name: "Savings", 
                    value: yearData.saved || 0, 
                    color: pink 
                },
                { 
                    name: "Reports", 
                    value: yearData.count || 0, 
                    color: blue 
                },
                { 
                    name: "Quantity", 
                    value: yearData.qty || 0, 
                    color: blue 
                }
            ];
        } else {
            // Month detail
            const monthData = selectedYear === thisYear ? paidTemplate[selectedMonth] : 
                (dashboard.summaryReportTableData?.[selectedYear]?.[selectedMonth] || { would_paid: 0, paid: 0, saved: 0, count_report: 0, qty: 0 });
            
            if (monthData.would_paid?.value !== undefined) {
                // Using paidTemplate format
                return [
                    { 
                        name: monthData.would_paid.name, 
                        value: monthData.would_paid.value || 0, 
                        color: monthData.would_paid.color 
                    },
                    { 
                        name: monthData.paid.name, 
                        value: monthData.paid.value || 0, 
                        color: monthData.paid.color 
                    },
                    { 
                        name: monthData.saved.name, 
                        value: monthData.saved.value || 0, 
                        color: monthData.saved.color 
                    },
                    { 
                        name: "Reports", 
                        value: dashboard.summaryReportTableData?.[selectedYear]?.[selectedMonth]?.count_report || 0, 
                        color: blue 
                    },
                    { 
                        name: "Quantity", 
                        value: dashboard.summaryReportTableData?.[selectedYear]?.[selectedMonth]?.qty || 0, 
                        color: blue 
                    }
                ];
            } else {
                // Using direct summary format
                return [
                    { 
                        name: "Would've Paid", 
                        value: monthData.would_paid || 0, 
                        color: yellow 
                    },
                    { 
                        name: "Paid", 
                        value: monthData.paid || 0, 
                        color: green 
                    },
                    { 
                        name: "Savings", 
                        value: monthData.saved || 0, 
                        color: pink 
                    },
                    { 
                        name: "Reports", 
                        value: monthData.count_report || 0, 
                        color: blue 
                    },
                    { 
                        name: "Quantity", 
                        value: monthData.qty || 0, 
                        color: blue 
                    }
                ];
            }
        }
    };

    // Get month names for dropdown
    const getMonthNames = () => {
        const months = [
            { value: 1, name: 'January' },
            { value: 2, name: 'February' },
            { value: 3, name: 'March' },
            { value: 4, name: 'April' },
            { value: 5, name: 'May' },
            { value: 6, name: 'June' },
            { value: 7, name: 'July' },
            { value: 8, name: 'August' },
            { value: 9, name: 'September' },
            { value: 10, name: 'October' },
            { value: 11, name: 'November' },
            { value: 12, name: 'December' }
        ];
        return months;
    };

    return isloading ? (
        <></>
    ) : (
        <>
            <Box
                sx={{
                    width:'100%',
                    padding:2,
                    paddingBottom:1,
                    borderBottom:'1px solid #e5e7eb',
                    backgroundColor:theme.selectTheme.background_2,
                    color: theme.selectTheme.fontColor
                }}
            >
                {/* Year and Month Selection */}
                <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
                    <FormControl sx={{ minWidth: 120 }}>
                        <InputLabel id="year-select-label">Year</InputLabel>
                        <Select
                            labelId="year-select-label"
                            id="year-select"
                            value={selectedYear || ''}
                            label="Year"
                            onChange={handleYearChange}
                            sx={{
                                color: theme.selectTheme.fontColor,
                                '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: theme.selectTheme.fontColor_3,
                                },
                            }}
                        >
                            {availableYears.map(year => (
                                <MenuItem key={year} value={year}>{year}</MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                    
                    <FormControl sx={{ minWidth: 120 }}>
                        <InputLabel id="month-select-label">Month</InputLabel>
                        <Select
                            labelId="month-select-label"
                            id="month-select"
                            value={selectedMonth || ''}
                            label="Month"
                            onChange={handleMonthChange}
                            disabled={!selectedYear}
                            sx={{
                                color: theme.selectTheme.fontColor,
                                '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: theme.selectTheme.fontColor_3,
                                },
                            }}
                        >
                            <MenuItem value="">All Months</MenuItem>
                            {getMonthNames().map(month => (
                                <MenuItem key={month.value} value={month.value}>{month.name}</MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </Box>

                {/* Data Table */}
                <TableContainer component={Paper} sx={{ backgroundColor: theme.selectTheme.background }}>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell sx={{ color: theme.selectTheme.fontColor, fontWeight: 'bold' }}>Category</TableCell>
                                <TableCell sx={{ color: theme.selectTheme.fontColor, fontWeight: 'bold' }}>Amount</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {getDisplayData().map((item, index) => (
                                <TableRow key={index}>
                                    <TableCell>
                                        <Box sx={{ 
                                            display: 'flex', 
                                            alignItems: 'center',
                                            gap: 1
                                        }}>
                                            <Box 
                                                sx={{ 
                                                    width: 16, 
                                                    height: 16, 
                                                    borderRadius: '50%',
                                                    backgroundColor: alpha(item.color.A400, 0.7)
                                                }} 
                                            />
                                            <Typography sx={{ color: theme.selectTheme.fontColor }}>
                                                {item.name}
                                            </Typography>
                                        </Box>
                                    </TableCell>
                                    <TableCell sx={{ color: theme.selectTheme.fontColor }}>
                                        {item.name === 'Reports' || item.name === 'Quantity' ? 
                                            (item.value || 0) : 
                                            `$${parseFloat(item.value || 0).toFixed(2)}`
                                        }
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            </Box>
        </>
    );
};

export default BudgetPaidCalc;
