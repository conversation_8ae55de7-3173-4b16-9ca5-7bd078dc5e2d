
import React, { Component } from 'react'
import { Box,Modal, Card, Checkbox, FormControl,Typography, FormControlLabel, FormGroup, IconButton, InputLabel, MenuItem, Select, styled, TextField } from '@mui/material';

// import { Box, Card, IconButton, Modal, TextField, Typography } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import dayjs from 'dayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { compose } from 'redux';
import CircularProgress from '@mui/joy/CircularProgress';
import Button from '@mui/joy/Button';
import { ColorButton } from '../ColorButton';
import themeHook from '../../HOK/ThemeHook';
import { purple } from '@mui/material/colors';
import authHook from '../../HOK/AuthHook';
import dashboardHook from '../../HOK/DashboardHook';
import { formatDate } from '../../utils/functions';
const initialOrderStatus = {
  'Backorder':1,
  'Incoming':2,
  'In-Progress':3,
  'Confirmed':4,
  'Partially Delivered':5,
  'Delivered':6,
  'In Storage':7,
  'Returned':8,
}
class InventoryReorder extends Component {
    constructor(props){
        super(props)
        this.state={
            theme:props.theme,
            product:props.item.product.name,
            s_k_u:props.item.product.s_k_u,
            vendor:props.item.vendor?props.item.vendor.name:'',
            qty:1,
            checkSpecific:props.item.specific,
            checkUrgent:props.item.urgent,
            timeline:props.item.timeline,
            statusIndex:3,
            statusSelectedOption:'In-Progress',
            submit:false,
        }
      }
    async handleSubmit(){
      this.setState({
        submit:true,
      })
      let inventoryReorderData={
        admin_id:null,
        product:this.state.product,
        s_k_u:this.state.s_k_u,

      // "product":this.state.product,
        vendor:this.state.vendor,
        qty:this.state.qty,
        specific:this.state.checkSpecific,
        urgent:this.state.checkUrgent,
        // "exp_delivery":"",
        // "received":this.state.checkReceive,
        timeline:this.state.timeline,
        status:this.state.statusIndex
      }
      if (this.state.checkUrgent){
        const timeline=new Date(this.state.timeline)
        inventoryReorderData.timeline=`${timeline.getFullYear()}-${timeline.getMonth() + 1}-${timeline.getDate()}`
      }else{
        inventoryReorderData.timeline=""
      }
      if (this.props.auth.user.user_group==2){
        inventoryReorderData.admin_id=this.props.auth.selectUser.id
      }else if(this.props.auth.user.user_group==3){
        inventoryReorderData.admin_id=this.props.auth.user.id
      }else if(this.props.auth.user.user_group==4){
        inventoryReorderData.admin_id=this.props.auth.user.ref_admin.id
      }
      else{

      }
      let response=await this.props.dashboard.insertOrder(this.props.auth.token,inventoryReorderData)
      if (response.status!=200) {
        this.props.setInfoAlert({
          msg: `Error reordering : ${response.msg}`,
          status: false,
        })
        this.setState({
          submit:false,
        });
        this.props.showAlertNotes();
        this.props.refetchOrder(true)
      } else {
          if (response.msg == 'success') {
            this.props.setInfoAlert({
              msg: 'reorder successfully',
              status: true,
            })
            this.props.refetchOrder(true)
            this.setState({
              submit:false,
            });
            this.props.showAlertNotes();
            this.props.onClose();
          } else {
            this.props.setInfoAlert({
              msg: `Failed to reorder: ${response.msg}`,
              status: false,
            })
            this.setState({
              submit:false,
            });
            this.props.showAlertNotes();
            this.props.refetchOrder(true)
          }
      }
    }
  render() {
    return (
        <>
        <Modal
        disablePortal
        disableEnforceFocus
        disableAutoFocus
        open
        aria-labelledby="server-modal-title"
        aria-describedby="server-modal-description"
        sx={{
          display: 'flex',
          p: 1,
          alignItems: 'center',
          justifyContent: 'center',
          overflow:'auto'
        }}
        // onClose={this.props.onClose}
        // container={() => rootRef.current}
      >
        <Card
          sx={{
              width:'40%',
              padding:4,
              textAlign:'center',
              padding:2,
            overflow:'auto'

              // border:'gray',
              // shadow:true
          }}
        >
          <Box
            sx={(theme) => ({
                position: 'relative',
                bgcolor: 'background.paper',
                overflow:'auto'

                // boxShadow: theme.shadows[5],
            })}
          >
            <Box
              sx={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  flexDirection:'column',
                  textAlign:'left',
                  gap:2,
              }}
            >
              <Box
                sx={{
                  display:'flex',
                  flexDirection:'row',
                  justifyContent:'space-between'
                }}
              >
                <Typography
                  // width={'90%'}
                >
                </Typography>
                <IconButton
                  onClick={() => {
                    this.props.onClose()
                  }}
                >
                  <CloseIcon  fontSize="small"/>
                </IconButton>
              </Box>
              <Box>
                <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                  Add Item
                </Typography>
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='product'
                    label="product"
                    placeholder='product'
                    required
                    type='text'
                    value={this.state.product}
                    onChange={(e) =>
                        this.setState({
                            product:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                  <TextField
                      fullWidth
                      size='small'
                      id='s_k_u'
                      label="SKU"
                      placeholder='SKU(Manufacturer Code)'
                      type='text'
                      sx={{
                          backgroundColor:'inherit',
                          color:'inherit',
                      }}  
                      value={this.state.s_k_u}
                      onChange={(e) =>
                          this.setState({
                              s_k_u:e.target.value
                          })
                      }
                  />
              </Box>
              <Box
                sx={{ 
                  display: 'flex',
                  position: 'relative' 
                  }}
              >
                  <TextField 
                      size='small'
                      id='vendor'
                      // className='!focus:border-purple-700'
                      label="vendor" 
                      placeholder='vendor'
                      type='text'
                      fullWidth
                      value={this.state.vendor}
                      onChange={(e) =>
                          this.setState({
                              vendor:e.target.value
                          })
                      }
                  />
              </Box>
              <Box
                 sx={{ 
                  display: 'flex',
                  position: 'relative',
                  width:'100%',
                  justifyContent:'space-between'
                  }}
              >
                <Box 
                  size='small'
                  fullWidth
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    width:'45%',
                    position:'relative'
                    // borderColor:this.props.theme.fontColor,
                      // backgroundColor:this.props.theme.background,
                      // color:this.props.theme.fontColor
                  }}
                >
                  <TextField 
                    required
                    size='medium'
                    id='vendor'
                    label="Qty" 
                    placeholder='1'
                    defaultValue={1}
                    type='text'
                    fullWidth
                    value={this.state.qty}
                    onChange={(e) =>
                        this.setState({
                            qty:e.target.value
                        })
                    }
                  />
                  <Box
                    sx={{ 
                      position: 'absolute',
                      display:'flex',
                      flexDirection:'column',
                      height:'100%',
                      width:'15%',
                      right: 0,
                      //  top: 0 ,
                      boxShadow:'none',
                      alignItems:'center',
                      justifyContent:'space-between'
                    }}
                  >
                    <IconButton 
                      sx={{
                        padding:0
                      }}
                      onClick={()=>{
                        this.setState({
                          qty:this.state.qty+1
                        })
                      }}
                    >
                      <ArrowDropUpIcon />
                    </IconButton>
                    <IconButton
                      sx={{
                        padding:0
                      }}
                      onClick={()=>{
                        if(this.state.qty>1){
                          this.setState({
                            qty:this.state.qty-1
                          })
                        }
                      }}
                    >
                      <ArrowDropDownIcon/>
                    </IconButton>
                  </Box>
                </Box>
                <Box
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    marginLeft:2,
                    justifyContent:'space-between',
                    width:'50%',
                    alignItems:'center'
                  }}
                >
                  <FormGroup
                    sx={{
                      display:'flex',
                      flexDirection:'row',
                      justifyContent:'space-between',
                      width:'100%'
                    }}
                    >
                    <FormControlLabel control={<Checkbox />}  label="Specific" onClick={()=>{
                        this.setState({
                          checkSpecific:!this.state.checkSpecific
                        })}}/>
                    <FormControlLabel control={<Checkbox />}  label="Urgent"  onClick={()=>{
                        this.setState({
                          checkUrgent:!this.state.checkUrgent
                        })}}
                      />
                  </FormGroup>
                </Box>
              </Box>
              <Box
                sx={{ 
                  display: 'flex',
                  position: 'relative',
                  width:'100%',
                  color:this.props.theme.fontColor,

                  }}
              >
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                      disabled={!this.state.checkUrgent}
                      sx={{
                        width:'100%'
                      }}
                      format=' YYYY / MM / DD'
                      label="Timeline"
                      defaultValue={dayjs(this.state.timeline?this.state.timeline:formatDate('Amm-dd-yyyy'))}
                      // value={dayjs(this.state.item.timeline)}
                      onChange={(newValue) => this.setState({
                        timeline:newValue})
                    }/>
                </LocalizationProvider>
              </Box>
              {/* <Box
                sx={{ 
                  display: 'flex',
                  position: 'relative',
                  width:'100%'
                  }}
                  
              >
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                    {this.state.checkUrgent?(
                      <DatePicker
                        sx={{
                          width:'100%'
                        }}
                        format=' YYYY / MM / DD'
                        label="Timeline"
                        // value={this.state.timeline}
                        onChange={(newValue) =>{ 
                          this.setState({
                            timeline:newValue
                          })
                          }
                        }/>
                    ):(
                      <DatePicker
                        disabled
                        sx={{
                          width:'100%'
                        }}
                        format=' MM / DD / YYYY'
                        label="Timeline"
                        // value={this.state.timeline}
                        onChange={(newValue) => this.setState({
                          timeline:newValue})
                          }
                      />  
                    )}
                </LocalizationProvider>
              </Box> */}
              <Box>
                <FormControl 
                  size='small'
                  disabled
                  fullWidth
                  sx={{
                    borderColor:this.props.theme.fontColor,
                    backgroundColor:this.props.theme.background,
                    color:this.props.theme.fontColor
                  }}
                >
                  <InputLabel 
                      id="demo-simple-select-label"
                      sx={{
                          backgroundColor:this.props.theme.background,
                          color:this.props.theme.fontColor,
                          // width:'80%',
                          borderRight:'1px solid '
                      }}
                  >Status</InputLabel>
                  <Select
                      value={this.state.statusSelectedOption}
                      label="Status"
                      // onChange={(e)=>{
                      //     this.setState({
                      //       statusIndex:initialOrderStatus[e.target.value],
                      //       statusSelectedOption:e.target.value
                      //     })
                      //   }
                      // }
                      // components={CustomOption}
                      sx={{
                          display:'flex',
                          flexDirection:'row',
                          backgroundColor:this.props.theme.background,
                          color:this.props.theme.fontColor,
                          '& .MuiSelect-select':{
                              display:'flex',
                              backgroundColor:this.props.theme.background,
                              color:this.props.theme.fontColor
                          },
                      }}
                      style={{display:'flex',flexDirection:'row',alignItems:'center'}}
                  >
                      {Object.keys(initialOrderStatus).map((item,index)=>{
                        return(
                            <MenuItem 
                              key={index}
                              value={item}>
                                <Typography>
                                  {item}
                                </Typography>
                            </MenuItem>
                        )
                      })
                      }
                      {/* <MenuItem 
                        key={2}
                        value={"incoming"}>
                          <Typography>
                            {'incoming'}
                          </Typography>
                      </MenuItem> */}

                  </Select>
                </FormControl>
              </Box>
                <Box
                    sx={{
                        display:'flex',
                        alignItems:'center',
                        padding:0,
                        margin:0
                    }}
                >
                    {this.state.submit ? (
                        <>
                            <Button 
                                sx={{
                                    backgroundColor:purple[600],
                                    marginRight:1
                                    // marginTop:1
                                }}
                                startDecorator={
                                    <CircularProgress variant="solid"/>
                                }
                            >
                                Loading…
                            </Button>
                        </>
                    ) : (
                        <ColorButton 
                            variant="contained"
                            sx={{
                            marginRight:1
                            }}
                            onClick={() =>this.handleSubmit()}   
                        >
                            Submit
                        </ColorButton>
                    )}
                    <ColorButton 
                        // variant="contained"
                        onClick={() => {
                        this.props.onClose()
                            // setStatus(false);
                            // onClose();
                        }}
                        // onClick={() =>this.handleSignIn()}   
                    >
                        Close
                    </ColorButton>
                </Box>
            </Box>       
          </Box>
        </Card>
      </Modal>
      </>
    )
  }
}
export default compose(
  themeHook,
  authHook,
  dashboardHook
)(InventoryReorder)
