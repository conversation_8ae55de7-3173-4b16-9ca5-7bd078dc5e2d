import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { Box, Card, IconButton, Modal, TextField, Typography } from '@mui/material';
import { ColorButton } from '../ColorButton';
import { purple } from '@mui/material/colors';
import CloseIcon from '@mui/icons-material/Close';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import themeHook from '../../HOK/ThemeHook';
import Button from '@mui/joy/Button';
import CircularProgress from '@mui/joy/CircularProgress';
import { compose } from 'redux';
import authHook from '../../HOK/AuthHook';
import dayjs from 'dayjs';

import dashboardHook from '../../HOK/DashboardHook';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { formatDate } from '../../utils/functions';
class InventoryAdd extends Component {
    constructor(props){
        super(props)
        this.state={
            theme:props.theme,
            product_name:'',
            s_k_u:'',
            qty:0,
            notes:'',
            expiration_date:'',
            submit:false,
        }
      }
    async handleSubmit(){
        this.setState({
            submit:true,
            status:false,
          })
          let inventoryData={
            product:this.state.product_name,
            s_k_u:this.state.s_k_u,
            admin_id:null,
            qty:this.state.qty,
            notes:this.state.notes,
            expiration_date:this.state.expiration_date
          }
        if(this.state.expiration_date!=''){
            const expiration_date=new Date(this.state.expiration_date)
            inventoryData.expiration_date=`${expiration_date.getFullYear()}-${expiration_date.getMonth() + 1}-${expiration_date.getDate()}`
        }
        if (this.props.auth.user.user_group==2){
            inventoryData.admin_id=this.props.auth.selectUser.id
        }else if(this.props.auth.user.user_group==3){
            inventoryData.admin_id=this.props.auth.user.id
        }else if(this.props.auth.user.user_group==4){
            inventoryData.admin_id=this.props.auth.user.ref_admin.id
        }
        else{
    
        }
        let response=await this.props.dashboard.insertInventory(this.props.auth.token,inventoryData)
        if (response.status!=200) {
            this.props.setInfoAlert({
                msg: `Error insrting inventory: ${response.msg}`,
                status: false,
              })
            this.setState({
                submit:false,
            });
            this.props.showAlertNotes();
            this.props.refetchOrder(true)
        } else {
            if (response.msg == 'success') {
                this.props.setInfoAlert({
                    msg: 'inventory inserted successfully',
                    status: true,
                })
                this.setState({
                    submit:false,
                });
                this.props.refetchOrder(true)
                this.props.showAlertNotes();
                this.props.onClose();
            } else {
                this.props.setInfoAlert({
                    msg: `Failed to insert inventory: ${response.msg}`,
                    status: false,
                })
                this.setState({
                    submit:false,
                });
                this.props.showAlertNotes();
                this.props.refetchOrder(true)
            }
        }
    }
    render() {
        return (
            <>
                <Modal
                    disablePortal
                    disableEnforceFocus
                    disableAutoFocus
                    open
                    aria-labelledby="server-modal-title"
                    aria-describedby="server-modal-description"
                    sx={{
                        display: 'flex',
                        p: 1,
                        alignItems: 'center',
                        justifyContent: 'center',
                        overflow:'auto'
                    }}
                    // onClose={this.props.onClose}
                    // container={() => rootRef.current}
                >
                <Card
                    sx={{
                        width:'40%',
                        padding:4,
                        textAlign:'center',
                        padding:2,
                        backgroundColor:this.props.theme.selectTheme.background_2,
                        color:this.props.theme.selectTheme.fontColor_2
                    }}
                >
                    <Box
                        sx={(theme) => ({
                            display:'flex',
                            position: 'relative',
                            bgcolor: 'background.paper',
                            justifyContent:'center',
                            overflow:'auto',
                            backgroundColor:this.props.theme.selectTheme.background_2,
                            color:this.props.theme.selectTheme.fontColor_3
                            // boxShadow: theme.shadows[5],
                        })}
                    >
                        <Box
                            sx={{
                                width: '95%',
                                height: '100%',
                                display: 'flex',
                                flexDirection:'column',
                                textAlign:'left',
                                justifyContent:'center',
                                gap:2,
                                backgroundColor:this.props.theme.selectTheme.background_2,
                                color:this.props.theme.selectTheme.fontColor_2
                            }}
                        >
                            <Box
                                sx={{
                                    display:'flex',
                                    flexDirection:'row',
                                    justifyContent:'space-between'
                                }}
                            >
                                <Typography
                                    // width={'90%'}
                                >
                                </Typography>
                                <IconButton
                                    onClick={() => {
                                    this.props.onClose()
                                    }}
                                >
                                    <CloseIcon  fontSize="small"/>
                                </IconButton>
                            </Box>
                            <Box>
                                <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                                    Add Product
                                </Typography>
                            </Box>
                            <Box
                                sx={{
                                width: '100%',                                           
                                }}
                            >
                                <TextField
                                    fullWidth
                                    size='small'
                                    id='product_name'
                                    label="Product Name"
                                    placeholder='Product Name'
                                    required
                                    type='text'
                                    value={this.state.product_name}
                                    onChange={(e) =>
                                        this.setState({
                                            product_name:e.target.value
                                        })
                                    }
                                />
                            </Box>
                            <Box
                                sx={{
                                width: '100%',                                           
                                }}
                            >
                                <TextField
                                    fullWidth
                                    size='small'
                                    id='s_k_u'
                                    label="SKU"
                                    placeholder='SKU(Manufacturer Code)'
                                    type='text'
                                    sx={{
                                      backgroundColor:'inherit',
                                      color:'inherit',
                                    }}  
                                    value={this.state.s_k_u}
                                    onChange={(e) =>
                                        this.setState({
                                          s_k_u:e.target.value
                                        })
                                    }
                                />
                            </Box>
                            <Box
                                size='small'
                                fullWidth
                                sx={{ 
                                display: 'flex',
                                position: 'relative',
                                flexDirection:'row',

                                width:'100%',
                                justifyContent:'space-between',
                                // backgroundColor:this.props.theme.selectTheme.background_3,
                                // color:this.props.theme.selectTheme.fontColor_3
                                }}
                            >
                                <TextField
                                    size='medium'
                                    id='qty'
                                    label="Qty" 
                                    placeholder='1'
                                    defaultValue={1}
                                    type='text'
                                    fullWidth
                                    // color={this.props.theme.selectTheme.fontColor}
                                    sx={{
                                        // backgroundColor:this.props.theme.selectTheme.background_3,
                                        // color:this.props.theme.selectTheme.fontColor_3
                                    }}
                                    value={this.state.qty}
                                    onChange={(e) =>
                                        this.setState({
                                            qty:e.target.value
                                        })
                                    }
                                />
                                <Box
                                    sx={{ 
                                        position: 'absolute',
                                        display:'flex',
                                        flexDirection:'column',
                                        height:'100%',
                                        width:'10%',
                                        right: 0,
                                        //  top: 0 ,
                                        boxShadow:'none',
                                        alignItems:'center',
                                        justifyContent:'space-between'
                                    }}
                                >
                                    <IconButton 
                                        sx={{
                                        padding:0
                                        }}
                                        onClick={()=>{
                                        this.setState({
                                            qty:this.state.qty+1
                                        })
                                        }}
                                    >
                                        <ArrowDropUpIcon />
                                    </IconButton>
                                    <IconButton
                                        sx={{
                                        padding:0
                                        }}
                                        onClick={()=>{
                                        if(this.state.qty>1){
                                            this.setState({
                                            qty:this.state.qty-1
                                            })
                                        }
                                        }}
                                    >
                                        <ArrowDropDownIcon/>
                                    </IconButton>
                                </Box>
                            </Box>
                            <Box
                                sx={{ 
                                    display: 'flex',
                                    position: 'relative',
                                    width:'100%'
                                }}
                            >
                               <LocalizationProvider dateAdapter={AdapterDayjs}>
                                    <DatePicker
                                        // disabled={!this.state.checkUrgent}
                                        sx={{
                                            width:'100%'
                                        }}
                                        format=' YYYY / MM / DD'
                                        label="Expiration Date"
                                        // defaultValue={dayjs(this.state.expiration_date?this.state.expiration_date)}
                                        // value={dayjs(this.state.item.timeline)}
                                        onChange={(newValue) => this.setState({
                                            expiration_date:newValue})
                                        }
                                    />
                                </LocalizationProvider>
                            </Box>
                            <Box
                                sx={{
                                width: '100%',                                           
                                }}
                            >
                                <TextField
                                    fullWidth
                                    multiline
                                    aria-multiline
                                    rows={4}
                                    size='small'
                                    id='note'
                                    label="Note"
                                    placeholder='Note'
                                    type='text'
                                    value={this.state.notes}
                                    onChange={(e) =>
                                        this.setState({
                                            notes:e.target.value
                                        })
                                    }
                                />
                            </Box>
                            <Box
                                sx={{
                                    display:'flex',
                                    alignItems:'center',
                                    padding:0,
                                    margin:0
                                }}
                            >
                                {this.state.submit ? (
                                    <>
                                        <Button 
                                            sx={{
                                                backgroundColor:{purple},
                                                marginRight:1
                                                // marginTop:1
                                            }}
                                            startDecorator={
                                                <CircularProgress variant="solid"/>
                                            }
                                        >
                                            Loading…
                                        </Button>
                                    </>
                                ) : (
                                    <ColorButton 
                                        variant="contained"
                                        sx={{
                                        marginRight:1
                                        }}
                                        onClick={() =>this.handleSubmit()}   
                                    >
                                        Submit
                                    </ColorButton>
                                )}
                                <ColorButton 
                                    // variant="contained"
                                    onClick={() => {
                                    this.props.onClose()
                                        // setStatus(false);
                                        // onClose();
                                    }}
                                    // onClick={() =>this.handleSignIn()}   
                                >
                                    Close
                                </ColorButton>
                            </Box>
                        </Box>
                    </Box>
                </Card>
            </Modal>
        </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    dashboardHook
)(InventoryAdd)
