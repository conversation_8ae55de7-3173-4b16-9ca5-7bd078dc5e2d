import React, { Component, useEffect, useState,createContext, useContext } from 'react'

import {
  TbMessageStar,
  TbCertificate,
  TbUsers,
  TbUsersGroup,
  TbCurrencyDollar,
  TbHeart,
  TbCalculator,
  TbRosetteDiscountCheck,
  TbRefresh,
  TbShoppingBag,
  TbReportAnalytics,
  TbArchive,
  TbKeyframes,
  TbNote,
  TbMenu2,
} from 'react-icons/tb';
import Divider from '@mui/material/Divider';
import Drawer from '@mui/material/Drawer';
import InboxIcon from '@mui/icons-material/MoveToInbox';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Toolbar from '@mui/material/Toolbar';
import Collapse from '@mui/material/Collapse';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import { Alert, Badge, Box, IconButton, Skeleton, Tooltip } from '@mui/material';
import ComboboxWithStatus from './ComboboxWithStatus';
import navigateHook from '../HOK/NavigateHook';
import locationHook from '../HOK/LoctionHook';
import { compose } from 'redux';
import { grey, orange, red } from '@mui/material/colors';
import authHook from '../HOK/AuthHook';
import themeHook from '../HOK/ThemeHook';
import { useAuth } from '../context/AuthContext';
import { useLocation, useNavigate } from 'react-router-dom';
import { useTheme } from '../context/ThemeContext';
import LoaderAllPage from './LoaderAllPage';
import AlertSubSctiption from './AlertSubSctiption';
import { useDashboard } from '../context/DashboardContext';
import { BiErrorCircle } from 'react-icons/bi';
import BatteryAlertIcon from '@mui/icons-material/BatteryAlert';
import MenuIcon from '@mui/icons-material/Menu';

const drawerWidth = 255;
const collapsedDrawerWidth = 70; // Width when sidebar is collapsed
const drawerTop = 80;

// Create a context to share sidebar state across components
export const SidebarContext = createContext();
export const useSidebar = () => useContext(SidebarContext);

export const SidebarProvider = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  
  // Detect mobile devices on mount and window resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      
      // Auto-collapse sidebar on mobile
      if (window.innerWidth < 768) {
        setSidebarCollapsed(true);
      }
    };
    
    // Check on mount
    checkMobile();
    
    // Add resize listener
    window.addEventListener('resize', checkMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };
  
  return (
    <SidebarContext.Provider value={{ sidebarCollapsed, toggleSidebar, isMobile }}>
      {children}
    </SidebarContext.Provider>
  );
};

const menuItem = [
  {
      Collapse: true,
      open: true,
      icon: TbKeyframes,
      label: 'Procurement Hub',
      items: [
          { link: 'order', name: 'Order Sheet', icon: TbCalculator },
          { link: 'received', name: 'Received', icon: TbRosetteDiscountCheck },
          { link: 'inventory', name: 'Inventory', icon: TbShoppingBag },
          { link: 'return', name: 'Return', icon: TbRefresh },
          { link: 'notes', name: 'Notes', icon: TbNote },

          {
              link: 'vendor-list',
              name: 'Vendor List',
              icon: TbUsersGroup,
          },
          { link: 'report', name: 'Report', icon: TbReportAnalytics },
          { link: 'myproducts', name: 'My Products', icon: TbHeart },
          // { link: 'product-list', name: 'Product List', icon: TbArchive },
          { link: 'budget', name: 'Budget', icon: TbCurrencyDollar },
          { link: 'supply-hub', name: 'Supply Hub', icon:TbArchive }
      ],
  },
  {
      open: false,
      Collapse: false,
      icon: TbCertificate,
      label: 'Insurance Verification',
      items: [
          {
              link: 'insurance',
              name: 'Insurance Verification',
              icon: TbCertificate,
              tag:'Insurance_Verification'
          },
      ],
  },
  {
      open: false,
      Collapse: false,
      icon: TbMessageStar,
      label: 'Patient Experience',
      items: [
          {
              link: 'patient-experience',
              name: 'Patient Experience',
              icon: TbMessageStar,
              tag:'Patient_Experience'
          },
      ],
  },
//   {
//     open: false,
//     Collapse: false,
//     icon: TbUsers,
//     label: 'User List',
//     items: [
//         {
//             link: 'user-list',
//             name: 'User List',
//             icon: TbUsers,
//         },
//     ],
// },
];

const SideBar = () => {
  const location = useLocation();
  const user = useAuth();
  const theme = useTheme();
  const dashboard = useDashboard();
  const navigate = useNavigate();
  const { sidebarCollapsed, toggleSidebar } = useSidebar();
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [pathname, setPathName] = useState('');
  const [showAlert, setShowAlert] = useState(false);
  
  useEffect(() => {
    let response = user.getsubscription(user.token);
    if (response.status != 200) {
    } else {
      if (response == "success") {

      }
      else { }
    }

  }, [user.user]);
  
  useEffect(() => {
    setLoading(false);
    menuItem.map((item, index) => {
      item.items.map((menu, index) => {
        if (('/' + menu.link) == location.pathname) {
          console.log(location.pathname);
          setOpen(true);
          setLoading(false);
        }
      });
    });
    return () => {
      // clearTimeout(timer);
    };
  }, [location.pathname]);
  
  useEffect(() => {
    setLoading(false);
  }, [pathname]);
  
  const handleClickDropdown = () => {
    setOpen(!open);
  };
  
  const handleItemClick = (link) => {
    if ('/' + link != location.pathname) {
      // setLoading(true)
      setPathName('/' + link);
    }
    navigate('/' + link);
  };
  
  const handleCloseModal = () => {
    setShowAlert(false);
  };
  
  // const container = window !== undefined ? () => document.body : undefined;
  return (
    <>
      {/* {loading &&} */}
      {(loading) ? (
        <>
          <List
            color={theme.selectTheme.fontColor}
            key={'userlist'}
          >
            <Box
              color={theme.selectTheme.fontColor}
              sx={{
                paddingX: 2,
                paddingY: 1,

              }}
            >
              {Array.from({ length: 8 }, (_, index) => (
                <>
                  <Skeleton variant="text" sx={{ fontSize: '1rem', width: '250px' }} animation="wave" />
                </>
                // <ListItemIcon>
                //   <item.icon size={24}
                //     style={{minWidth:'24px'}}
                //     />
                // </ListItemIcon>
                // <ListItemText primary={}  />
              ))}

            </Box>
          </List>
          {/* <LoaderAllPage /> */}
        </>
      ) : (
        <>
          {showAlert && (
            <AlertSubSctiption
              onClose={handleCloseModal}
            />
          )}
          <Drawer
            variant="permanent"
            sx={{
              backgroundColor: theme.selectTheme.background,
              width: sidebarCollapsed ? collapsedDrawerWidth : drawerWidth,
              position: 'static',
              display: { xs: 'block', sm: 'block' },
              overflow: "auto",
              '& .MuiDrawer-paper': {
                top: drawerTop,
                boxSizing: 'border-box',
                width: sidebarCollapsed ? collapsedDrawerWidth : drawerWidth,
                paddingRight: sidebarCollapsed ? 0.5 : 1.5,
                paddingLeft: sidebarCollapsed ? 0.5 : 1.5,
                backgroundColor: theme.selectTheme.background,
                transition: 'width 0.3s ease-in-out',
              },
            }}
            open
          >
            <div>
              {/* Toggle button for mobile view */}
              <Box 
                sx={{ 
                  width: '100%',
                  display: { xs: 'flex', sm: 'flex' }, 
                  justifyContent: 'center',
                  paddingX: 1
                }}
              >
                <IconButton
                  sx={{
                    paddingX:1
                    // width:'100%'
                  }} 
                  onClick={toggleSidebar}
                >
                  <MenuIcon
                    sx={{
                      width:'100%'
                    }}
                    color={theme.selectTheme.fontColor} />
                </IconButton>
              </Box>
              
              {user.user ?
                (user.user.is_admin && !sidebarCollapsed &&
                  (
                    <Toolbar
                      sx={{
                        backgroundColor: theme.selectTheme.background,
                        // color:this.props.theme.selectTheme.fontColor,
                        padding: 0,
                        '&.MuiToolbar-root': {
                          padding: 0
                        }
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          padding: 0,
                          width: '100%',
                          backgroundColor: theme.selectTheme.background,
                          // color:this.props.theme.selectTheme.fontColor,

                        }}
                      >
                        <ComboboxWithStatus theme={theme.selectTheme} />
                        {/* <ComboboxWithStatus userListInfo={this.props.userListInfo} /> */}
                        {/* <FilterDate filter={filter} /> */}
                        {/* <SearchBox /> */}
                      </Box>
                    </Toolbar>
                  )
                ) : (
                  <>
                  </>
                )
              }
              <Divider />
              <List
                sx={{
                  width: '100%',
                  maxWidth: 360,
                  backgroundColor: theme.selectTheme.background,
                  color: theme.selectTheme.fontColor,
                }}
                component="nav"
                aria-labelledby="nested-list-subheader"
              >
                {
                  menuItem.map((item, index) => {
                    return item.Collapse ? (
                      <Box
                        key={index}
                        sx={{
                          paddingX: sidebarCollapsed ? 0.5 : 2,
                          paddingY: 1
                        }}
                      >
                        {sidebarCollapsed ? (
                          <Tooltip 
                            title={item.label}
                            placement="right"
                            arrow
                          >
                            <ListItemButton
                              key={index}
                              sx={{
                                padding: 0,
                                '&:hover': { backgroundColor: theme.selectTheme.selectHover },
                              }}
                              onClick={handleClickDropdown}
                            >
                              <ListItemIcon>
                                <item.icon size={24}
                                  color={theme.selectTheme.iconColor}
                                  style={{ minWidth: '24px' }}
                                />
                              </ListItemIcon>
                            </ListItemButton>
                          </Tooltip>
                        ) : (
                          <ListItemButton
                            key={index}
                            sx={{
                              padding: 0,
                              '&:hover': { backgroundColor: theme.selectTheme.selectHover },
                            }}
                            onClick={handleClickDropdown}
                          >
                            <ListItemIcon>
                              <item.icon size={24}
                                color={theme.selectTheme.iconColor}
                                style={{ minWidth: '24px' }}
                              />
                            </ListItemIcon>
                            <ListItemText primary={item.label} />
                            {open ? <ExpandLess color={theme.selectTheme.fontColor} /> : <ExpandMore color={theme.selectTheme.fontColor} />}
                          </ListItemButton>
                        )}

                        <Collapse key={"index"} in={open} timeout="auto" unmountOnExit>
                          <List component="div" disablePadding key={'list_child'} >
                            {item.items.map((menu, item_index) => (
                              <>
                                {sidebarCollapsed ? (
                                  <Tooltip
                                    key={item_index}
                                    title={menu.name}
                                    placement="right"
                                    arrow
                                  >
                                    <ListItemButton
                                      key={item_index}
                                      sx={
                                        {
                                          pl: sidebarCollapsed ? 1 : 4,
                                          pr: 0,
                                          background: location.pathname == '/' + menu.link
                                            ? theme.selectTheme.sidebarSelected : '',
                                          '&:hover': { backgroundColor: theme.selectTheme.selectHover },
                                        }}
                                      onClick={() => {
                                        handleItemClick(
                                          menu.link
                                        );
                                      }}
                                    >
                                      <ListItemIcon>
                                        <menu.icon size={24} color={theme.selectTheme.iconColor} />
                                      </ListItemIcon>
                                    </ListItemButton>
                                  </Tooltip>
                                ) : (
                                  <ListItemButton
                                    key={item_index}
                                    sx={
                                      {
                                        pl: sidebarCollapsed ? 1 : 4,
                                        pr: 0,
                                        background: location.pathname == '/' + menu.link
                                          ? theme.selectTheme.sidebarSelected : '',
                                        '&:hover': { backgroundColor: theme.selectTheme.selectHover },
                                      }}
                                    onClick={() => {
                                      handleItemClick(
                                        menu.link
                                      );
                                    }}
                                  >
                                    <ListItemIcon>
                                      <menu.icon size={24} color={theme.selectTheme.iconColor} />
                                    </ListItemIcon>
                                    <ListItemText primary={menu.name} />
                                    {menu.link == 'inventory' && (
                                      <>
                                        {dashboard.infoInventoryData.lowQtyIndex.length != 0 && (
                                          <Tooltip
                                            title={`you have ${dashboard.infoInventoryData.lowQtyIndex.length} product low in inventory`}>
                                            <IconButton
                                              sx={{
                                                padding: 0,
                                                marginLeft: dashboard.infoInventoryData.expirationDataIndex.length != 0 ? ("5px") : ("2px"),
                                                paddingRight: dashboard.infoInventoryData.expirationDataIndex.length == 0 ? ("5px") : ("2px"),
                                              }}
                                              size='small'
                                            >
                                              <Badge
                                                sx={{
                                                  color: theme.selectTheme.lowProductRowColorIcon,
                                                  fontSize: "6px"
                                                }}
                                                anchorOrigin={{
                                                  vertical: 'bottom',
                                                  horizontal: 'right',
                                                }}
                                                badgeContent={dashboard.infoInventoryData.lowQtyIndex.length}
                                              >
                                                <BatteryAlertIcon
                                                  sx={{
                                                    color: theme.selectTheme.lowProductRowColorIcon
                                                  }}
                                                  fontSize='small'
                                                />
                                              </Badge>
                                            </IconButton>
                                          </Tooltip>
                                        )
                                        }
                                        {dashboard.infoInventoryData.expirationDataIndex.length != 0 && (
                                          <Tooltip
                                            title={`you have ${dashboard.infoInventoryData.expirationDataIndex.length} product expiring`}>
                                            <IconButton
                                              sx={{
                                                padding: 0,
                                                paddingRight: "5px",
                                              }}
                                              size='small'
                                            >
                                              <Badge
                                                sx={{
                                                  color: theme.selectTheme.expiringProductRowColorIcon,
                                                  fontSize: "6px"
                                                }}
                                                anchorOrigin={{
                                                  vertical: 'bottom',
                                                  horizontal: 'right',
                                                }}
                                                badgeContent={dashboard.infoInventoryData.expirationDataIndex.length}
                                              >
                                                <BatteryAlertIcon
                                                  fontSize='small'
                                                  sx={{
                                                    color: theme.selectTheme.expiringProductRowColorIcon,
                                                  }}
                                                />
                                              </Badge>
                                            </IconButton>
                                          </Tooltip>
                                        )
                                        }
                                      </>
                                    )
                                    }
                                  </ListItemButton>
                                )}
                              </>
                            ))}
                          </List>
                        </Collapse>
                      </Box>
                    ) : (
                      item.items.map((menu, index) => (
                        <Box
                          key={index}
                          color={theme.selectTheme.fontColor}
                          sx={{
                            paddingX: sidebarCollapsed ? 0.5 : 2,
                            paddingY: 1,

                          }}
                        >
                          {sidebarCollapsed ? (
                            <Tooltip
                              title={menu.name}
                              placement="right"
                              arrow
                            >
                              <ListItemButton
                                key={index}
                                sx={{
                                  padding: 0,
                                  color: user.subscription ? (user.subscription.includes(menu.tag) ? 'dark' : grey[400]) : 'dark',
                                  '&:hover': { backgroundColor: theme.selectTheme.selectHover },
                                }}
                                onClick={() => {
                                  if (user.user.groups.includes(menu.tag)) {
                                    handleItemClick(menu.link);
                                  } else {
                                    setShowAlert(true);
                                  }
                                }}
                              >
                                <ListItemIcon>
                                  <menu.icon color={theme.selectTheme.iconColor} size={24}
                                    style={{ minWidth: '24px' }}
                                  />
                                </ListItemIcon>
                              </ListItemButton>
                            </Tooltip>
                          ) : (
                            <ListItemButton
                              key={index}
                              sx={{
                                padding: 0,
                                color: user.subscription ? (user.subscription.includes(menu.tag) ? 'dark' : grey[400]) : 'dark',
                                '&:hover': { backgroundColor: theme.selectTheme.selectHover },
                              }}
                              onClick={() => {
                                if (user.user.groups.includes(menu.tag)) {
                                  handleItemClick(menu.link);
                                } else {
                                  setShowAlert(true);
                                }
                              }}
                            >
                              <ListItemIcon>
                                <menu.icon color={theme.selectTheme.iconColor} size={24}
                                  style={{ minWidth: '24px' }}
                                />
                              </ListItemIcon>
                              <ListItemText primary={menu.name} />
                            </ListItemButton>
                          )}
                        </Box>
                      ))
                      )
                    }
                    )
                  }
              </List>
              {user.user ? (
                user.user.user_group == 2 ?
                  (
                    <>
                      <Divider />
                      <List
                        color={theme.selectTheme.fontColor}
                        key={'userlist'}
                      >
                        <Box
                          color={theme.selectTheme.fontColor}
                          sx={{
                            paddingX: sidebarCollapsed ? 0.5 : 2,
                            paddingY: 1,
                          }}
                        >
                          {sidebarCollapsed ? (
                            <Tooltip
                              title="User List"
                              placement="right"
                              arrow
                            >
                              <ListItemButton
                                onClick={() => {
                                  handleItemClick(
                                    'user-list'
                                  );
                                }}
                                sx={{
                                  padding: 0,
                                  '&:hover': { backgroundColor: theme.selectTheme.selectHover },
                                }}
                              >
                                <ListItemIcon >
                                  <TbUsers size={24} color={theme.selectTheme.iconColor} style={{ minWidth: '24px' }} />
                                </ListItemIcon>
                              </ListItemButton>
                            </Tooltip>
                          ) : (
                            <ListItemButton
                              onClick={() => {
                                handleItemClick(
                                  'user-list'
                                );
                              }}
                              sx={{
                                padding: 0,
                                '&:hover': { backgroundColor: theme.selectTheme.selectHover },
                              }}
                            >
                              <ListItemIcon >
                                <TbUsers size={24} color={theme.selectTheme.iconColor} style={{ minWidth: '24px' }} />
                              </ListItemIcon>
                              <ListItemText primary="User List" sx={{ color: theme.selectTheme.fontColor }} />
                            </ListItemButton>
                          )}
                        </Box>
                      </List>
                    </>
                  ) : (
                    user.user.user_group == 3 && (
                      <>
                        <Divider />
                        <List
                          color={theme.selectTheme.fontColor}
                          key={'userlist'}
                        >
                          <Box
                            color={theme.selectTheme.fontColor}
                            sx={{
                              paddingX: sidebarCollapsed ? 0.5 : 2,
                              paddingY: 1,
                            }}
                          >
                            {sidebarCollapsed ? (
                              <Tooltip
                                title="Manage Users"
                                placement="right"
                                arrow
                              >
                                <ListItemButton
                                  onClick={() => {
                                    handleItemClick(
                                      'user-list'
                                    );
                                  }}
                                  sx={{
                                    padding: 0,
                                    '&:hover': { backgroundColor: theme.selectTheme.selectHover },
                                  }}
                                >
                                  <ListItemIcon >
                                    <TbUsers size={24} color={theme.selectTheme.iconColor} style={{ minWidth: '24px' }} />
                                  </ListItemIcon>
                                </ListItemButton>
                              </Tooltip>
                            ) : (
                              <ListItemButton
                                onClick={() => {
                                  handleItemClick(
                                    'user-list'
                                  );
                                }}
                                sx={{
                                  padding: 0,
                                  '&:hover': { backgroundColor: theme.selectTheme.selectHover },
                                }}
                              >
                                <ListItemIcon >
                                  <TbUsers size={24} color={theme.selectTheme.iconColor} style={{ minWidth: '24px' }} />
                                </ListItemIcon>
                                <ListItemText primary="Manage Users" sx={{ color: theme.selectTheme.fontColor }} />
                              </ListItemButton>
                            )}
                          </Box>
                        </List>
                      </>
                    )
                  )) : (
                  <>
                  </>
                )
              }
            </div>
          </Drawer>
        </>
      )}
    </>
  );
};

export default SideBar;
