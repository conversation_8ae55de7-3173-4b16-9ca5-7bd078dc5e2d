
import React, { Component } from 'react'
import { <PERSON>, Card, IconButton, Modal, TextField, Typography } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close';
import { ColorButton } from '../ColorButton';
import { purple } from '@mui/material/colors';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import Button from '@mui/joy/Button';
import { CircularProgress } from '@mui/joy';
import { compose } from 'redux';
import themeHook from '../../HOK/ThemeHook';
import dashboardHook from '../../HOK/DashboardHook';
import authHook from '../../HOK/AuthHook';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
class InsuranceAdd extends Component {
  constructor(props){
    super(props)
    this.state={
        patiant_name:'',
        group:'',
        cert:'',
        date_of_birth:'',
        plan_holder_date_of_birth:'',
        plan_holder:'',
        address:'',
        relation_to_subscriber:'',
        notes:'',
        breakdown:'',
        submit:false,
    }
  }
  async handleSubmit(){
    this.setState({
      submit:true,
    })
    let insuranceData={
        patiant_name:this.state.patiant_name,
        admin_id:null,
        group:this.state.group,
        cert:this.state.cert,
        date_of_birth:this.state.date_of_birth,
        plan_holder_date_of_birth:this.state.plan_holder_date_of_birth,
        plan_holder:this.state.plan_holder,
        address:this.state.address,
        relation_to_subscriber:this.state.relation_to_subscriber,
        notes:this.state.notes,
        breakdown:this.state.breakdown,
    }
    if(this.state.date_of_birth){
      const date_of_birth=new Date(this.state.date_of_birth)
      insuranceData.date_of_birth=`${date_of_birth.getFullYear()}-${date_of_birth.getMonth() + 1}-${date_of_birth.getDate()}`
   }
   else{
     insuranceData.date_of_birth=''
   }
   if(this.state.plan_holder_date_of_birth){
     const plan_holder_date_of_birth=new Date(this.state.plan_holder_date_of_birth)
     insuranceData.plan_holder_date_of_birth=`${plan_holder_date_of_birth.getFullYear()}-${plan_holder_date_of_birth.getMonth() + 1}-${plan_holder_date_of_birth.getDate()}`
  }else{
   insuranceData.plan_holder_date_of_birth=''
  }
    insuranceData.admin_id=this.props.auth.user.is_admin?this.props.auth.selectUser.id:this.props.auth.user.id
    let response=await this.props.dashboard.insertInsurance(this.props.auth.token,insuranceData)
    if (response.status!=200) {
      this.props.setInfoAlert({
        msg: `Error inserting Insurance: ${response.msg}`,
        status: false,
      })
      this.setState({
        submit:false,
        });
      this.props.showAlertNotes();
      this.props.refetchOrder(true)
    } else {
        if (response.msg == 'success') {
          this.props.setInfoAlert({
            msg: 'Insurance inserted successfully',
            status: true,
          })
          this.props.refetchOrder(true)
          this.setState({
            submit:false,
          });
          this.props.showAlertNotes();
          this.props.onClose();
        } else {
          this.props.setInfoAlert({
            msg: `Failed to insert Insurance: ${response.msg}`,
            status: false,
          })
          this.setState({
            submit:false,
          });
          this.props.showAlertNotes();
          this.props.refetchOrder(true)
        }
    }
  }
  handleShowPassword(arg){
    this.setState({
        showPassword:arg,
    })
}
  render() {
    return (
      <>
        <Modal
        disablePortal
        disableEnforceFocus
        disableAutoFocus
        open
        aria-labelledby="server-modal-title"
        aria-describedby="server-modal-description"
        sx={{
          display: 'flex',
          p: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        // onClose={this.props.onClose}
        // container={() => rootRef.current}
      >
        <Card
          sx={{
              width:'40%',
              maxHeight:'60%',
              overflow:'auto',
              padding:4,
              textAlign:'center',
              padding:2,
              // border:'gray',
              // shadow:true
          }}
        >
           <Box
            sx={(theme) => ({
              position: 'relative',
              bgcolor: 'background.paper',
              // boxShadow: theme.shadows[5],
            })}
          >
            <Box
              sx={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  flexDirection:'column',
                  textAlign:'left',
                  gap:2,
              }}
            >
              <Box
                sx={{
                  display:'flex',
                  flexDirection:'row',
                  justifyContent:'space-between'
                }}
              >
                <Typography
                  // width={'90%'}
                >
                </Typography>
                <IconButton
                  onClick={() => {
                    this.props.onClose()
                  }}
                >
                  <CloseIcon  fontSize="small"/>
                </IconButton>
              </Box>
              <Box>
                <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                  Add Patient
                </Typography>
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='patiant_name'
                    label="Patiant Name"
                    placeholder='Patiant Name'
                    required
                    type='text'
                    value={this.state.patiant_name}
                    onChange={(e) =>
                        this.setState({
                            patiant_name:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='group'
                    label="Group"
                    placeholder='Group'
                    type='text'
                    value={this.state.group}
                    onChange={(e) =>
                        this.setState({
                            group:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='cert'
                    label="Cert"
                    placeholder='Cert'
                    type='text'
                    value={this.state.cert}
                    onChange={(e) =>
                        this.setState({
                            cert:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                sx={{ 
                  display: 'flex',
                  position: 'relative',
                  width:'100%'
                  }}
                  
              >
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                    sx={{
                        width:'100%'
                    }}
                    format=' YYYY / MM / DD'
                    label="Date of Birth"
                    // value={this.state.timeline}
                    onChange={(newValue) =>{ 
                        this.setState({
                            date_of_birth:newValue
                        })
                        }
                    }/>
                </LocalizationProvider>
              </Box>
              <Box
                sx={{ 
                  display: 'flex',
                  position: 'relative',
                  width:'100%'
                  }}
                  
              >
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                    sx={{
                        width:'100%'
                    }}
                    format=' YYYY / MM / DD'
                    label="Plan Holder Date of Birth"
                    // value={this.state.timeline}
                    onChange={(newValue) =>{ 
                        this.setState({
                        plan_holder_date_of_birth:newValue
                        })
                        }
                    }/>
                </LocalizationProvider>
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='plan_holder'
                    label="Plan Holder"
                    placeholder='Plan Holder'
                    type='text'
                    value={this.state.plan_holder}
                    onChange={(e) =>
                        this.setState({
                            plan_holder:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='relation_to_subscriber'
                    label="Relation to Subscriber"
                    placeholder='Relation to Subscriber'
                    type='text'
                    value={this.state.relation_to_subscriber}
                    onChange={(e) =>
                        this.setState({
                            relation_to_subscriber:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='notes'
                    label="Notes"
                    placeholder='Notes'
                    type='text'
                    value={this.state.notes}
                    onChange={(e) =>
                        this.setState({
                            notes:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                  multiline
                  aria-multiline
                  rows={4}
                  fullWidth
                  size='small'
                  id='breakdown'
                  label="Breakdown"
                  placeholder='Breakdown'
                  type='text'
                  value={this.state.breakdown}
                  onChange={(e) =>
                      this.setState({
                          breakdown:e.target.value
                      })
                  }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    multiline
                    aria-multiline
                    rows={4}
                    fullWidth
                    size='small'
                    id='address'
                    label="Address"
                    placeholder='Address'
                    type='text'
                    value={this.state.address}
                    onChange={(e) =>
                        this.setState({
                            address:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                sx={{
                  display:'flex',
                  alignItems:'center'
                }}
              >
                {this.state.submit ? (
                  <>
                    <Button 
                      sx={{
                        marginRight:1
                      }}
                      // color={purple}
                      startDecorator={
                        <CircularProgress variant="solid"/>
                      }
                    >
                      Loading…
                    </Button>
                  </>
                ) : (
                  <ColorButton 
                      variant="contained"
                      sx={{
                        marginRight:1
                      }}
                      onClick={() =>this.handleSubmit()}   
                  >
                      Submit
                  </ColorButton>
                )}
                <ColorButton 
                      variant="contained"
                      onClick={() => {
                        this.props.onClose()
                          // setStatus(false);
                          // onClose();
                      }}
                      // onClick={() =>this.handleSignIn()}   
                  >
                      Close
                  </ColorButton>
              </Box>
            </Box>
          </Box>
        </Card>
      </Modal></>
    )
  }
}
export default compose(
  themeHook,
  authHook,
  dashboardHook
)(InsuranceAdd)