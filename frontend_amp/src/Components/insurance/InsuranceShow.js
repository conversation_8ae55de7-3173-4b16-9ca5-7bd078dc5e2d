
import React, { Component } from 'react'
import { Avatar, Box, Button, Card, CircularProgress, IconButton, Modal, TextField, Typography } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close';
import { ColorButton } from '../ColorButton';
import { Label, Visibility, VisibilityOff } from '@mui/icons-material';
import { grey } from '@mui/material/colors';
export default class InsuranceShow extends Component {
  constructor(props){
    super(props)
    this.state={
      showPassword:false
    }
  }

  render() {
      return (
        <>
          <Modal
          disablePortal
          disableEnforceFocus
          disableAutoFocus
          open
          aria-labelledby="server-modal-title"
          aria-describedby="server-modal-description"
          sx={{
            display: 'flex',
            p: 1,
            alignItems: 'center',
            justifyContent: 'center',
          }}
          // onClose={this.props.onClose}
          // container={() => rootRef.current}
        >
          <Card
            sx={{
                width:'40%',
                maxHeight:'60%',
                overflow:'auto',
                padding:4,
                textAlign:'center',
                padding:2,
                // border:'gray',
                // shadow:true
            }}
          >
             <Box
              sx={(theme) => ({
                position: 'relative',
                bgcolor: 'background.paper',
                // boxShadow: theme.shadows[5],
              })}
            >
              <Box
                sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection:'column',
                    textAlign:'left',
                    gap:1,
                    marginBottom:4
                }}
              >
                <Box
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    justifyContent:'space-between'
                  }}
                >
                  <Typography
                    // width={'90%'}
                  >
                  </Typography>
                  <IconButton
                    onClick={() => {
                      this.props.onClose()
                    }}
                  >
                    <CloseIcon  fontSize="small"/>
                  </IconButton>
                </Box>
                <Box
                    sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                  patiant_name:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.patiant_name || '----'}
                  </Typography>
                </Box>
                <Box
                    sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                    Group:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.group || '----'}
                  </Typography>
                </Box>
                <Box
                    sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                    Cert:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.cert || '----'}
                  </Typography>
                </Box>
                <Box
                    sx={{
                      width: '100%',  
                      borderBottom:`1px ${grey[200]} solid`,
                      paddingTop:1.5,
                      paddingLeft:4,
                      paddingBottom:1,
                      paddingRight:1,
                      alignItems:'center'
                    }}
                >
                  <Typography variant='p'>
                    Date Of Birth:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.date_of_birth || '----'}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                   }}
                >
                  <Typography variant='p'>
                    Plan Holder Date Of Birth:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.plan_holder_date_of_birth || '----'}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                    Plan Holder:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.plan_holder || '----'}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                    Relation To Subscriber:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.relation_to_subscriber || '----'}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                    Notes:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.notes || '----'}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    position:'relative',
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                    BreakDown:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant=''
                    >
                      {this.props.item.breakdown}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    position:'relative',
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                    Address:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant=''
                    >
                      {this.props.item.address}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Card>
        </Modal></>
      )
  }
}
