import React, { Component } from 'react'
import { Avatar, Box, Card, FormControl, IconButton, InputLabel, Modal, Select, TextField, Typography } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close';
import { ColorButton } from '../ColorButton';
import { Label, Visibility, VisibilityOff } from '@mui/icons-material';
import { green, grey, red } from '@mui/material/colors';
import { compose } from 'redux';
import themeHook from '../../HOK/ThemeHook';
import { DEV_BASE_URL } from '../../Environments';
const services=[
  'Procurement_Hub',
  'Insurance_Verification',
  'Patient_Experience'
]
class UserShow extends Component {
  constructor(props){
    super(props)
    this.state={
      showPassword:false
    }
  }
  handleShowPassword(arg){
    this.setState({
        showPassword:arg,
    })
  }
  render() {
      return (
        <>
          <Modal
          disablePortal
          disableEnforceFocus
          disableAutoFocus
          open
          aria-labelledby="server-modal-title"
          aria-describedby="server-modal-description"
          sx={{
            display: 'flex',
            p: 1,
            alignItems: 'center',
            justifyContent: 'center',
          }}
          // onClose={this.props.onClose}
          // container={() => rootRef.current}
        >
          <Card
            sx={{
                width:'50%',
                padding:4,
                textAlign:'center',
                padding:2,
                backgroundColor:this.props.theme.selectTheme.background_2,

                // border:'gray',
                // shadow:true
            }}
          >
             <Box
              sx={(theme) => ({
                position: 'relative',
                bgcolor: 'background.paper',
                backgroundColor:this.props.theme.selectTheme.background_2,

                // boxShadow: theme.shadows[5],
              })}
            >
              <Box
                sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection:'column',
                    textAlign:'left',
                    backgroundColor:this.props.theme.selectTheme.background_2,
                    gap:1,
                }}
              >
                <Box
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    justifyContent:'space-between'
                  }}
                >
                  <Typography
                    // width={'90%'}
                  >
                  </Typography>
                  <IconButton
                    onClick={() => {
                      this.props.onClose()
                    }}
                  >
                    <CloseIcon  fontSize="small"/>
                  </IconButton>
                </Box>
                <Box
                  sx={{
                    display:'flex',
                    justifyContent:'space-between'
                  }}
                >
                  <Box
                    sx={{
                      display:'flex',
                      width:'50%',
                      alignItems:'center'

                    }}
                  >
                    {this.props.item.dental_logo?(
                      <img 
                        // sx={{ 
                        //   // height: '70px', 
                        //   // width: '250px',
                        //   backgroundRepeat: "no-repeat",
                        //   backgroundSize: "cover",
                        //  }}
                        alt={(this.props.item.dental_logo?(DEV_BASE_URL+'/'+this.props.item.dental_logo):(this.props.item.first_name.charAt(0) +this.props.item.first_name.charAt(1)))}
                        src={DEV_BASE_URL+'/'+(this.props.item.dental_logo?this.props.item.dental_logo:'')}
                      />
                      ):(
                        <Avatar   
                          sx={{
                            width:"108px",
                            height:'108px'
                          }}
                          alt={(this.props.item.first_name.charAt(0) +this.props.item.first_name.charAt(1))}
                          src={DEV_BASE_URL+'/'+(this.props.item.dental_logo?this.props.item.dental_logo:'')}
                          sizes='auto'
                        />
                      )
                    }
                    
                    
                    {/* <Box
                      sx={{
                        display:'flex',
                        width:'50%',
                        flexDirection:'column',
                        color:this.props.theme.selectTheme.fontColor,
                        marginLeft:2,
                        alignContent:'center',
                        gap:1
                      }}
                    >
                      <Typography variant='h3' fontSize={'1rem'} fontWeight={'500'}>
                        {this.props.item.first_name + ' ' +this.props.item.last_name || '----'}
                      </Typography>
                      <Typography variant='h3' fontSize={'1rem'} fontWeight={'500'}>
                        {this.props.item.email || '----'}
                      </Typography>
                    </Box> */}
                  </Box>
                </Box>
                <Box
                  sx={{
                    width: '100%',  
                    display:'flex',
                    flexDirection:'row',
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                   <Typography variant='h6'>
                    Name:
                  </Typography>
                  <Typography 
                   variant='h3' fontSize={'1rem'} fontWeight={'500'}
                    sx={{
                      color:this.props.theme.selectTheme.fontColor,
                      paddingX:1
                    }}
                    >
                    {this.props.item.first_name + ' ' +this.props.item.last_name || '----'}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',  
                    display:'flex',
                    flexDirection:'row',
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                   <Typography variant='h6'>
                    Email:
                  </Typography>
                  <Typography 
                   variant='h3' fontSize={'1rem'} fontWeight={'500'}
                    sx={{
                      color:this.props.theme.selectTheme.fontColor,
                      paddingX:1
                    }}
                    >
                      {this.props.item.email || '----'}
                  </Typography>
                </Box>
                <Box
                    sx={{
                    width: '100%',  
                    display:'flex',
                    flexDirection:'row',
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='h6'>
                    Department:
                  </Typography>
                  <Typography 
                    sx={{
                      color:this.props.theme.selectTheme.fontColor,
                      paddingX:1
                    }}
                    variant='p'>
                      {this.props.item.is_admin ? 'Admin' : 'Dental'}
                  </Typography>
                </Box>
                <Box
                    sx={{
                      width: '100%',  
                      display:'flex',
                      flexDirection:'row',
                      borderBottom:`1px ${grey[200]} solid`,
                      paddingTop:1.5,
                      paddingLeft:4,
                      paddingBottom:1,
                      paddingRight:1,
                      alignItems:'center'
                    }}
                >
                  <Typography variant='h6'>
                    Mobil Number:
                  </Typography>
                  <Typography 
                    sx={{
                      color:this.props.theme.selectTheme.fontColor,
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.phone_number || '----'}
                  </Typography>
                </Box>
                
                <Box
                  sx={{
                    width: '100%',  
                    display:'flex',
                    flexDirection:'row',
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                   }}
                >
                  <Typography variant='h6'>
                    Status:
                  </Typography>
                  <Typography 
                    sx={{
                      color:this.props.item.is_active?green[600]:red[600],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.is_active?'Active':'Not Active'}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',  
                    display:'flex',
                    flexDirection:'row',
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='h6'>
                    Budget:
                  </Typography>
                  <Typography 
                    sx={{
                      color:this.props.theme.selectTheme.fontColor,
                      paddingX:1
                    }}
                    variant='p'>
                    ${this.props.item.total_budget || 0.0}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',  
                    display:'flex',
                    flexDirection:'row',
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <FormControl fullWidth sx={{ }}>
                    <InputLabel shrink htmlFor="select-multiple-native">
                      Services
                    </InputLabel>
                    <Select
                      disabled
                      multiple
                      native
                      value={this.props.item.groups}
                      // onChange={(event)=>this.handleChangeMultiple(event)}
                      label="Native"
                      inputProps={{
                        id: 'select-multiple-native',
                      }}
                    >
                      {services.map((name) => (
                        <option key={name} value={name}>
                          {name}
                        </option>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
              </Box>
            </Box>
          </Card>
        </Modal></>
      )
  }
}
export default compose(
  themeHook,
)(UserShow)
