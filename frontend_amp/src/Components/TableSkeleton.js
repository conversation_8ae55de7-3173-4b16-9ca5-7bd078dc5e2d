// import { Table } from 'flowbite-react';
import { Box, Skeleton, TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import Table from '@mui/material/Table';

import React from 'react';
import { useTheme } from '../context/ThemeContext';
import { grey } from '@mui/material/colors';
// import 'flowbite/css/flowbite.css';
const tdClass = () => {
    return 'px-1 border border-gray-200 dark:border-gray-600 text-center py-2';
};
const TableSkeleton = () => {
    const tdclass = tdClass();
    const theme=useTheme()

    return (
        <Box
            sx={{
                background:theme.selectTheme.background_2,
                width:'100%'
            }}
            className='block w-full px-2 space-y-1 divide-y divide-gray-200 rounded animate-pulse dark:divide-gray-700'
            role='status'
        >
            <Table 
                hoverable 
                className='table-auto'
                sx={{
                    background:theme.selectTheme.background_2
                }}    
            >
                <TableHead>
                    <TableRow
                                sx={{
                                    // background:theme.selectTheme.background
                                }}
                                // key={index}
                                className='px-1 text-xs text-center border border-gray-200 dark:border-gray-600'
                            >
                        {Array.from({ length: 8 }, (_, index) => (
                            <TableCell
                                sx={{
                                    px:1,
                                    py:2,
                                    border:`1px ${grey[200]} solid`,
                                    textAlign:'center',
                                    backgroundColor:theme.selectTheme.background_2,
                                    color: theme.selectTheme.fontColor_3
                                }}
                            >
                                <Skeleton 
                                    variant="text" 
                                    sx={{ 
                                        fontSize: '1rem',
                                        width:'100px',
                                        background:theme.selectTheme.background
                                    }} 
                                    animation="wave" 
                                />
                            </TableCell>
                        ))}
                    </TableRow>
                </TableHead>
                <TableBody 
                    sx={{
                        height:'100%',
                        paddingTop:0,
                        marginTop:0,
                    }}
                    className='divide-y max-h-[500px] min-h-[400px] overflow-scroll'>
                    {Array.from({ length: 8 }, (_, index) => (
                        <TableRow
                            // sx={{
                            // background:theme.selectTheme.background_2
                            // }}
                            className='bg-white dark:bg-gray-800 min-h-[40px]'
                            key={index}
                        >
                            {Array.from({ length: 8 }, (_, key) => (
                                <TableCell 
                                    sx={{
                                        background:theme.selectTheme.background_2
                                    }}
                                    key={key}
                                    className={tdclass}
                                    >
                                    <div className='p-2'>
                                        <div className='h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-[70%] mb-2.5'></div>
                                        <div className=' w-[90%] h-2 bg-gray-200 rounded-full dark:bg-gray-700'></div>
                                    </div>
                                </TableCell>
                            ))}
                        </TableRow>
                    ))}
                </TableBody>
            </Table>

            {/* <Table hoverable className='w-full'>
                <Table.Head>
                    {Array.from({ length: 10 }, (_, index) => (
                        <Table.HeadCell className='grow text-left mx-3 items-center justify-between p-1 space-y-5 divide-y divide-gray-200 rounded-xl animate-pulse dark:divide-gray-700 bg-gray-300 rounded-2xl dark:bg-gray-700'>
                            <span className='bg-gray-300 rounded-xl dark:bg-gray-700 w-[300px] h-[40px]'></span>
                        </Table.HeadCell>
                    ))}
                </Table.Head>
                <Table.Body className='divide-y max-h-[500px] min-h-[400px] overflow-scroll'></Table.Body>
            </Table> */}
        </Box>
    );
};

export default TableSkeleton;
