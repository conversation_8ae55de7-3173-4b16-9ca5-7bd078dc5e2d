import React, { Component } from 'react'
import { Box, Button, Checkbox, FormControlLabel, FormGroup, IconButton, Menu, MenuItem, Radio, RadioGroup, Typography } from '@mui/material'
import { HiDocumentAdd } from 'react-icons/hi'
import { CiFilter } from "react-icons/ci";
import FilterDate from './FilterDate'
import SearchBox from './SearchBox'
import { purple } from '@mui/material/colors'
import { compose } from 'redux'
import themeHook from '../HOK/ThemeHook'
import locationHook from '../HOK/LoctionHook'
import authHook from '../HOK/AuthHook'
import { CancelOutlined } from '@mui/icons-material';
const menuItem={
    'dashboard':{label:'Dashboard',button:false,buttonLabel:'Dashboard',filter:false,searchBar:true},
    'order':{label:'Recent Orders',button:true,buttonLabel:'Add Order',filter:false,searchBar:true},
    'received':{label:'Received Orders',button:false,buttonLabel:'',filter:false,searchBar:true},
    'inventory':{label:'Inventory List ',button:true,buttonLabel:'Add Product',filter:true,filterValue:{'None':1,'Expire Date':2,'Low QTY':3,'Alerts':4},searchBar:true},
    'return':{label:'Returned Orders',button:false,buttonLabel:'',filter:false,searchBar:true},
    'notes':{label:'Notes',button:true,buttonLabel:'Add Notes',filter:false,searchBar:false},
    'vendor-list':{label:'Vendors List',button:true,buttonLabel:'Add Vendor',filter:false,searchBar:true},
    'report':{label:'Report',button:false,buttonLabel:'',filter:false,searchBar:false},
    'myproducts':{label:'My Products',button:false,buttonLabel:'',filter:false,searchBar:true},
    'budget':{label:'Invoices',button:true,buttonLabel:'Add Invoice',filter:false,searchBar:false},
    'insurance':{label:'Insurance Verification List',button:true,buttonLabel:'Add Patient',filter:false,searchBar:true},
    'patient-experience':{label:'Patient Experience List',button:true,buttonLabel:'Add Patient',filter:false,searchBar:true},
    'user-list':{label:'User List',button:true,buttonLabel:'Add User',filter:false,searchBar:true},
    // 'user-list':{label:'User Manager',button:true,buttonLabel:'Add SubUser',filter:false,searchBar:true},

}
class OrderMenu extends Component {
    constructor(props){
        super(props)
        this.state={
            theme:props.theme,
            path:props.location.pathname.slice(1,),
            // path:this.props.location.pathname.slice(1,),
            proccessing:null,
            filter:'',
            filterOpen:false,
            filterAnchorEl:null,
            filterChoice:''
        }
        this.handleFilterClick=this.handleFilterClick.bind(this)
        this.handleCloseFilterArea=this.handleCloseFilterArea.bind(this)
        this.handleChangeFilter=this.handleChangeFilter.bind(this)
    }
    handleFilterClick(event){
        this.setState({
          filterAnchorEl:event.currentTarget,
          filterOpen:true
        })
      }
      handleCloseFilterArea(){
        this.setState({
            filterAnchorEl:null,
            filterOpen:false
        });;
      }
    handleChangeFilter(event){
        this.props.applyFilter(menuItem[this.state.path].filterValue[event.target.value])
        this.setState({
            filterChoice:event.target.value
        })
    }
    componentDidMount(){
        let path=this.props.location.pathname
        this.setState({
            theme:this.props.theme,
            path:path.slice(1,),
            proccessing:null,
        })
    }
    render() {

    return (
        <>
            <Box
                sx={{
                display:'flex',
                flexDirection:'row',
                justifyContent:'space-between',
                alignItems:'center',
                // marginY:2.5,
                paddingY:2.5,
                backgroundColor:this.props.theme.selectTheme.background_3,
                color: this.props.theme.selectTheme.fontColor
                // backgroundColor:"#F3F4F6"
                }}
            >
                <Box 
                sx={{
                    display:'flex',
                    paddingLeft:5,
                    textAlign:'left',
                    alignItems:'center',
                }}
                >
                    <Typography
                        variant='h3'
                        sx={{
                        fontSize:'1.75rem'
                        }}
                    > 
                        {menuItem[this.state.path].label}
                    </Typography>
                    {menuItem[this.state.path].button && (
                        <Button
                            variant="contained"
                            sx={{
                            background:purple,
                            borderRadius:'25px',
                            padding:'0.5rem',
                            marginLeft:1.5,
                            marginRight:1.5,
                            alignItems:'center'
                            }}
                            onClick={() => {
                                    if (this.state.path=='user-list'){
                                        if(!this.props.auth.user.is_admin){
                                            this.props.handleStatus(1,0,0,'sub_user',this.props.auth.user)
                                        }else{
                                            this.props.handleStatus(1,0)
                                        }
                                    }else{
                                        this.props.handleStatus(1,0)
                                    }
                                }
                            }
                        >
                            <HiDocumentAdd 
                                style={{
                                marginRight:1,
                                alignItems:'center'
                                }}
                                size={'20px'}
                            />
                            <Typography 
                                sx={{
                                    marginTop:'2px',
                                    fontSize:'0.75rem',
                                    alignItems:'center'
                                }}
                                variant='span'
                                >
                                {menuItem[this.state.path].buttonLabel}
                            </Typography>
                            {/* <span style={{marginTop:'2px',fontSize:'0.75rem'}}></span> */}
                        </Button>
                    )}
                    {this.state.proccessing && <p>Proccessing...</p>}
                </Box>
                <Box 
                    sx={{
                        display:'flex',
                        flexDirection:'row',
                        backgroundColor:purple,
                        paddingRight:'0.75rem',
                    }}
                >
                    <Box 
                        sx={{
                            display:'flex',
                            flexDirection:'row',
                            background:purple,
                            paddingRight:'0.75rem',
                        }}
                    >
                        {menuItem[this.state.path].filter && (
                            <>
                                <Box
                                    variant="contained"
                                    sx={{
                                        background:purple,
                                        display:'flex',
                                        flexDirection:'row',
                                        alignContent:'center',
                                        alignItems:'center',
                                        borderRadius:'25px',
                                        padding:'0.5rem',
                                        marginLeft:1.5,
                                        marginRight:1.5,
                                        alignItems:'center'
                                    }}
                                    
                                >
                                    <IconButton
                                        id="filter-button"
                                        onClick={(e)=>{
                                            this.handleFilterClick(e)
                                        }
                                        }
                                        size="small"
                                        sx={{
                                            backgroundColor:purple,
                                            borderRadius:'25px',
                                            padding:'0.5rem',
                                            alignItems:'center',
                                            }}
                                        
                                        aria-label="more"
                                        aria-haspopup="true"                        
                                        aria-controls={this.state.filterOpen ? 'filter-menu' : undefined}
                                        aria-expanded={this.state.filterOpen ? 'true' : undefined}
                                    >
                                        <Typography>
                                            Sorted By
                                        </Typography>
                                        <CiFilter 
                                            style={{
                                                alignItems:'center'
                                            }}
                                            size={'20px'}
                                        />  
                                    </IconButton>
                                </Box>
                                <Menu
                                    anchorEl={this.state.filterAnchorEl}
                                    id="filter-menu"
                                    open={this.state.filterOpen}
                                    onClose={this.handleCloseFilterArea}
                                    // onClick={this.handleCloseFilterArea}
                                    MenuListProps={{
                                        'aria-labelledby': 'filter-button',
                                    }}
                                    slotProps={{
                                        paper: {
                                        style: {
                                            maxHeight: 100 * 4.5,
                                        },
                                        elevation: 0,
                                        sx: {
                                            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                                            margin:1,
                                            padding:2,
                                            textAlign:'center'
                                        },
                                        },
                                    }}
                                    sx={{
                                        width:'100%',
                                        display:'flex',
                                        flexDirection:'column',
                                        justifyContent:'center',
                                        alignContent:'center',
                                        alignItems:'center',
                                        textAlign:'center',
                                        marginLeft:'2',
                                        padding:2,
                                        gap:'1'
                                    }}
                                    transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                                    anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                                >
                                    <RadioGroup
                                        aria-labelledby="demo-controlled-radio-buttons-group"
                                        name="controlled-radio-buttons-group"
                                        value={this.state.filterChoice}
                                        onChange={this.handleChangeFilter}
                                    >
                                        {Object.keys(menuItem[this.state.path].filterValue).map((filter,index)=>{
                                            return(
                                                    <FormControlLabel 
                                                        key={index}
                                                        value={filter}
                                                        sx={{
                                                            display:'flex',
                                                            flexDirection:'row',
                                                            justifyContent:'space-between',
                                                            textAlign:'left '
                                                        }}
                                                        control={
                                                        <Radio
                                                            sx={{
                                                                color:'inherit',
                                                            }}
                                                        />
                                                        }  
                                                        label={filter} 
                                                    />                                                    
                                                )
                                            }
                                        )}
                                    </RadioGroup>

                                </Menu>   
                            </>                   
                            // </Button>
                            // <FilterDate filter={this.state.filter} />
                        )}
                        {this.state.filterChoice && (
                            <Box
                                sx={{
                                    display:'flex',
                                    justifyContent:'left',
                                    alignContent:'center',
                                    alignItems:'center'
                                }}
                            >
                                <Typography>
                                    {this.state.filterChoice}
                                </Typography>
                                <IconButton
                                    onClick={()=>{
                                        this.props.applyFilter(1)
                                        this.setState({
                                            filterChoice:''
                                        })
                                    }}
                                >
                                    <CancelOutlined/>
                                </IconButton>
                            </Box>
                        )}
                    </Box>
                    <Box
                        sx={{
                            display:'flex',
                            flexDirection:'row',
                            background:purple,
                            paddingRight:'0.75rem',
                        }}
                        >
                        {menuItem[this.state.path].searchBar && (
                            <SearchBox />
                        )}
                    </Box>
                </Box>
            </Box>
        </>
    )
    }
}
export default compose(
    locationHook,
    themeHook,
    authHook
)(OrderMenu)