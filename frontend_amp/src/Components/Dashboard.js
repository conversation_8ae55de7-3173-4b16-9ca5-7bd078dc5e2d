// Import the sidebar context
import { useSidebar } from './SideBar';

// Inside your Dashboard component
const Dashboard = (props) => {
  const { sidebarCollapsed } = useSidebar();
  
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        // Adjust padding based on sidebar state
        padding: sidebarCollapsed ? '0 8px' : '0 16px',
        transition: 'padding 0.3s ease-in-out',
      }}
    >
      {/* ComboboxWithStatus container */}
      <Box
        sx={{
          width: '100%',
          // Ensure this container adjusts with sidebar changes
          maxWidth: sidebarCollapsed ? 'calc(100% - 10px)' : '100%',
          transition: 'max-width 0.3s ease-in-out',
        }}
      >
        <ComboboxWithStatus />
      </Box>
      
      {/* Rest of your dashboard content */}
    </Box>
  );
};