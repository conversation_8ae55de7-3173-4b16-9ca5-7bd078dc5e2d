import React, { Component, useState } from 'react'
import Modal from '@mui/material/Modal';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import CloseIcon from '@mui/icons-material/Close';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import dayjs from 'dayjs';
import { DemoContainer } from '@mui/x-date-pickers/internals/demo';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
// import {
//   Unstable_NumberInput as BaseNumberInput,
//   numberInputClasses,
// } from '@mui/base/Unstable_NumberInput';
import { Alert, AlertTitle, Card, Checkbox, FormControl, FormControlLabel, FormGroup, IconButton, InputLabel, MenuItem, Select, styled, TextField } from '@mui/material';
import { green, purple, red } from '@mui/material/colors';
import { compose } from 'redux';
import dashboardHook from '../HOK/DashboardHook';
import themeHook from '../HOK/ThemeHook';
import authHook from '../HOK/AuthHook';
import { ColorButton } from './ColorButton';
import locationHook from '../HOK/LoctionHook';
import Button from '@mui/joy/Button';
import { CircularProgress } from '@mui/joy';
import DeliveredQty from './order/DeliveredQty';
import { formatDate, orderStatusFn } from '../utils/functions';
import DoneIcon from '@mui/icons-material/Done';
import { Cancel } from '@mui/icons-material';

const status={
  'Partially Delivered':5,
  'Delivered':6,
}

class OrderEdit extends Component {
  constructor(props){
    super(props)
    this.state={
      theme:props.theme,
      id:props.item.id,
      product:props.item.product.name,
      s_k_u:props.item.product.s_k_u,
      vendor:props.item.vendor?props.item.vendor.name:'',
      note:props.item.note || '', // Add notes field to state
      qty:props.item.qty,
      qty_pending:0,
      qty_delivered:0,
      checkSpecific:props.item.specific,
      checkUrgent:props.item.urgent,
      checkReceived:props.item.received,
      checkMyProduct:props.item.my_product,
      timeline:props.item.timeline,
      statusIndex:this.props.dashboard.orderStatus[orderStatusFn(props.item.status).name],
      statusSelectedOption:props.item.status,
      statusChoiceColor:'',
      statusChoice:false,
      submitEdit:false,
      statusDeliveredQty:false,
      error:'',
      item:props.item,
    }
  }
  componentDidMount(){
    this.setState({
      item:this.props.item,
      id:this.props.item.id,
      product:this.props.item.product.name,
      s_k_u:this.props.item.product.s_k_u,
      vendor:this.props.item.vendor?this.props.item.vendor.name:'',
      note:this.props.item.note || '', // Add notes to componentDidMount
      qty:this.props.item.qty,
      checkSpecific:this.props.item.specific,
      checkUrgent:this.props.item.urgent,
      checkReceived:this.props.item.received,
      checkMyProduct:this.props.item.my_product,
      timeline:this.props.item.timeline,
      statusIndex:this.props.dashboard.orderStatus[orderStatusFn(this.props.item.status).name],
      statusSelectedOption:this.props.item.status,
      statusChoiceColor:null
    })
  }
  async handleSubmit(){
    this.setState({
      submitEdit:true
      }
    )
    let orderData={
      id:this.props.item.id,
      admin_id:null,
      product:this.state.product,
      s_k_u:this.state.s_k_u,
      vendor:this.state.vendor,
      note:this.state.note, // Add notes to orderData
      qty:this.state.qty,
      specific:this.state.checkSpecific,
      urgent:this.state.checkUrgent,
      my_product:this.state.checkMyProduct,
      // "exp_delivery":"",
      received:this.state.checkReceived,
      status:this.state.statusIndex
    }
    if (!this.props.auth.user.is_admin){
      if(!this.state.checkReceived){
        orderData.status=this.props.dashboard.orderStatus[orderStatusFn(this.props.item.status).name]
      }else{
        if (this.state.statusIndex==5){
          orderData={
            ...orderData,
            qty_pending:this.state.qty_pending,
            qty_delivered:this.state.qty_delivered
          }
        }
      }
    }else{
      if (this.state.statusIndex==5){
        orderData={
          ...orderData,
          qty_pending:this.state.qty_pending,
          qty_delivered:this.state.qty_delivered
        }
      }
    }
    if (this.state.checkUrgent){
      const timeline=new Date(this.state.timeline)
      orderData.timeline=`${timeline.getFullYear()}-${timeline.getMonth() + 1}-${timeline.getDate()}`
    }
    if (this.props.auth.user.user_group==2){
      orderData.admin_id=this.props.auth.selectUser.id
    }else if(this.props.auth.user.user_group==3){
      orderData.admin_id=this.props.auth.user.id
    }else if(this.props.auth.user.user_group==4){
      orderData.admin_id=this.props.auth.user.ref_admin.id
    }
    else{

    }
    let response=await this.props.dashboard.updateOrder(this.props.auth.token,orderData)
    if (response.status!=200) {
      this.props.setInfoAlert({
        msg: `Error upadting order: ${response.msg}`,
        status: false,
      })
      this.setState({
        submitEdit:false,
        });
      this.props.showAlertNotes();
      this.props.refetchOrder(true)
    } else {
        if (response.msg == 'success') {
          this.props.setInfoAlert({
            msg: 'Order Update successfully',
            status: true,
          })
          this.props.refetchOrder(true)
            this.setState({
              submitEdit:false,
            });
          this.props.showAlertNotes();
          this.props.onClose();
        } else {
          this.props.setInfoAlert({
            msg: `Failed to upadte order: ${response.msg}`,
            status: false,
          })
          this.setState({
            submitEdit:false,
          });
          this.props.showAlertNotes();
          this.props.refetchOrder(true)
        }
    }
  }
  handleCloseModal(){
    this.setState({
      statusDeliveredQty:false
    })
  }
  render() {
    return (
        <>
         
          <Modal
          disablePortal
          disableEnforceFocus
          disableAutoFocus
          open
          aria-labelledby="server-modal-title"
          aria-describedby="server-modal-description"
          sx={{
            display: 'flex',
            p: 1,
            alignItems: 'center',
            justifyContent: 'center',
          }}
          // onClose={this.props.onClose}
          // container={() => rootRef.current}
        >
          <Card
            sx={{
                width:'40%',
                padding:4,
                textAlign:'center',
                padding:2,
                // border:'gray',
                // shadow:true
            }}
          >
            <Box
              sx={(theme) => ({
                position: 'relative',
                bgcolor: 'background.paper',
                // boxShadow: theme.shadows[5],
              })}
            >
              <Box
                sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection:'column',
                    textAlign:'left',
                    gap:2,
                }}
              >
                <Box
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    justifyContent:'space-between'
                  }}
                >
                  <Typography
                    // width={'90%'}
                  >
                  </Typography>
                  <IconButton
                    onClick={() => {
                      this.props.onClose()
                    }}
                  >
                    <CloseIcon  fontSize="small"/>
                  </IconButton>
                </Box>
                <Box>
                  <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                    Edit Order
                  </Typography>
                </Box>
                {this.state.error && (
                  <Box>
                    <Alert 
                        variant="filled" 
                        severity="error"
                        onClose={() => {
                            this.setState({
                                error:""
                            })
                        }}
                    >
                        <AlertTitle>
                            Info alert!
                        </AlertTitle>
                        {this.state.error}
                    </Alert> 
                  </Box>         
                )}
                <Box
                    sx={{
                    width: '100%',                                           
                    }}
                >
                  <TextField
                      fullWidth
                      disabled={(!this.props.auth.user.is_admin) && (this.state.statusSelectedOption=="incoming")}
                      size='small'
                      id='product'
                      label="product"
                      placeholder='product'
                      required
                      type='text'
                      value={this.state.product}
                      onChange={(e) =>
                          this.setState({
                              product:e.target.value
                          })
                      }
                  />
                </Box>
              <Box
                sx={{
                  width: '100%',  
                  backgroundColor:'inherit',
                  color:'inherit',                                      
                }}
              >
                <TextField
                  fullWidth
                  disabled={(!this.props.auth.user.is_admin) && (this.state.statusSelectedOption=="incoming")}
                  size='small'
                  id='s_k_u'
                  label="SKU"
                  placeholder='SKU(Manufacturer Code)'
                  type='text'
                  sx={{
                    backgroundColor:'inherit',
                    color:'inherit',
                  }}  
                  value={this.state.s_k_u}
                  onChange={(e) =>
                      this.setState({
                        s_k_u:e.target.value
                      })
                  }
                />
              </Box>
              {/* Add notes field below product */}
                <Box
                    sx={{
                      width: '100%',
                      backgroundColor:'inherit',
                      color:'inherit',
                      marginTop: 2                                      
                    }}
                >
                  <TextField
                      fullWidth
                      multiline
                      rows={3}
                      disabled={(!this.props.auth.user.is_admin) && (this.state.statusSelectedOption=="incoming")}
                      size='small'
                      id='note'
                      label="Note"
                      placeholder='Add note about this order'
                      type='text'
                      sx={{
                        backgroundColor:'inherit',
                        color:'inherit',
                      }}  
                      value={this.state.note}
                      onChange={(e) =>
                          this.setState({
                              note:e.target.value
                          })
                      }
                  />
                </Box>
                <Box
                  sx={{ 
                    display: 'flex',
                    position: 'relative' 
                    }}
                >
                    <TextField 
                        required
                        disabled={(!this.props.auth.user.is_admin) && (this.state.statusSelectedOption=="incoming")}
                        size='small'
                        id='vendor'
                        // className='!focus:border-purple-700'
                        label="vendor" 
                        placeholder='vendor'
                        type='text'
                        fullWidth
                        value={this.state.vendor}
                        onChange={(e) =>
                            this.setState({
                                vendor:e.target.value
                            })
                        }
                    />
                </Box>
                <Box
                  sx={{ 
                    display: 'flex',
                    position: 'relative',
                    width:'100%',
                    justifyContent:'space-between'
                    }}
                >
                  <Box 
                    size='small'
                    fullWidth
                    sx={{
                      display:'flex',
                      flexDirection:'row',
                      width:'45%',
                      position:'relative',
                      alignItems:'center'
                      // borderColor:this.props.theme.fontColor,
                        // backgroundColor:this.props.theme.background,
                        // color:this.props.theme.fontColor
                    }}
                  >
                    <TextField 
                      required
                      disabled={(!this.props.auth.user.is_admin) && (this.state.statusSelectedOption=="incoming")}
                      size='medium'
                      id='vendor'
                      label="Qty" 
                      placeholder='1'
                      defaultValue={1}
                      type='text'
                      fullWidth
                      value={this.state.qty}
                      onChange={(e) =>
                          this.setState({
                              qty:e.target.value
                          })
                      }
                    />
                    <Box
                  
                      sx={{ 
                        position: 'absolute',
                        display:'flex',
                        flexDirection:'column',
                        // height:'100%',
                        width:'15%',
                        right: 0,
                        //  top: 0 ,
                        boxShadow:'none',
                        alignItems:'center',
                        justifyContent:'space-between'
                      }}
                    >
                      <IconButton 
                        disabled={(!this.props.auth.user.is_admin) && (this.state.statusSelectedOption=="incoming")}
                        sx={{
                          padding:0
                        }}
                        onClick={()=>{
                          this.setState({
                            qty:this.state.qty+1
                          })
                        }}
                      >
                        <ArrowDropUpIcon />
                      </IconButton>
                      <IconButton
                        disabled={(!this.props.auth.user.is_admin) && (this.state.statusSelectedOption=="incoming")}
                        sx={{
                          padding:0
                        }}
                        onClick={()=>{
                          if(this.state.qty>1){
                            this.setState({
                              qty:this.state.qty-1
                            })
                          }
                        }}
                      >
                        <ArrowDropDownIcon/>
                      </IconButton>
                    </Box>
                  </Box>
                  <Box
                    sx={{
                      display:'flex',
                      flexDirection:'row',
                      marginLeft:2,
                      justifyContent:'space-evenly',
                      width:'50%',
                      alignItems:'center'
                    }}
                  >
                    <FormGroup
                      sx={{
                        display:'flex',
                        flexDirection:'row',
                        justifyContent:'space-between',
                        width:'100%'
                      }}
                      >
                        <FormControlLabel 
                          disabled={(!this.props.auth.user.is_admin) && (this.state.statusSelectedOption=="incoming")}
                          control={
                            <Checkbox                           
                              disabled={(!this.props.auth.user.is_admin) && (this.state.statusSelectedOption=="incoming")}
                            />
                          }
                          checked={this.state.checkSpecific}
                          label="Specific" 
                          onClick={()=>{
                            if (!((!this.props.auth.user.is_admin) && (this.state.statusSelectedOption=="incoming"))){
                              this.setState({
                                checkSpecific:!this.state.checkSpecific
                              })
                          }
                          }}
                        />
                        <FormControlLabel v
                          disabled={(!this.props.auth.user.is_admin) && (this.state.statusSelectedOption=="incoming")}
                          control={
                            <Checkbox
                              disabled={(!this.props.auth.user.is_admin) && (this.state.statusSelectedOption=="incoming")}
                             />
                            }
                          checked={this.state.checkUrgent}  
                          label="Urgent"  
                          onClick={()=>{
                            if (!((!this.props.auth.user.is_admin) && (this.state.statusSelectedOption=="incoming"))){
                              this.setState({
                                checkUrgent:!this.state.checkUrgent
                              })
                          }
                          }}
                        />
                      {/* </FormGroup>
                      <FormGroup
                        sx={{
                          display:'flex',
                          flexDirection:'column',
                          justifyContent:'space-between',
                          width:'100%'
                        }}
                      > */}
                        <FormControlLabel 
                          control={<Checkbox />}  
                          checked={this.state.checkReceived}
                          label="Received"  
                          onChange={()=>{
                              this.setState({
                                checkReceived:!this.state.checkReceived
                              })
                            }
                          }
                          onClick={()=>{
                            this.setState({
                              checkReceived:!this.state.checkReceived
                            })}}
                        />
                        {/* <FormControlLabel 
                          control={<Checkbox />} 
                          checked={this.state.checkMyProduct} 
                          label="Favorite"  
                          onClick={()=>{
                            this.setState({
                              checkMyProduct:!this.state.checkMyProduct
                            })}}
                        /> */}
                    </FormGroup>
                  </Box>
                </Box>
                <Box
                  sx={{ 
                    display: 'flex',
                    position: 'relative',
                    width:'100%',
                    color:this.props.theme.fontColor,

                    }}
                >
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DatePicker
                        disabled={!this.state.checkUrgent}
                        sx={{
                          width:'100%'
                        }}
                        format=' YYYY / MM / DD'
                        label="Timeline"
                        defaultValue={dayjs(this.state.timeline?this.state.timeline:formatDate('Amm-dd-yyyy'))}
                        // value={dayjs(this.state.item.timeline)}
                        onChange={(newValue) => this.setState({
                          timeline:newValue})
                      }/>
                  </LocalizationProvider>
                </Box>
               {this.props.permission?(
                    <Box>
                      {console.log(this.state.statusSelectedOption)}
                      <FormControl
                        size='small'
                        fullWidth
                        sx={{
                          borderColor:this.props.theme.fontColor,
                          backgroundColor:this.props.theme.background,
                          color:this.props.theme.fontColor,
                          // borderColor:red[600]
                        }}
                      >
                        <InputLabel 
                            id="demo-simple-select-label"
                            sx={{
                                backgroundColor:this.props.theme.background,
                                color:this.props.theme.fontColor,
                                // width:'80%',
                                // borderRight:'1px solid '
                            }}
                        >Status</InputLabel>
                        <Select
                          value={this.state.statusSelectedOption}
                          label="Status"
                          onChange={(e)=>{
                              if(this.state.checkReceived){
                                if(status[e.target.value]==3){

                                }else if(status[e.target.value]==5){
                                  this.setState({
                                    statusDeliveredQty:true,
                                    statusChoice:true,
                                    // statusChoiceColor:green[600],
                                    statusIndex:this.props.dashboard.orderStatus[e.target.value],
                                    statusSelectedOption:e.target.value
                                  })
                                }else{
                                  this.setState({
                                    statusChoice:true,
                                    // statusChoiceColor:green[600],
                                    statusIndex:this.props.dashboard.orderStatus[e.target.value],
                                    statusSelectedOption:e.target.value
                                  })
                                }
                              }else{
                                if(status[e.target.value]==5 || status[e.target.value]==6){
                                  this.setState({
                                    error:'CheckBox Received is not Selected',
                                    // statusChoiceColor:red[600],
                                    statusChoice:false,
                                    statusIndex:this.props.dashboard.orderStatus[e.target.value],
                                    statusSelectedOption:e.target.value
                                  })
                                }else{
                                  this.setState({
                                    statusChoice:true,
                                    // statusChoiceColor:green[600],
                                    statusIndex:this.props.dashboard.orderStatus[e.target.value],
                                    statusSelectedOption:e.target.value
                                  })
                                }
                              }
                            }
                          }
                          // components={CustomOption}
                          sx={{
                              display:'flex',
                              flexDirection:'row',
                              backgroundColor:this.props.theme.background,
                              color:this.props.theme.fontColor,
                              '& .MuiSelect-select':{
                                  display:'flex',
                                  backgroundColor:this.props.theme.background,
                                  color:this.props.theme.fontColor
                              },
                          }}
                          style={{display:'flex',flexDirection:'row',alignItems:'center'}}
                      >
                        {
                          Object.keys(this.props.dashboard.orderStatus).map((item,index)=>{
                            return(
                                <MenuItem 
                                  key={index}
                                  value={item}>
                                    <Typography>
                                      {item}
                                    </Typography>
                                </MenuItem>
                            )
                          })
                          }
                        </Select>
                      </FormControl>
                    </Box>
                  ):(
                    <Box
                      sx={{
                        display:'flex',
                        flexDirection:'row',
                        alignContent:'center',
                        alignItems:'center'
                      }}
                    >
                    <FormControl
                      size='small'
                      fullWidth
                      sx={{
                        borderColor:this.props.theme.fontColor,
                        backgroundColor:this.props.theme.background,
                        color:this.props.theme.fontColor,
                        // border:`1px solid ${this.state.statusChoiceColor}`

                      }}
                    >
                      <InputLabel 
                          id="demo-simple-select-label"
                          sx={{
                              backgroundColor:this.props.theme.background,
                              color:this.props.theme.fontColor,
                              // width:'80%',
                              // borderRight:`1px solid`
                          }}
                      >Status</InputLabel>
                      <Select
                        value={this.state.statusSelectedOption}
                        label="Status"
                        disabled={!this.state.checkReceived}
                        onChange={(e)=>{
                          if(this.state.checkReceived&&status[e.target.value]==3){
                            // this.setState({
                              // statusChoiceColor:red[600],
                            // })
                          }else{
                            if(this.state.checkReceived&&status[e.target.value]==5){
                              this.setState({
                                statusDeliveredQty:true,
                              })
                            }
                            this.setState({
                              statusChoice:true,
                              // statusChoiceColor:green[600],
                              statusIndex:status[e.target.value],
                              statusSelectedOption:e.target.value
                            })
                          }
                          }
                        }
                        // components={CustomOption}
                        sx={{
                            display:'flex',
                            flexDirection:'row',
                            backgroundColor:this.props.theme.background,
                            color:this.props.theme.fontColor,
                            '& .MuiSelect-select':{
                                display:'flex',
                                backgroundColor:this.props.theme.background,
                                color:this.props.theme.fontColor
                            },
                        }}
                        style={{display:'flex',flexDirection:'row',alignItems:'center'}}
                    >
                      {Object.keys(status).map((item,key)=>{
                          return (
                            <MenuItem 
                              key={key}
                              value={item}
                            >
                              <Typography>
                                {item}
                              </Typography>
                          </MenuItem>
                          )
                        })
                      }
                      </Select>
                    </FormControl>
                    {this.state.checkReceived&&(
                      (this.state.checkReceived && this.state.statusChoice)?(
                          <DoneIcon color="success"/>
                      ):(
                        <Cancel color="error"/>
                      )
                      )
                    }
                  </Box>
                  )
                }
                 {this.state.statusDeliveredQty && (
                    <DeliveredQty
                      // isLoadingOrders={this.state.isLoadingOrders}
                      onClose={(closeStatus)=>{
                        this.setState({
                          statusDeliveredQty:false
                        })
                      }}
                      DeliveredQty={(qty_delivered)=>{this.setState({
                        qty_delivered:parseInt(qty_delivered),
                        qty_pending:this.state.qty-parseInt(qty_delivered),
                        statusDeliveredQty:false
                      })}}
                    />
                  )}
                <Box
                  sx={{
                    display:'flex',
                    alignItems:'center'
                  }}
                >
                  {this.state.submitEdit ? (
                    <>
                      <Button 
                       // color={'primary'}
                       sx={{
                           backgroundColor:purple[400],
                           marginRight:1
                           // marginTop:1
                       }}
                       startDecorator={
                           <CircularProgress variant="solid"/>
                       }
                   >
                       Loading…
                   </Button>
                    </>
                  ) : (
                    <ColorButton 
                        variant="contained"
                        sx={{
                          marginRight:1
                        }}
                        onClick={() =>{
                          if(this.state.checkReceived&&this.state.statusIndex==3){
                            this.setState({
                              error:'Status is not Selected',
                              statusChoice:false,

                              // statusChoiceColor:red[600]
                            })
                          }else{
                            this.handleSubmit()
                          }
                        }}   
                    >
                        Submit
                    </ColorButton>
                  )}
                  <ColorButton 
                        variant="contained"
                        onClick={() => {
                          this.props.onClose()
                            // setStatus(false);
                            // onClose();
                        }}
                        // onClick={() =>this.handleSignIn()}   
                    >
                        Close
                    </ColorButton>
                </Box>
              </Box>       
            </Box>
          </Card>
        </Modal>
      </>
    )
  }
}
export default compose(
  themeHook,
  locationHook,
  dashboardHook,
  authHook
)(OrderEdit)
