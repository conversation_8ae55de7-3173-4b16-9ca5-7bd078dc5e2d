// RichTextInput.jsx
import React, { useEffect, useState } from 'react';
import ReactQuill from 'react-quill';
import DOMPurify from 'dompurify';
import 'react-quill/dist/quill.snow.css';
import { Box, Typography, InputLabel } from '@mui/material';

const RichTextInput = ({ label, value, onChange }) => {
  const [editorValue, setEditorValue] = useState(value || '');

  useEffect(() => {
    setEditorValue(value || '');
  }, [value]);

  const handleChange = (val) => {
    setEditorValue(val);
    if (onChange) {
      onChange(DOMPurify.sanitize(val));
    }
  };

  return (
    <Box sx={{}}>
      {label && <InputLabel sx={{  border: '1px solid #ccc' }}>{label}</InputLabel>}
      <ReactQuill
        value={editorValue}
        defaultValue={editorValue}
        onChange={handleChange}
        modules={{
          toolbar: [
            [{ 'header': [1, 2, false] }],
            ['bold', 'italic', 'underline'],
            ['link'],
            [{ 'list': 'ordered' }, { 'list': 'bullet' }],
            ['clean'],
          ]
        }}
      />
    </Box>
  );
};

export default RichTextInput;
