import React, { useState, useEffect, useRef } from 'react';
import { 
  Box, 
  Card, 
  TextField, 
  IconButton, 
  Typography, 
  Avatar, 
  Divider,
  List,
  ListItem,
  Paper,
  CircularProgress
} from '@mui/material';
import { TbSend, TbX } from 'react-icons/tb';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { purple, grey } from '@mui/material/colors';
import { chatService } from '../../services/chatService';
import { useChat } from '../../context/ChatContext';
import CONFIG from '../../config';

const ChatBox = ({ onClose, recipient }) => {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [conversation, setConversation] = useState(null);
  const messagesEndRef = useRef(null);
  const auth = useAuth();
  const theme = useTheme();
  const chat = useChat();
  const { socketStatus } = chat;
  
  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  // Initialize conversation and load messages
  useEffect(() => {
    const initializeChat = async () => {
      // If user is not authenticated, show a message and return
      if (!auth.user || !auth.user.id) {
        setLoading(false);
        setMessages([{
          id: 1,
          sender: 'other',
          text: 'Please sign in to use the chat feature.',
          timestamp: new Date()
        }]);
        return;
      }
      
      try {
        setLoading(true);
        console.log('Using API URL:', CONFIG.API_URL);
        
        // If recipient is provided, create or get existing conversation
        if (recipient) {
          try {
            // Use fetch with CONFIG.API_URL
            const response = await fetch(`${CONFIG.API_URL}/chat/conversations`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${auth.token}`
              },
              body: JSON.stringify({ user_id: recipient.id })
            });
            
            if (!response.ok) {
              console.error(`Failed to create conversation: ${response.status}`);
              const responseText = await response.text();
              console.error('Response text:', responseText);
              throw new Error(`Failed to create conversation: ${response.status}`);
            }
            
            const responseText = await response.text();
            console.log('Raw response:', responseText);
            
            // Only try to parse if there's actual content
            let conversationData;
            if (responseText.trim()) {
              try {
                conversationData = JSON.parse(responseText);
              } catch (parseError) {
                console.error('JSON parse error:', parseError);
                console.error('Response that failed to parse:', responseText);
                throw new Error('Invalid JSON response from server');
              }
            } else {
              console.error('Empty response from server');
              throw new Error('Empty response from server');
            }
            
            console.log("Conversation created/retrieved:", conversationData);
            setConversation(conversationData);
            
            // Load messages for this conversation
            const messagesResponse = await fetch(`${CONFIG.API_URL}/chat/conversations/${conversationData.id}/messages`, {
              headers: {
                'Authorization': `Bearer ${auth.token}`
              }
            });
            
            if (!messagesResponse.ok) {
              throw new Error(`Failed to fetch messages: ${messagesResponse.status}`);
            }
            
            const messagesText = await messagesResponse.text();
            let messagesData;
            
            try {
              messagesData = JSON.parse(messagesText);
            } catch (parseError) {
              console.error('Failed to parse messages JSON:', messagesText);
              throw parseError;
            }
            
            console.log("Messages loaded:", messagesData);
            setMessages(messagesData.map(msg => ({
              id: msg.id,
              sender: msg.sender_id === auth.user.id ? 'user' : 'other',
              text: msg.content,
              timestamp: new Date(msg.created_at)
            })));
          } catch (error) {
            console.error('Error with recipient conversation:', error);
            // Create a fallback conversation
            setConversation({
              id: 'temp-' + Date.now(),
              participants: []
            });
            setMessages([{
              id: Date.now(),
              sender: 'other',
              text: 'There was an error loading your conversation. Please try again later.',
              timestamp: new Date()
            }]);
          }
        } else {
          // If no recipient, get the most recent conversation
          try {
            const conversationsResponse = await fetch(`${CONFIG.API_URL}/chat/conversations`, {
              headers: {
                'Authorization': `Bearer ${auth.token}`
              }
            });
            
            if (!conversationsResponse.ok) {
              console.error(`Failed to fetch conversations: ${conversationsResponse.status}`);
              const responseText = await conversationsResponse.text();
              console.error('Response text:', responseText);
              throw new Error(`Failed to fetch conversations: ${conversationsResponse.status}`);
            }
            
            const conversationsText = await conversationsResponse.text();
            console.log('Raw conversations response:', conversationsText);
            
            let conversations;
            try {
              conversations = JSON.parse(conversationsText);
            } catch (parseError) {
              console.error('Failed to parse conversations JSON:', conversationsText);
              throw parseError;
            }
            
            console.log("Conversations loaded:", conversations);
            if (conversations && conversations.length > 0) {
              // Sort by updated_at and get the most recent
              const sortedConversations = conversations.sort((a, b) => 
                new Date(b.updated_at) - new Date(a.updated_at)
              );
              
              console.log("Selected conversation:", sortedConversations[0]);
              setConversation(sortedConversations[0]);
              
              // Load messages for this conversation
              const messagesResponse = await fetch(`${CONFIG.API_URL}/chat/conversations/${sortedConversations[0].id}/messages`, {
                headers: {
                  'Authorization': `Bearer ${auth.token}`
                }
              });
              
              if (!messagesResponse.ok) {
                throw new Error(`Failed to fetch messages: ${messagesResponse.status}`);
              }
              
              const messagesText = await messagesResponse.text();
              let messagesData;
              
              try {
                messagesData = JSON.parse(messagesText);
              } catch (parseError) {
                console.error('Failed to parse messages JSON:', messagesText);
                throw parseError;
              }
              
              console.log("Messages loaded:", messagesData);
              setMessages(messagesData.map(msg => ({
                id: msg.id,
                sender: msg.sender_id === auth.user.id ? 'user' : 'other',
                text: msg.content,
                timestamp: new Date(msg.created_at)
              })));
            } else {
              // No conversations yet, show empty state
              console.log("No conversations found");
              setMessages([]);
              // Create a default conversation with support
              try {
                // Find a support user (first admin)
                const adminsResponse = await fetch(`${CONFIG.API_URL}/chat/users/admins`, {
                  headers: {
                    'Authorization': `Bearer ${auth.token}`
                  }
                });
                
                if (!adminsResponse.ok) {
                  throw new Error(`Failed to fetch admins: ${adminsResponse.status}`);
                }
                
                const adminsText = await adminsResponse.text();
                let admins;
                
                try {
                  admins = JSON.parse(adminsText);
                } catch (parseError) {
                  console.error('Failed to parse admins JSON:', adminsText);
                  throw parseError;
                }
                
                if (admins && admins.length > 0) {
                  // Create a conversation with the first admin
                  const newConvResponse = await fetch(`${CONFIG.API_URL}/chat/conversations`, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                      'Authorization': `Bearer ${auth.token}`
                    },
                    body: JSON.stringify({ user_id: admins[0].id })
                  });
                  
                  if (!newConvResponse.ok) {
                    throw new Error(`Failed to create new conversation: ${newConvResponse.status}`);
                  }
                  
                  const newConvText = await newConvResponse.text();
                  let newConversation;
                  
                  try {
                    newConversation = JSON.parse(newConvText);
                  } catch (parseError) {
                    console.error('Failed to parse new conversation JSON:', newConvText);
                    throw parseError;
                  }
                  
                  console.log("Created new conversation:", newConversation);
                  setConversation(newConversation);
                } else {
                  // No admins found, create a fallback conversation
                  setConversation({
                    id: 'temp-' + Date.now(),
                    participants: []
                  });
                }
              } catch (error) {
                console.error("Error creating default conversation:", error);
                // Create a fallback conversation
                setConversation({
                  id: 'temp-' + Date.now(),
                  participants: []
                });
              }
            }
          } catch (error) {
            console.error('Error loading conversations:', error);
            // Create a fallback conversation
            setConversation({
              id: 'temp-' + Date.now(),
              participants: []
            });
            setMessages([{
              id: Date.now(),
              sender: 'other',
              text: 'There was an error loading your conversations. Please try again later.',
              timestamp: new Date()
            }]);
          }
        }
      } catch (error) {
        console.error('Error initializing chat:', error);
        // Create a fallback conversation to allow typing
        setConversation({
          id: 'temp-' + Date.now(),
          participants: []
        });
        setMessages([{
          id: Date.now(),
          sender: 'other',
          text: 'Welcome to chat support! How can we help you today?',
          timestamp: new Date()
        }]);
      } finally {
        setLoading(false);
      }
    };
    
    initializeChat();
    
    // Add a message listener for this specific conversation
    const handleWebSocketMessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === 'chat_message' && data.message.conversation_id === conversation?.id) {
          // Add new message to the chat
          const newMsg = {
            id: data.message.id,
            sender: data.message.sender_id === auth.user.id ? 'user' : 'other',
            text: data.message.content,
            timestamp: new Date(data.message.created_at)
          };
          setMessages(prev => [...prev, newMsg]);
          
          // Scroll to bottom when new message arrives
          scrollToBottom();
        }
      } catch (error) {
        console.error('Error handling WebSocket message:', error);
      }
    };
    
    // Get the socket reference from the chat context
    const socket = chat.socketRef?.current;
    
    // Add event listener to the WebSocket
    if (socket) {
      socket.addEventListener('message', handleWebSocketMessage);
    }
    
    // Clean up event listener
    return () => {
      if (socket) {
        socket.removeEventListener('message', handleWebSocketMessage);
      }
    };
  }, [recipient, auth.user, auth.token, chat.socketRef, conversation?.id]);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  const handleSendMessage = async () => {
    // Check if user is authenticated
    if (!auth.user || !auth.user.id) {
      // Show a message that user needs to sign in
      setMessages(prev => [...prev, {
        id: Date.now(),
        sender: 'other',
        text: 'Please sign in to send messages.',
        timestamp: new Date()
      }]);
      return;
    }
    
    if (message.trim() === '' || !conversation) return;
    
    // Add message to UI immediately for better UX
    const newMessage = {
      id: Date.now(), // Temporary ID
      sender: 'user',
      text: message,
      timestamp: new Date()
    };
    
    setMessages([...messages, newMessage]);
    const messageText = message;
    setMessage('');
    
    // Check if we're using a temporary conversation
    if (conversation.id && conversation.id.toString().startsWith('temp-')) {
      // For temporary conversations, just show the message locally
      console.log('Using temporary conversation, not sending to server');
      
      // Add a mock response after a short delay
      setTimeout(() => {
        setMessages(prev => [...prev, {
          id: Date.now(),
          sender: 'other',
          text: 'Our chat system is currently experiencing issues. Please try again later or contact support via email.',
          timestamp: new Date()
        }]);
      }, 1000);
      
      return;
    }
    
    try {
      console.log('Sending message to:', `${CONFIG.API_URL}/chat/conversations/${conversation.id}/messages`);
      // Send message to API using fetch with CONFIG.API_URL
      const response = await fetch(`${CONFIG.API_URL}/chat/conversations/${conversation.id}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify({ content: messageText })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.status}`);
      }
      
      const responseText = await response.text();
      let sentMessage;
      
      try {
        sentMessage = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Failed to parse sent message response:', responseText);
        throw parseError;
      }
      
      // Update the message with the correct ID from the server
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? {
                id: sentMessage.id,
                sender: 'user',
                text: sentMessage.content,
                timestamp: new Date(sentMessage.created_at)
              }
            : msg
        )
      );
    } catch (error) {
      console.error('Error sending message:', error);
      // Show error state or retry option
      setMessages(prev => [...prev, {
        id: Date.now(),
        sender: 'other',
        text: 'Failed to send your message. Please try again.',
        timestamp: new Date()
      }]);
    }
  };
  
  const formatTime = (date) => {
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  const getRecipientName = () => {
    if (recipient) {
      return recipient.name;
    } else if (conversation) {
      // Find the other participant in the conversation
      const otherParticipant = conversation.participants.find(
        p => p.user_id !== auth.user.id
      );
      return otherParticipant ? otherParticipant.username : 'Support';
    }
    return 'Support Chat';
  };
  
  console.log("Chat input state:", {
    loading,
    hasConversation: !!conversation,
    isDisabled: loading || !conversation,
    auth: !!auth.user
  });

  return (
    <Card
      sx={{
        position: 'fixed',
        bottom: 20,
        right: 20,
        width: 350,
        height: 450,
        display: 'flex',
        flexDirection: 'column',
        boxShadow: 3,
        zIndex: 1000,
        backgroundColor: theme.selectTheme.background,
        '& .chat-status': {
          padding: '2px 8px',
          fontSize: '0.75rem',
          textAlign: 'center',
          backgroundColor: theme.selectTheme.fontColor === '#fff' ? '#333' : '#f5f5f5',
          borderBottom: `1px solid ${theme.selectTheme.fontColor === '#fff' ? '#444' : '#ddd'}`,
        },
        '& .status-connected': {
          color: '#4caf50',
        },
        '& .status-connecting': {
          color: '#ff9800',
        },
        '& .status-disconnected': {
          color: '#f44336',
        }
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: 2,
          backgroundColor: purple[400],
          color: 'white',
        }}
      >
        <Typography variant="h6">
          {`Chat with ${getRecipientName()}`}
        </Typography>
        <IconButton onClick={onClose} sx={{ color: 'white' }}>
          <TbX />
        </IconButton>
      </Box>
      
      {/* WebSocket Status */}
      <div className="chat-status">
        {socketStatus === 'connected' ? (
          <span className="status-connected">Connected</span>
        ) : socketStatus === 'connecting' ? (
          <span className="status-connecting">Connecting...</span>
        ) : (
          <span className="status-disconnected">Disconnected</span>
        )}
      </div>
      
      {/* Messages */}
      <Box
        sx={{
          flexGrow: 1,
          overflow: 'auto',
          padding: 2,
          backgroundColor: theme.selectTheme.background,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: loading ? 'center' : 'flex-start',
          alignItems: loading ? 'center' : 'stretch',
        }}
      >
        {loading ? (
          <CircularProgress size={40} sx={{ color: purple[400] }} />
        ) : messages.length === 0 ? (
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center', 
            justifyContent: 'center',
            height: '100%',
            color: grey[500]
          }}>
            <Typography variant="body1">
              No messages yet. Start a conversation!
            </Typography>
          </Box>
        ) : (
          <List sx={{ width: '100%', padding: 0 }}>
            {messages.map((msg) => (
              <ListItem
                key={msg.id}
                sx={{
                  display: 'flex',
                  justifyContent: msg.sender === 'user' ? 'flex-end' : 'flex-start',
                  padding: 0.5,
                }}
              >
                <Paper
                  elevation={1}
                  sx={{
                    padding: 1,
                    maxWidth: '70%',
                    backgroundColor: msg.sender === 'user' ? purple[100] : theme.selectTheme.sidebarSelected,
                    borderRadius: 2,
                  }}
                >
                  <Typography variant="body2">{msg.text}</Typography>
                  <Typography variant="caption" sx={{ display: 'block', textAlign: 'right' }}>
                    {formatTime(msg.timestamp)}
                  </Typography>
                </Paper>
              </ListItem>
            ))}
            <div ref={messagesEndRef} />
          </List>
        )}
      </Box>
      
      {/* Input */}
      <Box
        sx={{
          display: 'flex',
          padding: 1,
          borderTop: `1px solid ${theme.selectTheme.fontColor === '#fff' ? '#444' : '#ddd'}`,
        }}
      >
        {!auth.user ? (
          <Box sx={{ p: 1, textAlign: 'center', color: 'error.main' }}>
            <Typography variant="caption">Please sign in to send messages</Typography>
          </Box>
        ) : !conversation ? (
          <Box sx={{ p: 1, textAlign: 'center', color: 'info.main' }}>
            <Typography variant="caption">Initializing chat...</Typography>
          </Box>
        ) : null}
        <TextField
          fullWidth
          size="small"
          placeholder="Type a message..."
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
          disabled={loading || !conversation}
          sx={{
            backgroundColor: theme.selectTheme.background,
          }}
        />
        <IconButton 
          onClick={handleSendMessage}
          disabled={loading || !conversation}
          sx={{ color: purple[400] }}
        >
          <TbSend />
        </IconButton>
      </Box>
    </Card>
  );
};

export default ChatBox;
