.websocket-tester {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.websocket-tester h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
}

.connection-controls {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 5px;
}

.url-input {
  margin-bottom: 10px;
}

.url-input input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-family: monospace;
}

.connection-status {
  margin-bottom: 10px;
  font-weight: bold;
}

.status-connected {
  color: #2e7d32;
}

.status-connecting {
  color: #f57c00;
}

.status-disconnected {
  color: #757575;
}

.status-error {
  color: #d32f2f;
}

.connection-buttons {
  display: flex;
  gap: 10px;
}

.connection-buttons button {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  background-color: #1976d2;
  color: white;
  cursor: pointer;
}

.connection-buttons button:disabled {
  background-color: #bbdefb;
  cursor: not-allowed;
}

.message-controls {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  align-items: center;
}

.message-input {
  flex: 1;
  display: flex;
}

.message-input input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px 0 0 4px;
}

.message-input button {
  padding: 8px 12px;
  border: none;
  border-radius: 0 4px 4px 0;
  background-color: #1976d2;
  color: white;
  cursor: pointer;
}

.message-input button:disabled {
  background-color: #bbdefb;
  cursor: not-allowed;
}

.message-controls > button {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  background-color: #388e3c;
  color: white;
  cursor: pointer;
}

.message-controls > button:disabled {
  background-color: #c8e6c9;
  cursor: not-allowed;
}

.message-log {
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  overflow: hidden;
}

.message-log h3 {
  margin: 0;
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.messages {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;
}

.no-messages {
  padding: 20px;
  text-align: center;
  color: #757575;
}

.message {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 4px;
  border-left: 4px solid transparent;
}

.message.system {
  background-color: #e3f2fd;
  border-left-color: #2196f3;
}

.message.sent {
  background-color: #e8f5e9;
  border-left-color: #4caf50;
}

.message.received {
  background-color: #fff8e1;
  border-left-color: #ffc107;
}

.message.error {
  background-color: #ffebee;
  border-left-color: #f44336;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 0.85rem;
}

.message-type {
  font-weight: bold;
  text-transform: uppercase;
}

.message-time {
  color: #757575;
}

.message-content {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: monospace;
  font-size: 0.9rem;
}