import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  TextField, 
  Button, 
  List, 
  ListItem, 
  ListItemText, 
  Divider,
  IconButton,
  Avatar
} from '@mui/material';
import { TbSend } from 'react-icons/tb';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { purple, grey } from '@mui/material/colors';
import { useChat } from '../../context/ChatContext';

const UserChatPage = () => {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const auth = useAuth();
  const theme = useTheme();
  const chat = useChat();
  
  useEffect(() => {
    // Load messages from API
    // Replace with actual API call
    const mockMessages = [
      { id: 1, sender: 'admin', senderName: 'Support Agent', text: 'Hello, how can I help you today?', timestamp: new Date(Date.now() - 3600000) },
      { id: 2, sender: 'user', senderName: 'You', text: 'I have a question about my recent order', timestamp: new Date(Date.now() - 3500000) },
      { id: 3, sender: 'admin', senderName: 'Support Agent', text: 'Sure, I\'d be happy to help. Can you provide your order number?', timestamp: new Date(Date.now() - 3400000) },
      { id: 4, sender: 'user', senderName: 'You', text: 'It\'s AMP-12345', timestamp: new Date(Date.now() - 3300000) },
    ];
    setMessages(mockMessages);
  }, []);
  
  const handleSendMessage = () => {
    if (message.trim() === '') return;
    
    const newMessage = {
      id: messages.length + 1,
      sender: 'user',
      senderName: 'You',
      text: message,
      timestamp: new Date()
    };
    
    setMessages([...messages, newMessage]);
    setMessage('');
    
    // Here you would send the message to your backend
    // Example: chat.sendMessage(conversationId, message);
  };
  
  const formatTime = (date) => {
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  const formatDate = (date) => {
    const today = new Date();
    const messageDate = new Date(date);
    
    if (messageDate.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (messageDate.toDateString() === new Date(today.setDate(today.getDate() - 1)).toDateString()) {
      return 'Yesterday';
    } else {
      return messageDate.toLocaleDateString();
    }
  };
  
  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 0, height: 'calc(100vh - 200px)', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box sx={{ 
          p: 2, 
          backgroundColor: purple[500], 
          color: 'white',
          display: 'flex',
          alignItems: 'center'
        }}>
          <Avatar sx={{ bgcolor: 'white', color: purple[500], mr: 2 }}>S</Avatar>
          <Typography variant="h6">Support Chat</Typography>
        </Box>
        
        {/* Messages */}
        <Box sx={{ 
          flexGrow: 1, 
          overflow: 'auto', 
          p: 2,
          backgroundColor: theme.selectTheme.background
        }}>
          {messages.map((msg, index) => {
            // Check if we need to show date separator
            const showDateSeparator = index === 0 || 
              formatDate(msg.timestamp) !== formatDate(messages[index - 1].timestamp);
            
            return (
              <React.Fragment key={msg.id}>
                {showDateSeparator && (
                  <Box sx={{ 
                    textAlign: 'center', 
                    my: 2,
                    position: 'relative'
                  }}>
                    <Divider sx={{ position: 'absolute', top: '50%', left: 0, right: 0 }} />
                    <Typography 
                      variant="caption" 
                      sx={{ 
                        backgroundColor: theme.selectTheme.background, 
                        px: 2, 
                        position: 'relative',
                        color: grey[600]
                      }}
                    >
                      {formatDate(msg.timestamp)}
                    </Typography>
                  </Box>
                )}
                
                <Box 
                  sx={{
                    display: 'flex',
                    justifyContent: msg.sender === 'user' ? 'flex-end' : 'flex-start',
                    mb: 2
                  }}
                >
                  <Paper
                    elevation={1}
                    sx={{
                      p: 1.5,
                      maxWidth: '70%',
                      backgroundColor: msg.sender === 'user' ? purple[100] : theme.selectTheme.sidebarSelected,
                      borderRadius: 2
                    }}
                  >
                    <Typography variant="subtitle2" color="text.secondary">
                      {msg.senderName}
                    </Typography>
                    <Typography variant="body1">{msg.text}</Typography>
                    <Typography variant="caption" sx={{ display: 'block', textAlign: 'right', mt: 0.5 }}>
                      {formatTime(msg.timestamp)}
                    </Typography>
                  </Paper>
                </Box>
              </React.Fragment>
            );
          })}
        </Box>
        
        {/* Input */}
        <Box sx={{ 
          p: 2, 
          borderTop: `1px solid ${grey[300]}`,
          backgroundColor: theme.selectTheme.background,
          display: 'flex' 
        }}>
          <TextField
            fullWidth
            variant="outlined"
            size="small"
            placeholder="Type your message here..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
          />
          <IconButton 
            color="primary" 
            onClick={handleSendMessage}
            sx={{ ml: 1, color: purple[500] }}
          >
            <TbSend />
          </IconButton>
        </Box>
      </Paper>
    </Container>
  );
};

export default UserChatPage;
