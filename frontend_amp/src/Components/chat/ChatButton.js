import React, { useState } from 'react';
import { Fab, Badge } from '@mui/material';
import { purple } from '@mui/material/colors';
import ChatIcon from '@mui/icons-material/Chat';
import ChatBox from './ChatBox';
import { useChat } from '../../context/ChatContext';
import { useAuth } from '../../context/AuthContext';

const ChatButton = () => {
  const [chatOpen, setChatOpen] = useState(false);
  const chat = useChat();
  const auth = useAuth();
  
  // Don't render the button if user is not authenticated
  if (!auth.user || !auth.user.id) {
    return null;
  }
  
  const toggleChat = () => {
    setChatOpen(!chatOpen);
    if (!chatOpen) {
      // When opening chat, refresh conversations
      chat.refreshConversations();
    }
  };
  
  return (
    <>
      <Fab 
        color="primary" 
        aria-label="chat"
        onClick={toggleChat}
        sx={{ 
          position: 'fixed', 
          bottom: 20, 
          right: 20,
          backgroundColor: purple[400],
          '&:hover': {
            backgroundColor: purple[600],
          }
        }}
      >
        <Badge badgeContent={chat.getTotalUnreadCount()} color="error">
          <ChatIcon />
        </Badge>
      </Fab>
      
      {chatOpen && <ChatBox onClose={() => setChatOpen(false)} />}
    </>
  );
};

export default ChatButton;
