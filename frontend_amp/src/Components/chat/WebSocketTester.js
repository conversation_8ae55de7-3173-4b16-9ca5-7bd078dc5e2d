import React, { useState, useEffect, useRef } from 'react';
import { CONFIG } from '../../config';
import { useAuth } from '../../context/AuthContext';
import './WebSocketTester.css';

const WebSocketTester = () => {
  const [status, setStatus] = useState('disconnected');
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [wsUrl, setWsUrl] = useState('');
  const socketRef = useRef(null);
  const auth = useAuth();

  useEffect(() => {
    // Generate WebSocket URL based on current user
    if (auth.user && auth.user.id) {
      const url = `${CONFIG.wsUrl}/ws/chat/${auth.user.id}`;
      setWsUrl(url);
    }
  }, [auth.user]);

  const connect = () => {
    // Close existing connection if any
    if (socketRef.current) {
      socketRef.current.close();
    }

    try {
      setStatus('connecting');
      addMessage('system', `Connecting to ${wsUrl}...`);
      
      const ws = new WebSocket(wsUrl);
      socketRef.current = ws;
      
      ws.onopen = () => {
        setStatus('connected');
        addMessage('system', 'Connected successfully!');
      };
      
      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          addMessage('received', JSON.stringify(data, null, 2));
        } catch (error) {
          addMessage('received', event.data);
        }
      };
      
      ws.onerror = (error) => {
        setStatus('error');
        addMessage('error', `WebSocket error: ${error.message || 'Unknown error'}`);
      };
      
      ws.onclose = (event) => {
        setStatus('disconnected');
        addMessage('system', `Disconnected with code ${event.code}: ${event.reason || 'No reason provided'}`);
      };
    } catch (error) {
      setStatus('error');
      addMessage('error', `Failed to create WebSocket: ${error.message}`);
    }
  };

  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }
  };

  const sendMessage = () => {
    if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      addMessage('error', 'WebSocket is not connected');
      return;
    }
    
    try {
      const messageObj = { type: 'message', content: inputMessage };
      socketRef.current.send(JSON.stringify(messageObj));
      addMessage('sent', JSON.stringify(messageObj, null, 2));
      setInputMessage('');
    } catch (error) {
      addMessage('error', `Failed to send message: ${error.message}`);
    }
  };

  const addMessage = (type, text) => {
    setMessages(prev => [...prev, {
      id: Date.now(),
      type,
      text,
      timestamp: new Date()
    }]);
  };

  const sendPing = () => {
    if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      addMessage('error', 'WebSocket is not connected');
      return;
    }
    
    try {
      const pingObj = { type: 'ping', timestamp: Date.now() };
      socketRef.current.send(JSON.stringify(pingObj));
      addMessage('sent', JSON.stringify(pingObj, null, 2));
    } catch (error) {
      addMessage('error', `Failed to send ping: ${error.message}`);
    }
  };

  const clearMessages = () => {
    setMessages([]);
  };

  return (
    <div className="websocket-tester">
      <h2>WebSocket Tester</h2>
      
      <div className="connection-controls">
        <div className="url-input">
          <input 
            type="text" 
            value={wsUrl} 
            onChange={(e) => setWsUrl(e.target.value)}
            placeholder="WebSocket URL"
          />
        </div>
        
        <div className="connection-status">
          Status: <span className={`status-${status}`}>{status}</span>
        </div>
        
        <div className="connection-buttons">
          <button 
            onClick={connect} 
            disabled={status === 'connecting' || status === 'connected'}
          >
            Connect
          </button>
          <button 
            onClick={disconnect} 
            disabled={status !== 'connected'}
          >
            Disconnect
          </button>
          <button onClick={clearMessages}>
            Clear Messages
          </button>
        </div>
      </div>
      
      <div className="message-controls">
        <div className="message-input">
          <input 
            type="text" 
            value={inputMessage} 
            onChange={(e) => setInputMessage(e.target.value)}
            placeholder="Enter message to send"
            disabled={status !== 'connected'}
          />
          <button 
            onClick={sendMessage} 
            disabled={status !== 'connected' || !inputMessage.trim()}
          >
            Send
          </button>
        </div>
        <button 
          onClick={sendPing} 
          disabled={status !== 'connected'}
        >
          Send Ping
        </button>
      </div>
      
      <div className="message-log">
        <h3>Message Log</h3>
        <div className="messages">
          {messages.length === 0 ? (
            <div className="no-messages">No messages yet</div>
          ) : (
            messages.map(msg => (
              <div key={msg.id} className={`message ${msg.type}`}>
                <div className="message-header">
                  <span className="message-type">{msg.type}</span>
                  <span className="message-time">
                    {msg.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                <pre className="message-content">{msg.text}</pre>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default WebSocketTester;
