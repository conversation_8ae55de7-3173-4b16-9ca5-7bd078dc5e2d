import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemAvatar, 
  Avatar, 
  Divider, 
  Paper,
  TextField,
  IconButton,
  Badge
} from '@mui/material';
import { TbSend, TbUserCircle } from 'react-icons/tb';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { purple, grey } from '@mui/material/colors';
import { useChat } from '../../context/ChatContext';

const AdminChat = () => {
  const [message, setMessage] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [userMessages, setUserMessages] = useState([]);
  const [users, setUsers] = useState([]);
  const auth = useAuth();
  const theme = useTheme();
  const chat = useChat();
  
  // Load users with active conversations
  useEffect(() => {
    // Replace with actual API call
    const mockUsers = [
      { id: 1, name: '<PERSON>', unread: 2, lastMessage: 'I need help with my order', timestamp: new Date(Date.now() - 1000000) },
      { id: 2, name: 'Jane <PERSON>', unread: 0, lastMessage: 'Thanks for your help', timestamp: new Date(Date.now() - 5000000) },
      { id: 3, name: 'Bob Johnson', unread: 1, lastMessage: 'When will my order arrive?', timestamp: new Date(Date.now() - 10000000) },
    ];
    setUsers(mockUsers);
  }, []);
  
  // Load messages for selected user
  useEffect(() => {
    if (selectedUser) {
      // Replace with actual API call
      const mockMessages = [
        { id: 1, sender: 'user', text: 'Hello, I have a question about my order', timestamp: new Date(Date.now() - 3600000) },
        { id: 2, sender: 'admin', text: 'Hi there! I\'d be happy to help. What\'s your order number?', timestamp: new Date(Date.now() - 3500000) },
        { id: 3, sender: 'user', text: 'It\'s #12345', timestamp: new Date(Date.now() - 3400000) },
        { id: 4, sender: 'admin', text: 'Thank you. Let me check that for you.', timestamp: new Date(Date.now() - 3300000) },
      ];
      setUserMessages(mockMessages);
      
      // Mark messages as read
      setUsers(prevUsers => 
        prevUsers.map(user => 
          user.id === selectedUser.id ? { ...user, unread: 0 } : user
        )
      );
    }
  }, [selectedUser]);
  
  const handleSendMessage = () => {
    if (!message.trim() || !selectedUser) return;
    
    const newMessage = {
      id: userMessages.length + 1,
      sender: 'admin',
      text: message,
      timestamp: new Date()
    };
    
    setUserMessages([...userMessages, newMessage]);
    setMessage('');
    
    // Here you would send the message to your backend
    // Example: sendMessageToAPI(message, selectedUser.id);
  };
  
  const formatTime = (date) => {
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  const formatDate = (date) => {
    const today = new Date();
    const messageDate = new Date(date);
    
    if (messageDate.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (messageDate.toDateString() === new Date(today.setDate(today.getDate() - 1)).toDateString()) {
      return 'Yesterday';
    } else {
      return messageDate.toLocaleDateString();
    }
  };
  
  return (
    <Box sx={{ display: 'flex', height: 'calc(100vh - 150px)', backgroundColor: theme.selectTheme.background }}>
      {/* User list */}
      <Box sx={{ width: 300, borderRight: `1px solid ${grey[300]}`, overflow: 'auto' }}>
        <Typography variant="h6" sx={{ padding: 2 }}>Conversations</Typography>
        <Divider />
        <List>
          {users.map(user => (
            <React.Fragment key={user.id}>
              <ListItem 
                button 
                selected={selectedUser?.id === user.id}
                onClick={() => setSelectedUser(user)}
                sx={{ 
                  backgroundColor: selectedUser?.id === user.id ? theme.selectTheme.sidebarSelected : 'transparent',
                  '&:hover': { backgroundColor: theme.selectTheme.selectHover }
                }}
              >
                <ListItemAvatar>
                  <Badge badgeContent={user.unread} color="error">
                    <Avatar sx={{ bgcolor: purple[500] }}>
                      {user.name.charAt(0)}
                    </Avatar>
                  </Badge>
                </ListItemAvatar>
                <ListItemText 
                  primary={user.name} 
                  secondary={
                    <Typography noWrap variant="body2" color="text.secondary">
                      {user.lastMessage}
                    </Typography>
                  }
                />
                <Typography variant="caption" color="text.secondary">
                  {formatDate(user.timestamp)}
                </Typography>
              </ListItem>
              <Divider variant="inset" component="li" />
            </React.Fragment>
          ))}
        </List>
      </Box>
      
      {/* Chat area */}
      <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        {selectedUser ? (
          <>
            {/* Chat header */}
            <Box sx={{ 
              padding: 2, 
              borderBottom: `1px solid ${grey[300]}`,
              backgroundColor: theme.selectTheme.background,
              display: 'flex',
              alignItems: 'center'
            }}>
              <Avatar sx={{ bgcolor: purple[500], marginRight: 2 }}>
                {selectedUser.name.charAt(0)}
              </Avatar>
              <Typography variant="h6">{selectedUser.name}</Typography>
            </Box>
            
            {/* Messages */}
            <Box sx={{ flexGrow: 1, overflow: 'auto', padding: 2 }}>
              {userMessages.map(msg => (
                <Box 
                  key={msg.id}
                  sx={{
                    display: 'flex',
                    justifyContent: msg.sender === 'admin' ? 'flex-end' : 'flex-start',
                    mb: 2
                  }}
                >
                  <Paper
                    elevation={1}
                    sx={{
                      padding: 1.5,
                      maxWidth: '70%',
                      backgroundColor: msg.sender === 'admin' ? purple[100] : theme.selectTheme.sidebarSelected,
                      borderRadius: 2
                    }}
                  >
                    <Typography variant="body1">{msg.text}</Typography>
                    <Typography variant="caption" sx={{ display: 'block', textAlign: 'right', mt: 0.5 }}>
                      {formatTime(msg.timestamp)}
                    </Typography>
                  </Paper>
                </Box>
              ))}
            </Box>
            
            {/* Input */}
            <Box sx={{ 
              padding: 2, 
              borderTop: `1px solid ${grey[300]}`,
              display: 'flex' 
            }}>
              <TextField
                fullWidth
                size="small"
                placeholder="Type a message..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              />
              <IconButton 
                onClick={handleSendMessage}
                sx={{ ml: 1, color: purple[400] }}
              >
                <TbSend />
              </IconButton>
            </Box>
          </>
        ) : (
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            justifyContent: 'center', 
            alignItems: 'center',
            height: '100%',
            color: grey[500]
          }}>
            <TbUserCircle size={64} />
            <Typography variant="h6" sx={{ mt: 2 }}>
              Select a conversation to start chatting
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default AdminChat;
