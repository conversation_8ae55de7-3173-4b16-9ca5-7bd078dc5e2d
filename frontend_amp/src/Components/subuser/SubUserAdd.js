
import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { Box, Card, FormControl, FormControlLabel, FormGroup, IconButton, InputAdornment, InputLabel, ListItemButton, Modal, OutlinedInput, Radio, Select, TextField, Typography } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close';
import { ColorButton } from '../ColorButton';
import { green, purple, red } from '@mui/material/colors';
import { Done, Visibility, VisibilityOff } from '@mui/icons-material';
import Button from '@mui/joy/Button';
import { CircularProgress } from '@mui/joy';
import { compose } from 'redux';
import themeHook from '../../HOK/ThemeHook';
import dashboardHook from '../../HOK/DashboardHook';
import authHook from '../../HOK/AuthHook';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import Checkbox from '@mui/joy/Checkbox';
import List from '@mui/joy/List';
import ListItem from '@mui/joy/ListItem';
const services=[
  'Procurement_Hub',
  'Insurance_Verification',
  'Patient_Experience'
]
class SubUserAdd extends Component {
  constructor(props){
    super(props)
    this.state={
      first_name:'',
      last_name:'',
      email:'',
      phone_number:'',
      dental_name:'',
      dental_logo:'',
      avatar:'',
      total_budget:0,
      password:'',
      is_active:true,
      statusActive:true,
      statusInActive:false,
      showPassword:false,
      groups:[],
      submit:false,
    }
  }
  async handleSubmit(){
    this.setState({
      submit:true,
    })
    let userData={
      first_name:this.state.first_name,
      last_name:'',
      email:this.state.email,
      phone_number:this.state.phone_number,
      dental_name:'',
      // dental_logo:this.state.dental_logo,
      // avatar:this.state.avatar,
      groups:this.state.groups,
      total_budget:parseFloat(this.state.total_budget),
      password:this.state.password,
      is_active:this.state.is_active
    }
    if(this.props.auth.user.is_admin){
      userData={
        ...userData,
        ref_admin:this.props.refUser.id,
      }
    }
    let response=await this.props.auth.insertUser(this.props.auth.token,userData)
    if (response.status!=200) {
      this.props.setInfoAlert({
        msg: `Error inserting User: ${response.msg}`,
        status: false,
      })
      this.setState({
        submit:false,
        });
      this.props.showAlertNotes();
      this.props.refetchOrder(true)
    } else {
        if (response.msg == 'success') {
          this.props.setInfoAlert({
            msg: 'User inserted successfully',
            status: true,
          })
          this.props.refetchOrder(true)
          this.setState({
            submit:false,
          });
          this.props.showAlertNotes();
          this.props.onClose();
        } else {
          this.props.setInfoAlert({
            msg: `Failed to insert User: ${response.msg}`,
            status: false,
          })
          this.setState({
            submit:false,
          });
          this.props.showAlertNotes();
          this.props.refetchOrder(true)
        }
    }
  }
  handleChangeMultiple(event){
    const { options } = event.target;
    const value = [];
    for (let i = 0, l = options.length; i < l; i += 1) {
      if (options[i].selected) {
        value.push(options[i].value);
      }
    }
    this.setState({
      groups:value
    })
  }
  handleShowPassword(arg){
    this.setState({
        showPassword:arg,
    })
}
  render() {
    return (
      <>
        <Modal
        disablePortal
        disableEnforceFocus
        disableAutoFocus
        open
        aria-labelledby="server-modal-title"
        aria-describedby="server-modal-description"
        sx={{
          display: 'flex',
          p: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        // onClose={this.props.onClose}
        // container={() => rootRef.current}
      >
        <Card
          sx={{
              width:'40%',
              padding:4,
              textAlign:'center',
              padding:2,
              // border:'gray',
              // shadow:true
          }}
        >
           <Box
            sx={(theme) => ({
              position: 'relative',
              bgcolor: 'background.paper',
              // boxShadow: theme.shadows[5],
            })}
          >
            <Box
              sx={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  flexDirection:'column',
                  textAlign:'left',
                  gap:2,
              }}
            >
              <Box
                sx={{
                  display:'flex',
                  flexDirection:'row',
                  justifyContent:'space-between'
                }}
              >
                <Typography
                  // width={'90%'}
                >
                </Typography>
                <IconButton
                  onClick={() => {
                    this.props.onClose()
                  }}
                >
                  <CloseIcon  fontSize="small"/>
                </IconButton>
              </Box>
              <Box>
                <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                  Add SubUser
                </Typography>
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                  fullWidth
                  size='small'
                  id='first_name'
                  label="Name"
                  placeholder='Name'
                  type='text'
                  value={this.state.first_name}
                  onChange={(e) =>
                      this.setState({
                        first_name:e.target.value
                      })
                  }
                />
              </Box>
              {/* <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                  fullWidth
                  size='small'
                  id='last_name'
                  label="Last Name"
                  placeholder='Last Name'
                  type='text'
                  value={this.state.last_name}
                  onChange={(e) =>
                      this.setState({
                        last_name:e.target.value
                      })
                  }
                />
              </Box> */}
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                  required
                  fullWidth
                  size='small'
                  id='email'
                  label="Email"
                  placeholder='Email'
                  type='text'
                  value={this.state.email}
                  onChange={(e) =>
                      this.setState({
                        email:e.target.value
                      })
                  }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                  fullWidth
                  size='small'
                  id='phone_number'
                  label="Phone Number"
                  placeholder='Phone Number'
                  type='text'
                  value={this.state.phone_number}
                  onChange={(e) =>
                      this.setState({
                        phone_number:e.target.value
                      })
                  }
                />
              </Box>
              {/* <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                  required
                    fullWidth
                    size='small'
                    id='dental_name'
                    label="Dental Name"
                    placeholder='Dental Name'
                    type='text'
                    value={this.state.dental_name}
                    onChange={(e) =>
                        this.setState({
                          dental_name:e.target.value
                        })
                    }
                />
              </Box> */}
              {/* <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <FormControl 
                    fullWidth sx={{ }}>
                    <InputLabel htmlFor="outlined-adornment-amount">Dental Logo</InputLabel>
                    <OutlinedInput
                      value={this.state.dental_logo}
                      onChange={(e) =>
                          this.setState({
                            dental_logo:e.target.value
                          })
                      }
                      id="outlined-adornment-amount"
                      endAdornment={
                        <InputAdornment
                          position="end">
                          <IconButton>
                            <UploadFileIcon />
                          </IconButton>
                        </InputAdornment>}
                      label="Avatar" 
                      />
                  </FormControl>  
              </Box> */}
              {/* <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                  <FormControl 
                    fullWidth sx={{ }}>
                    <InputLabel htmlFor="outlined-adornment-amount">Avatar</InputLabel>
                    <OutlinedInput
                      value={this.state.avatar}
                      onChange={(e) =>
                          this.setState({
                            avatar:e.target.value
                          })
                      }
                      id="outlined-adornment-amount"
                      endAdornment={
                        <InputAdornment
                          position="end">
                          <IconButton>
                            <UploadFileIcon />
                          </IconButton>
                        </InputAdornment>}
                      label="Avatar" 
                      />
                  </FormControl>  
              </Box> */}
              {/* <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <FormControl 
                    fullWidth sx={{ }}>
                    <InputLabel htmlFor="outlined-adornment-amount">Total Budget</InputLabel>
                    <OutlinedInput
                      value={this.state.total_budget}
                      onChange={(e) =>
                          this.setState({
                            total_budget:e.target.value
                          })
                      }
                      id="outlined-adornment-amount"
                      startAdornment={
                        <InputAdornment
                          position="start">
                            <AttachMoneyIcon />
                        </InputAdornment>}
                      label="Total Budget" 
                      />
                  </FormControl>
              </Box> */}
              <Box
                  sx={{
                  width: '100%', 
                  position: 'relative'                                           
                  }}
              >
                <TextField
                  required
                    fullWidth
                    size='small'
                    id='password'
                    label="Password"
                    placeholder='Password'
                    type={this.state.showPassword ? 'text' : 'password'}
                    value={this.state.password}
                    onChange={(e) =>
                        this.setState({
                          password:e.target.value
                        })
                    }
                />
                <IconButton
                  onClick={() => this.handleShowPassword(!this.state.showPassword)} 
                  sx={{ position: 'absolute',height:'100%',width:'15%',right: 0, top: 0 ,boxShadow:'none' }}>
                  {this.state.showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </Box>
              <Box
                sx={{
                  display:'flex',
                  flexDirection:'row',
                  justifyContent:'space-between',
                  width:'100%',
                  alignItems:'center'
                }}
              >
                <List
                  orientation="horizontal"
                  wrap
                  sx={{
                    '--List-gap': '8px',
                    '--ListItem-radius': '20px',
                    '--ListItem-minHeight': '32px',
                    '--ListItem-gap': '4px',
                  }}
                >
                  {services.map((name) => (
                    <>
                      <ListItem 
                      
                        key={name}
                      >
                        <ListItemButton
                          disabled={this.props.auth.user.is_admin?(!this.props.groups.includes(name)):(!this.props.auth.subscription.includes(name))}// this.checkGroup(name)}

                          // disabled={!this.props.groups.includes(name)}// this.checkGroup(name)}
                        >
                        {/* <ListItemButton
                          disabled={!this.props.auth.subscription.includes(name)}
                        > */}
                          {this.state.groups.includes(name) && (
                            <Done
                              fontSize="md"
                              color="primary"
                              sx={{ ml: -0.5, zIndex: 2, pointerEvents: 'none' }}
                            />
                          )}
                          <Checkbox
                            size="sm"
                            // disabled={index === 0}
                            disableIcon
                            overlay
                            label={name}
                            checked={this.state.groups.includes(name)}
                            variant={this.state.groups.includes(name) ? 'soft' : 'outlined'}
                            onChange={(event) => {
                              if (event.target.checked) {
                                this.setState({
                                  groups:[...this.state.groups, name]
                                })
                              } else {
                                const newGroups=this.state.groups.filter((text) => text !== name)
                                this.setState({
                                  groups:newGroups
                                })
                              }
                            }}
                            slotProps={{
                              action: ({ checked }) => ({
                                sx: checked
                                  ? {
                                      border: '1px solid',
                                      borderColor: 'primary.500',
                                    }
                                  : {},
                              }),
                            }}
                          />
                      </ListItemButton>

                      </ListItem>
                    </>
                  ))}
                </List>
              </Box>
              <Box
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    marginLeft:2,
                    justifyContent:'space-between',
                    width:'50%',
                    alignItems:'center'
                  }}
                >
                  <FormGroup
                    sx={{
                      display:'flex',
                      flexDirection:'row',
                      justifyContent:'space-between',
                      width:'100%'
                    }}
                    >
                    <FormControlLabel control={<Radio />}  label="Active" onClick={()=>{
                        this.setState({
                          statusActive:!this.state.statusInActive,
                          statusInActive:!this.state.statusInActive
                        })}}/>
                    <FormControlLabel control={<Radio />}  label="InActive"  onClick={()=>{
                        this.setState({
                          statusActive:!this.state.statusInActive,
                          statusInActive:!this.state.statusInActive
                        })}}
                      />
                  </FormGroup>
              </Box>
              <Box
                sx={{
                  display:'flex',
                  alignItems:'center'
                }}
              >
                {this.state.submit ? (
                  <>
                    <Button 
                      sx={{
                        backgroundColor:green[600],
                        marginRight:1
                      }}
                      startDecorator={
                        <CircularProgress variant="solid"/>
                      }
                    >
                      Loading…
                    </Button>
                  </>
                ) : (
                  <ColorButton 
                    variant="contained"
                    sx={{
                      marginRight:1,
                      backgroundColor:green[600],
                    }}
                    onClick={() =>this.handleSubmit()}   
                  >
                    Submit
                  </ColorButton>
                )}
                <ColorButton 
                  variant="contained"
                  sx={{
                    backgroundColor:red[600],
                  }}
                  onClick={() => {
                    this.props.onClose()
                  }}
                >
                  Close
                </ColorButton>
              </Box>
            </Box>
          </Box>
        </Card>
      </Modal></>
    )
  }
}
export default compose(
  themeHook,
  authHook,
  dashboardHook
)(SubUserAdd)