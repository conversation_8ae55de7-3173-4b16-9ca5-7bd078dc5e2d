import React, { Component } from 'react'
import { Box, Card, IconButton, Modal, TextField, Typography } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close';
import { ColorButton } from '../ColorButton';
import { compose } from 'redux';
import themeHook from '../../HOK/ThemeHook';
import dashboardHook from '../../HOK/DashboardHook';
import authHook from '../../HOK/AuthHook';
import Button from '@mui/joy/Button';
import { CircularProgress } from '@mui/joy';
import { purple } from '@mui/material/colors';
class BudgetEdit extends Component {
    constructor(props){
        super(props)
        this.state={
            totalBudget:props.item,
            submit:false,
        }
    }
    async handleSubmit(changeBudget){
        this.setState({
            submit:true,
          })
        if (this.props.auth.user.is_admin){
          if(this.props.auth.selectUser){
            this.setState({
              totalBudget:changeBudget
            })
            // console.log(changeBudget)
            let updateBudget={
              id:this.props.auth.selectUser.id,
              admin_id:null,
              total_budget:parseFloat(this.state.totalBudget)
            }
            
            if (this.props.auth.user.user_group==2){
                updateBudget.admin_id=this.props.auth.selectUser.id
            }else if(this.props.auth.user.user_group==3){
                updateBudget.admin_id=this.props.auth.user.id
            }else if(this.props.auth.user.user_group==4){
                updateBudget.admin_id=this.props.auth.user.ref_admin.id
            }
            else{
        
            }
            let response=await this.props.dashboard.updateTotalBudget(this.props.auth.token,updateBudget)
            if (response.status!=200) {
                this.props.setInfoAlert({
                    msg: `Error updating Total Budget: ${response.msg}`,
                    status: false,
                  })
                  this.setState({
                    submit:false,
                  });
                  this.props.showAlertNotes();
                  this.props.refetchOrder(true)
            }else {
                if (response.msg == 'success') {
                    this.props.setInfoAlert({
                        msg: 'Total Budget update successfully',
                        status: true,
                    })
                    await this.props.auth.getUserListAction(this.props.auth.token)
                    this.props.refetchOrder(true)
                    this.setState({
                        submit:false,
                    });
                    this.props.showAlertNotes();
                    this.props.onClose();
              } else {
                    this.props.setInfoAlert({
                        msg: `Failed to upadate Total Budget: ${response.msg}`,
                        status: false,
                    })
                    this.setState({
                        submit:false,
                    });
                    this.props.showAlertNotes();
                    this.props.refetchOrder(true)
              }
            }
          }
        }
      }
    render() {
        return (
        <>
            <Modal
                disablePortal
                disableEnforceFocus
                disableAutoFocus
                open
                aria-labelledby="server-modal-title"
                aria-describedby="server-modal-description"
                sx={{
                display: 'flex',
                p: 1,
                alignItems: 'center',
                justifyContent: 'center',
                }}
                // onClose={this.props.onClose}
                // container={() => rootRef.current}
            >
                <Card
                    sx={{
                        width:'40%',
                        padding:4,
                        textAlign:'center',
                        padding:2,
                        // border:'gray',
                        // shadow:true
                    }}
                >
                    <Box
                        sx={(theme) => ({
                        position: 'relative',
                        bgcolor: 'background.paper',
                        // boxShadow: theme.shadows[5],
                        })}
                    >
                        <Box
                        sx={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            flexDirection:'column',
                            textAlign:'left',
                            gap:2,
                        }}
                        >
                            <Box
                                sx={{
                                display:'flex',
                                flexDirection:'row',
                                justifyContent:'space-between'
                                }}
                            >
                                <Typography
                                // width={'90%'}
                                >
                                </Typography>
                                <IconButton
                                onClick={() => {
                                    this.props.onClose()
                                }}
                                >
                                <CloseIcon  fontSize="small"/>
                                </IconButton>
                            </Box>
                            <Box>
                                <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                                Total Budget
                                </Typography>
                                {/* {this.state.} */}
                            </Box>
                            <Box
                                sx={{
                                width: '100%',                                           
                                }}
                            >
                                <TextField
                                    fullWidth
                                    size='small'
                                    id='total_budget'
                                    label="Total Budget"
                                    placeholder='Total Budget'
                                    required
                                    type='text'
                                    value={this.state.totalBudget}
                                    onChange={(e) =>
                                        this.setState({
                                        totalBudget:e.target.value
                                        })
                                    }
                                />
                            </Box>
                            <Box
                                sx={{
                                    display:'flex',
                                    flexDirection:'row',
                                    alignItems:'center'
                                }}
                            >
                                {this.state.submit ? (
                                <>
                                    <Button 
                                        sx={{
                                            backgroundColor:purple[400],
                                            marginRight:1
                                        }}
                                        startDecorator={
                                            <CircularProgress variant="solid"/>
                                        }
                                    >
                                        Loading…
                                    </Button>
                                </>
                                ) : (
                                <ColorButton
                                    variant="contained"
                                    sx={{
                                        marginRight:1
                                    }}
                                    onClick={() =>this.handleSubmit()}   
                                >
                                    Submit
                                </ColorButton>
                                )}
                                <ColorButton 
                                    variant="contained"
                                    onClick={() => {
                                        this.props.onClose()
                                        // setStatus(false);
                                        // onClose();
                                    }}
                                    // onClick={() =>this.handleSignIn()}   
                                >
                                    Close
                                </ColorButton>
                            </Box>
                        </Box>
                    </Box>
                </Card>
            </Modal>
        </>
        )
  }
}
export default compose(
    authHook,
    themeHook,
    dashboardHook
)(BudgetEdit)
