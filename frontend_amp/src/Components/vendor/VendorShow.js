
import React, { Component } from 'react'
import { Avatar, Box, Button, Card, CircularProgress, IconButton, Modal, TextField, Typography } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close';
import { ColorButton } from '../ColorButton';
import { Label, Visibility, VisibilityOff } from '@mui/icons-material';
import { grey } from '@mui/material/colors';
export default class VendorShow extends Component {
  constructor(props){
    super(props)
    this.state={
      showPassword:false
    }
  }
  handleShowPassword(arg){
    this.setState({
        showPassword:arg,
    })
  }
  render() {
      return (
        <>
          <Modal
          disablePortal
          disableEnforceFocus
          disableAutoFocus
          open
          aria-labelledby="server-modal-title"
          aria-describedby="server-modal-description"
          sx={{
            display: 'flex',
            p: 1,
            alignItems: 'center',
            justifyContent: 'center',
          }}
          // onClose={this.props.onClose}
          // container={() => rootRef.current}
        >
          <Card
            sx={{
                width:'40%',
                padding:4,
                textAlign:'center',
                padding:2,
                // border:'gray',
                // shadow:true
            }}
          >
             <Box
              sx={(theme) => ({
                position: 'relative',
                bgcolor: 'background.paper',
                // boxShadow: theme.shadows[5],
              })}
            >
              <Box
                sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection:'column',
                    textAlign:'left',
                    gap:1,
                }}
              >
                <Box
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    justifyContent:'space-between'
                  }}
                >
                  <Typography
                    // width={'90%'}
                  >
                  </Typography>
                  <IconButton
                    onClick={() => {
                      this.props.onClose()
                    }}
                  >
                    <CloseIcon  fontSize="small"/>
                  </IconButton>
                </Box>
                <Box
                  sx={{
                    display:'flex',
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Box
                    sx={{
                      display:'flex',
                      flexDirection:'column',
                      marginLeft:2,
                      alignContent:'center',
                      // alignItems:'center'
                    }}
                  >
                    <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                      {this.props.item.name || '----'}
                    </Typography>
                    <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                      {this.props.item.email || '----'}
                    </Typography>
                  </Box>
                </Box>
                <Box
                    sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                    Contact Number:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.contact_name || '----'}
                  </Typography>
                </Box>
                <Box
                    sx={{
                      width: '100%',  
                      borderBottom:`1px ${grey[200]} solid`,
                      paddingTop:1.5,
                      paddingLeft:4,
                      paddingBottom:1,
                      paddingRight:1,
                      alignItems:'center'
                    }}
                >
                  <Typography variant='p'>
                    Account Number:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.account_number || '----'}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                   }}
                >
                  <Typography variant='p'>
                    How to Order:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.how_to_order || '----'}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                    Phone Number:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.phone || '----'}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                    Website:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.website || '----'}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                    Username:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.user_name || '----'}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    position:'relative',
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                    Password:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant=''
                    >
                      {this.props.item.password?(
                        this.state.showPassword?(
                            this.props.item.password
                          ):(
                            "*".repeat(this.props.item.password.length)
                          )
                        ):(
                          '----'
                        )
                      } 
                  </Typography>
                  <IconButton
                    onClick={() => this.handleShowPassword(!this.state.showPassword)} 
                    sx={{ 
                      position: 'absolute',
                      height:'100%',
                      width:'15%',
                      right: 0,
                      top: 0 ,
                      boxShadow:'none' }}
                  >
                    {this.state.showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </Box>
              </Box>
            </Box>
          </Card>
        </Modal></>
      )
  }
}
