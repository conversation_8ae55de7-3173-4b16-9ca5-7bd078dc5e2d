import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { Box, Card, IconButton, Modal, TextField, Typography } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close';
import { ColorButton } from '../ColorButton';
import { purple } from '@mui/material/colors';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import Button from '@mui/joy/Button';
import { CircularProgress } from '@mui/joy';
import { compose } from 'redux';
import themeHook from '../../HOK/ThemeHook';
import dashboardHook from '../../HOK/DashboardHook';
import authHook from '../../HOK/AuthHook';
class VendorAdd extends Component {
  constructor(props){
    super(props)
    this.state={
      vendor_name:'',
      account_number:'',
      contact_name:'',
      how_to_order:'',
      phone:'',
      email:'',
      website:'',
      user_name:'',
      password:'',
      showPassword:false,
      submit:false,
    }
  }
  async handleSubmit(){
    this.setState({
      submit:true,
    })
    let vendorData={
      name:this.state.vendor_name,
      admin_id:null,
      account_number:this.state.account_number,
      contact_name:this.state.contact_name,
      how_to_order:this.state.how_to_order,
      phone:this.state.phone,
      email:this.state.email,
      website:this.state.website,
      user_name:this.state.user_name,
      password:this.state.password,
    }
    if (this.props.auth.user.user_group==2){
      vendorData.admin_id=this.props.auth.selectUser.id
    }else if(this.props.auth.user.user_group==3){
      vendorData.admin_id=this.props.auth.user.id
    }else if(this.props.auth.user.user_group==4){
      vendorData.admin_id=this.props.auth.user.ref_admin.id
    }
    else{

    }
    let response=await this.props.dashboard.insertVendor(this.props.auth.token,vendorData)
    if (response.status!=200) {
      this.props.setInfoAlert({
        msg: `Error inserting vendor: ${response.msg}`,
        status: false,
      })
      this.setState({
        submit:false,
        });
      this.props.showAlertNotes();
      this.props.refetchOrder(true)
    } else {
        if (response.msg == 'success') {
          this.props.setInfoAlert({
            msg: 'Vendor inserted successfully',
            status: true,
          })
          this.props.refetchOrder(true)
          this.setState({
            submit:false,
          });
          this.props.showAlertNotes();
          this.props.onClose();
        } else {
          this.props.setInfoAlert({
            msg: `Failed to insert Vendor: ${response.msg}`,
            status: false,
          })
          this.setState({
            submit:false,
          });
          this.props.showAlertNotes();
          this.props.refetchOrder(true)
        }
    }
  }
  handleShowPassword(arg){
    this.setState({
        showPassword:arg,
    })
}
  render() {
    return (
      <>
        <Modal
        disablePortal
        disableEnforceFocus
        disableAutoFocus
        open
        aria-labelledby="server-modal-title"
        aria-describedby="server-modal-description"
        sx={{
          display: 'flex',
          p: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        // onClose={this.props.onClose}
        // container={() => rootRef.current}
      >
        <Card
          sx={{
              width:'40%',
              padding:4,
              textAlign:'center',
              padding:2,
              // border:'gray',
              // shadow:true
          }}
        >
           <Box
            sx={(theme) => ({
              position: 'relative',
              bgcolor: 'background.paper',
              // boxShadow: theme.shadows[5],
            })}
          >
            <Box
              sx={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  flexDirection:'column',
                  textAlign:'left',
                  gap:2,
              }}
            >
              <Box
                sx={{
                  display:'flex',
                  flexDirection:'row',
                  justifyContent:'space-between'
                }}
              >
                <Typography
                  // width={'90%'}
                >
                </Typography>
                <IconButton
                  onClick={() => {
                    this.props.onClose()
                  }}
                >
                  <CloseIcon  fontSize="small"/>
                </IconButton>
              </Box>
              <Box>
                <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                  Add Item
                </Typography>
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='vendor_name'
                    label="Vendor Name"
                    placeholder='Vendor Name'
                    required
                    type='text'
                    value={this.state.vendor_name}
                    onChange={(e) =>
                        this.setState({
                          vendor_name:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='account_number'
                    label="Account Number"
                    placeholder='Account Number'
                    type='text'
                    value={this.state.account_number}
                    onChange={(e) =>
                        this.setState({
                          account_number:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='contact_name'
                    label="Contact Name"
                    placeholder='Contact Name'
                    type='text'
                    value={this.state.contact_name}
                    onChange={(e) =>
                        this.setState({
                          contact_name:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='how_to_order'
                    label="How to Order"
                    placeholder='How to Order'
                    type='text'
                    value={this.state.how_to_order}
                    onChange={(e) =>
                        this.setState({
                          how_to_order:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='phone'
                    label="Phone"
                    placeholder='Phone'
                    type='text'
                    value={this.state.phone}
                    onChange={(e) =>
                        this.setState({
                          phone:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='email'
                    label="Email"
                    placeholder='Email'
                    type='email'
                    value={this.state.email}
                    onChange={(e) =>
                        this.setState({
                          email:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='website'
                    label="WebSite"
                    placeholder='WebSite'
                    type='text'
                    value={this.state.website}
                    onChange={(e) =>
                        this.setState({
                          website:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='user_name'
                    label="Username"
                    placeholder='Username'
                    type='text'
                    value={this.state.user_name}
                    onChange={(e) =>
                        this.setState({
                          user_name:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%', 
                  position: 'relative'                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='password'
                    label="Password"
                    placeholder='Password'
                    type={this.state.showPassword ? 'text' : 'password'}
                    value={this.state.password}
                    onChange={(e) =>
                        this.setState({
                          password:e.target.value
                        })
                    }
                />
                <IconButton
                  onClick={() => this.handleShowPassword(!this.state.showPassword)} 
                  sx={{ position: 'absolute',height:'100%',width:'15%',right: 0, top: 0 ,boxShadow:'none' }}>
                  {this.state.showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </Box>
              <Box
                sx={{
                  display:'flex',
                  alignItems:'center'
                }}
              >
                {this.state.submit ? (
                  <>
                    <Button 
                      sx={{
                        marginRight:1
                      }}
                      // color={purple}
                      startDecorator={
                        <CircularProgress variant="solid"/>
                      }
                    >
                      Loading…
                    </Button>
                  </>
                ) : (
                  <ColorButton 
                      variant="contained"
                      sx={{
                        marginRight:1
                      }}
                      onClick={() =>this.handleSubmit()}   
                  >
                      Submit
                  </ColorButton>
                )}
                <ColorButton 
                      variant="contained"
                      onClick={() => {
                        this.props.onClose()
                          // setStatus(false);
                          // onClose();
                      }}
                      // onClick={() =>this.handleSignIn()}   
                  >
                      Close
                  </ColorButton>
              </Box>
            </Box>
          </Box>
        </Card>
      </Modal></>
    )
  }
}
export default compose(
  themeHook,
  authHook,
  dashboardHook
)(VendorAdd)