import React, { Component } from 'react'
import Modal from '@mui/material/Modal';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import CloseIcon from '@mui/icons-material/Close';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import dayjs from 'dayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
// import {
//   Unstable_NumberInput as BaseNumberInput,
//   numberInputClasses,
// } from '@mui/base/Unstable_NumberInput';
import { Card, Checkbox, FormControl, FormControlLabel, FormGroup, IconButton, InputLabel, MenuItem, Select, styled, TextField } from '@mui/material';
import { compose } from 'redux';

import themeHook from '../../HOK/ThemeHook';
import locationHook from '../../HOK/LoctionHook';
import dashboardHook from '../../HOK/DashboardHook';
import authHook from '../../HOK/AuthHook';
import { ColorButton } from '../ColorButton';
import { purple } from '@mui/material/colors';
import Button from '@mui/joy/Button';
import { CircularProgress } from '@mui/joy';
import { orderStatusFn } from '../../utils/functions';
const status={
  'Delivered':6,
  'Returned':8
}
class ReveivedEdit extends Component {
  constructor(props){
    super(props)
    this.state={
      path:props.location.pathname.slice(1,),
      id:props.item.id,
      product:props.item.product.name,
      s_k_u:props.item.product.s_k_u,
      vendor:props.item.vendor?props.item.vendor.name:'',
      note:props.item.note || '', // Add notes field to state
      qty:props.item.qty,
      checkSpecific:props.item.specific,
      checkUrgent:props.item.urgent,
      checkReceived:props.item.received,
      checkMyProduct:props.item.my_product,
      timeline:props.item.timeline,
      statusIndex:props.dashboard.orderStatus[orderStatusFn(props.item.status).name],
      statusSelectedOption:props.item.status,
      submitEdit:false,
      item:props.item,
    }
  }
  componentDidMount(){
    this.setState({
      item:this.props.item,
    })
  }
  async handleSubmit(){
    this.setState({
      submitEdit:true,
      status:false,
    })
    let receivedData={
      "id":this.state.item.id,
      "admin_id":null,
      "product":this.state.product,
      "s_k_u":this.state.s_k_u,
      "vendor":this.state.vendor,
      "note":this.state.note,
      "qty":this.state.qty,
      "specific":this.state.checkSpecific,
      "urgent":this.state.checkUrgent,
      "my_product":this.state.checkMyProduct,
      // "exp_delivery":"",
      "received":this.state.checkReceived,
      "timeline":this.state.timeline,
      "status":this.state.statusIndex
    }
    // console.log(this.state.statusIndex)
    if (this.state.timeline==null){
      receivedData.timeline=""
    }
    if (this.props.auth.user.user_group==2){
        receivedData.admin_id=this.props.auth.selectUser.id
      }else if(this.props.auth.user.user_group==3){
        receivedData.admin_id=this.props.auth.user.id
      }else if(this.props.auth.user.user_group==4){
        receivedData.admin_id=this.props.auth.user.ref_admin.id
      }
      else{
  
      }
    let response=await this.props.dashboard.updateOrder(this.props.auth.token,receivedData)
    if (response.status!=200) {
      this.props.setInfoAlert({
        msg: `Error upadting order: ${response.msg}`,
        status: false,
      })
      this.setState({
        submit:false,
      });
      this.props.showAlertNotes();
      this.props.refetchOrder(true)
    } else {
        if (response.msg === 'success') {
          this.props.setInfoAlert({
            msg: 'Order Update successfully',
            status: true,
          })
          this.props.refetchOrder(true)
          this.setState({
            submit:false,
          });
          this.props.showAlertNotes();
          this.props.onClose();
        } else {
          this.props.setInfoAlert({
            msg: `Failed to upadte order: ${response.msg}`,
            status: false,
          })
          this.setState({
            submit:false,
          });
          this.props.showAlertNotes();
          this.props.refetchOrder(true)
        }
    }
  }

  render() {
    return (
        <>
          <Modal
          disablePortal
          disableEnforceFocus
          disableAutoFocus
          open
          aria-labelledby="server-modal-title"
          aria-describedby="server-modal-description"
          sx={{
            display: 'flex',
            p: 1,
            alignItems: 'center',
            justifyContent: 'center',
          }}
          // onClose={this.props.onClose}
          // container={() => rootRef.current}
        >
          <Card
            sx={{
                width:'40%',
                padding:4,
                textAlign:'center',
                padding:2,
                // border:'gray',
                // shadow:true
            }}
          >
            <Box
              sx={(theme) => ({
                position: 'relative',
                bgcolor: 'background.paper',
                // boxShadow: theme.shadows[5],
              })}
            >
              <Box
                sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection:'column',
                    textAlign:'left',
                    gap:2,
                }}
              >
                <Box
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    justifyContent:'space-between'
                  }}
                >
                  <Typography
                    // width={'90%'}
                  >
                  </Typography>
                  <IconButton
                    onClick={() => {
                      this.props.onClose()
                    }}
                  >
                    <CloseIcon  fontSize="small"/>
                  </IconButton>
                </Box>
                <Box>
                  <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                    Edit Order
                  </Typography>
                </Box>
                <Box
                    sx={{
                    width: '100%',                                           
                    }}
                >
                  <TextField
                      fullWidth
                      size='small'
                      id='product'
                      label="product"
                      placeholder='product'
                      required
                      type='text'
                      value={this.state.product}
                      onChange={(e) =>
                          this.setState({
                              product:e.target.value
                          })
                      }
                  />
                </Box>
                <Box
                sx={{
                  width: '100%',  
                  backgroundColor:'inherit',
                  color:'inherit',                                      
                }}
              >
                <TextField
                  fullWidth
                  size='small'
                  id='s_k_u'
                  label="SKU"
                  placeholder='SKU(Manufacturer Code)'
                  type='text'
                  sx={{
                    backgroundColor:'inherit',
                    color:'inherit',
                  }}  
                  value={this.state.s_k_u}
                  onChange={(e) =>
                      this.setState({
                        s_k_u:e.target.value
                      })
                  }
                />
              </Box>
               {/* Add notes field below product */}
              <Box
                  sx={{
                    width: '100%',
                    backgroundColor:'inherit',
                    color:'inherit',
                    marginTop: 2                                      
                  }}
              >
                <TextField
                    fullWidth
                    multiline
                    rows={3}
                    size='small'
                    id='note'
                    label="Note"
                    placeholder='Add note about this order'
                    type='text'
                    sx={{
                      backgroundColor:'inherit',
                      color:'inherit',
                    }}  
                    value={this.state.note}
                    onChange={(e) =>
                        this.setState({
                            note:e.target.value
                        })
                    }
                />
              </Box>
                <Box
                  sx={{ 
                    display: 'flex',
                    position: 'relative' 
                    }}
                >
                    <TextField 
                        required
                        size='small'
                        id='vendor'
                        // className='!focus:border-purple-700'
                        label="vendor" 
                        placeholder='vendor'
                        type='text'
                        fullWidth
                        value={this.state.vendor}
                        onChange={(e) =>
                            this.setState({
                                vendor:e.target.value
                            })
                        }
                    />
                </Box>
                <Box
                  sx={{ 
                    display: 'flex',
                    position: 'relative',
                    width:'100%',
                    justifyContent:'space-between'
                    }}
                >
                  <Box 
                    size='small'
                    fullWidth
                    sx={{
                      display:'flex',
                      flexDirection:'row',
                      width:'45%',
                      position:'relative',
                      alignItems:'center'
                      // borderColor:this.props.theme.fontColor,
                        // backgroundColor:this.props.theme.background,
                        // color:this.props.theme.fontColor
                    }}
                  >
                    <TextField 
                      required
                      size='medium'
                      id='vendor'
                      label="Qty" 
                      placeholder='1'
                      defaultValue={1}
                      type='text'
                      fullWidth
                      value={this.state.qty}
                      onChange={(e) =>
                          this.setState({
                              qty:e.target.value
                          })
                      }
                    />
                    <Box
                      sx={{ 
                        position: 'absolute',
                        display:'flex',
                        flexDirection:'column',
                        // height:'100%',
                        width:'15%',
                        right: 0,
                        //  top: 0 ,
                        boxShadow:'none',
                        alignItems:'center',
                        justifyContent:'space-between'
                      }}
                    >
                      <IconButton 
                        sx={{
                          padding:0
                        }}
                        onClick={()=>{
                          this.setState({
                            qty:this.state.qty+1
                          })
                        }}
                      >
                        <ArrowDropUpIcon />
                      </IconButton>
                      <IconButton
                        sx={{
                          padding:0
                        }}
                        onClick={()=>{
                          if(this.state.qty>1){
                            this.setState({
                              qty:this.state.qty-1
                            })
                          }
                        }}
                      >
                        <ArrowDropDownIcon/>
                      </IconButton>
                    </Box>
                  </Box>
                  <Box
                    sx={{
                      display:'flex',
                      flexDirection:'row',
                      marginLeft:2,
                      justifyContent:'space-evenly',
                      width:'50%',
                      alignItems:'center'
                    }}
                  >
                    <FormGroup
                      sx={{
                        display:'flex',
                        flexDirection:'row',
                        justifyContent:'space-between',
                        width:'100%'
                      }}
                      >
                        <FormControlLabel 
                          control={<Checkbox />} 
                          checked={this.state.checkSpecific}
                          label="Specific" 
                          onClick={()=>{
                            this.setState({
                              checkSpecific:!this.state.checkSpecific
                            })}}
                        />
                        <FormControlLabel 
                          control={<Checkbox  />}
                          disabled={!this.props.permission}
                          checked={this.state.checkUrgent}  
                          label="Urgent"  
                          onClick={()=>{
                            if (this.props.permission){
                              this.setState({
                                checkUrgent:!this.state.checkUrgent
                              })
                            }
                          }}
                        />
                      {/* </FormGroup>
                      <FormGroup
                        sx={{
                          display:'flex',
                          flexDirection:'column',
                          justifyContent:'space-between',
                          width:'100%'
                        }}
                      > */}
                        <FormControlLabel 
                          control={<Checkbox />}  
                          checked={this.state.checkReceived}
                          label="Received"  
                          onClick={()=>{
                            this.setState({
                              checkReceived:!this.state.checkReceived
                            })}}
                        />
                        {/* <FormControlLabel 
                          control={<Checkbox />} 
                          checked={this.state.checkMyProduct} 
                          label="Favorite"  
                          onClick={()=>{
                            this.setState({
                              checkMyProduct:!this.state.checkMyProduct
                            })}}
                        /> */}
                    </FormGroup>
                  </Box>
                </Box>
                <Box
                  sx={{ 
                    display: 'flex',
                    position: 'relative',
                    width:'100%'
                    }}
                >
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DatePicker
                        disabled={!this.props.permission}
                        sx={{
                          width:'100%'
                        }}
                        format=' YYYY / MM / DD'
                        label="Timeline"
                        defaultValue={dayjs(this.state.timeline)}
                        onChange={(newValue) => this.setState({
                          timeline:newValue})
                      }/>
                  </LocalizationProvider>
                </Box>
                {this.props.permission?(
                  <Box>
                    <FormControl 
                      size='small'
                      fullWidth
                      sx={{
                        borderColor:this.props.theme.fontColor,
                        backgroundColor:this.props.theme.background,
                        color:this.props.theme.fontColor
                      }}
                    >
                      <InputLabel 
                          id="demo-simple-select-label"
                          sx={{
                              backgroundColor:this.props.theme.background,
                              color:this.props.theme.fontColor,
                              // width:'80%',
                              borderRight:'1px solid '
                          }}
                      >Status</InputLabel>
                      <Select
                          value={this.state.statusSelectedOption}
                          label="Status"
                          onChange={(e)=>{
                            this.setState({
                              statusIndex:this.props.dashboard.orderStatus[e.target.value],
                              statusSelectedOption:e.target.value
                            })
                          }
                          }
                          // components={CustomOption}
                          sx={{
                              display:'flex',
                              flexDirection:'row',
                              backgroundColor:this.props.theme.background,
                              color:this.props.theme.fontColor,
                              '& .MuiSelect-select':{
                                  display:'flex',
                                  backgroundColor:this.props.theme.background,
                                  color:this.props.theme.fontColor
                              },
                          }}
                          style={{display:'flex',flexDirection:'row',alignItems:'center'}}
                      >
                        {Object.keys(this.props.dashboard.orderStatus).map((item,index)=>{
                            return(
                                <MenuItem 
                                  key={index}
                                  value={item}>
                                    <Typography>
                                      {item}
                                    </Typography>
                                </MenuItem>
                            )
                          })
                        }
                      </Select>
                    </FormControl>
                  </Box>
                  ):(
                    <Box>
                      <FormControl 
                        size='small'
                        fullWidth
                        sx={{
                          borderColor:this.props.theme.fontColor,
                          backgroundColor:this.props.theme.background,
                          color:this.props.theme.fontColor
                        }}
                      >
                        <InputLabel 
                            id="demo-simple-select-label"
                            sx={{
                                backgroundColor:this.props.theme.background,
                                color:this.props.theme.fontColor,
                                // width:'80%',
                                borderRight:'1px solid '
                            }}
                        >Status</InputLabel>
                        <Select
                            value={this.state.statusSelectedOption}
                            label="Status"
                            onChange={(e)=>{
                                this.setState({
                                  statusIndex:status[e.target.value],
                                  statusSelectedOption:e.target.value
                                })
                              }
                            }
                            sx={{
                                display:'flex',
                                flexDirection:'row',
                                backgroundColor:this.props.theme.background,
                                color:this.props.theme.fontColor,
                                '& .MuiSelect-select':{
                                    display:'flex',
                                    backgroundColor:this.props.theme.background,
                                    color:this.props.theme.fontColor
                                },
                            }}
                            style={{display:'flex',flexDirection:'row',alignItems:'center'}}
                        >
                          {Object.keys(status).map((item,key)=>{
                            return (
                              <MenuItem 
                                key={key}
                                value={item}
                              >
                                <Typography>
                                  {item}
                                </Typography>
                            </MenuItem>
                            )
                          }
                          )}
                        </Select>
                      </FormControl>
                    </Box>
                  )
                }
                
                <Box
                  sx={{
                    display:'flex',
                    alignItems:'center'
                  }}
                >
                  {this.state.submitEdit ? (
                    <>
                      <Button 
                       // color={'primary'}
                       sx={{
                           backgroundColor:purple[400],
                           marginRight:1
                           // marginTop:1
                       }}
                       startDecorator={
                           <CircularProgress variant="solid"/>
                       }
                    >
                       Loading…
                   </Button>
                    </>
                  ) : (
                    <ColorButton 
                        variant="contained"
                        sx={{
                          marginRight:1
                        }}
                        onClick={() =>this.handleSubmit()}   
                    >
                        Submit
                    </ColorButton>
                  )}
                  <ColorButton 
                        variant="contained"
                        onClick={() => {
                          this.props.onClose()
                            // setStatus(false);
                            // onClose();
                        }}
                        // onClick={() =>this.handleSignIn()}   
                    >
                        Close
                    </ColorButton>
                </Box>
              </Box>       
            </Box>
          </Card>
        </Modal>
      </>
    )
  }
}
export default compose(
  themeHook,
  locationHook,
  dashboardHook,
  authHook
)(ReveivedEdit)
