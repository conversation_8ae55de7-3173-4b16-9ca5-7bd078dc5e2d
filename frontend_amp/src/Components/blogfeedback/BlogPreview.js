
import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { Box, Divider, IconButton, Typography } from '@mui/material'
import { compose } from 'redux'
import { BiSolidLike } from "react-icons/bi";
import { BiSolidDislike } from "react-icons/bi";
import { Badge } from '@mui/joy'
import themeHook from '../../HOK/ThemeHook';
import authHook from '../../HOK/AuthHook';
import navigateHook from '../../HOK/NavigateHook';
import locationHook from '../../HOK/LoctionHook';
import dashboardHook from '../../HOK/DashboardHook';
import { DEV_BASE_URL } from '../../Environments';
import { formatDate } from '../../utils/functions';
class BlogPreview extends Component {
  constructor(props){
    super(props)
    this.state={
      theme:props.theme,
      darkTheme:props.theme.darkTheme,
      tableHeight:(this.props.theme.windowDimensions.height*70)/100,
      auth:props.auth,
      sendForAPI:false,
      blogData:Object(null),
      countLike:0,
      countDisLike:0,
      commentsData:Object(null),
      isLoading:true,
      item:Object(null)
    }
  }
  async componentDidMount(){
    console.log(this.props.location)
    if (this.props.dashboard.blogReviewData){
      this.setState({
        isLoading:false
      })
    }
    console.log(this.props.dashboard.blogReviewData)
    // const item=this.props.dashboard.blogReviewData
    // this.setState({
    //   item:item
    // })
    
  }
  render() {
    return (this.state.isLoading?(
        <>
        </>
      ):(
        <>
          <Box
              sx={{
                  width:'100%',
                  display:'flex',
                  flexDirection:'column',
                  textAlign:'center',
                  justifyContent:'center',
                  alignContent:'center',
                  alignItems:'center',
                  borderBottom:'',
                  paddingTop:2,
                  backgroundColor:this.props.theme.selectTheme.background_2,

                  // marginTop:2,
                  // marginBottom:2,
                  // marginX:5,
                  gap:1
              }}
          >
             <Box
              sx={{
                width:'100%',
                display:'flex',
                flexDirection:'column',
                textAlign:'center',
                justifyContent:'center',
                alignContent:'center',
                alignItems:'center',
                backgroundColor:this.props.theme.selectTheme.background_2,
                paddingTop:2,

                // marginTop:2,
                gap:1
              }}
            >
            <Box
              sx={{
                width:'70%',
                height:"400px",
                display:'flex',
                flexDirection:'row',
                justifyContent:'center',
                alignContent:'center',
              }}
            > 

              <img
                style={{
                  width:'400px',
                  height:'400px'
                }}
                alt='blog_img'
                src={DEV_BASE_URL+'/'+this.props.dashboard.blogReviewData.post_image}
                />
            </Box>
            <Box
              sx={{
                width:'90%',
                minHeight:'400px',
                display:'flex',
                flexDirection:'column',
                justifyContent:'start',
                borderTop:'1px solid #e5e7eb',
                borderLeft:'1px solid #e5e7eb',
                borderRight:'1px solid #e5e7eb',
                backgroundColor:this.props.theme.selectTheme.background_3,

                // minHeight:this.state.tableHeight,
                alignContent:'start',
                // borderBottom:'1px solid #e5e7eb',
                paddingY:'5%',
                paddingX:'10%'
              }}
            >
              <Box
                sx={{
                    width:'100%',
                    display:'flex',
                    flexDirection:'row',
                    justifyContent:'space-between',
                    marginBottom:2,
                    paddingX:2
                }}
              >
                <Box 
                  sx={{
                    display:'flex',
                    flexDirection:'column',
                  }}
                >
                  <Typography 
                      variant="h5"
                  >
                    {this.props.dashboard.blogReviewData.post_title} 
                  </Typography>
                  <Typography 
                    sx={{
                        fontSize:'10px',
                        paddingLeft:2
                    }}
                    variant="p"
                    color="text.secondary"
                    >
                    {this.props.dashboard.blogReviewData.post_author}<br/> 
                  </Typography>
                </Box>
                <Typography 
                  sx={{
                      fontSize:'10px',
                      alignContent:'center',
                      // alignItems:'center'
                  }}
                  variant="p" 
                  color="text.primary"
                >
                    {formatDate(
                    'Amm-dd-yyyy',this.props.dashboard.blogReviewData.created_at)}
                </Typography>
              </Box>
              <Box
                sx={{
                  display:'flex',
                  // minHeight:'250px',
                  justifyContent:'flex-start',
                  alignContent:'flex-start',
                  alignItems:'flex-start'
                }}
              >
                <Typography variant="span">
                  {this.props.dashboard.blogReviewData.post_content}
                </Typography>
              </Box>
            </Box>
            </Box>
          </Box>
        </>
      )
    )
  }
}
export default compose(
  themeHook,
  authHook,
  navigateHook,
  locationHook,
  dashboardHook
)(BlogPreview)