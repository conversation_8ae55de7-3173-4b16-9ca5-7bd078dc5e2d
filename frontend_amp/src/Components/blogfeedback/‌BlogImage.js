import React, { useState } from 'react';
import { CardMedia, Dialog, DialogContent, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/navigation';
import { Navigation } from 'swiper/modules';
import { DEV_BASE_URL } from '../../Environments';
const BlogImage = ({ post_image, alt }) => {
  const [open, setOpen] = useState(false);
  const [focusImageRow,setFocusImagerow]=useState({})
  const handleOpen = (row) => {
    setOpen(true)
    setFocusImagerow(row)
  }
  const handleClose = () => {
    setOpen(false);
    setFocusImagerow({})
  }
  return (
    <>
      {/* تصویر معمولی در بلاگ */}
       <Swiper
        modules={[Navigation]}
        navigation
        spaceBetween={10}
        slidesPerView={1}
        style={{ borderRadius: '8px' }}
      >
        {Object.keys(post_image).map((media, index) => {
                return(
                    <SwiperSlide key={index}>
                        <CardMedia
                          component="img"
                          height="400px"
                          width="400px"
                          image={post_image[media]["img_url"] ? `${DEV_BASE_URL}/${post_image[media]["img_url"]}` : '/path/to/fallback-image.jpg'}
                          alt={`slide-${index}`}
                          sx={{
                            maxWidth: '400px',
                            maxHeight: '400px',
                            cursor: 'pointer',
                            objectFit: 'cover'
                          }}
                          onError={(e) => { e.target.src = '/path/to/fallback-image.jpg'; }}
                          onClick={() => {
                            setOpen(true);
                            setFocusImagerow(post_image[media]);
                          }}
                        />
                    </SwiperSlide>
                )
            })
        }
      </Swiper>
      {/* مودال با استفاده از MUI Dialog */}
      <Dialog open={open} onClose={handleClose} maxWidth="xl">
        <DialogContent
          sx={{
            position: 'relative',
            p: 0,
            backgroundColor: 'black',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          {/* دکمه بستن */}
          <IconButton
            onClick={handleClose}
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              color: 'white',
              zIndex: 1000,
            }}
          >
            <CloseIcon />
          </IconButton>

          {/* تصویر بزرگ‌شده */}
          <img
            src={DEV_BASE_URL+'/'+focusImageRow['img_url']}
            alt={alt}
            style={{
              maxWidth: '90vw',
              maxHeight: '90vh',
              objectFit: 'contain',
              display: 'block',
              margin: 'auto'
            }}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default BlogImage;
