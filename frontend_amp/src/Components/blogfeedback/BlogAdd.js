import React, { Component } from 'react'
import PropTypes, { array } from 'prop-types'
import { Box, Card, FormControl, InputAdornment, InputLabel, MenuItem, Modal, OutlinedInput, Select, TextField, Typography } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close';
import themeHook from '../../HOK/ThemeHook';
import authHook from '../../HOK/AuthHook';
import navigateHook from '../../HOK/NavigateHook';
import dashboardHook from '../../HOK/DashboardHook';
import { compose } from 'redux';
import { DeleteForeverOutlined } from '@mui/icons-material';
import { Button, CircularProgress } from '@mui/joy';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import { styled } from '@mui/material/styles';
import { green, purple, red } from '@mui/material/colors';
import { ColorButton } from '../ColorButton';
import IconButton from '@mui/joy/IconButton';
import OpenInNew from '@mui/icons-material/OpenInNew';
import { DEV_BASE_URL } from '../../Environments';
import { Link } from 'react-router-dom';
const post_status={
    "Published":"Published",
    "Hidden":"Hidden",
    "Draft":"Draft"
}
const VisuallyHiddenInput = styled('input')({
    clip: 'rect(0 0 0 0)',
    clipPath: 'inset(50%)',
    height: 1,
    overflow: 'hidden',
    position: 'absolute',
    bottom: 0,
    left: 0,
    whiteSpace: 'nowrap',
    width: 1,
  });
class BlogAdd extends Component {
    constructor(props){
        super(props)
        this.state={
          theme:props.theme,
          darkTheme:props.theme.darkTheme,
          post_title:'',
          blogphotos:'',
          blogPhotosName:[],
          submit:false,
          chaosePicture:false,
          chaoseFile:false,
          submitUpload:false,
          uploadProgress:0,
          selecte_file:false,
          blogFile:'',
          select_blogFile:false,
          uploadProgressBlogFile:0,
          submitUploadBlogFile:false,
          post_status:"Published"
        }
    }
    async handleSubmit(){
        this.setState({
          submit:true,
        })
        let blogData={
            post_title:this.state.post_title,
            post_description:this.state.post_description,
            post_content:this.state.post_content,
            post_status:this.state.post_status,
            post_author:'',
            post_image:this.props.dashboard.blogPictures,
            blog_file:this.props.dashboard.blogFile?this.props.dashboard.blogFile:''
          }
        let response=await this.props.dashboard.inserBlog(this.props.auth.token,blogData)
        if (response.status!=200) {
          this.props.setInfoAlert({
            msg: `Error inserting User: ${response.msg}`,
            status: false,
          })
          this.setState({
            submit:false,
            });
          this.props.showAlertNotes();
          this.props.refetchOrder(true)
        } else {
            if (response.msg == 'success') {
              this.props.setInfoAlert({
                msg: 'User inserted successfully',
                status: true,
              })
              this.props.refetchOrder(true)
              this.setState({
                submit:false,
              });
              this.props.showAlertNotes();
              this.props.onClose();
            } else {
              this.props.setInfoAlert({
                msg: `Failed to insert User: ${response.msg}`,
                status: false,
              })
              this.setState({
                submit:false,
              });
              this.props.showAlertNotes();
              this.props.refetchOrder(true)
            }
        }
      }
    async handleUploadPicture(file){
        this.setState({
          submitUpload:true,
        })
        let pictureData={
          // dental_logo:this.state.dental_logo,
          blogphotos:file,
        //   blogphotos:file,
          post_title:this.state.post_title
        }
        let response=await this.props.dashboard.uploadBlogPicture(this.props.auth.token,pictureData,(value)=>{
          this.setState({
            uploadProgress:value
          })
          // this.state.uploadProgress
        })
        if (response.status!=200) {
          this.props.setInfoAlert({
            msg: `Error Upload File: ${response.msg}`,
            status: false,
          })
          this.setState({
            submitUpload:false,
            });
          this.props.showAlertNotes();
          // this.props.refetchOrder(true)
        } else {
            if (response.msg == 'success') {
              this.props.setInfoAlert({
                msg: 'File Upload successfully',
                status: true,
              })
              // this.props.refetchOrder(true)
              this.setState({
                submitUpload:false,
              });
              this.props.showAlertNotes();
              // this.props.onClose();
            } else {
              this.props.setInfoAlert({
                msg: `Failed to Upload Picture: ${response.msg}`,
                status: false,
              })
              this.setState({
                submitUpload:false,
              });
              this.props.showAlertNotes();
              // this.props.refetchOrder(true)
            }
        }
    }
    async handleUploadFile(file){
      this.setState({
        submitUploadBlogFile:true,
      })
      let blogFileData={
        blogFile:file,
        post_title:this.state.post_title
      }
      let response=await this.props.dashboard.uploadBlogFile(this.props.auth.token,blogFileData,(value)=>{
        this.setState({
          uploadProgressBlogFile:value
        })
        // this.state.uploadProgress
      })
      if (response.status!=200) {
        this.props.setInfoAlert({
          msg: `Error Upload File: ${response.msg}`,
          status: false,
        })
        this.setState({
          submitUploadBlogFile:false,
          });
        this.props.showAlertNotes();
      } else {
          if (response.msg == 'success') {
            this.props.setInfoAlert({
              msg: 'File Upload successfully',
              status: true,
            })
            this.setState({
              submitUploadBlogFile:false,
            });
            this.props.showAlertNotes();
          } else {
            this.props.setInfoAlert({
              msg: `Failed to Upload Picture: ${response.msg}`,
              status: false,
            })
            this.setState({
              submitUploadBlogFile:false,
            });
            this.props.showAlertNotes();
          }
      }
    }
    async openInNewTab(url){
      window.open(url, "_blank", "noreferrer");
    };
  render() {
    return (
      <>
        <Modal
        disablePortal
        disableEnforceFocus
        disableAutoFocus
        open
        aria-labelledby="server-modal-title"
        aria-describedby="server-modal-description"
        sx={{
          display: 'flex',
          p: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        // onClose={this.props.onClose}
        // container={() => rootRef.current}
      >
        <Card
          sx={{
            width:'40%',
            padding:4,
            textAlign:'center',
            padding:2,
            // border:'gray',
            // shadow:true
          }}
        >
           <Box
                sx={(theme) => ({
                position: 'relative',
                bgcolor: 'background.paper',
                // boxShadow: theme.shadows[5],
                })}
            >
                <Box
                sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection:'column',
                    textAlign:'left',
                    gap:2,
                }}
                >
                    <Box
                        sx={{
                            display:'flex',
                            flexDirection:'row',
                            justifyContent:'space-between'
                        }}
                    >
                        <Typography>
                        </Typography>
                        <IconButton
                            onClick={() => {
                                this.props.onClose()
                            }}
                            >
                            <CloseIcon  fontSize="small"/>
                        </IconButton>
                    </Box>
                    <Box>
                        <Typography 
                            variant='h3'
                            fontSize={'1.25rem'} 
                            fontWeight={'500'}
                        >
                            Add Blog
                        </Typography>
                    </Box>
                    <Box
                        sx={{
                        width: '100%',                                           
                        }}
                    >
                        <TextField
                        fullWidth
                        size='small'
                        id='post_title'
                        label="Title"
                        placeholder='Title'
                        type='text'
                        value={this.state.post_title}
                        onChange={(e) =>
                            this.setState({
                                post_title:e.target.value
                            })
                        }
                        />
                    </Box>
                    <Box
                        sx={{
                        width: '100%',                                           
                        }}
                    >
                        <TextField
                        fullWidth
                        size='small'
                        id='post_description'
                        label="Description"
                        placeholder='Description'
                        type='text'
                        value={this.state.post_description}
                        onChange={(e) =>
                            this.setState({
                                post_description:e.target.value
                            })
                        }
                        />
                    </Box>
                    <Box
                        sx={{
                        width: '100%',                                           
                        }}
                    >
                        <TextField
                            multiline
                            aria-multiline
                            rows={10}
                            fullWidth
                            size='small'
                            id='post_content'
                            label="Content"
                            placeholder='Content'
                            type='text'
                            value={this.state.post_content}
                            onChange={(e) =>
                                this.setState({
                                    post_content:e.target.value
                                })
                            }
                        />
                    </Box>
                    <Box
                        sx={{
                        width: '100%',                                           
                        }}
                    >
                        <FormControl 
                            // multiline
                            // aria-multiline
                            // rows={2}
                            fullWidth sx={{ }}>
                            <InputLabel
                                aria-label='Blog Photos'
                                sx={{
                                    paddingRight:5
                                }}
                                htmlFor="outlined-adornment-amount"
                                >
                                Blog Photos
                            </InputLabel>
                            <OutlinedInput
                                value={this.state.blogphotos.name}
                                onChange={(e) =>{
                                    this.setState({
                                        blogphotos:e.target.files[0],
                                    })
                                    this.handleUploadPicture()
                                    }
                                }
                                id="outlined-adornment-amount"                      
                                endAdornment={
                                    <InputAdornment
                                    position="end">
                                    {this.state.selecte_file?(
                                        this.state.uploadProgress==100?(
                                        <>
                                            <IconButton
                                              onClick={()=>{
                                                  this.setState({
                                                  blogphotos:'',
                                                  selecte_file:false,
                                                  uploadProgress:0,
                                                  submitUpload:false
                                                  })
                                            }}
                                            >
                                            <DeleteForeverOutlined
                                                color="error"
                                            />
                                            </IconButton>
                                            {/* <IconButton
                                            // onClick={}
                                            >
                                            <DoneIcon
                                                color="success"
                                            />
                                            </IconButton> */}
                                        </>
                                        ):(
                                        <CircularProgress 
                                            // size="lg" 
                                            determinate
                                            value={this.state.uploadProgress}
                                            >
                                            <Typography>
                                            {this.state.uploadProgress}%
                                            </Typography>
                                        </CircularProgress>
                                        )
                                    ):(
                                        <Button
                                        component="label"
                                        role={undefined}
                                        variant="contained"
                                        tabIndex={-1}
                                        startIcon={<CloudUploadIcon />}
                                        >
                                        <UploadFileIcon />
                                        <VisuallyHiddenInput
                                            type="file"
                                            onChange={
                                            (e) =>{
                                                // const blogphotos=this.state.blogphotos
                                                // const blogPhotosName=this.state.blogPhotosName
                                                // if (!blogphotos.includes(e.target.files[0])){
                                                //     blogphotos.push(e.target.files[0])
                                                //     blogPhotosName.push(e.target.files[0].name)
                                                // }
                                                this.setState({
                                                    // ...this.state.blogphotos,
                                                    blogphotos:e.target.files[0],
                                                    // blogPhotosName:blogPhotosName
                                                })
                                                this.setState({
                                                  chaosePicture:true,
                                                // blogphotos:e.target.files[0],
                                                selecte_file:true
                                                })
                                                this.handleUploadPicture(e.target.files[0])
                                            }
                                            }
                                            // multiple
                                        />
                                        </Button>
                                    )
                                    }
                                    </InputAdornment>}
                                label="Dental Logo" 
                                />
                        </FormControl>  
                    </Box>
                    <Box
                        sx={{
                        width: '100%',                                           
                        }}
                    >
                        <FormControl 
                            // multiline
                            // aria-multiline
                            // rows={2}
                            fullWidth sx={{ }}>
                            <InputLabel
                                aria-label='Blog Pdf File'
                                sx={{
                                    paddingRight:5
                                }}
                                htmlFor="outlined-adornment-amount"
                                >
                                Blog File
                            </InputLabel>
                            <OutlinedInput
                                value={this.state.blogFile.name}
                                onChange={(e) =>{
                                    this.setState({
                                      blogFile:e.target.files[0],
                                    })
                                    this.handleUploadPicture()
                                  }
                                }
                                id="outlined-adornment-amount"                      
                                endAdornment={
                                    <InputAdornment
                                    position="end">
                                    {this.state.select_blogFile?(
                                        this.state.uploadProgressBlogFile==100?(
                                        <>
                                            <IconButton
                                              onClick={()=>{
                                                  this.setState({
                                                  // blogphotos:'',
                                                  blogFile:'',
                                                  select_blogFile:false,
                                                  uploadProgressBlogFile:0,
                                                  submitUploadBlogFile:false
                                                  })
                                            }}
                                            >
                                            <DeleteForeverOutlined
                                                color="error"
                                            />
                                            </IconButton>
                                            {/* <IconButton
                                            // onClick={}
                                            >
                                            <DoneIcon
                                                color="success"
                                            />
                                            </IconButton> */}
                                        </>
                                        ):(
                                        <CircularProgress 
                                            // size="lg" 
                                            determinate
                                            value={this.state.uploadProgressBlogFile}
                                            >
                                            <Typography>
                                            {this.state.uploadProgressBlogFile}%
                                            </Typography>
                                        </CircularProgress>
                                        )
                                    ):(
                                        <Button
                                        component="label"
                                        role={undefined}
                                        variant="contained"
                                        tabIndex={-1}
                                        startIcon={<CloudUploadIcon />}
                                        >
                                        <UploadFileIcon />
                                        <VisuallyHiddenInput
                                            type="file"
                                            onChange={
                                            (e) =>{
                                                // const blogphotos=this.state.blogphotos
                                                // const blogPhotosName=this.state.blogPhotosName
                                                // if (!blogphotos.includes(e.target.files[0])){
                                                //     blogphotos.push(e.target.files[0])
                                                //     blogPhotosName.push(e.target.files[0].name)
                                                // }
                                                this.setState({
                                                    // ...this.state.blogphotos,
                                                    blogFile:e.target.files[0],
                                                    // blogPhotosName:blogPhotosName
                                                })
                                                this.setState({
                                                  chaoseFile:true,
                                                  select_blogFile:true
                                                })
                                                this.handleUploadFile(e.target.files[0])
                                            }
                                            }
                                            // multiple
                                        />
                                        </Button>
                                    )
                                    }
                                    </InputAdornment>}
                                label="Blog File" 
                                />
                        </FormControl>  
                    </Box>
                    <Box>
                      <FormControl
                        size='small'
                        fullWidth
                        sx={{
                          borderColor:this.props.theme.fontColor,
                          backgroundColor:this.props.theme.background,
                          color:this.props.theme.fontColor,
                        //   borderColor:red[600]
                        }}
                      >
                        <InputLabel 
                            id="demo-simple-select-label"
                            sx={{
                                backgroundColor:this.props.theme.background,
                                color:this.props.theme.fontColor,
                                // width:'80%',
                                // borderRight:'1px solid '
                            }}
                        >Status</InputLabel>
                        <Select
                          value={this.state.post_status}
                          label="Status"
                          onChange={(e)=>{
                            console.log(e.target.value)
                                this.setState({
                                    post_status:e.target.value
                                })
                            }
                          }
                          // components={CustomOption}
                          sx={{
                              display:'flex',
                              flexDirection:'row',
                              backgroundColor:this.props.theme.background,
                              color:this.props.theme.fontColor,
                              '& .MuiSelect-select':{
                                  display:'flex',
                                  backgroundColor:this.props.theme.background,
                                  color:this.props.theme.fontColor
                              },
                          }}
                          style={{display:'flex',flexDirection:'row',alignItems:'center'}}
                      >
                        {
                          Object.keys(post_status).map((item,index)=>{
                            return(
                                <MenuItem 
                                  key={index}
                                  value={item}>
                                    <Typography>
                                      {item}
                                    </Typography>
                                </MenuItem>
                            )
                          })
                          }
                        </Select>
                      </FormControl>
                    </Box>
                    <Box
                        sx={{
                        display:'flex',
                        alignItems:'center'
                        }}
                    >
                        {/* <Button 
                            component="a" 
                            target="_blank"
                            // href={"http://localhost:3000/#"+"/blogs/preview/"+0} 
                            // href={DEV_BASE_URL+'/'+item.invoice_file}
                            sx={{
                                marginRight:1
                            }}
                            startDecorator={<OpenInNew />}
                            onClick={()=>{
                              this.props.dashboard.setBlogReviewData({
                                  post_title:this.state.post_title,
                                  post_description:this.state.post_description,
                                  post_content:this.state.post_content,
                                  post_status:this.state.post_status,
                                  post_author:'',
                                  post_image:this.props.dashboard.blogPictures,
                                  blog_file:this.props.dashboard.blogFile
                              })
                              
                              window.open(DEV_BASE_URL+"/#"+"/blogs/preview/"+0,'_blank')

                              // this.props.navigate("/blogs/preview/"+0,{
                              //                                         state: {
                              //                                           item: blogData,
                              //                                         },
                              //                                         replace:true
                              //                                       })
                              // this.props.navigate(1)
                            }}
                        >
                            Preview in new tab
                        </Button> */}
                        {this.state.submit ? (
                            <>
                                <Button 
                                    // color={'primary'}
                                    sx={{
                                        backgroundColor:purple[400],
                                        marginRight:1
                                        // marginTop:1
                                    }}
                                    startDecorator={
                                        <CircularProgress variant="solid"/>
                                    }
                                >
                                    Loading…
                                </Button>
                            </>
                        ) : (
                            <ColorButton 
                                variant="contained"
                                // className='mt-4'
                                sx={{
                                    marginRight:1
                                }}
                                onClick={() =>{
                                    if(this.state.checkUrgent&&this.state.timeline==''){
                                        this.setState({
                                        error:'Timeline is not Selected',
                                        })
                                    }else{
                                        this.handleSubmit()
                                    }
                                    }
                                }   
                            >
                                Submit
                            </ColorButton>
                        )}
                        <ColorButton 
                            variant="contained"
                            sx={{
                                marginRight:1
                            }}
                            onClick={() => {
                                this.props.onClose()
                                // setStatus(false);
                                // onClose();
                            }}
                            // onClick={() =>this.handleSignIn()}   
                        >
                            Close
                        </ColorButton>
                    </Box>
                </Box>
            </Box>
            </Card>
        </Modal>
      </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    navigateHook,
    dashboardHook
  )(BlogAdd)