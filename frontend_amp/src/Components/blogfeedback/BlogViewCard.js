import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>ontent, Typo<PERSON>, Button } from '@mui/material';

const BlogViewCard = ({ title, author, date, description, content, downloadLink }) => (
  <Card sx={{ maxWidth: 800, margin: 'auto', mt: 4 }}>
    <CardHeader
      title={title}
      subheader={`By ${author} | ${new Date(date).toLocaleDateString()}`}
    />
    <CardContent>
      <Typography variant="subtitle1" color="text.secondary" gutterBottom>
        {description}
      </Typography>
      <Typography variant="body1" paragraph>
        {content}
      </Typography>
      {downloadLink && (
        <Button variant="contained" color="primary" href={downloadLink} download>
          Download File
        </Button>
      )}
    </CardContent>
  </Card>
);

export default BlogViewCard;
