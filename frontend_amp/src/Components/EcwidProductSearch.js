import React, { Component } from 'react';
import {
  Box,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Typography,
  CircularProgress,
  Alert,
  Chip,
  Paper,
  Divider
} from '@mui/material';
import { Search as SearchIcon, ShoppingCart as ShoppingCartIcon } from '@mui/icons-material';
import ecwidService from '../services/EcwidService';
import { ECWID_CONFIG } from '../config/ecwid';

class EcwidProductSearch extends Component {
  constructor(props) {
    super(props);
    this.state = {
      searchTerm: '',
      products: [],
      loading: false,
      error: null,
      showResults: false,
      selectedProduct: null
    };

    this.searchTimeout = null;
    this.handleSearchChange = this.handleSearchChange.bind(this);
    this.handleProductSelect = this.handleProductSelect.bind(this);
    this.performSearch = this.performSearch.bind(this);
  }

  componentDidMount() {
    // Set initial search term if provided
    if (this.props.initialValue) {
      this.setState({ searchTerm: this.props.initialValue });
      this.performSearch(this.props.initialValue);
    }
  }

  componentDidUpdate(prevProps) {
    // Update search term if props change
    if (prevProps.initialValue !== this.props.initialValue && this.props.initialValue) {
      this.setState({ searchTerm: this.props.initialValue });
      this.performSearch(this.props.initialValue);
    }
  }

  handleSearchChange(event) {
    const value = event.target.value;
    this.setState({ searchTerm: value });

    // Clear previous timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Debounce search
    if (value.trim()) {
      this.searchTimeout = setTimeout(() => {
        this.performSearch(value);
      }, ECWID_CONFIG.SEARCH.DEBOUNCE_MS);
      this.setState({ showResults: true });
    } else {
      this.setState({
        products: [],
        showResults: false,
        error: null
      });
    }
  }

  async performSearch(searchTerm) {
    if (!searchTerm.trim()) return;

    this.setState({ loading: true, error: null });

    try {
      console.log('🔍 Searching for:', searchTerm);
      
      const response = await ecwidService.searchProductsByKeyword(searchTerm, {
        limit: 10,
        sortBy: 'RELEVANCE'
      });

      console.log('📦 Search results:', response);

      const formattedProducts = response.items.map(product => 
        ecwidService.formatProductForDisplay(product)
      );

      this.setState({
        products: formattedProducts,
        loading: false,
        showResults: true
      });

    } catch (error) {
      console.error('Search error:', error);
      const errorInfo = ecwidService.handleApiError(error);
      
      this.setState({
        error: errorInfo.error,
        loading: false,
        products: []
      });
    }
  }

  handleProductSelect(product) {
    console.log('🎯 Product selected:', product);
    
    this.setState({
      selectedProduct: product,
      showResults: false,
      searchTerm: product.name
    });

    // Call parent callback if provided
    if (this.props.onProductSelect) {
      this.props.onProductSelect(product);
    }
  }

  render() {
    const { searchTerm, products, loading, error, showResults } = this.state;
    const { theme } = this.props;

    return (
      <Box sx={{ position: 'relative', width: '100%' }}>
        {/* Search Input */}
        <TextField
          fullWidth
          size="small"
          placeholder="Search products..."
          value={searchTerm}
          onChange={this.handleSearchChange}
          InputProps={{
            startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
            endAdornment: loading && <CircularProgress size={20} />
          }}
          sx={{
            backgroundColor: 'inherit',
            color: 'inherit',
            '& .MuiOutlinedInput-root': {
              backgroundColor: theme?.selectTheme?.background || 'inherit',
              color: theme?.selectTheme?.fontColor || 'inherit',
            }
          }}
        />

        {/* Search Results */}
        {showResults && (
          <Paper
            elevation={3}
            sx={{
              position: 'absolute',
              top: '100%',
              left: 0,
              right: 0,
              zIndex: 1000,
              maxHeight: '400px',
              overflow: 'auto',
              mt: 1,
              backgroundColor: theme?.selectTheme?.background || 'white',
              color: theme?.selectTheme?.fontColor || 'inherit'
            }}
          >
            {/* Error Display */}
            {error && (
              <Alert severity="error" sx={{ m: 1 }}>
                {error}
              </Alert>
            )}

            {/* Loading State */}
            {loading && (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <CircularProgress size={24} />
                <Typography sx={{ ml: 1 }}>Searching products...</Typography>
              </Box>
            )}

            {/* No Results */}
            {!loading && !error && products.length === 0 && searchTerm.trim() && (
              <Box sx={{ p: 2, textAlign: 'center' }}>
                <Typography color="text.secondary">
                  No products found for "{searchTerm}"
                </Typography>
              </Box>
            )}

            {/* Product Results */}
            {!loading && !error && products.length > 0 && (
              <List sx={{ p: 0 }}>
                {products.map((product, index) => (
                  <React.Fragment key={product.id}>
                    <ListItem
                      button
                      onClick={() => this.handleProductSelect(product)}
                      sx={{
                        '&:hover': {
                          backgroundColor: theme?.selectTheme?.selectHover || 'rgba(0, 0, 0, 0.04)'
                        }
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar
                          src={product.imageUrl}
                          alt={product.name}
                          sx={{ width: 48, height: 48 }}
                        >
                          <ShoppingCartIcon />
                        </Avatar>
                      </ListItemAvatar>
                      
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="subtitle2" noWrap>
                              {product.name}
                            </Typography>
                            <Typography variant="h6" color="primary">
                              {product.formattedPrice}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <Box>
                            {product.sku && (
                              <Typography variant="caption" color="text.secondary">
                                SKU: {product.sku}
                              </Typography>
                            )}
                            <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                              <Chip
                                label={product.inStock ? 'In Stock' : 'Out of Stock'}
                                size="small"
                                color={product.inStock ? 'success' : 'error'}
                                variant="outlined"
                              />
                              {product.unlimited && (
                                <Chip
                                  label="Unlimited"
                                  size="small"
                                  color="info"
                                  variant="outlined"
                                />
                              )}
                            </Box>
                            {product.description && (
                              <Typography 
                                variant="caption" 
                                color="text.secondary"
                                sx={{ 
                                  display: 'block', 
                                  mt: 0.5,
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap'
                                }}
                              >
                                {product.description}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < products.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}

            {/* Footer with result count */}
            {!loading && !error && products.length > 0 && (
              <Box sx={{ p: 1, backgroundColor: 'action.hover', textAlign: 'center' }}>
                <Typography variant="caption" color="text.secondary">
                  Showing {products.length} results for "{searchTerm}"
                </Typography>
              </Box>
            )}
          </Paper>
        )}
      </Box>
    );
  }
}

export default EcwidProductSearch;
