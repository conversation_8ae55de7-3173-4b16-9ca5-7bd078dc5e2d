import React, { Component } from 'react'
import { Box, Card, Checkbox, FormControl, FormControlLabel, FormGroup, IconButton, InputAdornment, InputLabel, MenuItem, Modal, OutlinedInput, Select, styled, TextField, Typography } from '@mui/material';
import { ColorButton } from './ColorButton';
import CloseIcon from '@mui/icons-material/Close';
import { Money } from '@mui/icons-material';
import { compose } from 'redux';
import authHook from '../HOK/AuthHook';
import themeHook from '../HOK/ThemeHook';
import dashboardHook from '../HOK/DashboardHook';
import Button from '@mui/joy/Button';
import CircularProgress from '@mui/joy/CircularProgress';
import { red } from '@mui/material/colors';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
class OrderPaid extends Component {
    constructor(props){
      super(props)
      this.state={
        theme:props.theme,
        id:this.props.item.id,
        product:props.item.product.name,
        would_paid:props.item.would_paid,
        paid:props.item.paid,
        submit:false,
        item:props.item,
      }
    }
    async handleSubmit(){
      let orderData={
        id:this.state.id,
        // admin_id:null,
        // product:this.state.product,
        would_paid:parseFloat(this.state.would_paid),
        paid:parseFloat(this.state.paid),
        saved:this.state.would_paid-this.state.paid
      }
      this.setState({
        submit:true
        }
      )
      orderData.admin_id=this.props.auth.user.is_admin?this.props.auth.selectUser.id:this.props.auth.user.id
      let response=await this.props.dashboard.updatePaid(this.props.auth.token,orderData)
      // let response=await this.state.dashboard.updateOrderStatus
      console.log(response)
      if (response.status!=200) {
        this.props.setInfoAlert({
          msg: `Error upadting Paid order: ${response.msg}`,
          status: false,
        })
        this.setState({
          submit:false,
          });
        this.props.showAlertNotes();
        this.props.refetchOrder(true)
      } else {
          if (response.msg == 'success') {
            this.props.setInfoAlert({
              msg: 'Order Paid Update successfully',
              status: true,
            })
            this.props.refetchOrder(true)
            this.setState({
              submit:false,
            });
            this.props.showAlertNotes();
            this.props.onClose()
          } else {
            this.props.setInfoAlert({
              msg: `Failed to upadte Paid order: ${response.msg}`,
              status: false,
            })
            this.setState({
              submit:false,
            });
            this.props.showAlertNotes()
            this.props.refetchOrder(true)
          }
      }
    }
  render() {
    return (
        <>
          <Modal
          disablePortal
          disableEnforceFocus
          disableAutoFocus
          open
          aria-labelledby="server-modal-title"
          aria-describedby="server-modal-description"
          sx={{
            display: 'flex',
            p: 1,
            alignItems: 'center',
            justifyContent: 'center',
          }}
          // onClose={this.props.onClose}
          // container={() => rootRef.current}
        >
          <Card
            sx={{
                width:'40%',
                padding:4,
                textAlign:'center',
                padding:2,
                // border:'gray',
                // shadow:true
            }}
          >
            <Box
              sx={(theme) => ({
                position: 'relative',
                bgcolor: 'background.paper',
                // boxShadow: theme.shadows[5],
              })}
            >
              <Box
                sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection:'column',
                    textAlign:'left',
                    gap:2,
                }}
              >
                <Box
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    justifyContent:'space-between'
                  }}
                >
                  <Typography
                    // width={'90%'}
                  >
                  </Typography>
                  <IconButton
                    onClick={() => {
                      this.props.onClose()
                    }}
                  >
                    <CloseIcon  fontSize="small"/>
                  </IconButton>
                </Box>
                <Box
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    alignItems:'center'
                  }}
                >
                  <Typography 
                    variant='h3' 
                    fontSize={'1.25rem'} 
                    fontWeight={'500'}
                    sx={{
                      alignItems:'center'
                    }}
                  >
                    Paid Order
                  </Typography>
                  <IconButton>
                    <Typography >
                      {this.state.item.product.name}
                    </Typography>  
                  </IconButton>
                  
                </Box>
                <Box
                  sx={{
                    width: '100%',    
                    display: 'flex',
                    position: 'relative' ,
                  }}
                > 
                  <FormControl 
                    fullWidth sx={{ m: 1 }}>
                    <InputLabel htmlFor="outlined-adornment-amount">WOULD’VE PAID</InputLabel>
                    <OutlinedInput
                      value={this.state.would_paid}
                      onChange={(e) =>
                          this.setState({
                            would_paid:e.target.value
                          })
                      }
                      id="outlined-adornment-amount"
                      startAdornment={
                        <InputAdornment
                          position="start">
                            <AttachMoneyIcon />
                        </InputAdornment>}
                      label="WOULD’VE PAID" 
                      />
                  </FormControl>  
                </Box>
                <Box
                  sx={{ 
                    width: '100%',    
                    display: 'flex',
                    position: 'relative' ,
                    justifyContent:'end',
                    }}
                >
                  <FormControl fullWidth sx={{ m: 1 }}>
                    <InputLabel htmlFor="outlined-adornment-amount">Paid</InputLabel>
                    <OutlinedInput
                      value={this.state.paid}
                      onChange={(e) =>
                        this.setState({
                            paid:e.target.value
                        })
                      }
                      id="outlined-adornment-amount"
                      startAdornment={
                        <InputAdornment
                          position="start">
                            <AttachMoneyIcon />
                        </InputAdornment>}
                      label="Paid" 
                      />
                  </FormControl>
                </Box>
                <Box
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    alignItems:'center',

                  }}
                >
                  {this.state.submit ? (
                    <>
                      <Button 
                          // color={'primary'}
                          sx={{
                              backgroundColor:red[700],
                              marginRight:1
                              // marginTop:1
                          }}
                          startDecorator={
                              <CircularProgress variant="solid"/>
                          }
                      >
                          Loading…
                      </Button>
                    </>
                  ) : (
                    <ColorButton 
                        variant="contained"
                        sx={{
                          marginRight:1
                        }}
                        onClick={() =>this.handleSubmit()}   
                    >
                        Submit
                    </ColorButton>
                  )}
                  <ColorButton 
                        variant="contained"
                        onClick={() => {
                          this.props.onClose()
                            // setStatus(false);
                            // onClose();
                        }}
                        // onClick={() =>this.handleSignIn()}   
                    >
                        Close
                    </ColorButton>
                </Box>
              </Box>       
            </Box>
          </Card>
        </Modal>
      </>
    )
  }
}
export default compose(
  authHook,themeHook,dashboardHook
)(OrderPaid)