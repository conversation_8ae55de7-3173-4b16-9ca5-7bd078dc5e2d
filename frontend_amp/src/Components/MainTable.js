import  {React,Component} from 'react';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';
import { Box, Button, Checkbox, Tooltip } from '@mui/material';
import { Value } from 'sass';
import { formatDate, orderStatusFn } from '../utils/functions';
import { grey, purple } from '@mui/material/colors';
import NoDataList from './NoDataList';
import { HiInformationCircle } from 'react-icons/hi';
import { TbEdit, TbTrash } from 'react-icons/tb';
import { compose } from 'redux';
import authHook from '../HOK/AuthHook';
import themeHook from '../HOK/ThemeHook';
import dashboardHook from '../HOK/DashboardHook';
import locationHook from '../HOK/LoctionHook';
const head = [
  { name: 'Date', style: '8' },
  { name: 'Product', style: '40' },
  { name: 'QTY', style: '6' },
  { name: 'Specific', style: '4' },
  { name: 'Vendor', style: '7' },
  { name: 'Urgent', style: '4' },
  // { name: 'Exp. Delivery', style: '10' },
  { name: 'Status', style: '8' },
  { name: 'Timeline', style: '9' },
  { name: 'Received', style: '4' },
  { name: 'Action', style: '8' },
];
const optionToFetch={
  'order':{received:false},
  'received':{received:true},
  // 'inventory':{label:'Inventory List ',button:false,buttonLabel:'',filter:false,searchBar:false},
  // 'Received':{label:' Recent Orders',button:false,buttonLabel:'',filter:false,searchBar:false},
  // 'Received':{label:' Recent Orders',button:false,buttonLabel:'',filter:false,searchBar:false},
}
const checkboxClassFn = (color: String) => {
  return `text-${color} focus:ring-${color} dark:focus:ring-${color} focus:ring-2 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600 rounded bg-gray-100 border-gray-300`;
};
class OrderTable extends Component {
  constructor(props){
    super(props)
    this.state={
      theme:props.theme,
      auth:props.auth,
      dashboard:props.dashboard,
      path:this.props.location.pathname.slice(1,),
      page:0,
      rowsPerPage:5,
      dataTable:null,
      statusEdit:false,
      statusDelete:false,
      StatusPaid:false,
    }
    this.handleChangePage.bind(this.handleChangePage)
    this.handleChangeRowsPerPage.bind(this.handleChangeRowsPerPage)
  }
  componentDidMount(){
    // const auth=this.props.auth
    // const order=this.props.order
    this.setState({
      theme:this.props.theme,
      auth:this.props.auth,
      dashboard:this.props.dashboard,
      ordersTableData:this.props.dashboard.ordersTableData
    })
    this.shouldComponentUpdate=true
    this.forceUpdate()
  }
  handleChangePage(event, newPage){
    this.setState({
        page:this.state.page
    })
  };
  handleChangeRowsPerPage(event){
    this.setState({
        rowsPerPage:+event.target.value,
        page:0
    });
  };

  render() {
    return
  //   return isLoadingVendors ?   
  //     (
  //       <TableSkeleton />
  //     ) :
  //     (this.state.dataTable && (
  //         <>
  //           {showAlert && (
  //               <AlertNotes
  //                   message={infoAlert.msg}
  //                   action={infoAlert.status}
  //               />
  //           )}
  //           {statusAdd && (
  //               <VendorAdd
  //                   data={statusAdd}
  //                   onClose={handleCloseModal}
  //                   onFormInsert={handleInsert}
  //               />
  //           )}
  //           {statusEdit && (
  //               <VendorEdit
  //                   item={rowsItem}
  //                   data={statusEdit}
  //                   onClose={handleCloseModal}
  //                   onFormEdit={handleEdit}
  //               />
  //           )}
  //           {statusDelete && (
  //               <VendorDelete
  //                   data={statusDelete}
  //                   onClose={handleCloseModal}
  //                   item={rowsItem}
  //                   showAlertNotes={showAlertNotes}
  //                   setInfoAlert={setInfoAlert}
  //                   refetch={refetchVendor}
  //               />
  //           )}
  //           {statusShow && (
  //               <VendorShow
  //                   data={statusShow}
  //                   item={rowsItem}
  //                   onClose={handleCloseModal}
  //               />
  //           )}
  //         </>
  //       )
  //     )
  }
}

export default compose(
  themeHook,
  locationHook,
  authHook,
  dashboardHook,
)(OrderTable)