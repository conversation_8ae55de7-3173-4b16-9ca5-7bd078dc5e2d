// import Image from "next/image";
import { alpha, Card, TableCell, TableRow, Typography } from '@mui/material';
import nodata from '../static/img/nodata1.jpg';
import React, { Component } from 'react'
import PropTypes from 'prop-types'
import themeHook from '../HOK/ThemeHook';
import { Opacity } from '@mui/icons-material';

class NoDataList extends Component {
    constructor(props){
        super(props)
        this.state={
            theme:props.theme,
            colSpan:props.colSpan
        }
    }
    componentDidMount(){
        this.setState({
          theme:this.props.theme,
          colSpan:this.props.colSpan
        })
    }
  render() {
    return (
      <>
         <TableRow>
            <TableCell
                colSpan={this.state.colSpan}
                sx={{
                    // px:1,
                    // py:2,
                    // border:`1px ${grey[200]} solid`,
                    textAlign:'center',
                    backgroundColor:this.props.theme.selectTheme.background_2,

                    backgroundColor:this.props.theme.selectTheme.background_2,
                    color: this.props.theme.selectTheme.fontColor_3
                }}
            >
                <Card 
                    sx={{
                        backgroundColor:this.props.theme.selectTheme.background_2,
                    }}
                    className='flex item-center justify-center'>
                    <img 
                        src={nodata}
                        alt='No Data'
                        style={{
                            width:'35%',
                            height:'35%',
                            // opacity: 0.2,
                            // filter: alpha(this.props.theme.selectTheme.background_2),
                        }}
                        // className='m-auto w-[35%]'
                        width={1000}
                        height={1000}
                        />
                    {/* <Image
                        src={'/assets/images/nodata1.jpg'}
                        alt='No Data'
                        className='m-auto w-[35%]'
                        width={1000}
                        height={1000}
                    /> */}
                    <Typography 
                        variant='h5'
                        className='text-2xl font-bold tracking-tight text-gray-900 dark:text-white'>
                        No Records
                    </Typography>
                </Card>
            </TableCell>
        </TableRow>  
      </>
    )
  }
}
export default (themeHook)(NoDataList)
// const NoDataList = ({colSpan,theme}:Props) => {
//     return (
       
//     );
// };
// export default NoDataList;