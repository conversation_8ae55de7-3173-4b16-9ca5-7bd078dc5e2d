import { Box, Card, FormControl, InputLabel, MenuItem, Modal, Select, Typography } from '@mui/material'
import React, { Component } from 'react'
const filters={
  'None':1,
  'Expire Date':2,
  'Low QTY':3,
  'Both':4,
}
export default class FilterDate extends Component {
  constructor(props){
    super(props)
    this.state={
      filter_index: 0
    }
  }
  render() {
    return (
      <> 
        <Modal
        disablePortal
        disableEnforceFocus
        disableAutoFocus
        open
        aria-labelledby="server-modal-title"
        aria-describedby="server-modal-description"
        sx={{
          display: 'flex',
          p: 1,
          alignItems: 'center',
          justifyContent: 'center',
          
        }}
        // onClose={this.props.onClose}
        // container={() => rootRef.current}
      >
        <Card
          sx={{
              width:'40%',
              padding:4,
              textAlign:'center',
              padding:2,
              // backgroundColor:this.props.theme.selectTheme.background_2,
              // color:this.props.theme.selectTheme.fontColor
              // border:'gray',
              // shadow:true
          }}
        >
          <Box
            sx={(theme) => ({
              position: 'relative',
              bgcolor: 'background.paper',
              backgroundColor:'inherit',
              color:'inherit',
              // boxShadow: theme.shadows[5],
            })}
          >
            <Box
              sx={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  flexDirection:'column',
                  textAlign:'left',
                  gap:2,
                  backgroundColor:'inherit',
                  color:'inherit',
              }}
            >
              hi
            </Box>
          </Box>
        </Card>
      </Modal>
      </>
    )
  }
}
