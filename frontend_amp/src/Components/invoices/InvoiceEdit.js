import React, { Component } from 'react'
import PropTypes, { array } from 'prop-types'
import { Box, Card, FormControl, InputAdornment, InputLabel, MenuItem, Modal, OutlinedInput, Select, TextField, Typography } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close';
import themeHook from '../../HOK/ThemeHook';
import authHook from '../../HOK/AuthHook';
import navigateHook from '../../HOK/NavigateHook';
import dashboardHook from '../../HOK/DashboardHook';
import { compose } from 'redux';
import { DeleteForeverOutlined } from '@mui/icons-material';
import { Button, CircularProgress } from '@mui/joy';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import { styled } from '@mui/material/styles';
import { green, purple, red } from '@mui/material/colors';
import { ColorButton } from '../ColorButton';
import IconButton from '@mui/joy/IconButton';
import OpenInNew from '@mui/icons-material/OpenInNew';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { formatDate } from '../../utils/functions';
const post_status={
    "Published":"Published",
    "Hidden":"Hidden",
    "Draft":"Draft"
}
const VisuallyHiddenInput = styled('input')({
    clip: 'rect(0 0 0 0)',
    clipPath: 'inset(50%)',
    height: 1,
    overflow: 'hidden',
    position: 'absolute',
    bottom: 0,
    left: 0,
    whiteSpace: 'nowrap',
    width: 1,
  });
class InvoiceEdit extends Component {
    constructor(props){
        super(props)
        this.state={
          theme:props.theme,
          darkTheme:props.theme.darkTheme,
          vendor:props.item.vendor,
          date: props.item.date,
          invoice_number:props.item.invoice_number,
          amount_paid:props.item.amount_paid,
          submit:false,
          chaoseFile:false,
          invoiceFile:props.item.invoice_file,
          select_InvoiceFile:false,
          uploadProgressInvoiceFile:0,
          submitUploadInvoiceFile:false,
        }
    }
    async handleSubmit(){
      this.setState({
        submit:true,
      })
      let InvoiceData={
        id:this.props.item.id,
        admin_id:null,
        vendor:this.state.vendor,
        invoice_number:this.state.invoice_number,
        amount_paid:this.state.amount_paid,
        date:this.state.date,
        invoice_file:this.props.dashboard.invoiceFile
      }
      if(this.state.chaoseFile){
        InvoiceData.invoice_file=this.props.dashboard.invoiceFile
      }
      InvoiceData.admin_id=this.props.auth.user.is_admin?this.props.auth.selectUser.id:this.props.auth.user.id        
      let response=await this.props.dashboard.editInvoice(this.props.auth.token,InvoiceData)
      if (response.status!=200) {
        this.props.setInfoAlert({
          msg: `Error Editing invoice: ${response.msg}`,
          status: false,
        })
        this.setState({
          submit:false,
          });
        this.props.showAlertNotes();
        this.props.refetchOrder(true)
      } else {
          if (response.msg == 'success') {
            this.props.setInfoAlert({
              msg: 'invoice Edited successfully',
              status: true,
            })
            this.props.refetchOrder(true)
            this.setState({
              submit:false,
            });
            this.props.showAlertNotes();
            this.props.onClose();
          } else {
            this.props.setInfoAlert({
              msg: `Failed to Edit invoice: ${response.msg}`,
              status: false,
            })
            this.setState({
              submit:false,
            });
            this.props.showAlertNotes();
            this.props.refetchOrder(true)
          }
      }
      }
    async handleUploadFile(file){
      this.setState({
        submitUploadInvoiceFile:true,
      })
      let invoiceFileData={
        invoiceFile:file,
        vendor:this.state.vendor
      }
      let response=await this.props.dashboard.uploadInvoiceFile(this.props.auth.token,invoiceFileData,(value)=>{
        this.setState({
          uploadProgressInvoiceFile:value
        })
        // this.state.uploadProgress
      })
      if (response.status!=200) {
        this.props.setInfoAlert({
          msg: `Error Upload File: ${response.msg}`,
          status: false,
        })
        this.setState({
          submitUploadInvoiceFile:false,
          });
        this.props.showAlertNotes();
      } else {
          if (response.msg == 'success') {
            this.props.setInfoAlert({
              msg: 'File Upload successfully',
              status: true,
            })
            this.setState({
              submitUploadInvoiceFile:false,
            });
            this.props.showAlertNotes();
          } else {
            this.props.setInfoAlert({
              msg: `Failed to Upload File: ${response.msg}`,
              status: false,
            })
            this.setState({
              submitUploadInvoiceFile:false,
            });
            this.props.showAlertNotes();
          }
      }
    }
  render() {
    return (
      <>
        <Modal
        disablePortal
        disableEnforceFocus
        disableAutoFocus
        open
        aria-labelledby="server-modal-title"
        aria-describedby="server-modal-description"
        sx={{
          display: 'flex',
          p: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        // onClose={this.props.onClose}
        // container={() => rootRef.current}
      >
        <Card
          sx={{
            width:'40%',
            padding:4,
            textAlign:'center',
            padding:2,
            // border:'gray',
            // shadow:true
          }}
        >
           <Box
                sx={(theme) => ({
                position: 'relative',
                bgcolor: 'background.paper',
                // boxShadow: theme.shadows[5],
                })}
            >
              <Box
                sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection:'column',
                    textAlign:'left',
                    gap:2,
                }}
              >
                <Box
                        sx={{
                            display:'flex',
                            flexDirection:'row',
                            justifyContent:'space-between'
                        }}
                    >
                        <Typography>
                        </Typography>
                        <IconButton
                            onClick={() => {
                                this.props.onClose()
                            }}
                            >
                            <CloseIcon  fontSize="small"/>
                        </IconButton>
                    </Box>
                    <Box>
                        <Typography 
                            variant='h3'
                            fontSize={'1.25rem'} 
                            fontWeight={'500'}
                        >
                            Edit Invoice
                        </Typography>
                    </Box>
                    <Box
                        sx={{
                        width: '100%',                                           
                        }}
                    >
                        <TextField
                        fullWidth
                        size='small'
                        id='vendor'
                        label="Vendor"
                        placeholder='Vendor'
                        type='text'
                        value={this.state.vendor}
                        onChange={(e) =>
                            this.setState({
                                vendor:e.target.value
                            })
                        }
                        />
                    </Box>
                    <Box
                        sx={{
                        width: '100%',                                           
                        }}
                    >
                        <TextField
                        fullWidth
                        size='small'
                        id='invoice_numebr'
                        label="Invoice Number"
                        placeholder='Invoice Number'
                        type='text'
                        value={this.state.invoice_number}
                        onChange={(e) =>
                            this.setState({
                              invoice_number:e.target.value
                            })
                        }
                        />
                    </Box>
                    <Box
                        sx={{
                        width: '100%',                                           
                        }}
                    >
                        <TextField
                        fullWidth
                        size='small'
                        id='amount_paid'
                        label="Amount Paid"
                        placeholder='Amount Paid'
                        type='text'
                        value={this.state.amount_paid}
                        onChange={(e) =>
                            this.setState({
                                amount_paid:e.target.value
                            })
                        }
                        />
                    </Box>
                    <Box
                      sx={{ 
                        display: 'flex',
                        position: 'relative',
                        width:'100%'
                        }}
                        
                    >
                      <LocalizationProvider dateAdapter={AdapterDayjs}>
                          <DatePicker
                          sx={{
                              width:'100%'
                          }}
                          format=' YYYY / MM / DD'
                          label="Date"
                          defaultValue={dayjs(this.state.date?this.state.date:formatDate('Amm-dd-yyyy'))}
                          onChange={(newValue) =>{ 
                              this.setState({
                                  date:newValue
                              })
                              }
                          }/>
                      </LocalizationProvider>
                    </Box>
                    <Box
                        sx={{
                        width: '100%',                                           
                        }}
                    >
                        <FormControl 
                            // multiline
                            // aria-multiline
                            // rows={2}
                            fullWidth sx={{ }}>
                            <InputLabel
                                aria-label='Invoice PDF File'
                                sx={{
                                    paddingRight:5
                                }}
                                htmlFor="outlined-adornment-amount"
                                >
                                Invoice File
                            </InputLabel>
                            <OutlinedInput
                                value={this.state.invoiceFile.name}
                                onChange={(e) =>{
                                    this.setState({
                                      invoiceFile:e.target.files[0],
                                    })
                                    this.handleUploadPicture()
                                  }
                                }
                                id="outlined-adornment-amount"                      
                                endAdornment={
                                    <InputAdornment
                                    position="end">
                                    {this.state.select_InvoiceFile?(
                                        this.state.uploadProgressInvoiceFile==100?(
                                        <>
                                            <IconButton
                                              onClick={()=>{
                                                  this.setState({
                                                  invoiceFile:'',
                                                  select_InvoiceFile:false,
                                                  uploadProgressInvoiceFile:0,
                                                  submitUploadInvoiceFile:false
                                                  })
                                            }}
                                            >
                                            <DeleteForeverOutlined
                                                color="error"
                                            />
                                            </IconButton>
                                            {/* <IconButton
                                            // onClick={}
                                            >
                                            <DoneIcon
                                                color="success"
                                            />
                                            </IconButton> */}
                                        </>
                                        ):(
                                        <CircularProgress 
                                            // size="lg" 
                                            determinate
                                            value={this.state.uploadProgressInvoiceFile}
                                            >
                                            <Typography>
                                            {this.state.uploadProgressInvoiceFile}%
                                            </Typography>
                                        </CircularProgress>
                                        )
                                    ):(
                                        <Button
                                        component="label"
                                        role={undefined}
                                        variant="contained"
                                        tabIndex={-1}
                                        startIcon={<CloudUploadIcon />}
                                        >
                                        <UploadFileIcon />
                                        <VisuallyHiddenInput
                                            type="file"
                                            onChange={
                                            (e) =>{
                                                this.setState({
                                                    invoiceFile:e.target.files[0],
                                                })
                                                this.setState({
                                                  chaoseFile:true,
                                                  select_InvoiceFile:true
                                                })
                                                this.handleUploadFile(e.target.files[0])
                                            }
                                            }
                                            // multiple
                                        />
                                        </Button>
                                    )
                                    }
                                    </InputAdornment>}
                                label="Invoice File" 
                                />
                        </FormControl>  
                    </Box>
                    <Box
                        sx={{
                        display:'flex',
                        alignItems:'center'
                        }}
                    >
                      {this.state.submit ? (
                          <>
                            <Button 
                                sx={{
                                  backgroundColor:purple[400],
                                  marginRight:1
                                }}
                                startDecorator={
                                  <CircularProgress variant="solid"/>
                                }
                            >
                                Loading…
                            </Button>
                          </>
                        ) : (
                          <ColorButton 
                              variant="contained"
                              sx={{
                                marginRight:1
                              }}
                              onClick={() =>{
                                this.handleSubmit()
                                }
                              }   
                          >
                            Submit
                          </ColorButton>
                        )}
                        <ColorButton 
                            variant="contained"
                            sx={{
                                marginRight:1
                            }}
                            onClick={() => {
                              this.props.onClose()
                            }}
                        >
                          Close
                        </ColorButton>
                    </Box>
              </Box>
            </Box>
            </Card>
        </Modal>
      </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    navigateHook,
    dashboardHook
  )(InvoiceEdit)