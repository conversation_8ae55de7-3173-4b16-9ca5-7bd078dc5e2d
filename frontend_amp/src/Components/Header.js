import React, { Component, useContext } from 'react'
import AppBar from '@mui/material/AppBar';
import { Avatar, Badge, Box, Checkbox, createMuiTheme, FormControlLabel, IconButton, Menu, Skeleton, TextField } from '@mui/material';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import MenuIcon from '@mui/icons-material/Menu';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/List';
import Divider from '@mui/material/Divider';
import PersonAdd from '@mui/icons-material/PersonAdd';
import Settings from '@mui/icons-material/Settings';
import Logout from '@mui/icons-material/Logout';
import LightModeIcon from '@mui/icons-material/LightMode';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import themeHook from '../HOK/ThemeHook';
import authHook from '../HOK/AuthHook';
import { compose } from 'redux';
import DashboardIcon from '@mui/icons-material/Dashboard';
import { blue, green, grey, red } from '@mui/material/colors';
import navigateHook from '../HOK/NavigateHook';
import logo from '../static/img/logo.png'
import NotificationsIcon from '@mui/icons-material/Notifications';
import MarkChatReadIcon from '@mui/icons-material/MarkChatRead';
import { PieChart } from '@mui/x-charts/PieChart';
import { GoRead } from "react-icons/go";
import { ColorButton } from './ColorButton';
import { formatDate } from '../utils/functions';
import { DEV_BASE_URL } from '../Environments';
import { TbNews } from "react-icons/tb";
import dashboardHook from '../HOK/DashboardHook';
import { Button } from '@mui/joy';
import BlogAdd from './blogfeedback/BlogAdd';
import BlogPreview from './blogfeedback/BlogPreview';
import AlertNotes from './AlertNotes';
import { Link, replace } from 'react-router-dom';
import { Navigate } from 'react-router';
import { TbMenu2 } from "react-icons/tb";
import { useSidebar } from './SideBar';
import ComboboxWithStatus from './ComboboxWithStatus';
class Header extends Component {
  constructor(props){
    super(props)
    this.state={
      theme:props.theme,
      darkTheme:props.theme.darkTheme,
      auth:props.auth,
      sendForAPI:false,
      notificationQuantity:0,
      notificationOpen:false,
      notificationAnchorEl:null,
      notificationData:Object(null),
      newsQuantity:0,
      newsOpen:false,
      newsAnchorEl:null,
      blogStatusAdd:false,
      blogStatusPreview:false,
      newsStatusShow:false,
      newsShowIndex:0,
      rowsItem:Object(null),
      newsData:Object(null),
      profileAnchorEl:null,
      profileOpen:false,
      themeOpen:false,
      themeAnchorEl:null,
      isLoading:true,
      showAlert:false,
      listMarkNotification:[],
      selectNotification:false,
      infoAlert:{ msg: '', status: undefined },
      sidebarCollapsed: false,
    }
    this.handleChangeTheme=this.handleChangeTheme.bind(this)
    this.handleCloseChangeThemeArea=this.handleCloseChangeThemeArea.bind(this)
    this.handleCloseNotificationArea=this.handleCloseNotificationArea.bind(this)
    this.handleNotificationClick=this.handleNotificationClick.bind(this)
    this.handleCloseNewsArea=this.handleCloseNewsArea.bind(this)
    this.handleNewsClick=this.handleNewsClick.bind(this)
    this.handleClick=this.handleClick.bind(this)
    this.handleClose=this.handleClose.bind(this)
    this.handleStatus=this.handleStatus.bind(this)
    this.showAlertNotes=this.showAlertNotes.bind(this)
    this.toggleSidebar = this.toggleSidebar.bind(this)
    this.handleCloseModal=this.handleCloseModal.bind(this)

  }
  toggleSidebar() {
    this.setState({
      sidebarCollapsed: !this.state.sidebarCollapsed
    });
  }
  async componentDidMount(){
    const theme=this.props.theme
    this.setState({
      theme:this.props.theme,
      darkTheme:theme.darkTheme
    })
    
    // Load user info and notifications
    try {
      let response = await this.props.auth.getUserInfoAction(await this.props.auth.token)
      
      if (await this.props.auth.user) {
        // Load notifications regardless of user type
        let responseNotif = await this.props.auth.getNotifications(await this.props.auth.token)
        
        if (responseNotif.status === 200 && responseNotif.msg === "success") {
          let notificationData = await this.props.auth.notificationData
          let q = 0
          
          // Count unread notifications
          Object.keys(notificationData).forEach((notification) => {
            if (notificationData[notification].unread) {
              q += 1
            }
          })
          
          // Update state with notification data
          this.setState({
            notificationData: notificationData,
            notificationQuantity: q,
            isLoading: false
          })
        }
        
        // Load blogs for non-admin users
        if (!this.props.auth.user.is_admin) {
          let responseblogs = await this.props.dashboard.getBlog(await this.props.auth.token)
          if (responseblogs.status === 200 && responseblogs.msg === "success") {
            let q = 0
            Object.keys(this.props.dashboard.blogsData).forEach((blog) => {
              q += 1
            })
            this.setState({
              newsData: await this.props.dashboard.blogsData,
              newsQuantity: q
            })
          }
        }
      }
      
      this.setState({
        isLoading: false
      })
      
    } catch (error) {
      console.error('Error loading initial data:', error)
      this.setState({
        isLoading: false
      })
    }
    
    // Set up polling for notifications
    setInterval(async () => {
      if (await this.props.auth.user) {
        try {
          let responseNotif = await this.props.auth.getNotifications(await this.props.auth.token)
          if (responseNotif.status === 200 && responseNotif.msg === "success") {
            let notificationData = await this.props.auth.notificationData
            let q = 0
            
            Object.keys(notificationData).forEach((notification) => {
              if (notificationData[notification].unread) {
                q += 1
              }
            })
            
            this.setState({
              notificationData: notificationData,
              notificationQuantity: q,
              isLoading: false
            })
          }
        } catch (error) {
          console.error('Error in notification polling:', error)
        }
      }
    }, 900000) // 15 minutes
    
    // Set up polling for notification updates
    setInterval(async () => {
      if (this.state.sendForAPI) {
        try {
          let q = 0
          Object.keys(this.props.auth.notificationData).forEach((notification) => {
            if (this.props.auth.notificationData[notification].unread) {
              q += 1
            }
          })
          
          this.setState({
            notificationData: await this.props.auth.notificationData,
            notificationQuantity: q,
            isLoading: false,
            sendForAPI: false
          })
        } catch (error) {
          console.error('Error in notification update polling:', error)
          this.setState({
            sendForAPI: false,
            isLoading: false
          })
        }
      }
    }, 3000)
  }
  async handleReadNotification(item){
    console.log(item)
    let notificationDat={
      id:item.id,
      // admin_id:item.admin_id
    }
    let response=await this.props.auth.readNotifications(this.props.auth.token,notificationDat)
    if(response.status!=200){

    }else{
      if(response.msg=="success"){
        this.setState({
          sendForAPI:true
        })
      }else{

      }
    }
  }
  async handleReadMarkNotification(){}
  async handleReadAllNotification(){
    let response=await this.props.auth.readAllNotifications(this.props.auth.token)
    if(response.status!=200){

    }else{
      if(response.msg=="success"){
        this.setState({
          sendForAPI:true
        })
      }else{

      }
    }
  }
  handleClick(event) {
    this.setState({
      profileAnchorEl:event.currentTarget,
      profileOpen:true
    });
  };
  handleNotificationClick(event){
    this.setState({
      notificationAnchorEl:event.currentTarget,
      notificationOpen:true
    })
  }
  handleNewsClick(event){
    this.setState({
      newsAnchorEl:event.currentTarget,
      newsOpen:true
    })
  }
  handleCloseNewsArea(){
    this.setState({
      newsAnchorEl:null,
      newsOpen:false
    });;
  }
  handleChangeTheme(event){
    this.setState({
      themeAnchorEl:event.currentTarget,
      themeOpen:true,
    })
  }
  handleCloseNotificationArea(){
    this.setState({
      notificationAnchorEl:null,
      notificationOpen:false
    });;
  }
  handleCloseChangeThemeArea(){
    this.setState({
      themeAnchorEl:null,
      themeOpen:false,
    })
  }
  handleClose (){
    this.setState({
      profileAnchorEl:null,
      profileOpen:false,
    });;
  };
  showAlertNotes(){
    this.setState({
      showAlert:true
    })
    setTimeout(() => {
      this.setState({
        showAlert:false,
        infoAlert:{ msg: '', status: undefined }
      })
    }, 3000);
  }
  handleStatus(value,rowsItem,order){
    // console.log(value,rowsItem,order)
    if(value===1){
      this.setState({
        blogStatusAdd:true,
        rowsItem:rowsItem
      })
    }
    if(value===3){
      this.setState({
        blogStatusPreview:true,
        rowsItem:rowsItem
      })
    }
  }
  handleCloseModal(){
    this.setState({
      blogStatusAdd:false,
      blogStatusPreview:false,
      // newsStatusShow:false,
    })
    this.shouldComponentUpdate=true
    this.forceUpdate()
  }
  
  render() {
    const platforms = [
      {
        label: 'R',
        value: 120,
      },
      {
        label: 'G',
        value: 120,
      },
      {
        label: 'B',
        value: 120,
      }
    ]
    const colorPerItem = [
      { ...platforms[0], color: red.A200 },
      { ...platforms[1], color: green.A200 },
      { ...platforms[2], color: blue.A200 },

    ];
    const pieParams = {
      height: 200,
      margin: { right: 1 },
      slotProps: { 
        tooltip: {
          enabled: false
        },
        legend:{
          hidden:true,
        },
        hover: {
          mode: null
        }
      },
      // events: [],
      // onHover: (event) => {
      //     event.native.target.style.cursor = 'default';
      // },
    };
    return this.state.isLoading?(
      <>
        <Skeleton 
        sx={{
          height:"65px"
        }}/>
      </>
    ):(
      <>
        {this.state.showAlert && (
            <AlertNotes 
            message={this.state.infoAlert.msg} 
            action={this.state.infoAlert.status} 
            />
        )}
        {this.state.blogStatusAdd&&(
          <BlogAdd
            data={this.state.statusAdd}
            onClose={this.handleCloseModal}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
        {this.state.blogStatusPreview&&(
          <BlogPreview
            data={this.state.statusAdd}
            onClose={this.handleCloseModal}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
          <Box sx={{ flexGrow: 1 }}>
            <AppBar 
              position="static"
              sx={{
                height:'80px',
                borderBottom:'1px solid #e5e7eb',
                borderLeft:'0px solid',
                boxShadow:'none',
                backgroundColor:this.props.theme.selectTheme.background,
                color:this.props.theme.selectTheme.fontColor
              }}
              >
                <Toolbar 
                  sx={{
                    backgroundColor:this.props.theme.selectTheme.background,
                    color:this.props.theme.selectTheme.fontColor,
                    display:'flex',
                    justifyContent:'space-between',
                    alignItems:'center',
                    height:'64px',
                    paddingLeft:'0px',
                    paddingRight:'0px',
                    // Make toolbar more compact on mobile
                    minHeight: { xs: '56px', sm: '64px' },
                    // Ensure toolbar adjusts with sidebar changes
                    transition: 'padding 0.3s ease-in-out',
                  }}
                >
                  <Box 
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      width: '100%',
                    }}
                  >
                    {/* Left side: Combined toggle button and ComboboxWithStatus */}
                    <Box 
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        flexGrow: 0,
                        flexShrink: 0,
                        width: { xs: '100%', md: 'auto' },
                        maxWidth: { xs: '100%', md: '300px' },
                      }}
                    >
                      {/* <IconButton
                        edge="start"
                        color="inherit"
                        aria-label="menu"
                        onClick={this.props.toggleSidebar}
                        sx={{
                          color: this.props.theme.selectTheme.fontColor,
                        }}
                      >
                        <TbMenu2 size={24} />
                      </IconButton> */}

                      {/* Only show ComboboxWithStatus if user is admin */}
                      {(this.props.sidebarCollapsed)?(
                        (this.props.auth.user && this.props.auth.user.is_admin) ? (
                            <Box sx={{ 
                              flexGrow: 1,
                              minWidth: '180px',
                              maxWidth: '250px',
                              display: { xs: 'none', sm: 'block' }
                            }}>
                              <ComboboxWithStatus />
                            </Box>
                        ):(
                           <Box 
                            sx={{
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center',
                              flexGrow: 1,
                            }}
                          >
                            <IconButton
                              size="large"
                              edge="start"
                              color="white"
                              aria-label="menu"
                              sx={{
                                mr: 1,
                                color: this.props.theme.darkTheme ?
                                  this.props.theme.palettes.dark.fontColor :
                                  this.props.theme.palettes.light.fontColor,
                              }}
                            >
                                <Avatar src={logo} />
                            </IconButton>
                            <Typography 
                              sx={{ 
                                display: { xs: 'none', sm: 'flex' },
                                color: this.props.theme.selectTheme.fontColor,
                                alignItems: 'center',
                              }}>
                              A MEDICAL PARTNER
                            </Typography>
                          </Box>
                        )
                      ):(
                         <Box 
                          sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            flexGrow: 1,
                          }}
                        >
                          <IconButton
                            size="large"
                            edge="start"
                            color="white"
                            aria-label="menu"
                            sx={{
                              mr: 1,
                              color: this.props.theme.darkTheme ?
                                this.props.theme.palettes.dark.fontColor :
                                this.props.theme.palettes.light.fontColor,
                            }}
                          >
                              <Avatar src={logo} />
                          </IconButton>
                          <Typography 
                            sx={{ 
                              display: { xs: 'none', sm: 'flex' },
                              color: this.props.theme.selectTheme.fontColor,
                              alignItems: 'center',
                            }}>
                            A MEDICAL PARTNER
                          </Typography>
                        </Box>
                      )}
                    </Box>
                    {/* Center: Logo and app name */}
                   
                    <Box 
                    sx={{
                      display:'flex',
                      justifyContent:'space-around',
                      alignItems:'center',
                      color:this.props.theme.selectTheme.fontColor,
                    }}
                  >
                      {this.props.auth.user?
                        (this.props.auth.user.is_admin?
                          (
                            this.props.auth.selectUser&&
                            (this.props.auth.selectUser.dental_logo?
                              (
                                <img src={DEV_BASE_URL+'/'+this.props.auth.selectUser.dental_logo}/>
                              ):(
                                <Typography 
                                  variant="h6"
                                  // component="div"
                                  sx={{ 
                                    display:'flex',
                                    alignItems:'center',
                                    color:this.props.theme.selectTheme.fontColor,
                                    }}
                                >
                                  {this.props.auth.selectUser.first_name}
                                </Typography>
                              )
                            )
                          ):(
                            this.props.auth.user.dental_logo?(
                              <img 
                                src={
                                    DEV_BASE_URL+'/'+(this.props.user&&(
                                      this.props.user.user_group==3?
                                      this.props.auth.user.dental_logo:this.props.auth.user.ref_admin.dental_logo))
                                    }
                                  />
                            ):(
                              <Typography 
                                variant="h6"
                                // component="div"
                                sx={{ 
                                  display:'flex',
                                  alignItems:'center',
                                  color:this.props.theme.selectTheme.fontColor,
                                  }}
                              >

                                {this.props.user&&(this.props.user.user_group==3?
                                    (
                                      this.props.auth.user.first_name
                                    ):(
                                      this.props.auth.user.ref_admin.first_name
                                    )
                                  )
                                }
                              </Typography>
                            )
                          )
                        ):(
                          <img src={logo}/>
                        )
                      }
                    </Box>
                    {/* Right side: blogUser info, notifications, etc. */}
                    <Box 
                      sx={{
                        display:'flex',
                        justifyContent:'space-around'
                      }}
                    >
                      {/* Theme */}
                      <Box
                        sx={{
                          width:'100%',
                          height:'64px',
                          display:'flex',
                          justifyContent:'center',
                          alignItems:'center',
                        }}
                      >
                        <PieChart
                          height={24}
                          width={24}
                          onItemClick={(e)=>{this.handleChangeTheme(e)}}
                          // axisHighlight={false}
                          series={[
                              {
                                data: colorPerItem,
                              },
                          ]}
                          {...pieParams}
                          />
                        <Menu
                            anchorEl={this.state.themeAnchorEl}
                            id="account-menu"
                            open={this.state.themeOpen}
                            onClose={this.handleCloseChangeThemeArea}
                            onClick={this.handleCloseChangeThemeArea}
                            slotProps={{
                              paper: {
                                elevation: 0,
                                sx: {
                                  overflow: 'visible',
                                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                                  mt: 1.5,
                                  '& .MuiAvatar-root': {
                                    width: 32,
                                    height: 32,
                                    ml: -0.5,
                                    mr: 1,
                                  },
                                  '&::before': {
                                    content: '""',
                                    display: 'block',
                                    position: 'absolute',
                                    top: 0,
                                    right: 14,
                                    width: 10,
                                    height: 10,
                                    bgcolor: 'background.paper',
                                    transform: 'translateY(-50%) rotate(45deg)',
                                    zIndex: 0,
                                  },
                                },
                              },
                            }}
                            sx={{
                              display:'flex',
                              flexDirection:'column'
                            }}
                            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                          >
                            {Object.keys(this.props.theme.palettes).map((palette)=>{
                              const item=this.props.theme.palettes[palette]
                                  return(
                                      <MenuItem 
                                        key={palette}
                                        onClick={()=>{
                                            console.log(item,palette)
                                            this.props.theme.changeTheme(item,palette)
                                          }
                                        }
                                        >
                                        <Box
                                          sx={{
                                            width:"100%",
                                            display:'flex',
                                            flexDirection:'row',
                                            justifyContent:'space-between',
                                            alignItems:'center'
                                          }}
                                        >
                                          <IconButton 
                                            sx={{
                                              width:'24px',
                                              height:'24px',
                                              background:item.background,
                                            }}
                                          >
                                          </IconButton>
                                        </Box>
                                      </MenuItem>
                                  )
                              })
                            }
                          </Menu>
                      </Box>
                      {/* Blogs */}
                      <Box
                      sx={{
                          width:'100%',
                          height:'64px',
                          display:'flex',
                          justifyContent:'center',
                          alignItems:'center',
                        }}
                      >
                        <>
                          <IconButton
                            id="blogs-button"
                            onClick={(e)=>{
                                  this.handleNewsClick(e)
                              }
                            }
                            size="small"
                            sx={{ ml: 2 }}
                            aria-label="more"
                            aria-haspopup="true"                        
                            aria-controls={this.state.newsOpen ? 'blogs-menu' : undefined}
                            aria-expanded={this.state.newsOpen ? 'true' : undefined}
                          >
                            <TbNews 
                                sx={{
                                  color:this.props.theme.selectTheme.fontColor
                                }}
                                color={this.props.theme.selectTheme.fontColor}/>
                          </IconButton>
                          <Menu
                            anchorEl={this.state.newsAnchorEl}
                            id="blogs-menu"
                            open={this.state.newsOpen}
                            onClose={this.handleCloseNewsArea}
                            onClick={this.handleCloseNewsArea}
                            MenuListProps={{
                              'aria-labelledby': 'blogs-button',
                            }}
                            slotProps={{
                              paper: {
                                style: {
                                  maxHeight: 100 * 4.5,
                                },
                                elevation: 0,
                                sx: {
                                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                                  margin:1,
                                  textAlign:'center'
                                },
                              },
                            }}
                            sx={{
                              width:'100%',
                              display:'flex',
                              flexDirection:'column',
                              justifyContent:'center',
                              alignContent:'center',
                              alignItems:'center',
                              textAlign:'center',
                              marginLeft:'2',
                              gap:'1'
                            }}
                            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                          >
                            {this.props.dashboard.blogsData &&
                            (this.props.dashboard.blogsData.length?(
                              Object.keys(this.props.dashboard.blogsData).map((blog) => {
                                const item = this.props.dashboard.blogsData[blog];
                                return (
                                  <MenuItem 
                                    key={item.id} // Use a unique identifier like `item.id`
                                    onClick={() => {
                                      this.props.navigate("/blogs-by-id/" + item.id, {
                                        state: { id: item.id },
                                        replace: true,
                                      });
                                    }}
                                  >
                                    <Box
                                      sx={{
                                        width:"100%",
                                        display:'flex',
                                        flexDirection:'column',
                                        alignItems:'center'
                                      }}
                                    >
                                      <Box
                                        sx={{
                                          width:"100%",
                                          display:'flex',
                                          flexDirection:'row',
                                          justifyContent:'space-between',
                                          alignContent:'center',
                                          alignItems:'center'
                                        }}
                                      >
                                        <Typography>
                                          {item.post_title}
                                        </Typography>
                                        <Typography 
                                          sx={{
                                            fontSize:'8px',
                                          }}
                                        >
                                          {formatDate(
                                            'Amm-dd-yyyy',item.created_at)}
                                        </Typography>
                                      </Box>
                                      <Box
                                        sx={{
                                          width:'100%',
                                          marginTop:1,
                                          padding:1
                                        }}
                                      >
                                        <Typography
                                          // variant='p'
                                          // style={{whiteSpace: 'pre-line'}}
                                          display="block"
                                          sx={{
                                            // display:'block',
                                            fontSize:'11px',
                                            whiteSpace:'pre-wrap'
                                          }}
                                        >                   
                                          {item.post_description}
                                        </Typography>
                                      </Box>
                                    </Box>
                                  </MenuItem>
                                );
                              })
                            ):(
                                <MenuItem 
                                  sx={{
                                    width:'200px',
                                    borderBottom:'1px solid #e5e7eb',
                                    textAlign:'center',
                                    paddingX:'2',
                                    marginBottom:'2',
                                    justifyContent: 'space-between'
                                  }}
                                  key={"no-data"}
                                >
                                  <Typography>
                                    No Blogs
                                  </Typography>
                                </MenuItem>
                              )
                            )}
                            <MenuItem 
                              sx={{
                                width:'200px',
                                borderBottom:'1px solid #e5e7eb',
                                textAlign:'center',
                                paddingX:'2',
                                marginBottom:'2',
                                justifyContent: 'space-between',
                                fontSize:'10px'

                              }}
                              key={'all'}
                              onClick={()=>{
                                this.props.navigate("/blogs/")
                              }}
                            >
                              <Box
                                sx={{
                                  width:'100%',
                                  justifyContent:'flex-end',
                                  flexDirection:'row',
                                  marginTop:1
                                }}
                              > 
                                {this.props.auth.user?
                                  (this.props.auth.user.is_admin&&(
                                    <Button
                                      variant='soft'
                                      sx={{
                                        fontSize:'10px',
                                        marginRight:2
                                      }}
                                      onClick={()=>{
                                        this.handleStatus(1)
                                      }}
                                    >
                                      Add 
                                    </Button>
                                  )):(
                                    <></>
                                  )
                                  }
                                
                                <Button
                                  variant='soft'
                                  sx={{
                                    fontSize:'10px'
                                  }}
                                  onClick={()=>{
                                    this.props.navigate("/blogs/")
                                  }}
                                >
                                  See All
                                </Button>
                              </Box>
                            </MenuItem>
                          </Menu>
                        </>
                      </Box>
                      {/* Notifications */}
                      <Box
                        sx={{
                            width:'100%',
                            height:'64px',
                            display:'flex',
                            justifyContent:'center',
                            alignItems:'center',
                          }}
                      >
                        <>
                          <IconButton
                            id="notification-button"
                            onClick={(e)=>{
                                  this.handleNotificationClick(e)
                              }
                            }
                            size="small"
                            sx={{ ml: 2 }}
                            aria-label="more"
                            aria-haspopup="true"
                            aria-controls={this.state.notificationOpen ? 'notification-menu' : undefined}
                            aria-expanded={this.state.notificationOpen ? 'true' : undefined}
                          >
                            <Badge
                              anchorOrigin={{
                                vertical: 'bottom',
                                horizontal: 'right',
                              }} 
                              badgeContent={this.state.notificationQuantity==0?0:this.state.notificationQuantity} 
                              color="success"
                            >
                              <NotificationsIcon  
                                sx={{
                                  color:this.props.theme.selectTheme.fontColor
                                }}
                                color={this.props.theme.selectTheme.fontColor}/>
                            </Badge>
                          </IconButton>
                          <Menu
                            anchorEl={this.state.notificationAnchorEl}
                            id="notification-menu"
                            open={this.state.notificationOpen}
                            onClose={this.handleCloseNotificationArea}
                            MenuListProps={{
                              'aria-labelledby': 'notification-button',
                            }}
                            slotProps={{
                              paper: {
                                style: {
                                  maxHeight: 100 * 4.5,
                                },
                                elevation: 0,
                                sx: {
                                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                                  margin:1,
                                  textAlign:'center'
                                },
                              },
                            }}
                            sx={{
                              display:'flex',
                              flexDirection:'column',
                              justifyContent:'center',
                              alignContent:'center',
                              alignItems:'center',
                              textAlign:'center',
                              marginLeft:'2',
                              gap:'10'
                            }}
                            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                          >
                            {this.props.auth.notificationData.length?(
                              Object.keys(this.props.auth.notificationData).map((notification)=>{
                              const item=this.props.auth.notificationData[notification]
                                return(
                                  <MenuItem 
                                    sx={{
                                      width:'300px',
                                      borderBottom:'1px solid #e5e7eb',
                                      textAlign:'center',
                                      paddingX:'2',
                                      marginBottom:'2',
                                      justifyContent: 'space-between',
                                      fontSize:'10px'

                                    }}
                                    key={notification}
                                    onClick={()=>{
                                    // this.props.navigate("/dashboard")
                                  }}>
                                    <Box
                                      sx={{
                                        width:"100%",
                                        display:'flex',
                                        flexDirection:'row',
                                        justifyContent:'space-between',
                                        alignItems:'center'
                                      }}
                                    >
                                      {this.state.selectNotification&&(
                                        <FormControlLabel 
                                          control={
                                            <Checkbox
                                              sx={{
                                                color:'inherit'
                                              }}
                                            />
                                          }  
                                          checked={this.state.listMarkNotification.includes(item.id)}
                                          onClick={()=>{
                                            console.log(this.state.listMarkNotification)
                                            let listmarknotification=this.state.listMarkNotification
                                            if (listmarknotification.includes(item.id)){
                                              listmarknotification.filter((_, i) => _ !==item.id)
                                              this.setState({
                                                listMarkNotification:listmarknotification
                                              })
                                            }else{
                                              listmarknotification.push(item.id)
                                              this.setState({
                                                listMarkNotification:listmarknotification
                                              })
                                            }
                                          }
                                          }
                                        />
                                      )}
                                      {item.unread&&(
                                        <span
                                          style={{
                                            display:'block',
                                            width:'16px',
                                            height:'16px',
                                            marginLeft:'auto',
                                            marginRight:2,
                                            padding:1,
                                            backgroundColor:green[600],
                                            borderRadius:'50%',
                                            border:2,
                                          }}
                                        ></span>
                                      )}
                                      <Box
                                        sx={{
                                          width:"100%",
                                          display:'flex',
                                          flexDirection:'column',
                                          justifyContent:'left',
                                          alignItems:'flex-start'
                                        }}
                                      >
                                        <Typography 
                                            sx={{
                                              marginRight:2,
                                              fontSize:'10px',
                                              whiteSpace:'pre-wrap'
                                            }}
                                          >
                                            {this.props.auth.user&&(
                                              this.props.auth.user.is_admin?(
                                                <>
                                                  {item.description}
                                                  {/* user {item.recipient?item.recipient.first_name:'Undifined'} have  new Order  */}
                                                </>
                                              ):(
                                                <>
                                                  Inventory low for {item.inventory_object?item.inventory_object.product.name:'unAccess Product'}
                                                </>
                                              )
                                            )}
                                        </Typography>
                                        <Typography 
                                          sx={{
                                            fontSize:'8px',
                                            marginRight:2}}
                                        >
                                          {formatDate(
                                            'Amm-dd-yyyy',item.timestamp)}
                                        </Typography>
                                      </Box>
                                      {item.unread&&(
                                        <ListItemIcon>
                                          <IconButton 
                                            sx={{
                                            }}
                                            onClick={()=>{
                                              this.handleReadNotification(item)
                                            }}
                                          >
                                            <GoRead color={green[400]}/>
                                          </IconButton>
                                        </ListItemIcon>
                                      )}
                                    </Box>
                                  </MenuItem>
                                )
                              })
                            ):(
                                <MenuItem 
                                  sx={{
                                    width:'300px',
                                    borderBottom:'1px solid #e5e7eb',
                                    textAlign:'center',
                                    paddingX:'2',
                                    marginBottom:'2',
                                    justifyContent: 'space-between'
                                  }}
                                  key={"no-data"}
                                >
                                  <Typography>
                                    No Notification
                                  </Typography>
                                </MenuItem>
                              )
                            }
                            <MenuItem 
                              sx={{
                                width:'100%',
                                borderBottom:'1px solid #e5e7eb',
                                textAlign:'center',
                                paddingX:'2',
                                marginBottom:'2',
                                justifyContent: 'space-between',
                                fontSize:'10px',
                                position:'absolute'
                              }}
                              key={'all'}
                            >
                              <Box
                                sx={{
                                  width:'100%',
                                  justifyContent:'flex-end',
                                  flexDirection:'row',
                                  marginTop:1
                                }}
                              > 
                                <Button
                                  variant='soft'
                                  sx={{
                                    fontSize:'10px'
                                  }}
                                  onClick={()=>{
                                    if(this.state.selectNotification){
                                      this.handleReadMarkNotification()
                                    }else{
                                      this.handleReadAllNotification()
                                    }
                                  }}
                                >
                                  {this.state.selectNotification?(
                                    <Typography>
                                      Mark To Read  
                                    </Typography>
                                  ):(
                                    <Typography>
                                      Read All                                  
                                    </Typography>
                                  )}
                                </Button>
                              </Box>
                            </MenuItem>
                          </Menu>
                        </>
                      </Box>
                      {/* Profile */}
                      <Box
                      sx={{
                          width:'100%',
                          height:'64px',
                          display:'flex',
                          justifyContent:'center',
                          alignItems:'center',
                        }}
                      >
                        <IconButton
                          onClick={(e)=>{this.handleClick(e)}}
                          size="small"
                          sx={{ ml: 2 }}
                          aria-controls={this.state.profileOpen ? 'account-menu' : undefined}
                          aria-haspopup="true"
                          aria-expanded={this.state.profileOpen ? 'true' : undefined}
                        >
                          <Avatar />
                        </IconButton>
                        <Menu
                          anchorEl={this.state.profileAnchorEl}
                          id="account-menu"
                          open={this.state.profileOpen}
                          onClose={this.handleClose}
                          onClick={this.handleClose}
                          slotProps={{
                            paper: {
                              elevation: 0,
                              sx: {
                                overflow: 'visible',
                                filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                                mt: 1.5,
                                '& .MuiAvatar-root': {
                                  width: 32,
                                  height: 32,
                                  ml: -0.5,
                                  mr: 1,
                                },
                                '&::before': {
                                  content: '""',
                                  display: 'block',
                                  position: 'absolute',
                                  top: 0,
                                  right: 14,
                                  width: 10,
                                  height: 10,
                                  bgcolor: 'background.paper',
                                  transform: 'translateY(-50%) rotate(45deg)',
                                  zIndex: 0,
                                },
                              },
                            },
                          }}
                          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                        >
                          <MenuItem >
                            <Avatar /> Profile
                          </MenuItem>
                          <MenuItem onClick={()=>{
                            this.props.navigate("/dashboard")
                          }}>
                            <ListItemIcon>
                              <DashboardIcon />
                            </ListItemIcon>
                            Dashboard
                          </MenuItem>
                          <Divider />
                          {/* <MenuItem >
                            <ListItemIcon>
                              <Settings fontSize="small" />
                            </ListItemIcon>
                            Settings
                          </MenuItem> */}
                          <MenuItem onClick={this.props.auth.logOut}>
                            <ListItemIcon>
                              <Logout fontSize="small" />
                            </ListItemIcon>
                            Logout
                          </MenuItem>
                        </Menu>
                      </Box>
                    </Box>
                  </Box>
                </Toolbar>
            </AppBar>
          </Box>
      </>
    )
  }
}
export default compose(
  themeHook,
  authHook,
  navigateHook,
  dashboardHook
)(Header)
// export default (themeHook,authHook)(Header)
