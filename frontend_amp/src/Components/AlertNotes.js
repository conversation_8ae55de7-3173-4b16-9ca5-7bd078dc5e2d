import { <PERSON><PERSON>, Button, IconButton, Snackbar } from '@mui/material';
import React, {Component } from 'react';
// import { HiCheckCircle, HiXCircle } from 'react-icons/hi';
import CloseIcon from '@mui/icons-material/Close';

class AlertNotes extends Component{
    constructor(props){
        super(props)
        this.state={
            open:true,
            isVisible:true,
            vertical: 'top',
            horizontal: 'right',
            message:props.message,
            status:props.action,
        }
        this.handleClose=this.handleClose.bind(this)
    }
    handleClick (){
        this.setState({
            open:true
        });
    };
    handleClose (event,reason) {
        if (reason === 'clickaway') {
            return;
        }
        this.setState({
            open:false
        });
    };
    
    render(){
        let color = this.state.status ? 'green' : 'red';
        const vertical=this.state.vertical
        const horizontal=this.state.horizontal
        const Toast=(
            <React.Fragment>
                <Button color="secondary" size="small" onClick={this.handleClose}>
                    UNDO
                </Button>
                <IconButton
                    size="small"
                    aria-label="close"
                    color="inherit"
                    onClick={this.handleClose}
                >
                    <CloseIcon fontSize="small" />
                </IconButton>
            </React.Fragment>
        )
        return(
            <>  
                <Snackbar
                    anchorOrigin={{ vertical, horizontal }}
                    open={this.state.open}
                    autoHideDuration={12000}
                    onClose={this.handleClose}
                    key={this.state.vertical + this.state.horizontal}
                    // message={this.state.message ?? 'Changes were successfully saved'}
                    // action={Toast}
                >
                    <Alert
                        onClose={this.handleClose}
                        severity={this.state.status?'success':'error'}
                        variant="standard"
                        sx={{ width: '100%' }}
                    >
                        {/* {this.state.status ? (
                            <HiCheckCircle size={24} />
                        ) : (
                            <HiXCircle size={24} />
                        )} */}
                        {this.state.message ?? 'Changes were successfully saved'}
                    </Alert>
                     
                </Snackbar>
            </>
        )
    }
}

export default AlertNotes;
