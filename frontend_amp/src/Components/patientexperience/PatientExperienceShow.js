
import React, { Component } from 'react'
import { Avatar, Box, Button, Card, CircularProgress, IconButton, Modal, TextField, Typography } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close';
import { ColorButton } from '../ColorButton';
import { Label, Visibility, VisibilityOff } from '@mui/icons-material';
import { grey } from '@mui/material/colors';
export default class PatientExperienceShow extends Component {
  constructor(props){
    super(props)
    this.state={
    }
  }

  render() {
      return (
        <>
          <Modal
          disablePortal
          disableEnforceFocus
          disableAutoFocus
          open
          aria-labelledby="server-modal-title"
          aria-describedby="server-modal-description"
          sx={{
            display: 'flex',
            p: 1,
            alignItems: 'center',
            justifyContent: 'center',
          }}
          // onClose={this.props.onClose}
          // container={() => rootRef.current}
        >
          <Card
            sx={{
                width:'40%',
                maxHeight:'60%',
                overflow:'auto',
                padding:4,
                textAlign:'center',
                padding:2,
                // border:'gray',
                // shadow:true
            }}
          >
             <Box
              sx={(theme) => ({
                position: 'relative',
                bgcolor: 'background.paper',
                // boxShadow: theme.shadows[5],
              })}
            >
              <Box
                sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection:'column',
                    textAlign:'left',
                    gap:1,
                    marginBottom:4
                }}
              >
                <Box
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    justifyContent:'space-between'
                  }}
                >
                  <Typography
                    // width={'90%'}
                  >
                  </Typography>
                  <IconButton
                    onClick={() => {
                      this.props.onClose()
                    }}
                  >
                    <CloseIcon  fontSize="small"/>
                  </IconButton>
                </Box>
                <Box
                    sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                  patiant_name:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.patiant_name || '----'}
                  </Typography>
                </Box>
                <Box
                    sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                  Phone Number:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.phone_number || '----'}
                  </Typography>
                </Box>
                <Box
                    sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                  }}
                >
                  <Typography variant='p'>
                  Email:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.email || '----'}
                  </Typography>
                </Box>
                <Box
                    sx={{
                      width: '100%',  
                      borderBottom:`1px ${grey[200]} solid`,
                      paddingTop:1.5,
                      paddingLeft:4,
                      paddingBottom:1,
                      paddingRight:1,
                      alignItems:'center'
                    }}
                >
                  <Typography variant='p'>
                    Preferred Pronouns:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.preferred_pronouns || '----'}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                   }}
                >
                  <Typography variant='p'>
                  Last Visited:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.last_visited || '----'}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',  
                    borderBottom:`1px ${grey[200]} solid`,
                    paddingTop:1.5,
                    paddingLeft:4,
                    paddingBottom:1,
                    paddingRight:1,
                    alignItems:'center'
                   }}
                >
                  <Typography variant='p'>
                    Review:
                  </Typography>
                  <Typography 
                    sx={{
                      color:grey[500],
                      paddingX:1
                    }}
                    variant='p'>
                    {this.props.item.review || '----'}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Card>
        </Modal></>
      )
  }
}
