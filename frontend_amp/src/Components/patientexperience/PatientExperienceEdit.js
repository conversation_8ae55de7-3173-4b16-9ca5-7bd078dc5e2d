import React, { Component } from 'react'
import { Box, Card, IconButton, Modal, TextField, Typography } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close';
import { ColorButton } from '../ColorButton';
import { purple } from '@mui/material/colors';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import Button from '@mui/joy/Button';
import { CircularProgress } from '@mui/joy';
import { compose } from 'redux';
import themeHook from '../../HOK/ThemeHook';
import dashboardHook from '../../HOK/DashboardHook';
import authHook from '../../HOK/AuthHook';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';

class PatientExperienceEdit extends Component {
  constructor(props){
    super(props)
    this.state={
        patiant_name:this.props.item.patiant_name,
        preferred_pronouns:this.props.item.preferred_pronouns,
        phone_number:this.props.item.phone_number,
        email:this.props.item.email,
        last_visited:this.props.item.last_visited,
        review:this.props.item.review,
        submit:false,
    }
  }
  async handleSubmit(){
    this.setState({
      submit:true,
    })
    let patientExperienceData={
      id:this.props.item.id,
      patiant_name:this.state.patiant_name,
      admin_id:null,
      preferred_pronouns:this.state.preferred_pronouns,
      phone_number:this.state.phone_number,
      email:this.state.email,
      last_visited:this.state.last_visited,
      review:this.state.review
    }
    if(this.state.last_visited){
      const last_visited=new Date(this.state.last_visited)
      patientExperienceData.last_visited=`${last_visited.getFullYear()}-${last_visited.getMonth() + 1}-${last_visited.getDate()}`
   }
   else{
      patientExperienceData.last_visited=''
   }
    patientExperienceData.admin_id=this.props.auth.user.is_admin?this.props.auth.selectUser.id:this.props.auth.user.id
    let response=await this.props.dashboard.updatePatientExperience(this.props.auth.token,patientExperienceData)
    if (response.status!=200) {
      this.props.setInfoAlert({
        msg: `Error Updateing PatientExperience: ${response.msg}`,
        status: false,
      })
      this.setState({
        submit:false,
        });
      this.props.showAlertNotes();
      this.props.refetchOrder(true)
    } else {
        if (response.msg == 'success') {
          this.props.setInfoAlert({
            msg: 'PatientExperience Update successfully',
            status: true,
          })
          this.props.refetchOrder(true)
          this.setState({
            submit:false,
          });
          this.props.showAlertNotes();
          this.props.onClose();
        } else {
          this.props.setInfoAlert({
            msg: `Failed to Update PatientExperience: ${response.msg}`,
            status: false,
          })
          this.setState({
            submit:false,
          });
          this.props.showAlertNotes();
          this.props.refetchOrder(true)
        }
    }
  }
  handleShowPassword(arg){
    this.setState({
        showPassword:arg,
    })
}
  render() {
    return (
      <>
        <Modal
        disablePortal
        disableEnforceFocus
        disableAutoFocus
        open
        aria-labelledby="server-modal-title"
        aria-describedby="server-modal-description"
        sx={{
          display: 'flex',
          p: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        // onClose={this.props.onClose}
        // container={() => rootRef.current}
      >
        <Card
          sx={{
              width:'40%',
              maxHeight:'60%',
              overflow:'auto',
              padding:4,
              textAlign:'center',
              padding:2,
              // border:'gray',
              // shadow:true
          }}
        >
           <Box
            sx={(theme) => ({
              position: 'relative',
              bgcolor: 'background.paper',
              // boxShadow: theme.shadows[5],
            })}
          >
            <Box
              sx={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  flexDirection:'column',
                  textAlign:'left',
                  gap:2,
              }}
            >
              <Box
                sx={{
                  display:'flex',
                  flexDirection:'row',
                  justifyContent:'space-between'
                }}
              >
                <Typography
                  // width={'90%'}
                >
                </Typography>
                <IconButton
                  onClick={() => {
                    this.props.onClose()
                  }}
                >
                  <CloseIcon  fontSize="small"/>
                </IconButton>
              </Box>
              <Box>
                <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                  Edit Patient
                </Typography>
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='patiant_name'
                    label="Patiant Name"
                    placeholder='Patiant Name'
                    required
                    type='text'
                    value={this.state.patiant_name}
                    onChange={(e) =>
                        this.setState({
                            patiant_name:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='phone_number'
                    label="Phone Number"
                    placeholder='Phone Number'
                    type='text'
                    value={this.state.phone_number}
                    onChange={(e) =>
                        this.setState({
                            phone_number:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='Email'
                    label="Email"
                    placeholder='Email'
                    type='email'
                    value={this.state.email}
                    onChange={(e) =>
                        this.setState({
                            email:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='preferred_pronouns'
                    label="Preferred Pronouns"
                    placeholder='Preferred Pronouns'
                    type='text'
                    value={this.state.preferred_pronouns}
                    onChange={(e) =>
                        this.setState({
                            preferred_pronouns:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                sx={{ 
                  display: 'flex',
                  position: 'relative',
                  width:'100%'
                  }}
                  
              >
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                    sx={{
                        width:'100%'
                    }}
                    format=' YYYY / MM / DD'
                    label="Last Visited"
                    defaultValue={dayjs(this.state.last_visited)}
                    // value={this.state.timeline}
                    onChange={(newValue) =>{ 
                        this.setState({
                            last_visited:newValue
                        })
                        }
                    }/>
                </LocalizationProvider>
              </Box>
              <Box
                  sx={{
                  width: '100%',                                           
                  }}
              >
                <TextField
                  multiline
                  aria-multiline
                  rows={4}
                  fullWidth
                  size='small'
                  id='review'
                  label="Review"
                  placeholder='Review'
                  type='text'
                  value={this.state.review}
                  onChange={(e) =>
                      this.setState({
                        review:e.target.value
                      })
                  }
                />
              </Box>
              <Box
                sx={{
                  display:'flex',
                  alignItems:'center'
                }}
              >
                {this.state.submit ? (
                  <>
                    <Button 
                      sx={{
                        marginRight:1
                      }}
                      // color={purple}
                      startDecorator={
                        <CircularProgress variant="solid"/>
                      }
                    >
                      Loading…
                    </Button>
                  </>
                ) : (
                  <ColorButton 
                      variant="contained"
                      sx={{
                        marginRight:1
                      }}
                      onClick={() =>this.handleSubmit()}   
                  >
                      Submit
                  </ColorButton>
                )}
                <ColorButton 
                      variant="contained"
                      onClick={() => {
                        this.props.onClose()
                          // setStatus(false);
                          // onClose();
                      }}
                      // onClick={() =>this.handleSignIn()}   
                  >
                      Close
                  </ColorButton>
              </Box>
            </Box>
          </Box>
        </Card>
      </Modal></>
    )
  }
}
export default compose(
  themeHook,
  authHook,
  dashboardHook
)(PatientExperienceEdit)