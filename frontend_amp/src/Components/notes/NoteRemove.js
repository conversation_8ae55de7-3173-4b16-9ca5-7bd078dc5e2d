
import React, { Component } from 'react'
import { Box, Card, IconButton, Modal, Typography } from '@mui/material';
import { ColorButton } from '../ColorButton';
import { purple, red } from '@mui/material/colors';
import CloseIcon from '@mui/icons-material/Close';
import themeHook from '../../HOK/ThemeHook';
import Button from '@mui/joy/Button';
import CircularProgress from '@mui/joy/CircularProgress';
import { compose } from 'redux';
import authHook from '../../HOK/AuthHook';
import dashboardHook from '../../HOK/DashboardHook';
class NoteRemove extends Component {
    constructor(props){
        super(props)
        this.state={
            theme:props.theme,
            submit:false,
        }
      }
    async handleSubmit(){
        this.setState({
            submit:true,
          })
        let noteData={
            id:this.props.item.id,
            admin_id:null,
        }
        if (this.props.auth.user.user_group==2){
            noteData.admin_id=this.props.auth.selectUser.id
          }else if(this.props.auth.user.user_group==3){
            noteData.admin_id=this.props.auth.user.id
          }else if(this.props.auth.user.user_group==4){
            noteData.admin_id=this.props.auth.user.ref_admin.id
          }
          else{
      
          }
        let response=await this.props.dashboard.deleteNotes(this.props.auth.token,noteData)
        if (response.status!=200) {
            this.props.setInfoAlert({
                msg: `Error deleteing Note: ${response.msg}`,
                status: false,
            })
            this.setState({
                submit:false,
            });
            this.props.showAlertNotes();
            this.props.refetchOrder(true)
        } else {
            if (response.msg == 'success') {
                this.props.setInfoAlert({
                    msg: 'Note deleted successfully',
                    status: true,
                })
                this.props.refetchOrder(true)
                this.setState({
                    submit:false,
                });
                this.props.showAlertNotes();
                this.props.onClose();
            } else {
                this.props.setInfoAlert({
                    msg: `Failed to delete Note: ${response.msg}`,
                    status: false,
                })
                this.setState({
                    submit:false,
                });
                this.props.showAlertNotes();
                this.props.refetchOrder(true)
            }
        }
    }
  render() {
    return (
        <>
            <Modal
                disablePortal
                disableEnforceFocus
                disableAutoFocus
                open
                aria-labelledby="server-modal-title"
                aria-describedby="server-modal-description"
                sx={{
                    display: 'flex',
                    p: 1,
                    alignItems: 'center',
                    justifyContent: 'center',
                }}
                // onClose={this.props.onClose}
                // container={() => rootRef.current}
            >
            <Card
                sx={{
                    width:'40%',
                    padding:4,
                    textAlign:'center',
                    padding:2,
                    // backgroundColor:this.props.theme.theme.background_3
                    // border:'gray',
                    // shadow:true
                }}
            >
                <Box
                    sx={(theme) => ({
                        display:'flex',
                        position: 'relative',
                        bgcolor: 'background.paper',
                        justifyContent:'center',
                        // backgroundColor:this.props.theme.theme.background_3,
                        // color:this.props.theme.theme.fontColor_3
                        // boxShadow: theme.shadows[5],
                    })}
                >
                    <Box
                        sx={{
                            width: '95%',
                            height: '100%',
                            display: 'flex',
                            flexDirection:'column',
                            textAlign:'left',
                            justifyContent:'center',
                            gap:2,
                            // backgroundColor:this.props.theme.theme.background_3,
                            // color:this.props.theme.theme.fontColor_3
                        }}
                    >
                        <Box
                            sx={{
                                display:'flex',
                                flexDirection:'row',
                                justifyContent:'space-between'
                            }}
                        >
                            <Typography
                                // width={'90%'}
                            >
                            </Typography>
                            <IconButton
                                onClick={() => {
                                this.props.onClose()
                                }}
                            >
                                <CloseIcon  fontSize="small"/>
                            </IconButton>
                        </Box>
                        <Box
                            sx={{
                                display:'flex',
                                flexDirection:'row',
                                justifyContent:'center'
                            }}    
                        >
                            <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                                Delete Note
                            </Typography>
                        </Box>
                        <Box
                            sx={{
                                width: '100%',
                                display:'flex',
                                flexDirection:'row',
                                justifyContent:'center'
                            }}  
                        >
                            <Typography>
                                {this.props.item.id}
                            </Typography>
                        </Box>
                        <Box
                            sx={{
                                width: '100%',
                                display:'flex',
                                flexDirection:'row',
                                justifyContent:'center'                                           
                            }}
                        >
                            <Typography>
                                Are you sure you want to delete this Inventory ?
                            </Typography>
                        </Box>
                        <Box
                            sx={{
                                display:'flex',
                                justifyContent:'center',
                                alignItems:'center',
                                padding:0,
                                margin:0
                            }}
                        >
                            {this.state.submit ? (
                                <>
                                    <Button 
                                        sx={{
                                            backgroundColor:red[400],
                                            marginRight:1
                                            // marginTop:1
                                        }}
                                        startDecorator={
                                            <CircularProgress variant="solid"/>
                                        }
                                    >
                                        Loading…
                                    </Button>
                                </>
                            ) : (
                                <ColorButton 
                                    variant="contained"
                                    sx={{
                                    marginRight:1,
                                    backgroundColor:red[700]
                                    }}
                                    onClick={() =>this.handleSubmit()}   
                                >
                                    Yes, I am sure
                                </ColorButton>
                            )}
                            <ColorButton 
                                
                                variant="contained"
                                onClick={() => {
                                this.props.onClose()
                                    // setStatus(false);
                                    // onClose();
                                }}
                                // onClick={() =>this.handleSignIn()}   
                            >
                                No, cancel
                            </ColorButton>
                        </Box>
                    </Box>
                </Box>
            </Card>
            </Modal>
        </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    dashboardHook
)(NoteRemove)