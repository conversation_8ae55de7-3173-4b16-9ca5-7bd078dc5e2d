import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { Box, Card, IconButton, Modal, TextField, Typography } from '@mui/material';
import { ColorButton } from '../ColorButton';
import { purple } from '@mui/material/colors';
import CloseIcon from '@mui/icons-material/Close';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import themeHook from '../../HOK/ThemeHook';
import Button from '@mui/joy/Button';
import CircularProgress from '@mui/joy/CircularProgress';
import { compose } from 'redux';
import authHook from '../../HOK/AuthHook';
import dashboardHook from '../../HOK/DashboardHook';
class NoteAdd extends Component {
    constructor(props){
        super(props)
        this.state={
            note:'',
            submit:false,
        }
      }
    async handleSubmit(){
        this.setState({
            submit:true,
            status:false,
          })
          let noteData={
            admin_id:null,
            notes:this.state.note,
          }
          if (this.props.auth.user.user_group==2){
            noteData.admin_id=this.props.auth.selectUser.id
          }else if(this.props.auth.user.user_group==3){
            noteData.admin_id=this.props.auth.user.id
          }else if(this.props.auth.user.user_group==4){
            noteData.admin_id=this.props.auth.user.ref_admin.id
          }
          else{
      
          }
        let response=await this.props.dashboard.insertNotes(this.props.auth.token,noteData)
        if (response.status!=200) {
            this.props.setInfoAlert({
                msg: `Error insrting Note: ${response.msg}`,
                status: false,
              })
            this.setState({
                submit:false,
            });
            this.props.showAlertNotes();
            this.props.refetchOrder(true)
        } else {
            if (response.msg == 'success') {
                this.props.setInfoAlert({
                    msg: 'Note inserted successfully',
                    status: true,
                })
                this.setState({
                    submit:false,
                });
                this.props.refetchOrder(true)
                this.props.showAlertNotes();
                this.props.onClose();
            } else {
                this.props.setInfoAlert({
                    msg: `Failed to insert Note: ${response.msg}`,
                    status: false,
                })
                this.setState({
                    submit:false,
                });
                this.props.showAlertNotes();
                this.props.refetchOrder(true)
            }
        }
    }
    render() {
        return (
            <>
                <Modal
                    disablePortal
                    disableEnforceFocus
                    disableAutoFocus
                    open
                    aria-labelledby="server-modal-title"
                    aria-describedby="server-modal-description"
                    sx={{
                        display: 'flex',
                        p: 1,
                        alignItems: 'center',
                        justifyContent: 'center',
                        overflow:'auto'
                    }}
                    // onClose={this.props.onClose}
                    // container={() => rootRef.current}
                >
                <Card
                    sx={{
                        width:'40%',
                        padding:4,
                        textAlign:'center',
                        padding:2,
                        // backgroundColor:this.props.theme.theme.background_3
                        // border:'gray',
                        // shadow:true
                    }}
                >
                    <Box
                        sx={(theme) => ({
                            display:'flex',
                            position: 'relative',
                            bgcolor: 'background.paper',
                            justifyContent:'center',
                            overflow:'auto'

                            // backgroundColor:this.props.theme.theme.background_3,
                            // color:this.props.theme.theme.fontColor_3
                            // boxShadow: theme.shadows[5],
                        })}
                    >
                        <Box
                            sx={{
                                width: '95%',
                                height: '100%',
                                display: 'flex',
                                flexDirection:'column',
                                textAlign:'left',
                                justifyContent:'center',
                                gap:2,
                                // backgroundColor:this.props.theme.theme.background_3,
                                // color:this.props.theme.theme.fontColor_3
                            }}
                        >
                            <Box
                                sx={{
                                    display:'flex',
                                    flexDirection:'row',
                                    justifyContent:'space-between'
                                }}
                            >
                                <Typography
                                    // width={'90%'}
                                >
                                </Typography>
                                <IconButton
                                    onClick={() => {
                                    this.props.onClose()
                                    }}
                                >
                                    <CloseIcon  fontSize="small"/>
                                </IconButton>
                            </Box>
                            <Box>
                                <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                                    Add Note
                                </Typography>
                            </Box>
                            <Box
                                sx={{
                                width: '100%',                                           
                                }}
                            >
                                <TextField
                                    fullWidth
                                    multiline
                                    aria-multiline
                                    rows={4}
                                    size='small'
                                    id='note'
                                    label="Note"
                                    placeholder='Note'
                                    type='text'
                                    value={this.state.note}
                                    onChange={(e) =>
                                        this.setState({
                                            note:e.target.value
                                        })
                                    }
                                />
                            </Box>
                            <Box
                                sx={{
                                    display:'flex',
                                    alignItems:'center',
                                    padding:0,
                                    margin:0
                                }}
                            >
                                {this.state.submit ? (
                                    <>
                                        <Button 
                                            sx={{
                                                backgroundColor:{purple},
                                                marginRight:1
                                                // marginTop:1
                                            }}
                                            startDecorator={
                                                <CircularProgress variant="solid"/>
                                            }
                                        >
                                            Loading…
                                        </Button>
                                    </>
                                ) : (
                                    <ColorButton 
                                        variant="contained"
                                        sx={{
                                        marginRight:1
                                        }}
                                        onClick={() =>this.handleSubmit()}   
                                    >
                                        Submit
                                    </ColorButton>
                                )}
                                <ColorButton 
                                    // variant="contained"
                                    onClick={() => {
                                    this.props.onClose()
                                        // setStatus(false);
                                        // onClose();
                                    }}
                                    // onClick={() =>this.handleSignIn()}   
                                >
                                    Close
                                </ColorButton>
                            </Box>
                        </Box>
                    </Box>
                </Card>
            </Modal>
        </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    dashboardHook
)(NoteAdd)
