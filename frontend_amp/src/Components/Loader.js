import React, { Component } from 'react';

import { Box, CircularProgress } from "@mui/material";
import { styled } from '@mui/material/styles';
import { keyframes } from '@mui/system';
const $colorPrimary="#6845D6"

const spin = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

const RotatedBox = styled("div")({
  backgroundColor: "pink",
  width: 30,
  height: 30,
  animation: `${spin} 1s infinite ease`
});
class Loader extends Component {
    render(){
        return (
            <>
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: 'repeat(3, minmax(150px, 1fr))',
                gap: 1,
              }}
            >
              <CircularProgress variant={'solid'} color="primary" />
              {/* <CircularProgress variant={variant} color="neutral" /> */}
              {/* <CircularProgress variant={variant} color="danger" /> */}
              {/* <CircularProgress variant={variant} color="success" /> */}
              {/* <CircularProgress variant={variant} color="warning" /> */}
            </Box>
            
            </>
        );
    }
}
export default  Loader;
