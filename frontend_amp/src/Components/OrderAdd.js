import React, { Component } from 'react'
import Modal from '@mui/material/Modal';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import CloseIcon from '@mui/icons-material/Close';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import dayjs from 'dayjs';
import { DemoContainer } from '@mui/x-date-pickers/internals/demo';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
// import {
//   Unstable_NumberInput as BaseNumberInput,
//   numberInputClasses,
// } from '@mui/base/Unstable_NumberInput';
import {  Alert, AlertTitle, Card, Checkbox, FormControl, FormControlLabel, FormGroup, IconButton, Input<PERSON>abel, MenuItem, Select, styled, TextField } from '@mui/material';
import { purple } from '@mui/material/colors';
import Button from '@mui/joy/Button';
import { CircularProgress } from '@mui/joy';
import { compose } from 'redux';
import dashboardHook from '../HOK/DashboardHook';
import themeHook from '../HOK/ThemeHook';
import authHook from '../HOK/AuthHook';
import { ColorButton } from './ColorButton';


class OrderAdd extends Component {
  constructor(props){
    super(props)
    this.state={
      theme:props.theme,
      product:'',
      s_k_u:'',
      vendor:'',
      note:'',
      qty:1,
      checkSpecific:false,
      checkUrgent:false,
      timeline:'',
      statusIndex:3,
      statusSelectedOption:'In-Progress',
      error:'',
      submit:false,
      showEcwidSearch:false,
      ecwidScriptLoaded:false,
    }
    this.handleSubmit=this.handleSubmit.bind(this)
    this.handleProductInputChange=this.handleProductInputChange.bind(this)
    this.loadEcwidScript=this.loadEcwidScript.bind(this)
    this.showEcwidSearch=this.showEcwidSearch.bind(this)
    this.hideEcwidSearch=this.hideEcwidSearch.bind(this)
    this.handleModalClose=this.handleModalClose.bind(this)
  }
  componentDidMount(){
    this.setState({
      theme:this.props.theme,
    })
    this.shouldComponentUpdate=true
    this.forceUpdate()
  }

  // Load Ecwid script dynamically
  loadEcwidScript() {
    if (this.state.ecwidScriptLoaded) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://app.ecwid.com/script.js?31447282&data_platform=code&data_date=2025-06-03';
      script.charset = 'utf-8';
      script.setAttribute('data-cfasync', 'false');
      script.onload = () => {
        this.setState({ ecwidScriptLoaded: true });
        resolve();
      };
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  // Show Ecwid search and trigger search
  async showEcwidSearch() {
    try {
      await this.loadEcwidScript();
      this.setState({ showEcwidSearch: true });

      // Wait for DOM update then trigger search
      setTimeout(() => {
        if (window.xSearch && this.state.product.trim()) {
          window.xSearch("id=my-search-31447282");
        }
      }, 100);
    } catch (error) {
      console.error('Failed to load Ecwid script:', error);
    }
  }

  // Hide Ecwid search
  hideEcwidSearch() {
    this.setState({ showEcwidSearch: false });
    // Remove the search div content
    const searchDiv = document.getElementById('my-search-31447282');
    if (searchDiv) {
      searchDiv.innerHTML = '';
    }
  }

  // Handle product input changes
  handleProductInputChange(e) {
    const value = e.target.value;
    this.setState({ product: value });

    if (value.trim()) {
      this.showEcwidSearch();
    } else {
      this.hideEcwidSearch();
    }
  }

  // Handle modal close with cleanup
  handleModalClose() {
    this.hideEcwidSearch();
    this.props.onClose();
  }
  async handleSubmit(){
    let orderData={
      "admin_id":null,
      "product":this.state.product,
      "s_k_u":this.state.s_k_u,
      "vendor":this.state.vendor,
      "note":this.state.note,
      "qty":this.state.qty,
      "specific":this.state.checkSpecific,
      "urgent":this.state.checkUrgent,
      "status":this.state.statusIndex
    }
    this.setState({
      submit:true
      }
    )
    if (this.state.checkUrgent){
      const timeline=new Date(this.state.timeline)
      orderData.timeline=`${timeline.getFullYear()}-${timeline.getMonth() + 1}-${timeline.getDate()}`
    }
    if (this.props.auth.user.user_group==2){
      orderData.admin_id=this.props.auth.selectUser.id
    }else if(this.props.auth.user.user_group==3){
      orderData.admin_id=this.props.auth.user.id
    }else if(this.props.auth.user.user_group==4){
      orderData.admin_id=this.props.auth.user.ref_admin.id
    }
    else{

    }
    let response=await this.props.dashboard.insertOrder(this.props.auth.token,orderData)
    console.log(response)
    if (response.status!=200) {
      this.props.setInfoAlert({
        msg: `Error insrting order: ${response.msg}`,
        status: false,
      })
      this.setState({
        submit:false,
        });
      this.props.showAlertNotes();
      // this.props.refetchOrder(true)
    } else {
        if (response.msg == 'success') {
            this.props.setInfoAlert({
              msg: 'Order inserted successfully',
              status: true,
            })
            this.props.refetchOrder(true)
            this.setState({
              submit:false,
            });
            this.props.showAlertNotes();
            this.handleModalClose();
        } else {
          this.props.setInfoAlert({
            msg: `Failed to insert order: ${response.msg}`,
            status: false,
          })
          this.setState({
            submit:false,
          });
          this.props.showAlertNotes();
          // this.props.refetchOrder(true)
        }
    }
  }
  render() {
    return (
      <>
        <Modal
        disablePortal
        disableEnforceFocus
        disableAutoFocus
        open
        aria-labelledby="server-modal-title"
        aria-describedby="server-modal-description"
        sx={{
          display: 'flex',
          p: 1,
          alignItems: 'center',
          justifyContent: 'center',
          
        }}
        // onClose={this.props.onClose}
        // container={() => rootRef.current}
      >
        <Card
          sx={{
              width:'40%',
              padding:4,
              textAlign:'center',
              padding:2,
              // backgroundColor:this.props.theme.selectTheme.background_2,
              // color:this.props.theme.selectTheme.fontColor
              // border:'gray',
              // shadow:true
          }}
        >
          <Box
            sx={(theme) => ({
              position: 'relative',
              bgcolor: 'background.paper',
              backgroundColor:'inherit',
              color:'inherit',
              // boxShadow: theme.shadows[5],
            })}
          >
            <Box
              sx={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  flexDirection:'column',
                  textAlign:'left',
                  gap:2,
                  backgroundColor:'inherit',
                  color:'inherit',
              }}
            >
              <Box
                sx={{
                  display:'flex',
                  flexDirection:'row',
                  justifyContent:'space-between',
                  backgroundColor:'inherit',
                  color:'inherit',
                }}
              >
                <Typography
                  // width={'90%'}
                >
                </Typography>
                <IconButton
                  onClick={this.handleModalClose}
                >
                  <CloseIcon  fontSize="small"/>
                </IconButton>
              </Box>
              <Box
                sx={{
                  backgroundColor:'inherit',
                  color:'inherit',
                }}
              >
                <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                  Add Order
                </Typography>
              </Box>
              {this.state.error && (
                  <Box>
                    <Alert 
                        variant="filled" 
                        severity="error"
                        onClose={() => {
                            this.setState({
                                error:""
                            })
                        }}
                    >
                        <AlertTitle>
                            Info alert!
                        </AlertTitle>
                        {this.state.error}
                    </Alert> 
                  </Box>         
                )}
              <Box
                  sx={{
                    width: '100%',
                    backgroundColor:'inherit',
                    color:'inherit',
                  }}
              >
                <TextField
                    fullWidth
                    size='small'
                    id='product'
                    label="product"
                    placeholder='product'
                    required
                    type='text'
                    sx={{
                      backgroundColor:'inherit',
                      color:'inherit',
                    }}
                    value={this.state.product}
                    onChange={this.handleProductInputChange}
                    onFocus={() => {
                      if (this.state.product.trim()) {
                        this.showEcwidSearch();
                      }
                    }}
                />
                {/* Ecwid Search Block */}
                {this.state.showEcwidSearch && (
                  <Box
                    sx={{
                      boxSizing:'inherit',
                      width: '100%',
                      marginTop: 1,
                      backgroundColor: 'inherit',
                      color: 'inherit',
                      fontSize:'11px',
                      // CSS styles to control the dynamically added ecwid-search-widget class
                      '& .ecwid-search-widget': {
                        width: '100% !important',
                        maxWidth: '100% !important',
                        height: 'auto !important',
                        maxHeight: '300px !important',
                        overflow: 'auto !important',
                        border: '1px solid #ccc',
                        borderRadius: '4px',
                        backgroundColor: 'inherit',
                        color: 'inherit',
                        fontSize: '12px !important',
                        scale:1
                      },
                      '& .ecwid-search-widget *': {
                        maxWidth: '100% !important',
                        boxSizing: 'border-box !important',
                      },
                      '& .ecwid-search-widget input': {
                        width: '100% !important',
                        padding: '8px !important',
                        fontSize: '14px !important',
                        border: '1px solid #ddd !important',
                        borderRadius: '4px !important',
                      },
                      '& .ecwid-search-widget .search-results': {
                        maxHeight: '200px !important',
                        overflow: 'auto !important',
                        backgroundColor: 'inherit',
                      },
                      '& .ecwid-search-widget .search-result-item': {
                        padding: '8px !important',
                        borderBottom: '1px solid #eee !important',
                        cursor: 'pointer !important',
                      },
                      '& .ecwid-search-widget .search-result-item:hover': {
                        backgroundColor: this.props.theme?.selectTheme?.selectHover || '#f5f5f5 !important',
                      },
                      // Additional responsive styles
                      '& .ecwid-search-widget .ecwid-productBrowser': {
                        width: '100% !important',
                        maxWidth: '100% !important',
                      },
                      '& .ecwid-search-widget iframe': {
                        width: '100% !important',
                        maxWidth: '100% !important',
                        height: 'auto !important',
                      },
                      // Theme integration
                      '& .ecwid-search-widget, & .ecwid-search-widget *': {
                        color: this.props.theme?.selectTheme?.fontColor || 'inherit !important',
                        backgroundColor: this.props.theme?.selectTheme?.background || 'inherit !important',
                      }
                    }}
                  >
                    <div id="my-search-31447282">{this.state.product}</div>
                  </Box>
                )}
              </Box>
              <Box
                  sx={{
                    width: '100%',  
                    backgroundColor:'inherit',
                    color:'inherit',                                      
                  }}
              >
                <TextField
                  fullWidth
                  size='small'
                  id='s_k_u'
                  label="SKU"
                  placeholder='SKU(Manufacturer Code)'
                  type='text'
                  sx={{
                    backgroundColor:'inherit',
                    color:'inherit',
                  }}  
                  value={this.state.s_k_u}
                  onChange={(e) =>
                      this.setState({
                        s_k_u:e.target.value
                      })
                  }
                />
              </Box>
              {/* Add notes field below product */}
              <Box
                  sx={{
                    width: '100%',
                    backgroundColor:'inherit',
                    color:'inherit',
                    marginTop: 2                                      
                  }}
              >
                <TextField
                    fullWidth
                    multiline
                    rows={3}
                    size='small'
                    id='note'
                    label="Note"
                    placeholder='Add note about this order'
                    type='text'
                    sx={{
                      backgroundColor:'inherit',
                      color:'inherit',
                    }}  
                    value={this.state.note}
                    onChange={(e) =>
                        this.setState({
                            note:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                sx={{ 
                  display: 'flex',
                  position: 'relative',
                  backgroundColor:'inherit',
                  color:'inherit', 
                  }}
              >
                  <TextField 
                      size='small'
                      id='vendor'
                      // className='!focus:border-purple-700'
                      label="vendor" 
                      placeholder='vendor'
                      type='text'
                      fullWidth
                      value={this.state.vendor}
                      onChange={(e) =>
                          this.setState({
                              vendor:e.target.value
                          })
                      }
                  />
              </Box>
              <Box
                 sx={{ 
                  display: 'flex',
                  position: 'relative',
                  width:'100%',
                  justifyContent:'space-between',
                  backgroundColor:'inherit',
                  color:'inherit',
                  }}
              >
                <Box 
                  size='small'
                  fullWidth
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    width:'45%',
                    position:'relative',
                    backgroundColor:'inherit',
                    color:'inherit',
                    // borderColor:this.props.theme.fontColor,
                      // backgroundColor:this.props.theme.background,
                      // color:this.props.theme.fontColor
                  }}
                >
                  <TextField 
                    required
                    size='medium'
                    id='vendor'
                    label="Qty" 
                    placeholder='1'
                    defaultValue={1}
                    type='text'
                    fullWidth
                    value={this.state.qty}
                    onChange={(e) =>
                        this.setState({
                            qty:e.target.value
                        })
                    }
                  />
                  <Box
                    sx={{ 
                      position: 'absolute',
                      display:'flex',
                      flexDirection:'column',
                      height:'100%',
                      width:'15%',
                      right: 0,
                      //  top: 0 ,
                      boxShadow:'none',
                      alignItems:'center',
                      justifyContent:'space-between',
                      // backgroundColor:'inherit',
                      // color:'inherit',
                    }}
                  >
                    <IconButton 
                      sx={{
                        padding:0,
                        color:this.props.theme.selectTheme.fontColor
                      }}
                      onClick={()=>{
                        this.setState({
                          qty:this.state.qty+1
                        })
                      }}
                    >
                      <ArrowDropUpIcon color={this.props.theme.selectTheme.fontColor}/>
                    </IconButton>
                    <IconButton
                      sx={{
                        padding:0,
                        color:this.props.theme.selectTheme.fontColor
                      }}
                      onClick={()=>{
                        if(this.state.qty>1){
                          this.setState({
                            qty:this.state.qty-1
                          })
                        }
                      }}
                    >
                      <ArrowDropDownIcon color={this.props.theme.selectTheme.fontColor}/>
                    </IconButton>
                  </Box>
                </Box>
                <Box
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    marginLeft:2,
                    justifyContent:'space-between',
                    width:'50%',
                    alignItems:'center'
                  }}
                >
                  <FormGroup
                    sx={{
                      display:'flex',
                      flexDirection:'row',
                      justifyContent:'space-between',
                      width:'100%'
                    }}
                    >
                    <FormControlLabel 
                      control={
                        <Checkbox
                          sx={{
                            color:'inherit'
                          }}
                        />
                      }  
                      label="Specific" 
                      onClick={()=>{
                        this.setState({
                          checkSpecific:!this.state.checkSpecific
                        })}
                      }
                    />
                    <FormControlLabel 
                      control={
                        <Checkbox 
                          sx={{
                            color:'inherit'
                              }}
                        />
                      }  
                      label="Urgent"  
                      onClick={()=>{
                        this.setState({
                          checkUrgent:!this.state.checkUrgent
                        })}}
                      />
                  </FormGroup>
                </Box>
              </Box>
              <Box
                sx={{ 
                  display: 'flex',
                  position: 'relative',
                  width:'100%',
                  color:this.props.theme.selectTheme.fontColor
                }}
              >
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    disabled={!this.state.checkUrgent}
                    sx={{
                      width:'100%',
                      color:this.props.theme.selectTheme.fontColor
                    }}
                    format=' YYYY / MM / DD'
                    label="Timeline"
                    // value={this.state.timeline}
                    onChange={(newValue) =>{ 
                      this.setState({
                        timeline:newValue
                      })
                      }
                    }
                  />
                </LocalizationProvider>
              </Box>
              <Box>
                <FormControl 
                  size='small'
                  fullWidth
                  sx={{
                    borderColor:this.props.theme.fontColor,
                    backgroundColor:this.props.theme.background,
                    color:this.props.theme.fontColor
                  }}
                >
                  <InputLabel 
                      id="demo-simple-select-label"
                      sx={{
                          backgroundColor:this.props.theme.background,
                          color:this.props.theme.fontColor,
                          // width:'80%',
                          // borderRight:'1px solid '
                      }}
                  >Status</InputLabel>
                  <Select
                    disabled
                    value={this.state.statusSelectedOption}
                    label="Status"
                    // onChange={(e)=>{
                      //     console.log(this.state.order.orderStatus[e.target.value])
                      //     this.setState({
                      //       statusIndex:this.state.order.orderStatus[e.target.value],
                      //       statusSelectedOption:e.target.value
                      //     })
                      //   }
                      // }
                      // components={CustomOption}
                      sx={{
                          display:'flex',
                          flexDirection:'row',
                          backgroundColor:this.props.theme.background,
                          color:this.props.theme.fontColor,
                          '& .MuiSelect-select':{
                              display:'flex',
                              backgroundColor:this.props.theme.background,
                              color:this.props.theme.fontColor
                          },
                      }}
                      style={{display:'flex',flexDirection:'row',alignItems:'center'}}
                  >
                    {/* {this.state.auth.user.user_group<=2?
                    (
                      Object.keys(this.state.order.orderStatus).map((item,index)=>{
                        // {console.log(item,index)}
                        return(
                          <>
                            <MenuItem 
                              key={index}
                              value={item}>
                                <Typography>
                                  {item}
                                </Typography>
                            </MenuItem>
                          </>
                        )
                      })
                    ):( */}
                      <MenuItem 
                        key={3}
                        value={"In-Progress"}
                        sx={{
                          '&:hover': {backgroundColor: this.props.theme.selectTheme.selectHover},
                        }}
                        >
                          <Typography>
                            {'In-Progress'}
                          </Typography>
                      </MenuItem>
                    {/* )} */}
                  </Select>
                </FormControl>
              </Box>
              <Box
                sx={{
                  display:'flex',
                  alignItems:'center'
                }}
              >
                {this.state.submit ? (
                   <>
                   <Button 
                       // color={'primary'}
                       sx={{
                           backgroundColor:purple[400],
                           marginRight:1
                           // marginTop:1
                       }}
                       startDecorator={
                           <CircularProgress variant="solid"/>
                       }
                   >
                       Loading…
                   </Button>
               </>
                ) : (
                  <ColorButton 
                      variant="contained"
                      // className='mt-4'
                      sx={{
                        marginRight:1
                      }}
                      onClick={() =>{
                          if(this.state.checkUrgent&&this.state.timeline==''){
                            this.setState({
                              error:'Timeline is not Selected',
                            })
                          }else{
                            this.handleSubmit()
                          }
                        }
                      }   
                  >
                      Submit
                  </ColorButton>
                )}
                <ColorButton
                      variant="contained"
                      // className='m/t-4'
                      onClick={this.handleModalClose}
                      // onClick={() =>this.handleSignIn()}
                  >
                      Close
                  </ColorButton>
              </Box>
            </Box>       
          </Box>
        </Card>
      </Modal>
      </>
    )
  }
}
export default compose(
  themeHook,
  dashboardHook,
  authHook
)(OrderAdd)
