import React, { Component } from 'react'
import Modal from '@mui/material/Modal';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import CloseIcon from '@mui/icons-material/Close';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import dayjs from 'dayjs';
import { DemoContainer } from '@mui/x-date-pickers/internals/demo';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
// import {
//   Unstable_NumberInput as BaseNumberInput,
//   numberInputClasses,
// } from '@mui/base/Unstable_NumberInput';
import {  Alert, AlertTitle, Card, Checkbox, FormControl, FormControlLabel, FormGroup, IconButton, Input<PERSON>abel, MenuItem, Select, styled, TextField } from '@mui/material';
import { purple } from '@mui/material/colors';
import Button from '@mui/joy/Button';
import { CircularProgress } from '@mui/joy';
import { compose } from 'redux';
import dashboardHook from '../HOK/DashboardHook';
import themeHook from '../HOK/ThemeHook';
import authHook from '../HOK/AuthHook';
import { ColorButton } from './ColorButton';
import EcwidProductSearch from './EcwidProductSearch';
import EcwidApiTest from './EcwidApiTest';


class OrderAdd extends Component {
  constructor(props){
    super(props)
    this.state={
      theme:props.theme,
      product:'',
      s_k_u:'',
      vendor:'',
      note:'',
      qty:1,
      checkSpecific:false,
      checkUrgent:false,
      timeline:'',
      statusIndex:3,
      statusSelectedOption:'In-Progress',
      error:'',
      submit:false,
      selectedProduct:null,
      productPrice:0,
      productImageUrl:'',
      showApiTest:false,
    }
    this.handleSubmit=this.handleSubmit.bind(this)
    this.handleProductSelect=this.handleProductSelect.bind(this)
  }
  componentDidMount(){
    this.setState({
      theme:this.props.theme,
    })
    this.shouldComponentUpdate=true
    this.forceUpdate()
  }

  // Handle product selection from Ecwid API search
  handleProductSelect(product) {
    console.log('🎯 Product selected from API:', product);

    this.setState({
      selectedProduct: product,
      product: product.name,
      s_k_u: product.sku || '',
      productPrice: product.price || 0,
      productImageUrl: product.imageUrl || '',
      // Optionally populate vendor if available in product data
      vendor: product.vendor || this.state.vendor
    });
  }
  async handleSubmit(){
    let orderData={
      "admin_id":null,
      "product":this.state.product,
      "s_k_u":this.state.s_k_u,
      "vendor":this.state.vendor,
      "note":this.state.note,
      "qty":this.state.qty,
      "specific":this.state.checkSpecific,
      "urgent":this.state.checkUrgent,
      "status":this.state.statusIndex
    }
    this.setState({
      submit:true
      }
    )
    if (this.state.checkUrgent){
      const timeline=new Date(this.state.timeline)
      orderData.timeline=`${timeline.getFullYear()}-${timeline.getMonth() + 1}-${timeline.getDate()}`
    }
    if (this.props.auth.user.user_group==2){
      orderData.admin_id=this.props.auth.selectUser.id
    }else if(this.props.auth.user.user_group==3){
      orderData.admin_id=this.props.auth.user.id
    }else if(this.props.auth.user.user_group==4){
      orderData.admin_id=this.props.auth.user.ref_admin.id
    }
    else{

    }
    let response=await this.props.dashboard.insertOrder(this.props.auth.token,orderData)
    console.log(response)
    if (response.status!=200) {
      this.props.setInfoAlert({
        msg: `Error insrting order: ${response.msg}`,
        status: false,
      })
      this.setState({
        submit:false,
        });
      this.props.showAlertNotes();
      // this.props.refetchOrder(true)
    } else {
        if (response.msg == 'success') {
            this.props.setInfoAlert({
              msg: 'Order inserted successfully',
              status: true,
            })
            this.props.refetchOrder(true)
            this.setState({
              submit:false,
            });
            this.props.showAlertNotes();
            this.props.onClose();
        } else {
          this.props.setInfoAlert({
            msg: `Failed to insert order: ${response.msg}`,
            status: false,
          })
          this.setState({
            submit:false,
          });
          this.props.showAlertNotes();
          // this.props.refetchOrder(true)
        }
    }
  }
  render() {
    return (
      <>
        <Modal
        disablePortal
        disableEnforceFocus
        disableAutoFocus
        open
        aria-labelledby="server-modal-title"
        aria-describedby="server-modal-description"
        sx={{
          display: 'flex',
          p: 1,
          alignItems: 'center',
          justifyContent: 'center',
          overflowY:'hidden'
        }}
        // onClose={this.props.onClose}
        // container={() => rootRef.current}
      >
        <Card
          sx={{
              width:'40%',
              padding:4,
              textAlign:'center',
              padding:2,
              // backgroundColor:this.props.theme.selectTheme.background_2,
              // color:this.props.theme.selectTheme.fontColor
              // border:'gray',
              // shadow:true
          }}
        >
          <Box
            sx={(theme) => ({
              position: 'relative',
              bgcolor: 'background.paper',
              backgroundColor:'inherit',
              color:'inherit',
              // boxShadow: theme.shadows[5],
            })}
          >
            <Box
              sx={{
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  flexDirection:'column',
                  textAlign:'left',
                  gap:2,
                  backgroundColor:'inherit',
                  color:'inherit',
              }}
            >
              <Box
                sx={{
                  display:'flex',
                  flexDirection:'row',
                  justifyContent:'space-between',
                  backgroundColor:'inherit',
                  color:'inherit',
                }}
              >
                <Typography
                  // width={'90%'}
                >
                </Typography>
                <IconButton
                  onClick={() => {
                    this.props.onClose()
                  }}
                >
                  <CloseIcon  fontSize="small"/>
                </IconButton>
              </Box>
              <Box
                sx={{
                  backgroundColor:'inherit',
                  color:'inherit',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <Typography variant='h3' fontSize={'1.25rem'} fontWeight={'500'}>
                  Add Order
                </Typography>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => this.setState({ showApiTest: !this.state.showApiTest })}
                  sx={{ fontSize: '0.75rem' }}
                >
                  {this.state.showApiTest ? 'Hide' : 'Test'} API
                </Button>
              </Box>
              {this.state.error && (
                  <Box>
                    <Alert 
                        variant="filled" 
                        severity="error"
                        onClose={() => {
                            this.setState({
                                error:""
                            })
                        }}
                    >
                        <AlertTitle>
                            Info alert!
                        </AlertTitle>
                        {this.state.error}
                    </Alert> 
                  </Box>         
                )}

              {/* API Test Tool */}
              {this.state.showApiTest && (
                <Box sx={{ mb: 3 }}>
                  <EcwidApiTest />
                </Box>
              )}

              <Box
                  sx={{
                    width: '100%',
                    backgroundColor:'inherit',
                    color:'inherit',
                  }}
              >
                <EcwidProductSearch
                  theme={this.props.theme}
                  initialValue={this.state.product}
                  onProductSelect={this.handleProductSelect}
                />

                {/* Selected Product Display */}
                {this.state.selectedProduct && (
                  <Box
                    sx={{
                      mt: 1,
                      p: 2,
                      border: '1px solid #e0e0e0',
                      borderRadius: 1,
                      backgroundColor: 'rgba(0, 0, 0, 0.02)',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2
                    }}
                  >
                    {this.state.productImageUrl && (
                      <Box
                        component="img"
                        src={this.state.productImageUrl}
                        alt={this.state.selectedProduct.name}
                        sx={{
                          width: 48,
                          height: 48,
                          borderRadius: 1,
                          objectFit: 'cover'
                        }}
                      />
                    )}
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {this.state.selectedProduct.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        SKU: {this.state.selectedProduct.sku || 'N/A'}
                      </Typography>
                      {this.state.selectedProduct.formattedPrice && (
                        <Typography variant="body2" color="primary" fontWeight="bold">
                          {this.state.selectedProduct.formattedPrice}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                )}
              </Box>
              <Box
                  sx={{
                    width: '100%',  
                    backgroundColor:'inherit',
                    color:'inherit',                                      
                  }}
              >
                <TextField
                  fullWidth
                  size='small'
                  id='s_k_u'
                  label="SKU"
                  placeholder='SKU(Manufacturer Code)'
                  type='text'
                  sx={{
                    backgroundColor:'inherit',
                    color:'inherit',
                  }}  
                  value={this.state.s_k_u}
                  onChange={(e) =>
                      this.setState({
                        s_k_u:e.target.value
                      })
                  }
                />
              </Box>
              {/* Add notes field below product */}
              <Box
                  sx={{
                    width: '100%',
                    backgroundColor:'inherit',
                    color:'inherit',
                    marginTop: 2                                      
                  }}
              >
                <TextField
                    fullWidth
                    multiline
                    rows={3}
                    size='small'
                    id='note'
                    label="Note"
                    placeholder='Add note about this order'
                    type='text'
                    sx={{
                      backgroundColor:'inherit',
                      color:'inherit',
                    }}  
                    value={this.state.note}
                    onChange={(e) =>
                        this.setState({
                            note:e.target.value
                        })
                    }
                />
              </Box>
              <Box
                sx={{ 
                  display: 'flex',
                  position: 'relative',
                  backgroundColor:'inherit',
                  color:'inherit', 
                  }}
              >
                  <TextField 
                      size='small'
                      id='vendor'
                      // className='!focus:border-purple-700'
                      label="vendor" 
                      placeholder='vendor'
                      type='text'
                      fullWidth
                      value={this.state.vendor}
                      onChange={(e) =>
                          this.setState({
                              vendor:e.target.value
                          })
                      }
                  />
              </Box>
              <Box
                 sx={{ 
                  display: 'flex',
                  position: 'relative',
                  width:'100%',
                  justifyContent:'space-between',
                  backgroundColor:'inherit',
                  color:'inherit',
                  }}
              >
                <Box 
                  size='small'
                  fullWidth
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    width:'45%',
                    position:'relative',
                    backgroundColor:'inherit',
                    color:'inherit',
                    // borderColor:this.props.theme.fontColor,
                      // backgroundColor:this.props.theme.background,
                      // color:this.props.theme.fontColor
                  }}
                >
                  <TextField 
                    required
                    size='medium'
                    id='vendor'
                    label="Qty" 
                    placeholder='1'
                    defaultValue={1}
                    type='text'
                    fullWidth
                    value={this.state.qty}
                    onChange={(e) =>
                        this.setState({
                            qty:e.target.value
                        })
                    }
                  />
                  <Box
                    sx={{ 
                      position: 'absolute',
                      display:'flex',
                      flexDirection:'column',
                      height:'100%',
                      width:'15%',
                      right: 0,
                      //  top: 0 ,
                      boxShadow:'none',
                      alignItems:'center',
                      justifyContent:'space-between',
                      // backgroundColor:'inherit',
                      // color:'inherit',
                    }}
                  >
                    <IconButton 
                      sx={{
                        padding:0,
                        color:this.props.theme.selectTheme.fontColor
                      }}
                      onClick={()=>{
                        this.setState({
                          qty:this.state.qty+1
                        })
                      }}
                    >
                      <ArrowDropUpIcon color={this.props.theme.selectTheme.fontColor}/>
                    </IconButton>
                    <IconButton
                      sx={{
                        padding:0,
                        color:this.props.theme.selectTheme.fontColor
                      }}
                      onClick={()=>{
                        if(this.state.qty>1){
                          this.setState({
                            qty:this.state.qty-1
                          })
                        }
                      }}
                    >
                      <ArrowDropDownIcon color={this.props.theme.selectTheme.fontColor}/>
                    </IconButton>
                  </Box>
                </Box>
                <Box
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    marginLeft:2,
                    justifyContent:'space-between',
                    width:'50%',
                    alignItems:'center'
                  }}
                >
                  <FormGroup
                    sx={{
                      display:'flex',
                      flexDirection:'row',
                      justifyContent:'space-between',
                      width:'100%'
                    }}
                    >
                    <FormControlLabel 
                      control={
                        <Checkbox
                          sx={{
                            color:'inherit'
                          }}
                        />
                      }  
                      label="Specific" 
                      onClick={()=>{
                        this.setState({
                          checkSpecific:!this.state.checkSpecific
                        })}
                      }
                    />
                    <FormControlLabel 
                      control={
                        <Checkbox 
                          sx={{
                            color:'inherit'
                              }}
                        />
                      }  
                      label="Urgent"  
                      onClick={()=>{
                        this.setState({
                          checkUrgent:!this.state.checkUrgent
                        })}}
                      />
                  </FormGroup>
                </Box>
              </Box>
              <Box
                sx={{ 
                  display: 'flex',
                  position: 'relative',
                  width:'100%',
                  color:this.props.theme.selectTheme.fontColor
                }}
              >
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    disabled={!this.state.checkUrgent}
                    sx={{
                      width:'100%',
                      color:this.props.theme.selectTheme.fontColor
                    }}
                    format=' YYYY / MM / DD'
                    label="Timeline"
                    // value={this.state.timeline}
                    onChange={(newValue) =>{ 
                      this.setState({
                        timeline:newValue
                      })
                      }
                    }
                  />
                </LocalizationProvider>
              </Box>
              <Box>
                <FormControl 
                  size='small'
                  fullWidth
                  sx={{
                    borderColor:this.props.theme.fontColor,
                    backgroundColor:this.props.theme.background,
                    color:this.props.theme.fontColor
                  }}
                >
                  <InputLabel 
                      id="demo-simple-select-label"
                      sx={{
                          backgroundColor:this.props.theme.background,
                          color:this.props.theme.fontColor,
                          // width:'80%',
                          // borderRight:'1px solid '
                      }}
                  >Status</InputLabel>
                  <Select
                    disabled
                    value={this.state.statusSelectedOption}
                    label="Status"
                    // onChange={(e)=>{
                      //     console.log(this.state.order.orderStatus[e.target.value])
                      //     this.setState({
                      //       statusIndex:this.state.order.orderStatus[e.target.value],
                      //       statusSelectedOption:e.target.value
                      //     })
                      //   }
                      // }
                      // components={CustomOption}
                      sx={{
                          display:'flex',
                          flexDirection:'row',
                          backgroundColor:this.props.theme.background,
                          color:this.props.theme.fontColor,
                          '& .MuiSelect-select':{
                              display:'flex',
                              backgroundColor:this.props.theme.background,
                              color:this.props.theme.fontColor
                          },
                      }}
                      style={{display:'flex',flexDirection:'row',alignItems:'center'}}
                  >
                    {/* {this.state.auth.user.user_group<=2?
                    (
                      Object.keys(this.state.order.orderStatus).map((item,index)=>{
                        // {console.log(item,index)}
                        return(
                          <>
                            <MenuItem 
                              key={index}
                              value={item}>
                                <Typography>
                                  {item}
                                </Typography>
                            </MenuItem>
                          </>
                        )
                      })
                    ):( */}
                      <MenuItem 
                        key={3}
                        value={"In-Progress"}
                        sx={{
                          '&:hover': {backgroundColor: this.props.theme.selectTheme.selectHover},
                        }}
                        >
                          <Typography>
                            {'In-Progress'}
                          </Typography>
                      </MenuItem>
                    {/* )} */}
                  </Select>
                </FormControl>
              </Box>
              <Box
                sx={{
                  display:'flex',
                  alignItems:'center'
                }}
              >
                {this.state.submit ? (
                   <>
                   <Button 
                       // color={'primary'}
                       sx={{
                           backgroundColor:purple[400],
                           marginRight:1
                           // marginTop:1
                       }}
                       startDecorator={
                           <CircularProgress variant="solid"/>
                       }
                   >
                       Loading…
                   </Button>
               </>
                ) : (
                  <ColorButton 
                      variant="contained"
                      // className='mt-4'
                      sx={{
                        marginRight:1
                      }}
                      onClick={() =>{
                          if(this.state.checkUrgent&&this.state.timeline==''){
                            this.setState({
                              error:'Timeline is not Selected',
                            })
                          }else{
                            this.handleSubmit()
                          }
                        }
                      }   
                  >
                      Submit
                  </ColorButton>
                )}
                <ColorButton 
                      variant="contained"
                      // className='m/t-4'
                      onClick={() => {
                        this.props.onClose()
                          // setStatus(false);
                          // onClose();
                      }}
                      // onClick={() =>this.handleSignIn()}   
                  >
                      Close
                  </ColorButton>
              </Box>
            </Box>       
          </Box>
        </Card>
      </Modal>
      </>
    )
  }
}
export default compose(
  themeHook,
  dashboardHook,
  authHook
)(OrderAdd)
