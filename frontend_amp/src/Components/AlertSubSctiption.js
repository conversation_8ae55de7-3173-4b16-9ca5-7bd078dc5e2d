import React, { Component } from 'react'
import { <PERSON>, Card, IconButton, Link, Modal, Typography } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close';
import { ColorButton } from './ColorButton';
import themeHook from '../HOK/ThemeHook';
import { compose } from 'redux';

class AlertSubSctiption extends Component {
    constructor(props){
        super(props)
    }
  render() {
    return (
        <>
            <Modal
                disablePortal
                disableEnforceFocus
                disableAutoFocus
                open
                aria-labelledby="server-modal-title"
                aria-describedby="server-modal-description"
                sx={{
                    display: 'flex',
                    p: 1,
                    alignItems: 'center',
                    justifyContent: 'center',
                }}
                // onClose={this.props.onClose}
                // container={() => rootRef.current}
            >
            <Card
                sx={{
                    width:'30%',
                    padding:4,
                    textAlign:'center',
                    padding:2,
                    backgroundColor:this.props.theme.selectTheme.background_2,
                    border:'gray',
                    shadow:true
                }}
            >
                <Box
                    sx={(theme) => ({
                        display:'flex',
                        position: 'relative',
                        bgcolor: 'background.paper',
                        justifyContent:'center',
                        backgroundColor:this.props.theme.selectTheme.background_2,

                        // backgroundColor:this.props.theme.selectTheme.background_3,
                        // color:this.props.theme.selectTheme.fontColor_3
                        // boxShadow: theme.shadows[5],
                    })}
                >
                    <Box
                        sx={{
                            width: '95%',
                            height: '100%',
                            display: 'flex',
                            flexDirection:'column',
                            textAlign:'left',
                            justifyContent:'center',
                            gap:3,
                            backgroundColor:this.props.theme.selectTheme.background_2,

                            // backgroundColor:this.props.theme.selectTheme.background_3,
                            // color:this.props.theme.selectTheme.fontColor_3
                        }}
                    >
                        <Box
                            sx={{
                                display:'flex',
                                flexDirection:'row',
                                justifyContent:'space-between'
                            }}
                        >
                            <Typography
                                // width={'90%'}
                            >
                            </Typography>
                            <IconButton
                                onClick={() => {
                                this.props.onClose()
                                }}
                            >
                                <CloseIcon  fontSize="small"/>
                            </IconButton>
                        </Box>
                        {/* <Box
                            sx={{
                                display:'flex',
                                flexDirection:'row',
                                justifyContent:'center',
                                textAlign:'center'
                            }}    
                        >
                            <Typography 
                                variant='h3'
                                fontSize={'1.25rem'} 
                                fontWeight={'500'}
                            >
                                SubScription Info
                            </Typography>
                        </Box> */}
                        <Box
                            sx={{
                                width: '100%',
                                display:'flex',
                                flexDirection:'column',
                                justifyContent:'center',
                                gap:2                                           
                            }}
                        >
                            <Typography
                                sx={{
                                    textAlign:'center',
                                    display:'flex',
                                    justifyContent:'center'
                                }}
                            >
                               To learn more about your AMP Extensions,
                            </Typography>
                            <Typography
                                sx={{
                                    textAlign:'center',
                                    display:'flex',
                                    justifyContent:'center'
                                }}
                            >
                                please reach out to your AMP Team.
                            </Typography>
                            <Link
                                sx={{
                                    textAlign:'center',
                                    textDecoration:'none'
                                }}
                                href='mailto:<EMAIL>'
                            >
                                    <EMAIL>
                            </Link>
                        </Box>
                        <Box
                            sx={{
                                display:'flex',
                                justifyContent:'center',
                                alignItems:'center',
                                padding:0,
                                margin:0
                            }}
                        >
                            <ColorButton
                                variant="contained"
                                onClick={() => {
                                this.props.onClose()
                                    // setStatus(false);
                                    // onClose();
                                }}
                                // onClick={() =>this.handleSignIn()}   
                            >
                                Close
                            </ColorButton>
                        </Box>
                    </Box>
                </Box>
            </Card>
            </Modal>
        </>
    )
  }
}
export default compose(
    themeHook,
  )(AlertSubSctiption)