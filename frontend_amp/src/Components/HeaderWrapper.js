import React from 'react';
import Header from './Header';
import { useSidebar } from './SideBar';
import { compose } from 'redux';
import themeHook from '../HOK/ThemeHook';
import authHook from '../HOK/AuthHook';
import navigateHook from '../HOK/NavigateHook';
import dashboardHook from '../HOK/DashboardHook';

const HeaderWithSidebar = (props) => {
  // Get both toggleSidebar function and sidebarCollapsed state
  const { toggleSidebar, sidebarCollapsed } = useSidebar();
  
  // Pass both to the Header component
  return <Header 
    {...props} 
    toggleSidebar={toggleSidebar} 
    sidebarCollapsed={sidebarCollapsed}
  />;
};

export default compose(
  themeHook,
  authHook,
  navigateHook,
  dashboardHook
)(HeaderWithSidebar);
