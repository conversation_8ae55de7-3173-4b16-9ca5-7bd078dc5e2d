import React, { Component } from 'react';
import './App.css';
import { Provider } from 'react-redux';
import ReduxThunk from 'redux-thunk';
import reducers from './reducers';
import store from './store';
import MainRouter from './Router';
import {
  HashRouter as Router,
  Routes,
  Route,
  Link,
  Navigate
} from 'react-router-dom';
import ThemeProvider from './context/ThemeContext';
import AuthProvider from './context/AuthContext';
import { ChatProvider } from './context/ChatContext';
import ChatButton from './Components/chat/ChatButton';
import { SidebarProvider } from './Components/SideBar';
import ErrorBoundary from './utils/ErrorBoundary';
class App extends Component {
  render() {
    return (
      <Provider store={store}>
          <SidebarProvider>
            <ErrorBoundary>

              <Router>
                <ThemeProvider>
                  <AuthProvider>
                    {/* <ChatProvider> */}
                      <MainRouter />
                      {/* <ChatButton /> */}
                    {/* </ChatProvider> */}
                  </AuthProvider>
                </ThemeProvider>
              </Router>
            </ErrorBoundary>

          </SidebarProvider>
      </Provider>
    );
  }
}

export default App;
