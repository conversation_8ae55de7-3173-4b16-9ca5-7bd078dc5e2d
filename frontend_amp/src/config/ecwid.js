/**
 * Ecwid Configuration
 * Store all Ecwid-related configuration here
 */

export const ECWID_CONFIG = {
  // Your Ecwid Store ID
  STORE_ID: '31447282',
  
  // Ecwid API Base URL
  API_BASE_URL: 'https://app.ecwid.com/api/v3',
  
  // API Access Token (should be set from environment variables in production)
  // For development, you can set it here temporarily
  // In production, use process.env.REACT_APP_ECWID_TOKEN
  ACCESS_TOKEN: process.env.REACT_APP_ECWID_TOKEN || null,
  
  // Rate limiting configuration
  RATE_LIMIT: {
    MAX_REQUESTS: 600, // 600 requests per minute
    WINDOW_MS: 60000,  // 1 minute window
  },
  
  // Search configuration
  SEARCH: {
    DEFAULT_LIMIT: 20,
    MAX_LIMIT: 100,
    DEFAULT_SORT: 'RELEVANCE', // RELEVANCE, ADDED_TIME_ASC, ADDED_TIME_DESC, NAME_ASC, NAME_DESC, PRICE_ASC, PRICE_DESC
    DEBOUNCE_MS: 300, // Debounce search input
  },
  
  // Product display configuration
  DISPLAY: {
    THUMBNAIL_SIZE: 48,
    MAX_DESCRIPTION_LENGTH: 100,
    SHOW_OUT_OF_STOCK: true,
    SHOW_DISABLED: false,
  },
  
  // API Endpoints
  ENDPOINTS: {
    PRODUCTS: '/products',
    PRODUCT_BY_ID: '/products/{id}',
    CATEGORIES: '/categories',
    PROFILE: '/profile',
    ORDERS: '/orders',
  },
  
  // Error messages
  ERROR_MESSAGES: {
    RATE_LIMIT: 'Too many requests. Please wait a moment and try again.',
    AUTH_ERROR: 'Authentication failed. Please check your API token.',
    NOT_FOUND: 'Resource not found.',
    NETWORK_ERROR: 'Network error. Please check your connection.',
    UNKNOWN: 'An unexpected error occurred. Please try again.',
  },
  
  // Available sort options for products
  SORT_OPTIONS: [
    { value: 'RELEVANCE', label: 'Relevance' },
    { value: 'NAME_ASC', label: 'Name (A-Z)' },
    { value: 'NAME_DESC', label: 'Name (Z-A)' },
    { value: 'PRICE_ASC', label: 'Price (Low to High)' },
    { value: 'PRICE_DESC', label: 'Price (High to Low)' },
    { value: 'ADDED_TIME_DESC', label: 'Newest First' },
    { value: 'ADDED_TIME_ASC', label: 'Oldest First' },
  ],
  
  // Product status options
  PRODUCT_STATUS: {
    ENABLED: 'enabled',
    DISABLED: 'disabled',
    ALL: 'all',
  },
  
  // Stock status options
  STOCK_STATUS: {
    IN_STOCK: 'inStock',
    OUT_OF_STOCK: 'outOfStock',
    ALL: 'all',
  },
};

/**
 * Get the full API URL for an endpoint
 * @param {string} endpoint - The endpoint path
 * @returns {string} - Full API URL
 */
export const getApiUrl = (endpoint) => {
  return `${ECWID_CONFIG.API_BASE_URL}/${ECWID_CONFIG.STORE_ID}${endpoint}`;
};

/**
 * Get API headers with authentication
 * @returns {object} - Headers object
 */
export const getApiHeaders = () => {
  const headers = {
    'Content-Type': 'application/json',
  };
  
  if (ECWID_CONFIG.ACCESS_TOKEN) {
    headers['Authorization'] = `Bearer ${ECWID_CONFIG.ACCESS_TOKEN}`;
  }
  
  return headers;
};

/**
 * Check if API token is configured
 * @returns {boolean} - True if token is available
 */
export const isApiTokenConfigured = () => {
  return !!ECWID_CONFIG.ACCESS_TOKEN;
};

/**
 * Format product URL
 * @param {number} productId - Product ID
 * @returns {string} - Product URL
 */
export const getProductUrl = (productId) => {
  return ECWID_CONFIG.ENDPOINTS.PRODUCT_BY_ID.replace('{id}', productId);
};

/**
 * Validate search parameters
 * @param {object} params - Search parameters
 * @returns {object} - Validated parameters
 */
export const validateSearchParams = (params) => {
  const validated = { ...params };
  
  // Ensure limit is within bounds
  if (validated.limit) {
    validated.limit = Math.min(Math.max(1, validated.limit), ECWID_CONFIG.SEARCH.MAX_LIMIT);
  }
  
  // Ensure offset is non-negative
  if (validated.offset) {
    validated.offset = Math.max(0, validated.offset);
  }
  
  // Validate sort option
  if (validated.sortBy) {
    const validSorts = ECWID_CONFIG.SORT_OPTIONS.map(opt => opt.value);
    if (!validSorts.includes(validated.sortBy)) {
      validated.sortBy = ECWID_CONFIG.SEARCH.DEFAULT_SORT;
    }
  }
  
  return validated;
};

export default ECWID_CONFIG;
