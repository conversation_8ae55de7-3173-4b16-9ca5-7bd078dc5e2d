import { grey,amber, red, deepOrange, yellow, green, lightBlue, purple, pink, blue, orange } from "@mui/material/colors";
import { light } from "@mui/material/styles/createPalette";
import { useContext, createContext, useState, useEffect } from "react";

const ThemeContext = createContext();
export const palettes={
  light: {
    background: "#F9FAFB",
    background_2: "#FFFFFF",
    background_3:"#F3F4F6",
    fontColor: '#000000',
    fontColor_2:'#000000',
    fontColor_3:grey[800],
    iconColor:grey[700],
    sidebarSelected:grey[200],
    selectHover:grey[200],
    lowProductRowColor:red[100],
    expiringProductRowColor:orange[100],
    lowProductRowColorIcon:red[300],
    expiringProductRowColorIcon:orange[300]

  },  
  dark: {
    background: "#1F2937",
    background_2: "#374151",
    background_3:"#111827",
    fontColor: 'white',
    fontColor_2:'white',
    fontColor_3:grey[200],
    iconColor:grey[200],
    sidebarSelected:grey[700],
    selectHover:grey[500],
    lowProductRowColor:red[400],
    expiringProductRowColor:orange[400],
    lowProductRowColorIcon:red[300],
    expiringProductRowColorIcon:orange[300]
  },
  deep_orange:{
    background:deepOrange.A100,
    background_2: deepOrange[200],
    background_3:deepOrange[100],
    fontColor: '#000000',
    fontColor_2:'#000000',
    fontColor_3:grey[800],
    iconColor:grey[700],
    sidebarSelected:deepOrange[50],
    selectHover:grey[200],
    lowProductRowColor:red[100],
    expiringProductRowColor:orange[100],
    lowProductRowColorIcon:red[300],
    expiringProductRowColorIcon:orange[300]
  },
  red:{
    background:red.A100,
    background_2: red[200],
    background_3:red[100],
    fontColor: '#000000',
    fontColor_2:'#000000',
    fontColor_3:grey[800],
    iconColor:grey[700],
    sidebarSelected:grey[50],
    selectHover:grey[200],
    lowProductRowColor:red[100],
    expiringProductRowColor:orange[100],
    lowProductRowColorIcon:red[300],
    expiringProductRowColorIcon:orange[300]
  },
  yellow:{
    background:"#fad339",
    background_2: yellow[200],
    background_3:yellow[100],
    fontColor: '#000000',
    fontColor_2:'#000000',
    fontColor_3:grey[800],
    iconColor:grey[700],
    sidebarSelected:grey[50],
    selectHover:grey[200],
    lowProductRowColor:red[100],
    expiringProductRowColor:orange[100],
    lowProductRowColorIcon:red[300],
    expiringProductRowColorIcon:orange[300]
  },
  green:{
    background:green.A100,
    background_2: green[200],
    background_3:green[100],
    fontColor: '#000000',
    fontColor_2:'#000000',
    fontColor_3:grey[800],
    iconColor:grey[700],
    sidebarSelected:grey[50],
    selectHover:grey[200],
    lowProductRowColor:red[100],
    expiringProductRowColor:orange[100],
    lowProductRowColorIcon:red[300],
    expiringProductRowColorIcon:orange[300]
  },
  blue:{
    background:lightBlue.A100,
    background_2: lightBlue[200],
    background_3:lightBlue[100],
    fontColor: '#000000',
    fontColor_2:'#000000',
    fontColor_3:grey[800],
    iconColor:grey[700],
    sidebarSelected:grey[50],
    selectHover:grey[200],
    lowProductRowColor:red[100],
    expiringProductRowColor:orange[100],
    lowProductRowColorIcon:red[300],
    expiringProductRowColorIcon:orange[300]
  },
  // pruple:{
  //   // #c5d2ff
  //   background:pink.A100,
  //   background_2: pink[200],
  //   background_3:purple[100],
  //   fontColor: '#000000',
  //   fontColor_2:'#000000',
  //   fontColor_3:grey[800],
  //   iconColor:grey[700]
   
  // },
  pruple:{
    // dec5ff
    background:purple.A100,
    background_2: purple[200],
    background_3:purple[100],
    fontColor: '#000000',
    fontColor_2:'#000000',
    fontColor_3:grey[800],
    iconColor:grey[700],
    sidebarSelected:grey[50],
    selectHover:grey[200],
    lowProductRowColor:red[100],
    expiringProductRowColor:orange[100],
    lowProductRowColorIcon:red[300],
    expiringProductRowColorIcon:orange[300]
  },
  pink:{
    // fac5ff
    background:pink.A100,
    background_2: pink[200],
    background_3:pink[100],
    fontColor: '#000000',
    fontColor_2:'#000000',
    fontColor_3:grey[800],
    iconColor:grey[700],
    sidebarSelected:grey[50],
    selectHover:grey[200],
    lowProductRowColor:red[100],
    expiringProductRowColor:orange[100],
    lowProductRowColorIcon:red[300],
    expiringProductRowColorIcon:orange[300]
  },
  blue:{
    // 7ed0ff
    background:'#7ed0ff',
    background:blue.A100,
    background_2: blue[200],
    background_3:blue[100],
    fontColor: '#000000',
    fontColor_2:'#000000',
    fontColor_3:grey[800],
    iconColor:grey[700],
    sidebarSelected:grey[50],
    selectHover:grey[200],
    lowProductRowColor:red[100],
    expiringProductRowColor:orange[100],
    lowProductRowColorIcon:red[300],
    expiringProductRowColorIcon:orange[300]
  }
}
const getInitialState = () => {
  const themeIndexSessionStorage = sessionStorage.getItem("amp.amedicalpartner.com.theme");
  const themeIndexLocalStorage=localStorage.getItem("amp.amedicalpartner.com.theme")
  console.log(themeIndexSessionStorage ? themeIndexSessionStorage : themeIndexLocalStorage?themeIndexLocalStorage:'light')
  return themeIndexSessionStorage ? themeIndexSessionStorage : themeIndexLocalStorage?themeIndexLocalStorage:'light'
}
const ThemeProvider = ({ children }) => {
    const [darkTheme, setDarkTheme] = useState(false);
    const [themeIndex,setThemeIndex]=useState(getInitialState)
    const [selectTheme,setSelectTheme]=useState(palettes[themeIndex])
    // const [dimensions, setDimensions] = useState({ window, screen });
    const [windowDimensions, setWindowDimensions] = useState({
      width: window.innerWidth,
      height: window.innerHeight,
  
    });
    useEffect(() => {
      sessionStorage.setItem("amp.amedicalpartner.com.theme", themeIndex)
      localStorage.setItem("amp.amedicalpartner.com.theme", themeIndex);
  }, [themeIndex])
  // useEffect(() => {
  //   const subscription = Dimensions.addEventListener(
  //     "change",
  //     ({ window, screen }) => {
  //       setDimensions({ window, screen });
  //     }
  //   );
  //   return () => subscription?.remove();
  // });
//   const [token, setToken] = useState(localStorage.getItem("amp.amedicalpartner.com.theme") || "");
  const changeTheme=(theme,index)=>{
    if(index=='dark'){
      setDarkTheme(true)
    }else{
      setDarkTheme(false)
    }
    setThemeIndex(index)
    setSelectTheme(theme)
  }    
  return (
    <ThemeContext.Provider 
      value={{ 
        palettes,
        themeIndex,
        selectTheme,
        darkTheme,
        windowDimensions,
        setWindowDimensions,
        changeTheme
        }}>
      {children}
    </ThemeContext.Provider>
  );

};

export default ThemeProvider;

export const useTheme = () => {
  return useContext(ThemeContext);
};
