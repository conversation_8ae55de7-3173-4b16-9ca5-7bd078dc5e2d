
import { useContext, createContext, useState, useEffect } from "react";





const PermissionContext = createContext();
const PermissionProvider = ({ children }) => {
    const [pathPermission]=useState(
        {
            Procurement_Hub:[
                "order",
                "received",
                "inventory",
                "return",
                "notes",
                "vendor-list",
                "report",
                "myproducts",
                "budget"
            ],
            Insurance_Verification:[
                "insurance"
            ],
            Patient_Experience:[
                "patient-experience"
            ]
        }
    )

}
export default PermissionProvider;

export const permissionAuth = () => {
  return useContext(PermissionContext);
};