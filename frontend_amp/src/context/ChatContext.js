import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './AuthContext';
import { chatService } from '../services/chatService';
import CONFIG from '../config';

const ChatContext = createContext();

export const useChat = () => useContext(ChatContext);

export const ChatProvider = ({ children }) => {
  const [conversations, setConversations] = useState([]);
  const [activeConversation, setActiveConversation] = useState(null);
  const [unreadMessages, setUnreadMessages] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [socketStatus, setSocketStatus] = useState('disconnected'); // 'disconnected', 'connecting', 'connected'
  const socketRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const auth = useAuth();

  // Define handleNewMessage before it's used in setupWebSocket
  const handleNewMessage = useCallback((message) => {
    // Update conversations with new message
    setConversations(prevConversations => {
      const updatedConversations = prevConversations.map(conv => {
        if (conv.id === message.conversation_id) {
          // Update last message
          const updatedConv = {
            ...conv,
            last_message: {
              id: message.id,
              content: message.content,
              sender_id: message.sender_id,
              sender_name: message.sender_name,
              created_at: message.created_at,
              is_read: message.is_read
            },
            updated_at: message.created_at
          };
          
          // Update unread count if message is from someone else and user is authenticated
          if (auth.user && auth.user.id && message.sender_id !== auth.user.id) {
            setUnreadMessages(prev => ({
              ...prev,
              [conv.id]: (prev[conv.id] || 0) + 1
            }));
          }
          
          return updatedConv;
        }
        return conv;
      });
      
      // Sort conversations by most recent message
      return updatedConversations.sort((a, b) => 
        new Date(b.updated_at) - new Date(a.updated_at)
      );
    });
  }, [auth.user]);

  // Load conversations from API
  useEffect(() => {
    if (auth.user && auth.user.id) {
      fetchConversations();
    }
  }, [auth.user]);

  // Set up WebSocket connection with improved error handling and reconnection
  const setupWebSocket = useCallback(() => {
    // Only connect if user is authenticated and has an ID
    if (!auth.user || !auth.user.id) {
      setSocketStatus('disconnected');
      return;
    }
    
    // Clear any existing reconnect timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    // Close existing socket if it exists
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }
    
    try {
      setSocketStatus('connecting');
      const wsUrl = `${CONFIG.wsUrl}/ws/chat/${auth.user.id}`;
      console.log('Connecting to WebSocket:', wsUrl);
      
      const ws = new WebSocket(wsUrl);
      socketRef.current = ws;
      
      ws.onopen = () => {
        console.log('WebSocket connected');
        setSocketStatus('connected');
        
        // Send a ping message to test the connection
        ws.send(JSON.stringify({ type: 'ping' }));
      };
      
      ws.onmessage = (event) => {
        console.log('WebSocket message received:', event.data);
        try {
          const data = JSON.parse(event.data);
          
          if (data.type === 'chat_message') {
            handleNewMessage(data.message);
          } else if (data.type === 'connection_established') {
            console.log('Connection confirmed by server:', data.message);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
      
      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setSocketStatus('disconnected');
        setError('WebSocket connection error');
      };
      
      ws.onclose = (event) => {
        console.log(`WebSocket disconnected with code ${event.code}:`, event.reason);
        setSocketStatus('disconnected');
        
        // Attempt to reconnect after a delay, with exponential backoff
        const reconnectDelay = socketRef.current ? 3000 : 1000;
        console.log(`Attempting to reconnect in ${reconnectDelay}ms...`);
        
        reconnectTimeoutRef.current = setTimeout(() => {
          if (auth.user && auth.user.id) {
            console.log('Attempting to reconnect WebSocket...');
            setupWebSocket();
          }
        }, reconnectDelay);
      };
    } catch (error) {
      console.error('Error setting up WebSocket:', error);
      setSocketStatus('disconnected');
      setError('Failed to set up WebSocket connection');
      
      // Try to reconnect after a delay
      reconnectTimeoutRef.current = setTimeout(() => {
        if (auth.user && auth.user.id) {
          setupWebSocket();
        }
      }, 5000);
    }
  }, [auth.user, handleNewMessage]);

  // Initialize WebSocket when auth changes
  useEffect(() => {
    setupWebSocket();
    
    // Clean up on unmount
    return () => {
      if (socketRef.current) {
        socketRef.current.close();
        socketRef.current = null;
      }
      
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    };
  }, [auth.user, setupWebSocket]);

  const fetchConversations = async () => {
    // Only fetch if user is authenticated and has an ID
    if (!auth.user || !auth.user.id) return;
    
    try {
      setLoading(true);
      setError(null);
      const data = await chatService.getConversations(auth.token);
      
      // Process conversations to add additional info
      const processedConversations = data.map(conv => {
        // Find the other participant (not the current user)
        const otherParticipant = conv.participants.find(
          p => p.user_id !== auth.user.id
        );
        
        return {
          ...conv,
          otherParticipant: otherParticipant || null,
          displayName: otherParticipant ? otherParticipant.username : 'Unknown'
        };
      });
      
      setConversations(processedConversations);
      
      // Update unread counts
      const unreadCounts = {};
      processedConversations.forEach(conv => {
        unreadCounts[conv.id] = conv.unread_count || 0;
      });
      setUnreadMessages(unreadCounts);
      
    } catch (error) {
      console.error('Error fetching conversations:', error);
      setError('Failed to load conversations');
    } finally {
      setLoading(false);
    }
  };

  // Function to send a message
  const sendMessage = async (conversationId, content) => {
    // Only send if user is authenticated
    if (!auth.user || !auth.user.id) {
      setError('You must be logged in to send messages');
      return null;
    }
    
    try {
      const sentMessage = await chatService.sendMessage(conversationId, content, auth.token);
      
      // Update the conversation with the new message
      handleNewMessage(sentMessage);
      
      return sentMessage;
    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message');
      return null;
    }
  };

  // Function to create a new conversation
  const createConversation = async (userId) => {
    // Only create if user is authenticated
    if (!auth.user || !auth.user.id) {
      setError('You must be logged in to create conversations');
      return null;
    }
    
    try {
      const newConversation = await chatService.createConversation(userId, auth.token);
      
      // Refresh conversations to include the new one
      await fetchConversations();
      
      return newConversation;
    } catch (error) {
      console.error('Error creating conversation:', error);
      setError('Failed to create conversation');
      return null;
    }
  };

  // Function to mark messages as read
  const markMessagesAsRead = async (conversationId) => {
    // Only mark as read if user is authenticated
    if (!auth.user || !auth.user.id) return false;
    
    try {
      // Send read status to server via WebSocket
      if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
        socketRef.current.send(JSON.stringify({
          type: 'read_messages',
          conversation_id: conversationId
        }));
        console.log(`Sent read_messages for conversation ${conversationId}`);
      } else {
        console.warn('WebSocket not connected, cannot mark messages as read');
        // Try to reconnect the socket
        setupWebSocket();
      }
      
      // Update local state
      setUnreadMessages(prev => ({
        ...prev,
        [conversationId]: 0
      }));
      
      return true;
    } catch (error) {
      console.error('Error marking messages as read:', error);
      return false;
    }
  };

  // Get total unread count across all conversations
  const getTotalUnreadCount = () => {
    // Return 0 if user is not authenticated
    if (!auth.user || !auth.user.id) return 0;
    
    return Object.values(unreadMessages).reduce((total, count) => total + count, 0);
  };

  return (
    <ChatContext.Provider
      value={{
        conversations,
        activeConversation,
        setActiveConversation,
        unreadMessages,
        loading,
        error,
        socketStatus,
        sendMessage,
        createConversation,
        markMessagesAsRead,
        refreshConversations: fetchConversations,
        getTotalUnreadCount,
        isAuthenticated: !!(auth.user && auth.user.id)
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};
