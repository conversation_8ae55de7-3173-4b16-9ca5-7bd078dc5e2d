import { useContext, createContext, useState, useEffect } from "react";
import axios from 'axios';
import { green, grey, pink, yellow } from '@mui/material/colors'

import {
	BASE_URL,
    DEV_BASE_URL,
} from '../Environments';
import { useAuth } from "./AuthContext";
import { diff_months_count } from "../utils/functions";
const DashboardContext = createContext();

//   (1, 'back_order'),
//   (2, 'incoming'),
//   (3, 'in_progress'),
//   (4, 'confirmed'),
//   (5, 'partially_delivered'),
//   (6, 'delivered'),
//   (7, 'in_storage'),
//   (8, 'returned'),
// )

const DashboardProvider = ({ children }) => {
  const initialOrderStatus = {
    'Backorder':1,
    'Incoming':2,
    'In-Progress':3,
    'Confirmed':4,
    'Partially Delivered':5,
    'Delivered':6,
    'In Storage':7,
    'Returned':8,
    'Partially Returned':9,
    'Not Available':10
  }
  const reverseOrderStatus={
    1:'Backorder',
    2:'Incoming',
    3:'In-Progress',
    4:'Confirmed',
    5:'Partially Delivered',
    6:'Delivered',
    7:'In Storage',
    8:'Returned',
    9:'Partially Returned',
    10:'Not Available'
  }  
  const user=useAuth()
  const [prevSelectUser,setPrevSelectUser]=useState(null)
  const [calcPaidTemlate,setCalcPaidTemplate]=useState(0)
  const [ordersTableData,setOrderTableData]=useState(Object(null))
  const [totalBudgetLeftData,setTotalBudgetLeftData]=useState(0)
  const [receivedTableData,setReceivedTableData]=useState(Object(null))
  const [inventoriesTableData,setInventoriesTableData]=useState([])
  const [sortedByQty, setSortedByQty] = useState([]);
  const [sortedByExpiration, setSortedByExpiration] = useState([]);
  const [sortedByExpirationAndQty, setSortedByExpirationAndQty] = useState([]);
  
  const [returnTableData,setReturnTableData]=useState(Object(null))

  const [notesTableData,setNotesTableData]=useState(Object(null))
  const [vendorsTableData,setVendorsTableData]=useState(Object(null))
  const [reportTableData,setReportTableData]=useState([Object(null)])
  const [summaryReportTableData,setSummaryReportTableData]=useState([Object(null)])

  const [insuranceTableData,setInsuranceTableData]=useState(Object(null))
  const [patientExperienceTableData,setPatientExperienceTableData]=useState(Object(null))
  const [orderStatus,setOrderStatus]=useState(initialOrderStatus)
  const [blogsData,setBlogsData]=useState(Object(null))
  const [blogData,setBlogData]=useState(Object(null))
  const [blogPictures,setblogPictures]=useState([])
  const [blogFile,setblogFile]=useState('')
  const [blogReviewData,setBlogReviewData]=useState(Object(null))

  const [invoicesTableData,setInvoicesTableData]=useState(Object(null))
  const [invoiceFile,setInvoiceFile]=useState('');
  const [refetch,setRefetch]=useState(
      {
        refetchOrders:false,
        refetchVendors:false,
        refetchInventories:false,
        refetchNotes:false,
        refetchInsurance:false,
        refetchPatienExperience:false,
        refetchTotalBudgetLeft:false,
      }
    )
  const [infoInventoryData,setInfoInventoryData]=useState(Object({
    lowQtyIndex:[],
    expirationDataIndex:[]
  }))
  // const [insertOrderStatus,setinsertOrderStatus]=useState({ msg: '', status: 0 })
  // const [updateOrderStatus,setUpdateOrderStatus]=useState({ msg: '', status: 0 })
  // const [updateOrderPaidStatus,setUpdateOrderPaidStatus]=useState({ msg: '', status: 0 })
  // const [deleteOrderStatus,setDeleteOrderStatus]=useState({ msg: '', status: 0 })
  // const [insertVendorStatus,setInsertVendorStatus]=useState({ msg: '', status: 0 })
  // const [updateVendorStatus,setUpdateVendorStatus]=useState({ msg: '', status: 0 })
  // const [updateVendorStatus,setUpdateVendorStatus]=useState({ msg: '', status: 0 })
  // const [insertInventoryStatus,setInsertInventoryStatus]=useState({ msg: '', status: 0 })
  // const [deleteInventoryStatus,setDeleteInventoryStatus]=useState({ msg: '', status: 0 })
  // const [updateInventoryStatus,setUpdateInventoryStatus]=useState({ msg: '', status: 0 })
  // const [updateTotalBudgetStatus,setUpdateTotalBudgetStatus]=useState({ msg: '', status: 0 })
  
  useEffect(()=>{
    let reportData = new Object();
    Object.keys(ordersTableData).map((order,index)=>{
      if(ordersTableData[order].received&&ordersTableData[order].status!=='returned'){
        //received area
      }
      if(ordersTableData[order].received&&ordersTableData[order].status==='returned'){
        //return area
      } 
      if(ordersTableData[order].received||ordersTableData[order].status=='incoming'){
        //report area
        let [year, month, day]=ordersTableData[order].created_at.split('-')
        if (!reportData.hasOwnProperty(year)){
          reportData[year]={
            1:[],
            2:[],
            3:[],
            4:[],
            5:[],
            6:[],
            7:[],
            8:[],
            9:[],
            10:[],
            11:[],
            12:[],
          }
        }else{

        }
        reportData[parseInt(year)][parseInt(month)].push(ordersTableData[order])

      }
      if(!ordersTableData[order].received&&ordersTableData[order].status!=='returned'){
        //order area
      }
      if(ordersTableData[order].my_product){
        //product area 
      }
    })
    setReportTableData(reportData)
    // setCalcPaidTemplate(0)
  },[ordersTableData])
  useEffect(()=>{
    let infoInventoryData = new Object({
      lowQtyIndex:[],
      expirationDataIndex:[]
    });
    Object.keys(inventoriesTableData).map((inventory,index)=>{
      if(inventoriesTableData[inventory].qty<inventoriesTableData[inventory].min_qty){
        let newList = infoInventoryData.lowQtyIndex.concat(inventoriesTableData[inventory].id);
        infoInventoryData.lowQtyIndex= newList
      }
      if(diff_months_count(inventoriesTableData[inventory].expiration_date)){
        let newList = infoInventoryData.expirationDataIndex.concat(inventoriesTableData[inventory].id);
        infoInventoryData.expirationDataIndex=newList
      }
    })
    setInfoInventoryData({
      ...infoInventoryData,
      infoInventoryData
    }) 
    const currentDate = new Date();  // Current machine date

    // 1. Separate low qty items (qty <= min_qty)
    const lowQtyItems = inventoriesTableData.filter(item => item.qty < item.min_qty);

    // 2. Check for expiration dates within the next 6 months
    const sixMonthExpirationItems = inventoriesTableData.filter(item => {
      if (item.expiration_date) {
        const expirationDate = new Date(item.expiration_date);
        const timeDiff = expirationDate - currentDate;
        const diffMonths = timeDiff / (1000 * 3600 * 24 * 30); // Convert time difference to months
        return diffMonths <= 6;
      }
      return false;
    });

    // 3. Sort lowQtyItems by expiration date and qty (although expiration doesn't matter for low qty)
    const normalItemslLowqty = inventoriesTableData.filter(item => !lowQtyItems.includes(item));
    const sortedByQty = [...lowQtyItems, ...normalItemslLowqty];

    // 4. Sort sixMonthExpirationItems by expiration date
    const sortedSixMonthExpirationItems = sixMonthExpirationItems.sort((a, b) => {
      const dateA = a.expiration_date ? new Date(a.expiration_date).getTime() : Infinity;
      const dateB = b.expiration_date ? new Date(b.expiration_date).getTime() : Infinity;
      return dateA - dateB;
    });
    const normalItemsExpiration = inventoriesTableData.filter(item => !sixMonthExpirationItems.includes(item));
    const sortedByExpiration = [...sixMonthExpirationItems, ...normalItemsExpiration];

    // 5. Sort normal items by expiration date and qty
    const bothConditions = sixMonthExpirationItems.filter(expItem =>
      lowQtyItems.some(lowItem => lowItem.id === expItem.id)
    );
    // Unique only-expire and only-low-qty
    const onlyExpire = sixMonthExpirationItems.filter(item =>
      !bothConditions.some(dupe => dupe.id === item.id)
    );
    const onlyLowQty = lowQtyItems.filter(item =>
      !bothConditions.some(dupe => dupe.id === item.id)
    );
    
    // All other items (not in any condition)
    const normalItemsBothCondition = inventoriesTableData.filter(item =>
      !bothConditions.some(i => i.id === item.id) &&
      !onlyExpire.some(i => i.id === item.id) &&
      !onlyLowQty.some(i => i.id === item.id)
    );
    const finalSortedInventory = [
      ...bothConditions,
      ...onlyExpire,
      ...onlyLowQty,
      ...normalItemsBothCondition,
    ];
    setSortedByQty(sortedByQty)
    setSortedByExpiration(sortedByExpiration)
    setSortedByExpirationAndQty(finalSortedInventory)

  }, [inventoriesTableData]);
  // useEffect(()=>{
    // let inventoriesTableDataClone=inventoriesTableData.clone()
    // let infoInventoryData = new Object({
    //   lowQtyIndex:[],
    //   expirationDataIndex:[]
    // });
    // Object.keys(inventoriesTableData).map((inventory,index)=>{
      
      // if(inventoriesTableData[inventory].qty<inventoriesTableData[inventory].min_qty){
      //   let newList = infoInventoryData.lowQtyIndex.concat(inventoriesTableData[inventory].id);
      //   infoInventoryData.lowQtyIndex= newList
      // }
      // if(diff_months_count(inventoriesTableData[inventory].expiration_date)){
      //   let newList = infoInventoryData.expirationDataIndex.concat(inventoriesTableData[inventory].id);
      //   infoInventoryData.expirationDataIndex=newList
      // }
    // })
    // setInfoInventoryData({
    //   ...infoInventoryData,
    //   infoInventoryData
    // }) 
  // },[inventoriesTableData])
  const getOrders =async (token,user_id) => {
    try {
      const response = await fetch(DEV_BASE_URL +'/order/list_user_orders/'+user_id+'/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        // body: JSON.stringify(data),
      });
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setOrderTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    } catch (err) {
      return {msg:err,status:404}
    }
  };
  const insertOrder=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/order/create_order/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        // setinsertOrderStatus({msg:'success',status:status})
        setOrderTableData(res)
        // console.log(status)
        return {msg:'success',status:status}
      }else{
        // setinsertOrderStatus({msg:res.data.detail,status:status})
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      // setinsertOrderStatus({msg:err,status:404})
      return {msg:err,status:404}
    }
  }
  const updateOrder=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL+'/order/update_order/', {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setOrderTableData(res)
        // setUpdateOrderStatus({msg:'success',status:status})
        return {msg:'success',status:status}
      }else{
        // setUpdateOrderStatus({msg:res.detail,status:status})
        return {msg:res.detail,status:status}
      }
    }catch (err) {
      // setinsertOrderStatus({msg:err,status:404})
      return {msg:err,status:404}
    }
  }
  const updatePaid=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL+'/order/update_paid/', {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setOrderTableData(res)
        // setUpdateOrderPaidStatus({msg:'success',status:status})
        return {msg:'success',status:status}
      }else{
        // setUpdateOrderPaidStatus({msg:res.detail,status:status})
        return {msg:res.detail,status:status}
      }
    }catch (err) {
      // setUpdateOrderPaidStatus({msg:err,status:404})
      return {msg:err,status:404}
    }
  }
  const deleteOrder=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/order/delete_order/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setOrderTableData(res)
        // setDeleteOrderStatus({msg:'success',status:status})
        return {msg:'success',status:status}
      }else{
        // setDeleteOrderStatus({msg:res.detail,status:status})
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      // setDeleteOrderStatus({msg:err,status:404})
      return {msg:err,status:404}
    }
  }
  const getVendors =async (token,user_id) => {
    try {
      const response =await fetch(DEV_BASE_URL +'/vendor/list_user_vendors/'+user_id+'/', {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      // body: JSON.stringify(data),
    })
    const res = await response.json();
    const status=response.status;
    if(res){
      setVendorsTableData(res)
      return {msg:'success',status:status}
    }else{
      return {msg:res.detail,status:status}
    }
    }catch(err){
      return {msg:err,status:404}
    }
  };
  const insertVendor=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/vendor/create_vendor/', {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setVendorsTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const updateVendor=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL+'/vendor/update_vendor/', {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setVendorsTableData(res)
        // setUpdateVendorStatus({msg:'success',status:status})
        return {msg:'success',status:status}
      }else{
        // setUpdateVendorStatus({msg:res.detail,status:status})
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      // setUpdateVendorStatus({msg:err,status:404})
      return {msg:err,status:404}
    }
  }
  const deleteVendor=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL+'/vendor/delete_vendor/', {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setVendorsTableData(res)
        // setUpdateVendorStatus({msg:'success',status:status})
        return {msg:'success',status:status}
      }else{
        // setUpdateVendorStatus({msg:res.detail,status:status})
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      // setUpdateVendorStatus({msg:err,status:404})
      return {msg:err,status:404}
    }
  }
  const getBudgetLeft =async (token,user_id) => {
    try {
      const response = await fetch(DEV_BASE_URL +'/invoices/total_budget_left/'+user_id+'/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        // body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setTotalBudgetLeftData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      // setUpdateVendorStatus({msg:err,status:404})
      return {msg:err,status:404}
    }
  };
  const updateTotalBudget=async (token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/user/update_budget/', {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        // setUpdateTotalBudgetStatus({msg:'success',status:status})
        return {msg:'success',status:status}
      }else{
        // setUpdateTotalBudgetStatus({msg:res.detail,status:status})
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      // setUpdateTotalBudgetStatus({msg:err,status:404})
      return {msg:err,status:404}
    }
  }
  const getInventory=async (token,user_id,callback)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/inventory/list_user_inventory/'+user_id+'/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        // body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;

      if(res){
        callback(res)
        setInventoriesTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
      }catch(err){
        return {msg:err,status:404}
      }
  }
  const getInventoryById=async (token,inventory_id)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/inventory/get_by_id/'+inventory_id+'/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        // body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setInventoriesTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
      }catch(err){
        return {msg:err,status:404}
      }

  }
  const insertInventory=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/inventory/create_inventory/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setInventoriesTableData(res)
        // setInsertInventoryStatus({msg:'success',status:status})
        return {msg:'success',status:status}
      }else{
        // setInsertInventoryStatus({msg:res.detail,status:status})
          return {msg:res.detail,status:status}
        }
    }
    catch (err) {
      // setInsertInventoryStatus({msg:err,status:404})
      return {msg:err,status:404}
    }
  }
  const deleteInventory=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/inventory/delete_inventory/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setInventoriesTableData(res)
        // setDeleteInventoryStatus({msg:'success',status:status})
        return {msg:'success',status:status}
      }else{
        // setDeleteInventoryStatus({msg:res.detail,status:status})
        return {msg:res.detail,status:status}
        }
    }
    catch (err) {
      // setDeleteInventoryStatus({msg:err,status:404})
      return {msg:err,status:404}
    }
  }
  const updateInventory=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/inventory/update_inventory/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setInventoriesTableData(res)
        // setUpdateInventoryStatus({msg:'success',status:status})
        return {msg:'success',status:status}
      }else{
        // setUpdateInventoryStatus({msg:res.detail,status:status})
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      // setUpdateInventoryStatus({msg:err,status:404})
      return {msg:err,status:404}
    }
  }
  const getInsurance=async (token,user_id)=>{
    try {
      const response =await fetch(DEV_BASE_URL +'/insurance/list_user_insurance/'+user_id+'/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        // body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setInsuranceTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }catch (err) {
      return {msg:err,status:404}
    }
  }
  const insertInsurance=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/insurance/create_insurance/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setInsuranceTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const updateInsurance=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/insurance/update_insurance/', {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setInsuranceTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      // setUpdateInventoryStatus({msg:err,status:404})
      return {msg:err,status:404}
    }
  } 
  const deleteInsurance=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/insurance/delete_insurance/', {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setInsuranceTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
        }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const getPatientExperience=async (token,user_id)=>{
    try {
      const response =await fetch(DEV_BASE_URL +'/patient_experience/list_user_patient_experience/'+user_id+'/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        // body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setPatientExperienceTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }catch (err) {
      return {msg:err,status:404}
    }
  }
  const insertPatientExperience=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/patient_experience/create_patient_experience/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setPatientExperienceTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const updatePatientExperience=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/patient_experience/update_patient_experience/', {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setPatientExperienceTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      // setUpdateInventoryStatus({msg:err,status:404})
      return {msg:err,status:404}
    }
  } 
  const deletePatientExperience=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/patient_experience/delete_patient_experience/', {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setPatientExperienceTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
        }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const getNotes=async (token,user_id)=>{
    try {
      const response =await fetch(DEV_BASE_URL +'/notes/get_notes/'+user_id+'/', {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        // body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setNotesTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }catch (err) {
      return {msg:err,status:404}
    }
  }
  const insertNotes=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/notes/create_notes/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setNotesTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const updateNotes=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/notes/update_notes/', {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setNotesTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      // setUpdateInventoryStatus({msg:err,status:404})
      return {msg:err,status:404}
    }
  } 
  const deleteNotes=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/notes/delete_notes/', {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setNotesTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
        }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const updateFavorite=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/order/update_favorite/', {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setOrderTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
        }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const getBlog=async(token)=>{
    try {
      const response =await fetch(DEV_BASE_URL +'/blogs/get_blogs/', {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        // body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setBlogsData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }catch (err) {
      return {msg:err,status:404}
    }
  }
  const getBlogById=async(token,blog_id)=>{
    try {
      const response =await fetch(DEV_BASE_URL +'/blogs/get_blog_by_id/'+blog_id, {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        // body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setBlogData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }catch (err) {
      return {msg:err,status:404}
    }
  }
  const inserBlog=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/blogs/create_blog', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setBlogsData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const editBlog=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/blogs/edit_blog', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setBlogsData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const editBlogStatus=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/blogs/edit_blog_status', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setBlogsData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const deleteBlog=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/blogs/delete_blog', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setBlogsData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const uploadBlogPicture=async(token,data,progress)=>{
    try{
      const formData = new FormData();
      const files=[]
      formData.append("file", data.blogphotos,data.blogphotos.name)
      formData.append("post_title", data.post_title)

      const response=await axios.post(DEV_BASE_URL +'/blogs/add_blog/upload_blog_picture', 
        formData, 
        { 
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            console.log(percentCompleted)
            // setUploadProgress(percentCompleted);
            progress(percentCompleted)
          },
        headers: {
          Accept: "application/json",
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${token}`,
        },
        // body:JSON.stringify({"first_name":data.name})
       })
      const res=await response.data
      const status=response.status
      if(status==200){
        setblogPictures(res.filename)
        // setUsers(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err){
      return {msg:err,status:404}
    }
  }
  const uploadBlogFile=async(token,data,progress)=>{
    try{
      const formData = new FormData();
      const files=[]
      formData.append("file", data.blogFile,data.blogFile.name)
      formData.append("post_title", data.post_title)

      const response=await axios.post(DEV_BASE_URL +'/blogs/add_blog/upload_blog_file', 
        formData, 
        { 
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            console.log(percentCompleted)
            // setUploadProgress(percentCompleted);
            progress(percentCompleted)
          },
        headers: {
          Accept: "application/json",
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${token}`,
        },
        // body:JSON.stringify({"first_name":data.name})
       })
      const res=await response.data
      const status=response.status
      if(status==200){
        setblogFile(res.filename)
        // setUsers(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err){
      return {msg:err,status:404}
    }
  }
  const getInvoices=async(token,user_id)=>{
    try {
      const response =await fetch(DEV_BASE_URL +'/invoices/get_invoices/'+user_id+'/', {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        // body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setInvoicesTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }catch (err) {
      return {msg:err,status:404}
    }
  }
  const inserInvoice=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/invoices/create_invoice', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setInvoicesTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const editInvoice=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/invoices/edit_invoice', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setInvoicesTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const deleteInvoice=async(token,data)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/invoices/delete_invoice', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setInvoicesTableData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err) {
      return {msg:err,status:404}
    }
  }
  const uploadInvoiceFile=async(token,data,progress)=>{
    try{
      const formData = new FormData();
      const files=[]
      formData.append("file", data.invoiceFile,data.invoiceFile.name)
      formData.append("vendor", data.vendor)

      const response=await axios.post(DEV_BASE_URL +'/invoices/add_invoice/upload_invoice_file', 
        formData, 
        { 
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            console.log(percentCompleted)
            // setUploadProgress(percentCompleted);
            progress(percentCompleted)
          },
        headers: {
          Accept: "application/json",
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${token}`,
        },
        // body:JSON.stringify({"first_name":data.name})
       })
      const res=await response.data
      const status=response.status
      if(status==200){
        setInvoiceFile(res.filename)
        // setUsers(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err){
      return {msg:err,status:404}
    }
  }
  const initialAllState = async (token, user_id) => {
    console.log("i'm in initial all state")
    getOrders(token, user_id)
    getVendors(token, user_id)
    getBudgetLeft(token, user_id)
    getInventory(token, user_id)
    getInsurance(token, user_id)
    getPatientExperience(token, user_id)
    getNotes(token, user_id)
    getBlog(token, user_id)
    getInvoices(token, user_id)
    getSummaryReports(token, user_id)
    setRefetch(prevRefetch => ({...prevRefetch, refetchOrders: true}))
  }
  const getSummaryReports = async (token, user_id) => {
    try {
      const response = await fetch(DEV_BASE_URL + '/order/summery_reports/' + user_id + '/', {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });
      const res = await response.json();
      const status = response.status;
      if (status === 200) {
        setSummaryReportTableData(res);
        return { msg: 'success', status: status };
      } else {
        return { msg: res.detail, status: status };
      }
    } catch (err) {
      return { msg: err, status: 404 };
    }
  };
  return (
    <DashboardContext.Provider 
      value={{ 
        ordersTableData,
        totalBudgetLeftData,
        receivedTableData,
        inventoriesTableData,
        sortedByQty,
        sortedByExpiration,
        sortedByExpirationAndQty,
        reportTableData,
        summaryReportTableData,
        notesTableData,
        vendorsTableData,
        insuranceTableData,
        patientExperienceTableData,
        initialOrderStatus,
        reverseOrderStatus,
        orderStatus,
        blogData,
        blogsData,
        blogPictures,
        blogFile,
        blogReviewData,
        invoicesTableData,
        invoiceFile,
        infoInventoryData,
        // insertOrderStatus,
        // updateOrderStatus,
        // updateOrderPaidStatus,
        // deleteOrderStatus,
        // insertVendorStatus,
        // updateVendorStatus,
        // updateTotalBudgetStatus,
        // insertInventoryStatus,
        // deleteInventoryStatus,
        // updateInventoryStatus,
        refetch,
        setRefetch,
        getOrders,
        insertOrder,
        updateOrder,
        updatePaid,
        deleteOrder,
        getVendors,
        insertVendor,
        updateVendor,
        deleteVendor,
        getBudgetLeft,
        updateTotalBudget,
        getInventory,
        insertInventory,
        deleteInventory,
        updateInventory,
        getInsurance,
        insertInsurance,
        updateInsurance,
        deleteInsurance,
        getPatientExperience,
        insertPatientExperience,
        updatePatientExperience,
        deletePatientExperience,
        getNotes,
        insertNotes,
        updateNotes,
        deleteNotes,
        updateFavorite,
        getBlog,
        getBlogById,
        inserBlog,
        editBlog,
        editBlogStatus,
        deleteBlog,
        uploadBlogPicture,
        uploadBlogFile,
        setBlogReviewData,
        getInvoices,
        inserInvoice,
        editInvoice,
        deleteInvoice,
        uploadInvoiceFile,
        initialAllState,
        getSummaryReports
      }}
    >
      {children}
    </DashboardContext.Provider>
  );
};
export default DashboardProvider;
export const useDashboard = () => {
  return useContext(DashboardContext);
};
