import { useContext, createContext, useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axios from 'axios';
import {
	BASE_URL,
    DEV_BASE_URL,
} from '../Environments';
import { formatDate } from "../utils/functions";
import { fetchWithAuth } from '../utils/fetchInterceptor';
const AuthContext = createContext();
const getInitialState = () => {
  const tokenFromSessionStorage = sessionStorage.getItem("amp.amedicalpartner.com.token");
  const tokenFromLocalStorage=localStorage.getItem("amp.amedicalpartner.com.token")
  return tokenFromSessionStorage ? JSON.parse(tokenFromSessionStorage) : tokenFromLocalStorage?JSON.parse(tokenFromLocalStorage):''
}
const getInitialStateSelectUser = () => {
  const selectUserFromSessionStorage = sessionStorage.getItem("amp.amedicalpartner.com.selectuser");
  const selectUserFromLocalStorage=localStorage.getItem("amp.amedicalpartner.com.selectuser")
  return selectUserFromSessionStorage ? JSON.parse(selectUserFromSessionStorage) : selectUserFromLocalStorage?JSON.parse(selectUserFromLocalStorage):Object(null)
}
const getInitialUserInfo=()=>{
  const userInfo=localStorage.getItem("amp.amedicalpartner.com.userinfo")
  return userInfo?JSON.parse(userInfo):Object(null)
}
const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(getInitialUserInfo);
  const [subscription,setSubscription]=useState([])
  const [userProfilePicture,setUserProfilePicture]=useState('')
//   const [is_admin, setIsAdmin] = useState(false);
//   const [user_group,setUserGroup]=useState(3);
  const [authStatus,setAuthStatus]=useState({ msg: '', status: 0 })
  const [selectUser,setSelectUser]=useState(getInitialStateSelectUser);
  const [userList,setUserList]=useState([])
  const [users,setUsers]=useState(Object(null))
  const [token, setToken] = useState(getInitialState);//useState(sessionStorage.getItem("amp.amedicalpartner.com")||localStorage.getItem("amp.amedicalpartner.com") || "");
  const [notificationData,setNotificationData]=useState(Object(null))
  const navigate = useNavigate();
  const [isLoading,setIsLoading]=useState(false)
  useEffect(() => {
    sessionStorage.setItem("amp.amedicalpartner.com.token", JSON.stringify(token))
    localStorage.setItem("amp.amedicalpartner.com.token", JSON.stringify(token));
}, [token])
useEffect(() => {
  sessionStorage.setItem("amp.amedicalpartner.com.selectuser", JSON.stringify(selectUser))
  localStorage.setItem("amp.amedicalpartner.com.selectuser", JSON.stringify(selectUser));
}, [selectUser])
useEffect(()=>{
  if(selectUser){
    Object.keys(userList).map((user,index)=>{
      if(userList[user].id==selectUser.id){
        setSelectUser(userList[user])
        // if(selectUser){
        //   if(prevSelectUser!=selectUser.id){
        //     setPrevSelectUser(selectUser.id)
        //   }
        // }
      }
      
      
    })
  }
  const sortedUsers = userList.sort((a, b) => {
          const nameA = a.first_name.toUpperCase();  // تبدیل به حروف بزرگ برای مقایسه درست
          const nameB = b.first_name.toUpperCase();  // تبدیل به حروف بزرگ برای مقایسه درست

          if (nameA < nameB) {
            return -1;  // اگر نام A قبل از نام B باشد
          }
          if (nameA > nameB) {
            return 1;  // اگر نام A بعد از نام B باشد
          }
          return 0;  // اگر نام‌ها برابر باشند
        });

        // ذخیره کردن داده‌ها در استیت
        setUserList(sortedUsers);
},[userList])
useEffect(() => {
  // Check token validity on app start and when token changes
  if (token) {
    validateToken(token);
  }
}, [token]);

  const loginAction = async (data) => {
    const formData = new FormData();
    formData.append("username", data.username)
    formData.append("password", data.password)
    try {
      const response = await fetch(DEV_BASE_URL +'/auth/login/', 
        {
          method: "POST",
          body: formData
        });
      const res = await response.json();
      const status = response.status;
      
      if(status == 200){
        setIsLoading(true);
        // Set token immediately
        setToken(res.token);
        
        // Store token in both storage locations
        sessionStorage.setItem("amp.amedicalpartner.com.token", JSON.stringify(res.token));
        localStorage.setItem("amp.amedicalpartner.com.token", JSON.stringify(res.token));
        
        // Get user info with the new token
        const userInfoResult = await getUserInfoAction(res.token);
        
        // Return success only if we got user info successfully
        if (userInfoResult && userInfoResult.status === 200) {
          return {msg:'success', status:status}
        } else {
          return {msg:'Failed to get user information', status:status}
        }
      } else {
        return {msg:res.detail, status:status}
      }
    } catch (err) {
      return {msg:err.message || 'Network error', status:404}
    }
  };
  const getUserInfoAction = async(reqToken) => {
    try {
      const response = await fetchWithAuth(DEV_BASE_URL + '/user/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });
      
      const res = await response.json();
      const status = response.status;
      
      if(status == 200){
        // Set user state
        setUser(res);
        
        // Store user info in localStorage
        localStorage.setItem("amp.amedicalpartner.com.userinfo", JSON.stringify(res));
        
        setAuthStatus({msg:'success', status:status})
        
        if(res.is_admin){
          // Get user list if admin
          await getUserListAction(reqToken || token)
          setIsLoading(false);
          return {msg:'success', status:status}
        } else {
          setIsLoading(false);
          return {msg:'success', status:status}
        }
      }
      else {
        setIsLoading(false);
        setAuthStatus({msg:res.detail, status:status})
        return {msg:res.detail, status:status}
      }
    } 
    catch (err){
      setAuthStatus({msg:err.message || 'Unknown error', status:404})
      setIsLoading(false);
      return {msg:err.message || 'Unknown error', status:404}
    }
  };
  const getsubscription=async(reqToken)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/user/subscription_list', {
        method: "get",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token?token:reqToken}`,
        },
        // body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setSubscription(res);
        return {msg:"success",status:status}      }
      else{
        return {msg:res.detail,status:status}
      }
    } 
    catch (err){
        return {msg:err,status:404}
    }
  };
  const getUserListAction=async(reqToken)=>{
    try {
      const response = await fetch(DEV_BASE_URL +'/user/user_list', {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token?token:reqToken}`,
        },
        // body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        // console.log(res)
        setUserList(res);
        setIsLoading(false);
        return 
      }
      else{
        // console.log(res)
        setIsLoading(false);
        navigate("/signin");
        return
      }
      throw new Error(res.message);
    } 
    catch (err){
        console.log(err.message);
        setIsLoading(false);
        setAuthStatus({msg:err,status:404})
        return
    }finally{
      setIsLoading(false);
      return
    }
    
  }
  const getUsersAction=async(reqToken)=>{
    try {
      const response =await fetch(DEV_BASE_URL +'/user/users', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token?token:reqToken}`,
        },
        // body: JSON.stringify(data),
      }) 
      const res = await response.json();
      const status=response.status;
      if(status==200){
          setUsers(res);
          setIsLoading(false);
          return {msg:'success',status:status} 
        }
        else{
          return {msg:res.detail,status:status}
        }
      }
      catch (err){
        return {msg:err,status:404}
      }
  }
  const insertUser=async(reqToken,data)=>{
    try{
      const response=await fetch(DEV_BASE_URL +'/user/create_user', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token?token:reqToken}`,
        },
        body: JSON.stringify(data),
      })
      const res=await response.json()
      const status=response.status
      if(status==200){
        setUsers(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err){
      return {msg:err,status:404}

  }
  }
  const updateUser=async(reqToken,data)=>{
    try{
      const formData = new FormData();
      formData.append("dental_logo", data.dental_logo)
      const response=await fetch(DEV_BASE_URL +'/user/update_user', {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token?token:reqToken}`,
        },
        body: JSON.stringify(data),
      })
      const res=await response.json()
      const status=response.status
      if(status==200){
        setUsers(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err){
      return {msg:err,status:404}
  }
  }
  const uploadPicture=async(reqToken,data,progress)=>{
    try{
      const formData = new FormData();
      formData.append("file", data.dental_logo,data.dental_logo.name)
      formData.append("first_name", data.first_name)

      const response=await axios.post(DEV_BASE_URL +'/user/upload_picture', 
        formData, 
        { 
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            console.log(percentCompleted)
            // setUploadProgress(percentCompleted);
            progress(percentCompleted)
          },
        headers: {
          Accept: "application/json",
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${token?token:reqToken}`,
        },
        // body:JSON.stringify({"first_name":data.name})
       })
      const res=await response.data
      const status=response.status
      if(status==200){
        setUserProfilePicture(res.filename)
        // setUsers(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err){
      return {msg:err,status:404}
    }
  }
  const deleteUser=async(reqToken,data)=>{
    try{
      const response=await fetch(DEV_BASE_URL +'/user/delete_user', {
        method: "DELETE",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token?token:reqToken}`,
        },
        body: JSON.stringify(data),
      })
      const res=await response.json()
      const status=response.status
      if(status==200){
        setUsers(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }
    catch (err){
      return {msg:err,status:404}

  }
  }
  const getNotifications=async(reqToken)=>{
    try{
      const response = await fetch(DEV_BASE_URL +'/notification/get_notification/', {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token?token:reqToken}`,
        },
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setNotificationData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }catch (err) {
      return {msg:err,status:404}
    }
  }
  const readNotifications=async(reqToken,data)=>{
    try{
      const response = await fetch(DEV_BASE_URL +'/notification/read_notification/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token?token:reqToken}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setNotificationData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }catch (err) {
      return {msg:err,status:404}
    }
  }
  const readSingleUserNotifications=async(reqToken,data)=>{
    try{
      const response = await fetch(DEV_BASE_URL +'/notification/read_single_user_notification/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token?token:reqToken}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setNotificationData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }catch (err) {
      return {msg:err,status:404}
    }
  }
  const readMarkNotifications=async(reqToken,data)=>{
    try{
      const response = await fetch(DEV_BASE_URL +'/notification/read_mark_notification/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token?token:reqToken}`,
        },
        body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setNotificationData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }catch (err) {
      return {msg:err,status:404}
    }
  }
  const readAllNotifications=async(reqToken,data)=>{
    try{
      const response = await fetch(DEV_BASE_URL +'/notification/read_all_notification/', {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token?token:reqToken}`,
        },
        // body: JSON.stringify(data),
      })
      const res = await response.json();
      const status=response.status;
      if(status==200){
        setNotificationData(res)
        return {msg:'success',status:status}
      }else{
        return {msg:res.detail,status:status}
      }
    }catch (err) {
      return {msg:err,status:404}
    }
  }
  const logOut = () => {
    setUser(null);
    setToken("");
    sessionStorage.removeItem("amp.amedicalpartner.com.token");
    localStorage.removeItem("amp.amedicalpartner.com.token");
    sessionStorage.removeItem("amp.amedicalpartner.com.selectuser");
    localStorage.removeItem("amp.amedicalpartner.com.userinfo");
    
    // Use history API for cleaner navigation
    if (window.history && window.history.pushState) {
      window.history.pushState({}, '', '/');
      window.location.reload();
    } else {
      navigate("/");
    }
  };
  // Add a global fetch timeout utility
  const fetchWithTimeout = async (url, options, timeout = 10000) => {
    const controller = new AbortController();
    const id = setTimeout(() => controller.abort(), timeout);
    
    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      clearTimeout(id);
      return response;
    } catch (error) {
      clearTimeout(id);
      throw error;
    }
  };

  // Add a dedicated token validation function
  const validateToken = async (tokenToValidate) => {
    if (!tokenToValidate) return false;
    
    try {
      const response = await fetchWithAuth(`${DEV_BASE_URL}/auth/validate-token`, {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });
      
      if (response.status === 200) {
        // Token is valid
        return true;
      } else {
        // Token is invalid
        console.log('Token validation failed - logging out');
        logOut();
        return false;
      }
    } catch (err) {
      console.error('Error during token validation:', err);
      
      // Only log out if it's specifically a token expired error
      if (err.message === 'Token expired') {
        logOut();
      }
      
      return false;
    }
  };

  // Add this effect to validate token periodically
  useEffect(() => {
    // Validate token on mount
    if (token) {
      validateToken(token);
    }
    
    // Set up periodic validation (every 5 minutes)
    const intervalId = setInterval(() => {
      if (token) {
        validateToken(token);
      }
    }, 5 * 60 * 1000);
    
    return () => clearInterval(intervalId);
  }, [token]);
  return (
    <AuthContext.Provider value={{ 
      token,
      user,
      subscription,
      userProfilePicture,
      authStatus,
      selectUser,
      notificationData,
      users,
      isLoading,
      userList,
      loginAction,
      getsubscription,
      logOut,
      getUserInfoAction,
      getUserListAction ,
      setSelectUser,
      getUsersAction,
      insertUser,
      updateUser,
      deleteUser,
      getNotifications,
      readNotifications,
      readSingleUserNotifications,
      readMarkNotifications,
      readAllNotifications,
      uploadPicture,
      validateToken
      }}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;

export const useAuth = () => {
  return useContext(AuthContext);
};
