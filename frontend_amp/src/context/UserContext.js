
import React, { useState } from 'react';
// import lightLogo from './../assets/medium_light.png';
// import darkLogo from './../assets/medium_dark.png';
const initialState={
	token:''
}
export const UserContext = React.createContext(initialState);

export const UserProvider = ({ children }) => {
    const [token, setToken] = useState('');
    const updateToken=(token)=>{
      setToken(token)
    }
    return (
      <UserContext.Provider value={{ token, setToken }}>
        {children}
      </UserContext.Provider>
    );
  };

// function UserContextProvider({ children }:{children:React.ReactNode}) {
// 	const [state, dispatch] = React.useReducer(countReducer, { token: 'hi milad' })
// 	// NOTE: you *might* need to memoize this value
// 	// Learn more in http://kcd.im/optimize-context
// 	const value = { state, dispatch }
// 	return <UserContext.Provider value={value}>{children}</UserContext.Provider>
// }

// export {UserContextProvider,UserContext} 
// export { UserContextProvider }
// export default UserContextProvider,useCount,UserConsumer;