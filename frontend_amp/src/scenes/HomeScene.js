import React ,{Component} from 'react';
import {
  // <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>anager,

    // <PERSON>er<PERSON>omponent as Header,
    // DescriptionComponent as Description,
    // ProductComponent as Product,
    // InfoCustomerComponent as InfoCustomer,
    // MostRecentBlogComponent as MostRecent,

    // SearchBarComponent,
    // SearchCardsComponent as SearchCards,
    // CardExplainComponent,
    // ApplicationComponent as DownloadApp,
    // FooterComponent as Footer
} from '../Components';
import { connect } from 'react-redux';
import { Navigate, redirect, useNavigate } from 'react-router-dom';
import Cookies from 'js-cookie';
import navigateHook from '../HOK/NavigateHook';
// import {
  // homeInitial,
  // homeProducts,
// } from '../actions';

class HomeScene extends Component{
    constructor(props){
      super(props)
    }
    UNSAFE_componentWillMount() {
      // this.props.homeInitial();
      // this.props.homeProducts();

    }
    render() {
      const { navigate } = this.props;
    // Navigate to Another Component
      var session=!!Cookies.get('auth');
      if (!session) {
        // console.log("signin");
        navigate("/signin");
    } else {
      // console.log("order")
      navigate("/order",);
    }
        return (
          <div>
            {/* hi  */}
          </div>
        )
    }
}
const mapStateToProps = ({ home }) => {
    return {
      webPosts       : home.webPosts,
      instagramPosts : home.instagramPosts,
      products       : home.products,
      // searchResults  : home.searchResults,
      error          : home.error,
      loading        : home.loading
    }
  };
  export default navigateHook(HomeScene);
  // export default connect(mapStateToProps, {
  //   // homeInitial,
  //   // homeProducts
  // })(HomeScene);
  