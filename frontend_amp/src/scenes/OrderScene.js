import React, { Component } from 'react'
import dashboardHook from '../HOK/DashboardHook';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';
import { Box, Button, Checkbox, IconButton, Skeleton, Tooltip, Typography } from '@mui/material';
import { formatDate, orderStatusFn } from '../utils/functions';
import { grey, purple, red } from '@mui/material/colors';
import { HiInformationCircle } from 'react-icons/hi';
import { TbEdit, TbTrash } from 'react-icons/tb';
import { compose } from 'redux';
import authHook from '../HOK/AuthHook';
import themeHook from '../HOK/ThemeHook';
import locationHook from '../HOK/LoctionHook';
import BadgeNumber from '../Components/BadgeNumber';
import FilterDate from '../Components/FilterDate';
import SearchBox from '../Components/SearchBox';
import OrderAdd from '../Components/OrderAdd';
import AlertNotes from '../Components/AlertNotes';
import OrderMenu from '../Components/OrderMenu';
import OrderEdit from '../Components/OrderEdit';
import OrderDelete from '../Components/order/OrderDelete';
import OrderPaid from '../Components/OrderPaid';
import NoDataList from '../Components/NoDataList';
import TableSkeleton from '../Components/TableSkeleton';
import { Favorite } from '@mui/icons-material';
const head = [
  { name: 'Date', style: '8' },
  { name: 'Favorite', style: '8' },
  { name: 'Product', style: '35' },
  { name: 'SKU(Manufacturer Code)', style: '15' },
  { name: 'Note', style: '20' }, // New Note column
  { name: 'QTY', style: '6' },
  { name: 'Specific', style: '4' },
  { name: 'Vendor', style: '7' },
  { name: 'Urgent', style: '4' },
  // { name: 'Exp. Delivery', style: '10' },
  { name: 'Status', style: '8' },
  { name: 'Timeline', style: '9' },
  { name: 'Received', style: '4' },
  { name: 'Action', style: '8' },
];
class OrderScene extends Component {
  constructor(props){
    super(props)
    this.state={
      theme:props.theme,
      auth:props.auth,
      dashboard:props.dashboard,
      // window:props,
      mobileOpen:false,
      isClosing:false,
      proccessing:false,
      tableHeight:(this.props.theme.windowDimensions.height*70)/100,
      isLoadingOrders:true,
      ordersTableData:Object(null),
      emptyTableData:'',
      sendForAPI:false,
      filterDateFrom:'',
      filterDateTo:'',
      filterStatus:'',
      filterSearch:'',
      rowsItem:Object()|null,
      indexRow:0,
      statusPaid:false,
      statusAdd:false,
      statusEdit:false,
      statusDelete:false,
      showAlert:false,
      rowsPerPage:5,
      page:0,
      error:'',
      infoAlert:{ msg: '', status: undefined },
    }
    this.showAlertNotes=this.showAlertNotes.bind(this)
    this.handleStatus=this.handleStatus.bind(this)
    this.handleCloseModal=this.handleCloseModal.bind(this)
    const datePattern = /^\d{4}-\d{2}-\d{2}$/;
  }
  async componentDidMount(){
    this.props.theme.setWindowDimensions({width: window.innerWidth,height: window.innerHeight});
    this.setState({
      theme:this.props.theme,
      auth:this.props.auth,
      dashboard:this.props.dashboard,
    })   
    if(this.props.auth.user){
      if (this.props.auth.user.is_admin){
        if(this.props.auth.selectUser){
          let response=await this.props.dashboard.getOrders(this.props.auth.token,parseInt(this.props.auth.selectUser.id))
          if (response.status==200) {
            if(response.msg=="success"){
              this.setState({
                ordersTableData:await this.props.dashboard.ordersTableData,
                // isLoadingOrders:false,
              })
            }
          }
          this.setState({
            isLoadingOrders:false,
          })
        }
      }else{
        let user_id=0
          if(this.props.auth.user.user_group==3){
            user_id=this.props.auth.user.id
          }else if(this.props.auth.user.user_group==4){
            user_id=this.props.auth.user.ref_admin.id
          }
        let response=await this.props.dashboard.getOrders(await this.props.auth.token,user_id)
        if (response.status==200) {
          if(response.msg=="success"){
            this.setState({
              ordersTableData:await this.props.dashboard.ordersTableData,
              // isLoadingOrders:false,
            })
          }
        }
        this.setState({
          isLoadingOrders:false,
        })
        this.shouldComponentUpdate=true
        this.forceUpdate()
      }
    }
    setInterval(async () => {
      if (this.state.sendForAPI){
        console.log("sendforApi")
        this.setState({
          ordersTableData:await this.props.dashboard.ordersTableData,
          isLoadingOrders:true,
        })
        this.shouldComponentUpdate=true
        this.forceUpdate()
      }
      this.setState({
        sendForAPI:false,
        isLoadingOrders:false,
      })
    }, 3000);
    this.shouldComponentUpdate=true
    this.forceUpdate()
  }
  async handleUpdateFavorite(id,favorite){
    let orderData={
      id:id,
      admin_id:null,
      my_product:favorite,
    }
    if (this.props.auth.user.user_group==2){
      orderData.admin_id=this.props.auth.selectUser.id
    }else if(this.props.auth.user.user_group==3){
      orderData.admin_id=this.props.auth.user.id
    }else if(this.props.auth.user.user_group==4){
      orderData.admin_id=this.props.auth.user.ref_admin.id
    }
    else{

    }
    let response=await this.props.dashboard.updateFavorite(this.props.auth.token,orderData)
      if (response.status!=200) {
          this.setState({
            sendForAPI:true,
            infoAlert:{
              msg: `Error,Can\'t you\'r Favorite List: ${response.msg}`,
              status: false,
            }
          })
          this.showAlertNotes();
      } else {
          if (response.msg == 'success') {
              // refetchOrder();
              this.setState({
                sendForAPI:true,
                infoAlert:{
                  msg: 'Your\'r Favorite List Updated',
                  status: true,
                }
              })
              this.showAlertNotes();
              this.handleCloseModal();
          } else {
            this.setState({
              sendForAPI:true,
              infoAlert:{
                  msg: `Failed,Can\'t Update you\'r Favorite List: ${response.msg}`,
                  status: false,
                }
            })
            this.showAlertNotes();
          }
      }
  }
  showAlertNotes(){
    this.setState({
      showAlert:true
    })
    setTimeout(() => {
      this.setState({
        showAlert:false,
        infoAlert:{ msg: '', status: undefined }
      })
    }, 3000);
  }
  handleStatus(value,rowsItem,order){
    try{
      if(value===1){
        this.setState({
          statusAdd:true,
          // rowsItem:rowsItem
        })
      }else if(value===2){
        this.setState({
          statusEdit:true,
          rowsItem:rowsItem,
          indexRow:order
        })
      }else if(value===3){
        this.setState({
          statusPaid:true,
          rowsItem:rowsItem,
          indexRow:order
        })
      }else if(value===4){
        this.setState({
          statusDelete:true,      
          rowsItem:rowsItem,
          indexRow:order
        })
      }
    }catch(error){
    console.error('Error handling status:', error);
    }
  }
  handleCloseModal(){
    this.setState({
      statusAdd:false,
      statusEdit:false,
      statusPaid:false,
      statusDelete:false,
      ordersTableData:this.state.dashboard.ordersTableData
    })
    this.shouldComponentUpdate=true
    this.forceUpdate()
  }
  handleChangePage=(event, newPage)=>{
    this.setState({
        page:newPage,
    })
  };
  handleChangeRowsPerPage(event){
    this.setState({
        rowsPerPage:+event.target.value,
        page:0
    });
  };
  handleDeleteOrder(order, index) {
    if (this.props.auth.user.is_admin) {
      this.handleStatus(4, order, index);
    }
    else{ 
      if (this.props.dashboard.orderStatus[orderStatusFn(order.status).name] == 3) {
        this.handleStatus(4, order, index);
      }else{
        this.setState({
          infoAlert:{
            msg: 'You can\'t delete this order',
            status: false,
          },
        })
        this.showAlertNotes();
      }
  }
  }
  handleEditOrder = (order, index) => {
  this.handleStatus(2, order, index);
};
  render() {
    let controller=0
    return (
      <>
          {this.state.showAlert && (
            <AlertNotes
                message={this.state.infoAlert.msg}
                action={this.state.infoAlert.status}
            />
          )}
          {this.state.statusAdd && (
              <OrderAdd
                // isLoadingOrders={this.state.isLoadingOrders}
                onClose={this.handleCloseModal}
                showAlertNotes={this.showAlertNotes}
                setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
                refetchOrder={(sendForAPI)=>{this.setState({
                  sendForAPI:sendForAPI
                })}}
                // onFormInsert={this.handleInsertOrder}
                
              />
          )}
           {this.state.statusEdit && (
              <OrderEdit
                onClose={this.handleCloseModal}
                item={this.state.rowsItem}
                showAlertNotes={this.showAlertNotes}
                setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
                refetchOrder={(sendForAPI)=>{this.setState({
                  sendForAPI:sendForAPI
                })}}
                permission={this.state.auth.user.is_admin}
              />
          )}
           {this.state.statusDelete && (
              <OrderDelete
                onClose={this.handleCloseModal}
                item={this.state.rowsItem}
                showAlertNotes={this.showAlertNotes}
                setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
                refetchOrder={(sendForAPI)=>{this.setState({
                  sendForAPI:sendForAPI
                })}}
              />
          )}
           {this.state.statusPaid && (
              <OrderPaid
                  item={this.state.rowsItem}
                  data={this.state.statusPaid}
                  onClose={this.handleCloseModal}
                  permission={this.state.auth.user.is_admin}
                  showAlertNotes={this.showAlertNotes}
                  setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
                  refetchOrder={(sendForAPI)=>{this.setState({
                    sendForAPI:sendForAPI
                  })}}
              />
          )}
          <BadgeNumber  
            // sendForAPI={this.state.sendForAPI}
          />
          <Box
            sx={{
              width:'100%',
              height:'100%',
              display:'block',
              // backgroundColor:"#F3F4F6"
            }}
          >
            <OrderMenu handleStatus={this.handleStatus} />
            {this.state.isLoadingOrders ? (
              <>
                <Box 
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    gap:1,
                    // textAlign:'center',
                    // alignItems:'center',
                    minHeight:this.state.tableHeight,
                    height:'100%',
                    width:'100%',
                    paddingTop:0,
                    marginTop:0,
                    backgroundColor:this.props.theme.selectTheme.background_2,
                    // paddingY:5,
                    // marginBottom:3,
                  }}
                >
                  <TableSkeleton />
                </Box>
              </>
              ):(this.props.dashboard.ordersTableData&&(
              <Box
                  sx={{
                  height:'100%',
                  // display:'flex',
                  // paddingLeft:5,
                  borderTop:'1px solid #e5e7eb',
                  // textAlign:'left',
                  // alignItems:'center',
                  backgroundColor:this.props.theme.selectTheme.background_3,
                  overflow: 'auto'

                }}
              >
                <Paper 
                  sx={{ 
                    width: '100%',
                    height:'100%',
                    overflow: 'auto'
                  }}
                >
                <TableContainer 
                  sx={{ 
                    height:'100%',
                    minHeight: `${this.state.tableHeight}px` ,
                    backgroundColor:this.props.theme.selectTheme.background,
                    color: this.props.theme.selectTheme.fontColor,
                    overflow:'auto'

                  }}
                >
                  <Table 
                    stickyHeader 
                    aria-label="sticky table" 
                    sx={{
                        overflow:'auto'
                    }}
                  >
                    <TableHead sx={{
                        marginBottom:2,
                        backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                        color: this.props.theme.selectTheme.headerfontColor
                    }}>
                      <TableRow
                        sx={{
                          // backgroundColor:this.props.theme.selectTheme.headerBg,
                          // color: this.props.theme.selectTheme.headerfontColor
                        }}
                      >
                        {head.map((item,index) => (
                          <TableCell
                            key={index}
                            align={item.align}
                            sx={{
                              width:`${item.style}%`,
                              border:`1px ${grey[200]} solid`,
                              textAlign:'center',
                              backgroundColor:this.props.theme.selectTheme.background,
                              color: this.props.theme.selectTheme.fontColor
                            }}
                            // style={{ 
                            //   width: `${item.style}%`,
                            //   border:`1px ${grey[200]} solid`,
                            //   // borderColor:grey[200],
                            //   textAlign:'center',
                            //  }}
                          >
                            {item.name}
                          </TableCell>
                        ))}
                      </TableRow>
                    </TableHead>

                    <TableBody>
                      {this.props.dashboard.ordersTableData.length ? (
                      // this.props.dashboard.ordersTableData.slice(this.state.page * this.state.rowsPerPage, this.state.page * this.state.rowsPerPage + this.state.rowsPerPage)
                      Object.keys(this.props.dashboard.ordersTableData)
                        .map((order,index) => {
                          // console.log(this.props.dashboard.ordersTableData[index])
                          // console.log(this.props.dashboard.ordersTableData[order])
                            if ((!this.props.dashboard.ordersTableData[index].received)&&(this.props.dashboard.ordersTableData[index].status!=='Returned')){
                              controller=controller+1
                              return (
                                <>
                                  <TableRow 
                                      sx={{
                                        // backgroundColor:this.props.theme.selectTheme.background,
                                        // color: this.props.theme.selectTheme.fontColor
                                      }}
                                      hover role="checkbox" tabIndex={-1} key={index}>
                                      <TableCell 
                                        sx={{
                                          px:1,
                                          py:2,
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                        // 'px-1 border border-gray-200 dark:border-gray-600 text-center py-2';
                                        key={index.id}>
                                        {/* {console.log(this.props.dashboard.ordersTableData[order].created_at)} */}
                                        {this.props.dashboard.ordersTableData[index].created_at
                                          ? formatDate(
                                                'Amm-dd-yyyy',
                                                this.props.dashboard.ordersTableData[index].created_at
                                            )
                                          : formatDate('Amm-dd-yyyy')}
                                      </TableCell>
                                      <TableCell 
                                        sx={{
                                          px:1,
                                          py:2,
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                      >
                                        <IconButton
                                          onClick={()=>{
                                            let favorite=!this.props.dashboard.ordersTableData[index].my_product
                                            this.handleUpdateFavorite(this.props.dashboard.ordersTableData[index].id,favorite)
                                          }}
                                        >
                                          {this.props.dashboard.ordersTableData[index].my_product?
                                            (
                                              <Favorite 
                                                sx={{ color: red[500] }}
                                                size={24} 
                                              />
                                            ):(
                                              <Favorite 
                                              size={24} />
                                            )
                                          }
                                        </IconButton>
                                      </TableCell>
                                      <TableCell 
                                        sx={{
                                          px:1,
                                          py:2,
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                      >
                                        {this.props.dashboard.ordersTableData[index].product? this.props.dashboard.ordersTableData[index].product.name: ''}
                                      </TableCell>
                                      <TableCell 
                                        sx={{
                                          px:1,
                                          py:2,
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                      >
                                        {this.props.dashboard.ordersTableData[index].product? this.props.dashboard.ordersTableData[index].product.s_k_u: ''}
                                      </TableCell>
                                      <TableCell 
                                        sx={{
                                          px:1,
                                          py:2,
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                      >
                                        {this.props.dashboard.ordersTableData[index].note || ''}
                                      </TableCell>
                                      <TableCell 
                                        sx={{
                                          px:1,
                                          py:2,                          
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                      >
                                      {this.props.dashboard.ordersTableData[index].qty ?? ''}
                                      </TableCell>
                                      <TableCell 
                                        sx={{
                                          px:1,
                                          py:2,
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                      >
                                        <Checkbox
                                          sx={{
                                            color:purple[400],
                                          }}
                                          checked={this.props.dashboard.ordersTableData[index].specific}
                                          onClick={()=>{
                                            let specific=this.props.dashboard.ordersTableData[index].specific
                                            this.props.dashboard.ordersTableData[index].specific=!specific
                                            this.handleStatus(2,this.props.dashboard.ordersTableData[index])
                                          }}
                                          />
                                      </TableCell>
                                      <TableCell 
                                        sx={{
                                          px:1,
                                          py:2,
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                      >
                                        {this.props.dashboard.ordersTableData[index].vendor
                                            ? this.props.dashboard.ordersTableData[index].vendor.name
                                            : ''}
                                      </TableCell>
                                      <TableCell 
                                        sx={{
                                          px:1,
                                          py:2,
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                      >
                                        <Checkbox
                                          sx={{
                                            color:purple[400],
                                          }}
                                          checked={
                                            this.props.dashboard.ordersTableData[index].urgent
                                            }
                                            onClick={()=>{
                                              let urgent=this.props.dashboard.ordersTableData[index].urgent
                                              this.props.dashboard.ordersTableData[index].urgent=!urgent
                                              this.handleStatus(2,this.props.dashboard.ordersTableData[index])
                                            }}
                                          />
                                      </TableCell>            
                                      <TableCell 
                                        sx={{
                                          px:1,
                                          py:2,
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                      >
                                          {
                                            orderStatusFn(this.props.dashboard.ordersTableData[index].status).name
                                            // if(this.state.order.initialOrderStatus)
                                              // orderStatusFn(this.state.ordersTableData[order].status)
                                              //     .name
                                      }
                                      </TableCell>
                                      <TableCell 
                                        sx={{
                                          px:1,
                                          py:2,
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                      >
                                        {this.props.dashboard.ordersTableData[index].timeline
                                          ? formatDate(
                                                'Amm-dd-yyyy',
                                                this.props.dashboard.ordersTableData[index].timeline
                                            )
                                          : ''//formatDate('Amm-dd-yyyy')
                                          }
                                        {/* {this.state.ordersTableData[order].timeline ?? ''} */}
                                      </TableCell>
                                      <TableCell 
                                        sx={{
                                          px:1,
                                          py:2,
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                      >
                                        <Checkbox
                                            sx={{
                                              color:purple[400],
                                            }}
                                            checked={this.props.dashboard.ordersTableData[index].received}
                                            onClick={()=>{
                                              let received=this.props.dashboard.ordersTableData[index].received
                                              this.props.dashboard.ordersTableData[index].received=!received
                                              this.handleStatus(2,this.props.dashboard.ordersTableData[index])
                                            }}
                                        />
                                      </TableCell>
                                      <TableCell 
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                        >
                                            {this.props.auth.user.is_admin && (
                                              <Tooltip content="Paid Order">
                                                <Button
                                                  size="xs"
                                                  inline="true"
                                                  className="m-0 p-0 inline mb-2"
                                                  onClick={() => this.handleStatus(3, this.props.dashboard.ordersTableData[index], index)}
                                                >
                                                  <HiInformationCircle className="h-4 w-4 text-green-500" />
                                                </Button>
                                              </Tooltip>
                                            )}
                                            <Tooltip content="Delete Order">
                                              <Button
                                                size="xs"
                                                inline="true"
                                                className="m-0 p-0 inline"
                                                onClick={() => this.handleDeleteOrder(this.props.dashboard.ordersTableData[index], index)}
                                              >
                                                <TbTrash className="h-4 w-4 text-red-600" />
                                              </Button>
                                            </Tooltip>
                                            <Tooltip content="Edit Order" aria-label="Edit Order">
                                              <Button
                                                size="medium"
                                                sx={{ color: purple[400] }}
                                                onClick={() => this.handleEditOrder(this.props.dashboard.ordersTableData[index], index)}
                                              >
                                                <TbEdit className="h-4 w-4 text-purple-600" />
                                              </Button>
                                            </Tooltip>
                                        </TableCell>
                                      {/* {item.map((column) => {
                                        const value = row[column.id];
                                        return (
                                          <TableCell key={column.id} align={column.align}>
                                            {column.format && typeof value === 'number'
                                              ? column.format(value)
                                              : value}
                                          </TableCell>
                                        );
                                      })} */}
                                  </TableRow>
                                </>
                              )
                            }
                            if(index===this.props.dashboard.ordersTableData.length-1 && controller===0){  
                              return(
                                <NoDataList colSpan={head.length} />
                              )
                            }
                          }
                        )
                      ):(
                          <NoDataList colSpan={head.length} />
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
                {/* <Box 
                  sx={{
                    display:'flex',
                    justifyContent:'flex-end',
                    alignItems:'center',

                  }}
                >
                  <TablePagination
                    disabled
                    sx={{
                      width:'100%',
                      backgroundColor:this.props.theme.selectTheme.background,
                      color: this.props.theme.selectTheme.fontColor,
                      '&& .MuiTablePagination-selectLabel':{
                        margin:0
                      },
                      "&& .MuiTablePagination-displayedRows":{
                        margin:0
                      }
                    }}
                    rowsPerPageOptions={[5, 10, 25, 50, 100]}
                  //   component="div"
                    count={this.props.dashboard.ordersTableData.length}
                    rowsPerPage={this.state.rowsPerPage}
                    page={this.state.page}
                    onPageChange={this.handleChangePage}
                    onRowsPerPageChange={(e)=>{
                      console.log(e)
                      this.handleChangeRowsPerPage(e)}}
                  />
                </Box> */}
                </Paper>
              </Box>
              ))
            }
          </Box>
          {/* <Box
            sx={{
              width:'100%',
              minHeight:(this.props.theme.windowDimensions.height*10)/100,
              display:'block',
              backgroundColor:this.props.theme.selectTheme.background,
              // backgroundColor:"#F3F4F6"
            }}
          ></Box> */}
        </>
    )
  }
}

const enhance=compose(
  // locationHook,
  themeHook,
  dashboardHook,
  authHook,
)

export default enhance(OrderScene)
