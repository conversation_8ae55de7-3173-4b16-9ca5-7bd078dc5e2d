import React, { Component } from 'react'
import { Box, Button, Paper, Skeleton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tooltip } from '@mui/material'
import OrderMenu from '../Components/OrderMenu'
import VendorAdd from '../Components/vendor/VendorAdd'
import VendorEdit from '../Components/vendor/VendorEdit'
import VendorDelete from '../Components/vendor/VendorDelete'
import VendorShow from '../Components/vendor/VendorShow'
import { compose } from 'redux'
import themeHook from '../HOK/ThemeHook'
import authHook from '../HOK/AuthHook'
import locationHook from '../HOK/LoctionHook'
import dashboardHook from '../HOK/DashboardHook'
import AlertNotes from '../Components/AlertNotes'
import NoDataList from '../Components/NoDataList'
import { grey, purple } from '@mui/material/colors'
import { Tb<PERSON><PERSON><PERSON><PERSON><PERSON>, TbTrash, TbUserEdit } from 'react-icons/tb'
import TableSkeleton from '../Components/TableSkeleton'
const head = [
  { name: 'Vendor Name', style: '10' },
  { name: 'Account Number', style: '10' },
  { name: 'Contact Name', style: '10' },
  { name: 'How to Order', style: '15' },
  { name: 'Phone', style: '15' },
  { name: 'Email', style: '15' },
  { name: 'Website', style: '10' },
  { name: 'Action', style: '5' },
];
class VendorScene extends Component {
    constructor(props){
      super(props)
      this.state={
          theme:props.theme,
          auth:props.auth,
          dashboard:props.dashboard,
          vendorsTableData:Object(null),
          isVendorLoading:true,
          tableHeight:(this.props.theme.windowDimensions.height*85)/100,
          sendForAPI:false,
          statusAdd:false,
          statusEdit:false,
          statusDelete:false,
          statusShow:false,
          showAlert:false,
          rowsItem:Object()|null,
          infoAlert:{ msg: '', status: undefined },
      }
      this.showAlertNotes=this.showAlertNotes.bind(this)
      this.handleStatus=this.handleStatus.bind(this)
      this.handleCloseModal=this.handleCloseModal.bind(this)
    }
    async componentDidMount(){      
      this.setState({
        theme:this.props.theme,
        auth:this.props.auth,
        dashboard:this.props.dashboard,
      })
      if (this.props.auth.user.is_admin){
        if(this.props.auth.selectUser){
          await this.props.dashboard.getVendors(this.props.auth.token,this.props.auth.selectUser.id)
          this.setState({
            vendorsTableData:await this.props.dashboard.vendorsTableData,
          })
        }
      }
      else{
        let user_id=0
        if(this.props.auth.user.user_group==3){
          user_id=this.props.auth.user.id
        }else if(this.props.auth.user.user_group==4){
          user_id=this.props.auth.user.ref_admin.id
        }
        await this.props.dashboard.getVendors(this.props.auth.token,user_id)
        this.setState({
          vendorsTableData:await this.props.dashboard.vendorsTableData,
        })
      }
      setInterval(async () => {
        if (this.state.sendForAPI){
          console.log("sendforApi")
          this.setState({
            vendorsTableData:await this.props.dashboard.vendorsTableData,
            isVendorLoading:false,
          })
          this.shouldComponentUpdate=true
          this.forceUpdate()
        }
        this.setState({
          sendForAPI:false,
          isVendorLoading:false,
        })
      }, 3000);
      this.setState({
        isVendorLoading:false,
      })
      this.shouldComponentUpdate=true
      this.forceUpdate()
    }
    handleStatus(value,rowsItem){
      if(value===1){
        this.setState({
          statusAdd:true,
          // rowsItem:rowsItem
        })
      }else if(value===2){
        this.setState({
          statusEdit:true,
          rowsItem:rowsItem
        })
      }else if(value===3){
        this.setState({
          statusShow:true,
          rowsItem:rowsItem
        })
      }else if(value===4){
        this.setState({
          statusDelete:true,      
          rowsItem:rowsItem
        })
      }
    }
    showAlertNotes(){
      this.setState({
        showAlert:true
      })
      setTimeout(() => {
        this.setState({
          showAlert:false,
          infoAlert:{ msg: '', status: undefined }
        })
      }, 3000);
    }
    handleCloseModal(){
      this.setState({
        statusAdd:false,
        statusEdit:false,
        statusShow:false,
        statusDelete:false,
        ordersTableData:this.state.dashboard.ordersTableData
      })
      this.shouldComponentUpdate=true
      this.forceUpdate()
    }
  render() {
    return (
      <>
        {this.state.showAlert && (
          <AlertNotes
              message={this.state.infoAlert.msg}
              action={this.state.infoAlert.status}
          />
        )}
        {this.state.statusAdd && (
          <VendorAdd
            onClose={this.handleCloseModal}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
        {this.state.statusEdit && (
          <VendorEdit
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
            permission={this.state.auth.user.is_admin}
          />
        )}
        {this.state.statusDelete && (
          <VendorDelete
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
        {this.state.statusShow && (
          <VendorShow
            data={this.state.statusShow}
            item={this.state.rowsItem}
            onClose={this.handleCloseModal}
          />
        )}
        <Box
          sx={{
            width:'100%',
            height:'100%',
            display:'block',
            // backgroundColor:"#F3F4F6"
          }}
        >
          <OrderMenu handleStatus={this.handleStatus}/>
          {this.state.isVendorLoading ? 
            (
              <>
                <Box 
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    gap:1,
                    // textAlign:'center',
                    // alignItems:'center',
                    minHeight:this.state.tableHeight,
                    height:'100%',
                    width:'100%',
                    paddingTop:0,
                    marginTop:0,
                    backgroundColor:this.props.theme.selectTheme.background_2,
                    // paddingY:5,
                    // marginBottom:3,
                  }}
                >
                  <TableSkeleton />
                </Box>
              </>
            ):(this.props.dashboard.vendorsTableData&&(
                <Box
                  sx={{
                    height:'100%',
                    // display:'flex',
                    // paddingLeft:5,
                    borderTop:'1px solid #e5e7eb',
                    // textAlign:'left',
                    // alignItems:'center',
                    backgroundColor:this.props.theme.selectTheme.background_3
                  }}
                >
                  <Paper 
                    sx={{ 
                      width: '100%',
                      height:'100%',
                      overflow: 'hidden'
                    }}
                  >
                  <TableContainer 
                    sx={{ 
                      height:'100%',
                      minHeight: `${this.state.tableHeight}px` ,
                      backgroundColor:this.props.theme.selectTheme.background,
                      color: this.props.theme.selectTheme.fontColor
                    }}
                  >
                    <Table stickyHeader aria-label="sticky table">
                      <TableHead sx={{
                          marginBottom:2,
                          backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                          color: this.props.theme.selectTheme.headerfontColor
                      }}>
                        <TableRow
                          sx={{
                            // backgroundColor:this.props.theme.selectTheme.headerBg,
                            // color: this.props.theme.selectTheme.headerfontColor
                          }}
                        >
                          {head.map((item,index) => (
                            <TableCell
                              key={index}
                              align={item.align}
                              sx={{
                                width:`${item.style}%`,
                                border:`1px ${grey[200]} solid`,
                                textAlign:'center',
                                backgroundColor:this.props.theme.selectTheme.background,
                                color: this.props.theme.selectTheme.fontColor
                              }}
                            >
                              {item.name}
                            </TableCell>
                          ))}
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {this.props.dashboard.vendorsTableData.length ? (
                        Object.keys(this.props.dashboard.vendorsTableData)
                          .map((vendor,index) => {
                            return (
                                <TableRow 
                                    sx={{
                                      // backgroundColor:this.props.theme.selectTheme.background,
                                      // color: this.props.theme.selectTheme.fontColor
                                    }}
                                  hover 
                                  role="checkbox" 
                                  tabIndex={-1} 
                                  key={index}
                                >
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={vendor.id}
                                    >
                                    {this.props.dashboard.vendorsTableData[vendor].name}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={vendor.id}
                                    >
                                    {this.props.dashboard.vendorsTableData[vendor].account_number}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={vendor.id}
                                    >
                                    {this.props.dashboard.vendorsTableData[vendor].contact_name}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={vendor.id}
                                    >
                                    {this.props.dashboard.vendorsTableData[vendor].how_to_order}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={vendor.id}
                                    >
                                    {this.props.dashboard.vendorsTableData[vendor].phone}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={vendor.id}
                                    >
                                    {this.props.dashboard.vendorsTableData[vendor].email}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={vendor.id}
                                    >
                                    {this.props.dashboard.vendorsTableData[vendor].website}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={vendor.id}
                                  >
                                    {this.props.dashboard.vendorsTableData[vendor].id && (
                                      <Box
                                        sx={{
                                          display:'flex'
                                        }} 
                                        >
                                          <Tooltip
                                              content='Show Vendor'
                                          >
                                              <Button
                                                size='medium'
                                                sx={{
                                                  color:purple[400],
                                                }}
                                                pill
                                                onClick={() => {
                                                  this.handleStatus(3,this.props.dashboard.vendorsTableData[vendor])
                                                }}
                                              >
                                                  <TbEyeCheck size={24} />
                                              </Button>
                                          </Tooltip>
                                          <Tooltip
                                              content='Edit Vendor'
                                          >
                                              <Button
                                                  size='medium'
                                                  sx={{
                                                    color:purple[400],
                                                  }}
                                                  pill
                                                  onClick={() => {
                                                    this.handleStatus(2,this.props.dashboard.vendorsTableData[vendor])
                                                  }}
                                              >
                                                  <TbUserEdit size={24} />
                                              </Button>
                                          </Tooltip>
                                          {this.state.auth.user.is_admin && (
                                              <Tooltip
                                                  content='Delete Vendor'
                                              >
                                                  <Button
                                                    size='medium'
                                                    sx={{
                                                      color:purple[400],
                                                    }}
                                                    pill
                                                    inline='true'
                                                    onClick={() => {
                                                      this.handleStatus(4,this.props.dashboard.vendorsTableData[vendor])
                                                    }}
                                                  >
                                                      <TbTrash size={24} />
                                                  </Button>
                                              </Tooltip>
                                          )}
                                      </Box>
                                    )}
                                  </TableCell>
                                </TableRow>
                            )
                          })
                        ):(
                          <NoDataList colSpan={head.length}/>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Paper>
                </Box>
                )
              )
            }
        </Box>
      </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    locationHook,
    dashboardHook
  )(VendorScene)