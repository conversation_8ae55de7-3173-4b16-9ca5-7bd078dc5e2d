import React, { Component } from 'react'
import OrderMenu from '../Components/OrderMenu';
import { Box, FormControl, IconButton, InputAdornment, OutlinedInput, Paper, Skeleton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tooltip } from '@mui/material';
import { ColorButton } from '../Components/ColorButton';
import NoDataList from '../Components/NoDataList';
import { Button, CircularProgress } from '@mui/joy';
import DoneIcon from '@mui/icons-material/Done';
import TableSkeleton from '../Components/TableSkeleton';
import { green, grey, purple, red } from '@mui/material/colors';
import { compose } from 'redux';
import themeHook from '../HOK/ThemeHook';
import authHook from '../HOK/AuthHook';
import dashboardHook from '../HOK/DashboardHook';
import { formatDate } from '../utils/functions';
import NoteAdd from '../Components/notes/NoteAdd';
import AlertNotes from '../Components/AlertNotes';
import NoteRemove from '../Components/notes/NoteRemove';
import { TbTrash } from 'react-icons/tb';

const head = [
    // { name: 'Notes', style: '8' },
    { name: 'Date', style: '8' },
    { name: 'Notes', style: '40' },
    { name: 'Action', style: '8' },
  ];
class NotesScene extends Component {
    constructor(props){
        super(props)
        this.state={
            theme:props.theme,
            auth:props.auth,
            tableHeight:(this.props.theme.windowDimensions.height*85)/100,
            showAlert:false,
            rowsItem:Object()|null,
            sendForAPI:false,
            statusAdd:false,
            statusEdit:false,
            statusDelete:false,
            infoAlert:{ msg: '', status: undefined },
            notesTableData:Object(null),
            isLoadingNotes:true,
            notes:'',
            indexChange:[],
            statusNotedEdit:false
        }
        this.handleStatus=this.handleStatus.bind(this)
        this.showAlertNotes=this.showAlertNotes.bind(this)
        this.handleCloseModal=this.handleCloseModal.bind(this)
    }
    async componentDidMount(){
        this.props.theme.setWindowDimensions({width: window.innerWidth,height: window.innerHeight});
        this.setState({
          theme:this.props.theme,
          auth:this.props.auth,
          dashboard:this.props.dashboard,
        })   
        if (this.props.auth.user.is_admin){
          if(this.props.auth.selectUser){
            let response=await this.props.dashboard.getNotes(this.props.auth.token,parseInt(this.props.auth.selectUser.id))
            if (response.status==200) {
              if(response.msg=="success"){
                this.setState({
                  notesTableData:await this.props.dashboard.notesTableData,
                  // isLoadingOrders:false,
                })
              }
            }
            this.setState({
              isLoadingNotes:false,
            })
          }
        }else{
          let response=await this.props.dashboard.getNotes(await this.props.auth.token,await this.props.auth.user.id)
          if (response.status==200) {
            if(response.msg=="success"){
              this.setState({
                notesTableData:await this.props.dashboard.notesTableData,
                // isLoadingOrders:false,
              })
            }
          }
          this.setState({
            isLoadingNotes:false,
          })
          this.shouldComponentUpdate=true
          this.forceUpdate()
        }
        setInterval(async () => {
          if (this.state.sendForAPI){
            console.log("sendforApi")
            this.setState({
              notesTableData:await this.props.dashboard.notesTableData,
              isLoadingNotes:true,
            })
            this.shouldComponentUpdate=true
            this.forceUpdate()
          }
          this.setState({
            sendForAPI:false,
            isLoadingNotes:false,
          })
        }, 3000);
        this.shouldComponentUpdate=true
        this.forceUpdate()
    }
    async handleUpdateNoted(noteData){
        this.setState({
          statusNotedEdit:true
        })
        if (this.props.auth.user.user_group==2){
          noteData.admin_id=this.props.auth.selectUser.id
        }else if(this.props.auth.user.user_group==3){
          noteData.admin_id=this.props.auth.user.id
        }else if(this.props.auth.user.user_group==4){
          noteData.admin_id=this.props.auth.user.ref_admin.id
        }
        else{
    
        }
        let response=await this.state.dashboard.updateNotes(this.props.auth.token,noteData)
        if (response.status!=200) {
            this.setState({
              sendForAPI:true,
              infoAlert:{
                msg: `Error updating Note: ${response.msg}`,
                status: false,
              },
              statusNotedEdit:false
            })
            this.showAlertNotes();
        } else {
            if (response.msg == 'success') {
                // refetchOrder();
                this.setState({
                  sendForAPI:true,
                  infoAlert:{
                    msg: 'Note updated successfully',
                    status: true,
                  },
                  statusNotedEdit:false
                })
                this.showAlertNotes();
                this.handleCloseModal();
            } else {
              this.setState({
                sendForAPI:true,
                infoAlert:{
                    msg: `Failed to update Note: ${response.msg}`,
                    status: false,
                  },
                  statusNotedEdit:false
              })
              this.showAlertNotes();
            }
        }
        this.setState({
          statusNotedEdit:false
        })
      }
    handleStatus(value,rowsItem){
        if(value===1){
          this.setState({
            statusAdd:true,
            // rowsItem:rowsItem
          })
        }else if(value===2){
          this.setState({
            statusEdit:true,
            rowsItem:rowsItem,
          })
        }
       else if(value===4){
          this.setState({
            statusDelete:true,      
            rowsItem:rowsItem
          })
        }
      }
    showAlertNotes(){
        this.setState({
            showAlert:true
        })
        setTimeout(() => {
            this.setState({
            showAlert:false,
            infoAlert:{ msg: '', status: undefined },
            })
        }, 3000);
    }
    handleCloseModal(){
        this.setState({
            statusAdd:false,
            statusEdit:false,
            statusDelete:false,
        })
        this.shouldComponentUpdate=true
        this.forceUpdate()
    }
  render() {
    return (
      <>
        {this.state.showAlert && (
            <AlertNotes
                message={this.state.infoAlert.msg}
                action={this.state.infoAlert.status}
            />
        )}
        {this.state.statusAdd && (
            <NoteAdd
                // isLoadingOrders={this.state.isLoadingOrders}
                onClose={this.handleCloseModal}
                showAlertNotes={this.showAlertNotes}
                setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
                refetchOrder={(sendForAPI)=>{this.setState({
                    sendForAPI:sendForAPI
                })}}
                // onFormInsert={this.handleInsertOrder}
            />
        )}

        {this.state.statusDelete && (
          <NoteRemove
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
         <Box
          sx={{
            width:'100%',
            height:'100%',
            display:'block',
            // backgroundColor:"#F3F4F6"
          }}
        >
          <OrderMenu handleStatus={this.handleStatus}/>
          {this.state.isLoadingNotes ? 
            (
              <>
                <Box 
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    gap:1,
                    // textAlign:'center',
                    // alignItems:'center',
                    minHeight:this.state.tableHeight,
                    height:'100%',
                    width:'100%',
                    paddingTop:0,
                    marginTop:0,
                    backgroundColor:this.props.theme.selectTheme.background_2,
                    // paddingY:5,
                    // marginBottom:3,
                  }}
                >
                  <TableSkeleton />
                </Box>
              </>
            ):(this.props.dashboard.notesTableData && (
              <Box
                sx={{
                  height:'100%',
                  borderTop:'1px solid #e5e7eb',
                  backgroundColor:this.props.theme.selectTheme.background_3
                }}
              > 
                <Paper 
                  sx={{ 
                    width: '100%',
                    height:'100%',
                  }}
                >
                  <TableContainer 
                    sx={{ 
                      height:'100%',
                      minHeight: `${this.state.tableHeight}px` ,
                      backgroundColor:this.props.theme.selectTheme.background,
                      color: this.props.theme.selectTheme.fontColor
                    }}
                  >
                    <Table stickyHeader aria-label="sticky table">
                      <TableHead sx={{
                          marginBottom:2,
                          backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                          color: this.props.theme.selectTheme.headerfontColor
                      }}>
                        <TableRow
                          sx={{
                            // backgroundColor:this.props.theme.selectTheme.headerBg,
                            // color: this.props.theme.selectTheme.headerfontColor
                          }}
                        >
                          {head.map((item,index) => (
                            <TableCell
                              key={index}
                              align={item.align}
                              sx={{
                                width:`${item.style}%`,
                                border:`1px ${grey[200]} solid`,
                                textAlign:'center',
                                backgroundColor:this.props.theme.selectTheme.background,
                                color: this.props.theme.selectTheme.fontColor
                              }}
                            >
                              {item.name}
                            </TableCell>
                          ))}
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {this.props.dashboard.notesTableData.length ? (
                          Object.keys(this.props.dashboard.notesTableData)
                            .map((note,index) => {
                                const item=this.props.dashboard.notesTableData[note]
                              return (
                                  <TableRow
                                    // hover 
                                    // tabIndex={-1} 
                                    key={index}
                                  >
                                    <TableCell 
                                        sx={{
                                          px:1,
                                          py:2,
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                        // 'px-1 border border-gray-200 dark:border-gray-600 text-center py-2';
                                        key={note.id}>
                                        {this.props.dashboard.notesTableData[note].created_at
                                          ? formatDate(
                                                'Amm-dd-yyyy',
                                                this.props.dashboard.notesTableData[note].created_at
                                            )
                                          : formatDate('Amm-dd-yyyy')}
                                      </TableCell>
                                    <TableCell 
                                        sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                      key={note.id}
                                      >
                                        <FormControl 
                                          fullWidth 
                                          sx={{
                                            border:'none',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                              color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                        >
                                        {/* <InputLabel htmlFor="outlined-adornment-amount">Total Budget</InputLabel> */}
                                        <OutlinedInput
                                            multiline
                                          sx={{
                                            border:`1px solid ${this.props.theme.selectTheme.fontColor_3}`,
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                          value={this.props.dashboard.notesTableData[note].notes}
                                          onChange={(e) =>{
                                              this.props.dashboard.notesTableData[note].notes=e.target.value
                                              this.setState({
                                                indexChange:note
                                              })
                                            }
                                          }
                                          id="outlined-adornment-amount"
                                          endAdornment={
                                            <InputAdornment
                                              position="end">
                                                {(this.state.statusNotedEdit&& this.state.indexChange==note)?(
                                                  <Button 
                                                    sx={{
                                                      marginRight:1
                                                    }}
                                                    startDecorator={
                                                      <CircularProgress variant="solid"/>
                                                    }
                                                  >
                                                    Loading…
                                                  </Button>
                                                ):(
                                                  <IconButton
                                                    onClick={()=>{
                                                      let noteData={
                                                        id:this.props.dashboard.notesTableData[note].id,
                                                        notes:this.props.dashboard.notesTableData[note].notes
                                                      }
                                                      this.handleUpdateNoted(noteData)
                                                      // this.handleStatus(2,this.state.inventoriesTableData[inventory],inventory)
                                                    }}
                                                  >
                                                    <DoneIcon
                                                      color="success"
                                                    />
                                                  </IconButton>
                                                )}
                                            </InputAdornment>
                                          }
                                          />
                                      </FormControl>
                                    </TableCell>
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.props.theme.selectTheme.background_2,
                                        color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                      key={note.id}
                                    >
                                      {this.props.dashboard.notesTableData[note].id && (
                                        <Tooltip
                                            content='Remove'
                                        >
                                            <Button
                                                size='medium'
                                                // sx={{
                                                //   color:purple[400],
                                                // }}
                                                onClick={() => {
                                                this.handleStatus(4,this.props.dashboard.notesTableData[note])
                                            }}
                                                // onClick={() => {
                                                //   this.setState({
                                                //     statusDelete:true,
                                                //     rowsItem:order
                                                //   })
                                                // }}
                                            >
                                                <TbTrash color='error' />
                                            </Button>
                                            
                                        </Tooltip>
                                      )}
                                    </TableCell>
                                  </TableRow>
                              )
                            }
                          )
                        ):(
                          <NoDataList colSpan={head.length}/>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Paper>
              </Box>
            ))
          }
        </Box>
      </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    dashboardHook
)(NotesScene)