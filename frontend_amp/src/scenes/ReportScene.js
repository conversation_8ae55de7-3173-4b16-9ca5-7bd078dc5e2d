import React, { Component } from 'react'
import OrderMenu from '../Components/OrderMenu'
import TableSkeleton from '../Components/TableSkeleton'
import { compose } from 'redux'
import themeHook from '../HOK/ThemeHook'
import authHook from '../HOK/AuthHook'
import locationHook from '../HOK/LoctionHook'
import dashboardHook from '../HOK/DashboardHook'
import { Box, Button, Collapse, collapseClasses, IconButton, Paper, Skeleton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tooltip } from '@mui/material'
import NoDataList from '../Components/NoDataList'
import { green, grey, pink, yellow } from '@mui/material/colors'
import { formatDate } from '../utils/functions'
import { TbTrash } from 'react-icons/tb'
import BudgetPaidCalc from '../Components/report/BudgetPaidCalc'
import OrderDelete from '../Components/order/OrderDelete'
import AlertNotes from '../Components/AlertNotes'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import OrderPaid from '../Components/OrderPaid'
import { HiInformationCircle } from 'react-icons/hi'
const reportHead=[
  { name: 'Status', style: '2' },
  { name: 'Year', style: '15' },
  { name: 'Month', style: '15' },
  // { name: 'Count Report', style: '45' },
  { name: 'Would’ve Paid', stylis:'15'},
  { name: 'Paid', stylis:'15'},
  { name: 'Saved', stylis:'15'}
]
const head = [
  { name: 'Date', style: '8' },
  { name: 'Product', style: '25' },
  { name: 'SKU(Manufacturer Code)', style: '10' },
  { name: 'Note', style: '15' }, // Add Note column
  { name: 'QTY', style: '5' },
  { name: 'Would’ve Paid', style: '10' },
  { name: 'Paid', style: '8' },
  { name: 'Savings', style: '8' },
  { name: 'Vendor', style: '15' },
  { name: 'Action', style: '4' },
];
const initialPaidTemplate={
  1:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
  2:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
  3:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
  4:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
  5:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
  6:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
  7:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
  8:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
  9:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
  10:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
  11:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
  12:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
}
class ReportScene extends Component {
    constructor(props){
        super(props)
        this.state={
            theme:props.theme,
            auth:props.auth,
            dashboard:props.dashboard,
            selectUser:props.auth.selectUser.id,
            reportTableData:props.dashboard.reportTableData,
            sendForAPI:false,
            statusDelete:false,
            tableHeight:(this.props.theme.windowDimensions.height*85)/100,
            isLoadingOrders:true,
            showAlert:false,
            infoAlert:{ msg: '', status: undefined },
            rowsItem:Object()|null,
            reportCount:0,
            statusPaid:false,
            indexRow:0,
            rowReportDetail:{
              1:{name:'January',length:0,collapse:false},
              2:{name:'February',length:0,collapse:false},
              3:{name:'March',length:0,collapse:false},
              4:{name:'April',length:0,collapse:false},
              5:{name:'May',length:0,collapse:false},
              6:{name:'June',length:0,collapse:false},
              7:{name:'July',length:0,collapse:false},
              8:{name:'August',length:0,collapse:false},
              9:{name:'September',length:0,collapse:false},
              10:{name:'October',length:0,collapse:false},
              11:{name:'November',length:0,collapse:false},
              12:{name:'December',length:0,collapse:false},
            },
            paidTemplate:{
              1:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
              2:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
              3:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
              4:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
              5:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
              6:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
              7:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
              8:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
              9:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
              10:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
              11:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
              12:{would_paid: { value: 0.00, name: 'Would’ve Paid', color: yellow },paid: { value: 0.00, name: 'Paid', color: green },saved: { value: 0.00, name: 'Savings', color: pink }},
            },
            indexFocus:0,
            thisMonth:0
        }
      this.handleCloseModal=this.handleCloseModal.bind(this)
      this.showAlertNotes=this.showAlertNotes.bind(this)
    }
    async componentDidMount() {
      const month = new Date().getUTCMonth() + 1;
  
  this.setState({
    theme: this.props.theme,
    auth: this.props.auth,
    dashboard: this.props.dashboard,
    thisMonth: parseInt(month),
    isLoadingOrders: true
  });

  // Determine user ID for both API calls
  let userId = 0;
  if (this.props.auth.user.is_admin) {
    userId = this.props.auth.selectUser?.id;
  } else if (this.props.auth.user.user_group == 3) {
    userId = this.props.auth.user.id;
  } else if (this.props.auth.user.user_group == 4) {
    userId = this.props.auth.user.ref_admin.id;
  }

  if (!userId) {
    this.setState({ isLoadingOrders: false });
    return;
  }

  // Call both API functions
  const [ordersResponse, summaryResponse] = await Promise.all([
    this.props.dashboard.getOrders(this.props.auth.token, userId),
    this.props.dashboard.getSummaryReports(this.props.auth.token, userId)
  ]);

  // Process orders data as before
  let reportCount = 0;
  if (ordersResponse.status == 200 && ordersResponse.msg == "success") {
    const reportTableData = this.props.dashboard.reportTableData;
    
    // Create a fresh copy of the template to avoid duplication
    const paidTemplate = JSON.parse(JSON.stringify(initialPaidTemplate));
    
    // Process data and calculate totals
    Object.keys(reportTableData).forEach(year => {
      Object.keys(reportTableData[year]).forEach(month => {
        const monthReports = reportTableData[year][month];
        const monthNum = parseInt(month);
        
        // Count reports in this month
        reportCount += Object.keys(monthReports).length;
        
        // For each report in this month
        Object.keys(monthReports).forEach(report => {
          const item = monthReports[report];
          
          // Calculate values for this item
          const wouldPaid = parseFloat((item.qty * item.would_paid).toFixed(2));
          const paid = parseFloat((item.qty * item.paid).toFixed(2));
          const saved = parseFloat((item.qty * (item.would_paid - item.paid)).toFixed(2));
          
          // Add to monthly totals
          if (paidTemplate[monthNum]) {
            paidTemplate[monthNum].would_paid.value += wouldPaid;
            paidTemplate[monthNum].paid.value += paid;
            paidTemplate[monthNum].saved.value += saved;
          }
        });
      });
    });
    
    this.setState({
      reportTableData,
      paidTemplate,
      reportCount,
      isLoadingOrders: false
    });
  } else {
    this.setState({ isLoadingOrders: false });
  }

      this.refreshInterval = setInterval(this.handleDataRefresh, 3000);
  }

// New method to handle data refresh
    handleDataRefresh = async () => {
      if (this.props.dashboard.refetch.refetchOrders) {
        // Reset paidTemplate
        const paidTemplate = JSON.parse(JSON.stringify(initialPaidTemplate));
        let reportCount = 0;
        
        // Process data
        const reportTableData = this.props.dashboard.reportTableData;
        Object.keys(reportTableData).forEach(year => {
          Object.keys(reportTableData[year]).forEach(month => {
            const monthNum = parseInt(month);
            Object.keys(reportTableData[year][month]).forEach(report => {
              const item = reportTableData[year][month][report];
              reportCount++;
              
              if (paidTemplate[monthNum]) {
                paidTemplate[monthNum].would_paid.value += (item.qty * item.would_paid);
                paidTemplate[monthNum].paid.value += (item.qty * item.paid);
                paidTemplate[monthNum].saved.value += parseFloat((item.qty * item.saved).toFixed(2));
              }
            });
          });
        });
        
        this.setState({ paidTemplate, reportCount, isLoadingOrders: false });
        this.props.dashboard.setRefetch(prevRefetch => ({...prevRefetch, refetchOrders: false}));
      }
      
      if (this.state.sendForAPI) {
        this.setState({
          reportTableData: this.props.dashboard.reportTableData,
          sendForAPI: false,
          isLoadingOrders: false
        });
      }
    }

    componentWillUnmount() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval);
      }
    }
    showAlertNotes(){
      this.setState({
        showAlert:true
      })
      setTimeout(() => {
        this.setState({
          showAlert:false,
          infoAlert:{ msg: '', status: undefined }
        })
      }, 3000);
    }
    handleStatus(value,rowsItem,order){
      if(value===3){
        this.setState({
          statusPaid:true,
          rowsItem:rowsItem,
          indexRow:order
        })
      }
      if(value===4){
        this.setState({
          statusDelete:true,      
          rowsItem:rowsItem,
          indexRow:order
        })
      }
    }
    handleCloseModal(){
      this.setState({
        statusDelete:false,
        statusPaid:false,

      })
      this.shouldComponentUpdate=true
      this.forceUpdate()
    }
    componentDidUpdate(prevProps, prevState) {
      // Check if user selection has changed
      if (prevProps.auth.selectUser?.id !== this.props.auth.selectUser?.id) {
        
        // First reset the state completely
        this.setState({
          selectUser: this.props.auth.selectUser.id,
          paidTemplate: JSON.parse(JSON.stringify(initialPaidTemplate)),
          isLoadingOrders: true,
          reportCount: 0
        }, async () => {
          // Then fetch new data
          if (this.props.auth.selectUser) {
            let response = await this.props.dashboard.getOrders(
              this.props.auth.token, 
              this.props.auth.selectUser.id
            );
            
            if (response.status === 200 && response.msg === "success") {
              // Wait for reportTableData to be updated in the dashboard context
              setTimeout(() => {
                this.calculateMonthlyTotals();
                this.setState({ isLoadingOrders: false });
              }, 500);
            } else {
              this.setState({ isLoadingOrders: false });
            }
          }
        });
      }
      
      // Check if reportTableData has changed
      if (prevProps.dashboard.reportTableData !== this.props.dashboard.reportTableData &&
          this.state.selectUser === this.props.auth.selectUser?.id) {
        this.calculateMonthlyTotals();
      }
    }
    calculateMonthlyTotals() {
      const reportTableData = this.props.dashboard.reportTableData;
      let reportCount = 0;
      
      // Create a fresh copy of the template
      const newPaidTemplate = JSON.parse(JSON.stringify(initialPaidTemplate));
      
      // Process the data
      Object.keys(reportTableData).forEach(year => {
        Object.keys(reportTableData[year]).forEach(month => {
          const monthReports = reportTableData[year][month];
          Object.keys(monthReports).forEach(report => {
            const item = monthReports[report];
            reportCount++;
            
            // Calculate values and add to the appropriate month
            const monthNum = parseInt(month);
            if (newPaidTemplate[monthNum]) {
              newPaidTemplate[monthNum].would_paid.value += (item.qty * item.would_paid);
              newPaidTemplate[monthNum].paid.value += (item.qty * item.paid);
              newPaidTemplate[monthNum].saved.value += parseFloat((item.qty * item.saved).toFixed(2));
            }
          });
        });
      });
      
      
      // Update state with the new values
      this.setState({
        paidTemplate: newPaidTemplate,
        reportCount: reportCount
      });
    }
  render() {
    return (
      <>
        {this.state.showAlert && (
          <AlertNotes
            message={this.state.infoAlert.msg}
            action={this.state.infoAlert.status}
          />
        )}
        {this.state.statusPaid && (
            <OrderPaid
              item={this.state.rowsItem}
              data={this.state.statusPaid}
              onClose={this.handleCloseModal}
              permission={this.state.auth.user.is_admin}
              showAlertNotes={this.showAlertNotes}
              setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
              refetchOrder={(sendForAPI)=>{this.setState({
                sendForAPI:sendForAPI
              })}}
            />
          )}
        {this.state.statusDelete && (
          <OrderDelete
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
        <BudgetPaidCalc 
          // sendForAPI={this.state.sendForAPI}
          // // paidTemplate={this.state.paidTemplate} 
          // thisMonth={this.state.thisMonth}
          // index={this.state.indexFocus}
        />
        <Box
          sx={{
            width:'100%',
            height:'100%',
            display:'block',
            // backgroundColor:"#F3F4F6"
          }}
        >
            <OrderMenu handleStatus={this.handleStatus} />
            {this.state.isLoadingOrders ? (
                  <>
                    <Box 
                    sx={{
                      display:'flex',
                      flexDirection:'row',
                      gap:1,
                      // textAlign:'center',
                      // alignItems:'center',
                      minHeight:this.state.tableHeight,
                      height:'100%',
                      width:'100%',
                      paddingTop:0,
                      marginTop:0,
                      backgroundColor:this.props.theme.selectTheme.background_2,
                      // paddingY:5,
                      // marginBottom:3,
                    }}
                  >
                    <TableSkeleton />
                  </Box>
                </>
              ) : (
                <Box
                  sx={{
                    height:'100%',
                    // display:'flex',
                    // paddingLeft:5,
                    borderTop:'1px solid #e5e7eb',
                    // textAlign:'left',
                    // alignItems:'center',
                    backgroundColor:this.props.theme.selectTheme.background_3
                  }}
                >
                  <Paper 
                    sx={{ 
                      width: '100%',
                      height:'100%',
                      overflow: 'hidden'
                    }}
                  >
                    <TableContainer 
                      sx={{ 
                        height:'100%',
                        minHeight: `${this.state.tableHeight}px` ,
                        backgroundColor:this.props.theme.selectTheme.background,
                        color: this.props.theme.selectTheme.fontColor
                      }}
                    >
                      <Table stickyHeader aria-label="sticky table">
                        <TableHead sx={{
                            marginBottom:2,
                            backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                            color: this.props.theme.selectTheme.headerfontColor
                        }}>
                          <TableRow
                            sx={{
                              // backgroundColor:this.props.theme.selectTheme.headerBg,
                              // color: this.props.theme.selectTheme.headerfontColor
                            }}
                          >
                            {reportHead.map((item,index) => {   
                              return(                      
                              <TableCell
                                key={index}
                                align={item.align}
                                sx={{
                                  width:`${item.style}%`,
                                  border:`1px ${grey[200]} solid`,
                                  textAlign:'center',
                                  backgroundColor:this.props.theme.selectTheme.background,
                                  color: this.props.theme.selectTheme.fontColor
                                }}
                              >
                                {item.name}
                              </TableCell>
                              )
                            })
                          }
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {this.state.reportCount!=0?(
                            Object.keys(this.props.dashboard.reportTableData).map((year, yearIndex) => {
                                const yearReports=this.props.dashboard.reportTableData[year]
                                return (Object.keys(yearReports)
                                  .map((month,monthIndex) => {
                                    const monthReport=yearReports[month]
                                    if(monthReport.length!=0){
                                      return (
                                        <>
                                          <TableRow 
                                            hover 
                                            role="checkbox" 
                                            tabIndex={-1} 
                                            key={year}
                                            sx={{ 
                                              // marginBottom:1,
                                              // paddingBottom:1,
                                              '& > *':{ 
                                                borderBottom: 'unset' 
                                                }
                                            }}
                                          >
                                            <TableCell colSpan={1}
                                              sx={{
                                                backgroundColor:this.props.theme.selectTheme.background_2,
                                                  color: this.props.theme.selectTheme.fontColor_3
                                              }}
                                            >
                                              <IconButton
                                                aria-label="expand row"
                                                size="small"
                                                onClick={() => {
                                                  const rowReportDetail=this.state.rowReportDetail[month]
                                                  rowReportDetail.collapse=!this.state.rowReportDetail[month].collapse
                                                    this.setState({
                                                      ...this.state.rowReportDetail[month],
                                                      report:rowReportDetail,
                                                      indexFocus:month
                                                    })
                                                  }
                                                }
                                              >
                                                {this.state.rowReportDetail[month].collapse ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                                              </IconButton>
                                            </TableCell>
                                            <TableCell colSpan={1} 
                                                sx={{
                                                  px:1,
                                                  py:2,
                                                  border:`1px ${grey[200]} solid`,
                                                  textAlign:'center',
                                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                                  color: this.props.theme.selectTheme.fontColor_3
                                                }}
                                              >
                                              {year}
                                            </TableCell>
                                            <TableCell colSpan={1}
                                                sx={{
                                                  px:1,
                                                  py:2,
                                                  border:`1px ${grey[200]} solid`,
                                                  textAlign:'center',
                                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                                  color: this.props.theme.selectTheme.fontColor_3
                                                }}
                                              >
                                              {
                                                this.state.rowReportDetail[month].name
                                              }
                                            </TableCell>
                                            <TableCell colSpan={1}
                                                sx={{
                                                  px:1,
                                                  py:2,
                                                  border:`1px ${grey[200]} solid`,
                                                  textAlign:'center',
                                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                                  color: this.props.theme.selectTheme.fontColor_3
                                                }}
                                              >
                                              ${
                                                parseFloat(this.state.paidTemplate[month].would_paid.value).toFixed(2)
                                              }
                                            </TableCell>
                                            <TableCell colSpan={1}
                                                sx={{
                                                  px:1,
                                                  py:2,
                                                  border:`1px ${grey[200]} solid`,
                                                  textAlign:'center',
                                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                                  color: this.props.theme.selectTheme.fontColor_3
                                                }}
                                              >
                                              ${
                                                parseFloat(this.state.paidTemplate[month].paid.value).toFixed(2)
                                              }
                                            </TableCell>
                                            <TableCell colSpan={1}
                                                sx={{
                                                  px:1,
                                                  py:2,
                                                  border:`1px ${grey[200]} solid`,
                                                  textAlign:'center',
                                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                                  color: this.props.theme.selectTheme.fontColor_3
                                                }}
                                              >
                                              ${
                                                parseFloat(this.state.paidTemplate[month].saved.value).toFixed(2)
                                              }
                                            </TableCell>
                                          </TableRow>
                                          <TableRow
                                            sx={{
                                              marginBottom:1,
                                            }}
                                          >
                                            <TableCell style={{ paddingBottom: 0, paddingTop: 0, height:'auto' }} colSpan={7}>
                                              <Collapse in={this.state.rowReportDetail[month].collapse} timeout="auto" unmountOnExit>
                                                <Box
                                                  sx={{
                                                    // height:'100%',
                                                    height:'auto',
                                                    // display:'flex',
                                                    // paddingLeft:5,
                                                    borderTop:'1px solid #e5e7eb',
                                                    // textAlign:'left',
                                                    // alignItems:'center',
                                                    backgroundColor:this.props.theme.selectTheme.background_3
                                                  }}
                                                >
                                                  <Paper 
                                                    sx={{ 
                                                      width: '100%',
                                                      // height:'100%',
                                                      overflow: 'hidden'
                                                    }}
                                                  >
                                                    <TableContainer 
                                                      sx={{ 
                                                        height:'auto',
                                                        paddingBottom:4,
                                                        // minHeight: `${this.state.tableHeight}px` ,
                                                        backgroundColor:this.props.theme.selectTheme.background,
                                                        color: this.props.theme.selectTheme.fontColor
                                                      }}
                                                    >
                                                      <Table stickyHeader aria-label="sticky table">
                                                        <TableHead sx={{
                                                            marginBottom:2,
                                                            backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                                                            color: this.props.theme.selectTheme.headerfontColor
                                                        }}>
                                                          <TableRow
                                                            sx={{
                                                              // backgroundColor:this.props.theme.selectTheme.headerBg,
                                                              // color: this.props.theme.selectTheme.headerfontColor
                                                            }}
                                                          >
                                                            {head.map((item,index) => (
                                                              this.state.auth.user.is_admin?
                                                              (
                                                                  <TableCell
                                                                    key={index}
                                                                    align={item.align}
                                                                    sx={{
                                                                      width:`${item.style}%`,
                                                                      border:`1px ${grey[200]} solid`,
                                                                      textAlign:'center',
                                                                      backgroundColor:this.props.theme.selectTheme.background,
                                                                      color: this.props.theme.selectTheme.fontColor
                                                                    }}
                                                                  >
                                                                    {item.name}
                                                                  </TableCell>
                                                              ):(item.name=='Action' ?(
                                                                  <>
                                                                  </>
                                                                ):(
                                                                  <TableCell
                                                                    key={index}
                                                                    align={item.align}
                                                                    sx={{
                                                                      width:`${item.style}%`,
                                                                      border:`1px ${grey[200]} solid`,
                                                                      textAlign:'center',
                                                                      backgroundColor:this.props.theme.selectTheme.background,
                                                                      color: this.props.theme.selectTheme.fontColor
                                                                    }}
                                                                  >
                                                                    {item.name}
                                                                  </TableCell>
                                                              ))
                                                            ))}
                                                          </TableRow>
                                                        </TableHead>
                                                        <TableBody>
                                                          {monthReport.length ? (
                                                            Object.keys(monthReport)
                                                              .map((report,index) => {
                                                                const reportItem=monthReport[report]
                                                                return ( 
                                                                  <>
                                                                    <TableRow 
                                                                        sx={{
                                                                          // backgroundColor:this.props.theme.selectTheme.background,
                                                                          // color: this.props.theme.selectTheme.fontColor
                                                                        }}
                                                                        hover role="checkbox" tabIndex={-1} key={index}>
                                                                        <TableCell 
                                                                          sx={{
                                                                            px:1,
                                                                            py:2,
                                                                            border:`1px ${grey[200]} solid`,
                                                                            textAlign:'center',
                                                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                                                            color: this.props.theme.selectTheme.fontColor_3
                                                                          }}
                                                                          // 'px-1 border border-gray-200 dark:border-gray-600 text-center py-2';
                                                                          key={report.id}>
                                                                          {reportItem.created_at
                                                                            ? formatDate(
                                                                                  'Amm-dd-yyyy',
                                                                                  reportItem.created_at
                                                                              )
                                                                            : formatDate('Amm-dd-yyyy')}
                                                                        </TableCell>
                                                                        <TableCell 
                                                                          sx={{
                                                                            px:1,
                                                                            py:2,
                                                                            border:`1px ${grey[200]} solid`,
                                                                            textAlign:'center',
                                                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                                                            color: this.props.theme.selectTheme.fontColor_3
                                                                          }}
                                                                        >
                                                                          {reportItem.product? reportItem.product.name: ''}
                                                                        </TableCell>
                                                                        <TableCell 
                                                                          sx={{
                                                                            px:1,
                                                                            py:2,
                                                                            border:`1px ${grey[200]} solid`,
                                                                            textAlign:'center',
                                                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                                                            color: this.props.theme.selectTheme.fontColor_3
                                                                          }}
                                                                        >
                                                                          {reportItem.product? reportItem.product.s_k_u: ''}
                                                                        </TableCell>
                                                                        <TableCell 
                                                                          sx={{
                                                                            px:1,
                                                                            py:2,
                                                                            border:`1px ${grey[200]} solid`,
                                                                            textAlign:'center',
                                                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                                                            color: this.props.theme.selectTheme.fontColor_3
                                                                          }}
                                                                        >
                                                                          {reportItem.note || ''}
                                                                        </TableCell>
                                                                        <TableCell 
                                                                          sx={{
                                                                            px:1,
                                                                            py:2,
                                                                            border:`1px ${grey[200]} solid`,
                                                                            textAlign:'center',
                                                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                                                            color: this.props.theme.selectTheme.fontColor_3
                                                                          }}
                                                                        >
                                                                          {reportItem.qty ?? ''}
                                                                        </TableCell>
                                                                        <TableCell 
                                                                          sx={{
                                                                            px:1,
                                                                            py:2,
                                                                            border:`1px ${grey[200]} solid`,
                                                                            textAlign:'center',
                                                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                                                            color: this.props.theme.selectTheme.fontColor_3
                                                                          }}
                                                                        >
                                                                          ${
                                                                              parseFloat(reportItem.qty*reportItem.would_paid).toFixed(2)
                                                                            }
                                                                        </TableCell>
                                                                        <TableCell 
                                                                          sx={{
                                                                            px:1,
                                                                            py:2,
                                                                            border:`1px ${grey[200]} solid`,
                                                                            textAlign:'center',
                                                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                                                            color: this.props.theme.selectTheme.fontColor_3
                                                                          }}
                                                                        >
                                                                          ${parseFloat(reportItem.qty*reportItem.paid).toFixed(2)}
                                                                        </TableCell>
                                                                        <TableCell 
                                                                          sx={{
                                                                            px:1,
                                                                            py:2,
                                                                            border:`1px ${grey[200]} solid`,
                                                                            textAlign:'center',
                                                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                                                            color: this.props.theme.selectTheme.fontColor_3
                                                                          }}
                                                                        >
                                                                          ${parseFloat(reportItem.qty*parseFloat((reportItem.would_paid-reportItem.paid).toFixed(2))).toFixed(2)}
                                                                          {/* ${reportItem.qty*parseFloat((reportItem.would_paid-reportItem.paid).toFixed(2))} */}
                                                                        </TableCell>
                                                                        <TableCell 
                                                                          sx={{
                                                                            px:1,
                                                                            py:2,
                                                                            border:`1px ${grey[200]} solid`,
                                                                            textAlign:'center',
                                                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                                                            color: this.props.theme.selectTheme.fontColor_3
                                                                          }}
                                                                        >
                                                                          {reportItem.vendor
                                                                              ? reportItem.vendor.name
                                                                              : ''}
                                                                        </TableCell>
                                                                        {this.state.auth.user.is_admin && (
                                                                          <TableCell 
                                                                              sx={{
                                                                                px:1,
                                                                                py:2,
                                                                                border:`1px ${grey[200]} solid`,
                                                                                textAlign:'center',
                                                                                backgroundColor:this.props.theme.selectTheme.background_2,
                                                                              color: this.props.theme.selectTheme.fontColor_3
                                                                              }}
                                                                          >    
                                                                          <Tooltip
                                                                            content='Paid Order'
                                                                          >
                                                                            <Button
                                                                                size='xs'
                                                                                pill
                                                                                inline='true'
                                                                                className='m-0 p-0 inline mb-2'
                                                                                onClick={() => {
                                                                                  this.handleStatus(3,reportItem,report)
                                                                                }}
                                                                            >
                                                                              <HiInformationCircle className='h-4 w-4 text-green-500' />
                                                                            </Button>
                                                                          </Tooltip>
                                                                          <Tooltip
                                                                            content='Delete Order'
                                                                            // style='light'
                                                                          >
                                                                            <Button
                                                                              // color='gray'
                                                                              size='xs'
                                                                              pill
                                                                              inline='true'
                                                                              className='m-0 p-0 inline'
                                                                              onClick={() => {
                                                                                this.handleStatus(4,reportItem,report)
                                                                              }}
                                                                              // onClick={() => {
                                                                              //   this.setState({
                                                                              //     statusDelete:true,
                                                                              //     rowsItem:order
                                                                              //   })
                                                                              // }}
                                                                            >
                                                                              <TbTrash className='h-4 w-4 text-red-600' />
                                                                            </Button>
                                                                          </Tooltip>
                                                                        </TableCell>
                                                                        )}
                                                                    </TableRow>
                                                                  </>
                                                                  )
                                                                }
                                                              )
                                                            ):(
                                                              <NoDataList colSpan={head.length} />
                                                          )
                                                        }
                                                        </TableBody>
                                                      </Table>
                                                    </TableContainer>
                                                          {/* <Box 
                                                            sx={{
                                                              display:'flex',
                                                              justifyContent:'flex-end'
                                                            }}
                                                          >
                                                            <TablePagination
                                                              rowsPerPageOptions={[5, 10, 25, 50, 100]}
                                                            //   component="div"

                                                              count={this.state.ordersTableData.length}
                                                              rowsPerPage={this.state.rowsPerPage}
                                                              page={this.state.page}
                                                              onPageChange={(e)=>{this.handleChangePage(e)}}
                                                              onRowsPerPageChange={(e)=>{this.handleChangeRowsPerPage(e)}}
                                                            />
                                                          </Box> */}
                                                  </Paper>
                                                </Box>
                                              </Collapse>
                                            </TableCell>
                                          </TableRow>           
                                        </>
                                        )
                                    }
                                    }).reverse()
                                  )
                              })
                            ):(
                              <NoDataList colSpan={reportHead.length} />
                            )
                            
                          }
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Paper>
                </Box>
              )
            }
        </Box>
      </>
      )
  }
}
export default compose(
    themeHook,
    authHook,
    locationHook,
    dashboardHook
  )(ReportScene)
