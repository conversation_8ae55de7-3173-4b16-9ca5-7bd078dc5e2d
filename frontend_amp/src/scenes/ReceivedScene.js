import React, { Component } from 'react'
import dashboardHook from '../HOK/DashboardHook';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';
import { Box, Button, Checkbox, IconButton, Skeleton, Tooltip } from '@mui/material';
import { formatDate, orderStatusFn } from '../utils/functions';
import { grey, purple, red } from '@mui/material/colors';
import { HiInformationCircle } from 'react-icons/hi';
import { TbEdit, TbTrash } from 'react-icons/tb';
import { compose } from 'redux';
import authHook from '../HOK/AuthHook';
import themeHook from '../HOK/ThemeHook';
import locationHook from '../HOK/LoctionHook';
import ReveivedEdit from '../Components/received/ReceivedEdit';
import AlertNotes from '../Components/AlertNotes';
import OrderMenu from '../Components/OrderMenu';
import NoDataList from '../Components/NoDataList';
import OrderDelete from '../Components/order/OrderDelete';
import OrderPaid from '../Components/OrderPaid';
import TableSkeleton from '../Components/TableSkeleton';
import { Favorite } from '@mui/icons-material';
const head = [
  { name: 'Date', style: '8' },
  { name: 'Favorite', style: '8' },
  { name: 'Product', style: '30' },
  { name: 'SKU(Manufacturer Code)', style: '15' },
  { name: 'Note', style: '20' }, // New Note column
  { name: 'QTY', style: '6' },
  { name: 'Specific', style: '4' },
  { name: 'Vendor', style: '7' },
  { name: 'Urgent', style: '4' },
  // { name: 'Exp. Delivery', style: '10' },
  { name: 'Status', style: '8' },
  { name: 'Received', style: '4' },
  { name: 'Action', style: '8' },
];
class ReceivedScene extends Component {
  constructor(props){
    super(props)
    this.state={
      theme:props.theme,
      auth:props.auth,
      dashboard:props.dashboard,
      ordersTableData:Object(null),
      tableHeight:(this.props.theme.windowDimensions.height*85)/100,
      statusEdit:false,
      sendForAPI:false,
      statusPaid:false,
      showAlert:false,
      indexRow:0,
      isLoadingOrders:true,
      rowsItem:Object()|null,
      infoAlert:{ msg: '', status: undefined },
      rowsPerPage:5,
      page:0,
    }
    this.showAlertNotes=this.showAlertNotes.bind(this)
    this.handleStatus=this.handleStatus.bind(this)
    this.handleCloseModal=this.handleCloseModal.bind(this)
  }
  async componentDidMount(){
    if (this.props.auth.user.is_admin){
      if(this.props.auth.selectUser){
        let response=await this.props.dashboard.getOrders(this.props.auth.token,this.props.auth.selectUser.id)
        if (response.status==200) {
          if(response.msg=="success"){
              this.setState({
                ordersTableData:await this.props.dashboard.ordersTableData,
                
              })
          }
        }
        this.setState({
          isLoadingOrders:false,
        })
      }
    }
    else{
      let user_id=0
        if(this.props.auth.user.user_group==3){
          user_id=this.props.auth.user.id
        }else if(this.props.auth.user.user_group==4){
          user_id=this.props.auth.user.ref_admin.id
        }
      let response=await this.props.dashboard.getOrders(this.props.auth.token,user_id)
      if (response.status==200) {
        if(response.msg=="success"){
            this.setState({
              ordersTableData:await this.props.dashboard.ordersTableData,
              
            })
        }
      }
      this.setState({
        isLoadingOrders:false,
      })
    }
    setInterval(async () => {
      if (this.state.sendForAPI){
        console.log("sendforApi")
        this.setState({
          ordersTableData:await this.props.dashboard.ordersTableData,
          isLoadingOrders:true,
        })
        this.shouldComponentUpdate=true
        this.forceUpdate()
      }
      this.setState({
        sendForAPI:false,
        isLoadingOrders:false,
      })
    }, 3000);
    this.shouldComponentUpdate=true
    this.forceUpdate()
  }
  async handleUpdateFavorite(id,favorite){
    let orderData={
      id:id,
      admin_id:null,
      my_product:favorite,
    }
    if (this.props.auth.user.user_group==2){
      orderData.admin_id=this.props.auth.selectUser.id
    }else if(this.props.auth.user.user_group==3){
      orderData.admin_id=this.props.auth.user.id
    }else if(this.props.auth.user.user_group==4){
      orderData.admin_id=this.props.auth.user.ref_admin.id
    }
    else{

    }
    let response=await this.props.dashboard.updateFavorite(this.props.auth.token,orderData)
      if (response.status!=200) {
          this.setState({
            sendForAPI:true,
            infoAlert:{
              msg: `Error,Can\'t you\'r Favorite List: ${response.msg}`,
              status: false,
            }
          })
          this.showAlertNotes();
      } else {
          if (response.msg == 'success') {
              // refetchOrder();
              this.setState({
                sendForAPI:true,
                infoAlert:{
                  msg: 'Your\'r Favorite List Updated',
                  status: true,
                }
              })
              this.showAlertNotes();
              this.handleCloseModal();
          } else {
            this.setState({
              sendForAPI:true,
              infoAlert:{
                  msg: `Failed,Can\'t Update you\'r Favorite List: ${response.msg}`,
                  status: false,
                }
            })
            this.showAlertNotes();
          }
      }
  }
  showAlertNotes(){
    this.setState({
      showAlert:true
    })
    setTimeout(() => {
      this.setState({
        showAlert:false,
        infoAlert:{ msg: '', status: undefined }
      })
    }, 3000);
  }
  handleStatus(value,rowsItem,order){
    if(value===1){
      this.setState({
        statusAdd:true,
        // rowsItem:rowsItem
      })
    }else if(value===2){
      this.setState({
        statusEdit:true,
        rowsItem:rowsItem,
        indexRow:order
      })
    }else if(value===3){
      this.setState({
        statusPaid:true,
        rowsItem:rowsItem,
        indexRow:order
      })
    }else if(value===4){
      this.setState({
        statusDelete:true,      
        rowsItem:rowsItem,
        indexRow:order
      })
    }
  }
  handleCloseModal(){
    this.setState({
      statusAdd:false,
      statusEdit:false,
      statusPaid:false,
      statusDelete:false,
    })
    this.shouldComponentUpdate=true
    this.forceUpdate()
  }
  handleChangePage=(event, newPage)=>{
    this.setState({
        page:newPage,
    })
  };
  handleChangeRowsPerPage(event){
    this.setState({
        rowsPerPage:+event.target.value,
        page:0
    });
  };
  render() {
    let controller=0;
    const loderClass =
        'divide-y divide-gray-200 rounded-xl animate-pulse dark:divide-gray-700 bg-gray-300 rounded-2xl';
    return (
        <>
        {this.state.showAlert && (
          <AlertNotes
              message={this.state.infoAlert.msg}
              action={this.state.infoAlert.status}
          />
        )}
        {this.state.statusEdit && (
          <ReveivedEdit
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
            permission={this.state.auth.user.is_admin}
          />
        )}
        {this.state.statusDelete && (
              <OrderDelete
                onClose={this.handleCloseModal}
                item={this.state.rowsItem}
                showAlertNotes={this.showAlertNotes}
                setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
                refetchOrder={(sendForAPI)=>{this.setState({
                  sendForAPI:sendForAPI
                })}}
              />
          )}
          {this.state.statusPaid && (
              <OrderPaid
                item={this.state.rowsItem}
                data={this.state.statusPaid}
                onClose={this.handleCloseModal}
                permission={this.state.auth.user.is_admin}
                showAlertNotes={this.showAlertNotes}
                setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
                refetchOrder={(sendForAPI)=>{this.setState({
                  sendForAPI:sendForAPI
                })}}
              />
          )}
        <Box
          sx={{
            width:'100%',
            height:'100%',
            display:'block',
            // backgroundColor:"#F3F4F6"
          }}
        >
            <OrderMenu handleStatus={this.handleStatus} />
            {this.state.isLoadingOrders ? (
              <>
                <Box 
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    gap:1,
                    // textAlign:'center',
                    // alignItems:'center',
                    minHeight:this.state.tableHeight,
                    height:'100%',
                    width:'100%',
                    paddingTop:0,
                    marginTop:0,
                    backgroundColor:this.props.theme.selectTheme.background_2,
                    // paddingY:5,
                    // marginBottom:3,
                  }}
                >
                  <TableSkeleton />
                </Box>
              </>
            ) : (this.props.dashboard.ordersTableData&&(
            <Box
            sx={{
              height:'100%',
              // display:'flex',
              // paddingLeft:5,
              borderTop:'1px solid #e5e7eb',
              // textAlign:'left',
              // alignItems:'center',
              backgroundColor:this.props.theme.selectTheme.background_3
            }}
          >
            <Paper 
              sx={{ 
                width: '100%',
                height:'100%',
                overflow: 'hidden'
              }}
            >
            <TableContainer 
              sx={{ 
                height:'100%',
                minHeight: `${this.state.tableHeight}px` ,
                backgroundColor:this.props.theme.selectTheme.background,
                color: this.props.theme.selectTheme.fontColor
              }}
            >
              <Table stickyHeader aria-label="sticky table">
                <TableHead sx={{
                    marginBottom:2,
                    backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                    color: this.props.theme.selectTheme.headerfontColor
                }}>
                  <TableRow
                    sx={{
                      // backgroundColor:this.props.theme.selectTheme.headerBg,
                      // color: this.props.theme.selectTheme.headerfontColor
                    }}
                  >
                    {head.map((item,index) => (
                      <TableCell
                        key={index}
                        align={item.align}
                        sx={{
                          width:`${item.style}%`,
                          border:`1px ${grey[200]} solid`,
                          textAlign:'center',
                          backgroundColor:this.props.theme.selectTheme.background,
                          color: this.props.theme.selectTheme.fontColor
                        }}
                        // style={{ 
                        //   width: `${item.style}%`,
                        //   border:`1px ${grey[200]} solid`,
                        //   // borderColor:grey[200],
                        //   textAlign:'center',
                        //  }}
                      >
                        {item.name}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {this.props.dashboard.ordersTableData.length ? (
                  // this.props.dashboard.ordersTableData.slice(this.state.page * this.state.rowsPerPage, this.state.page * this.state.rowsPerPage + this.state.rowsPerPage)
                  Object.keys(this.props.dashboard.ordersTableData)
                    .map((order,index) => {
                      const orderItem=this.props.dashboard.ordersTableData[index]

                      if (this.props.dashboard.ordersTableData[index].received && this.props.dashboard.ordersTableData[index].status!='returned'){
                        controller=controller+1
                        return ( 
                          <TableRow 
                              sx={{
                                // backgroundColor:this.props.theme.selectTheme.background,
                                // color: this.props.theme.selectTheme.fontColor
                              }}
                              hover role="checkbox" tabIndex={-1} key={index}>
                              <TableCell 
                                sx={{
                                  px:1,
                                  py:2,
                                  border:`1px ${grey[200]} solid`,
                                  textAlign:'center',
                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                  color: this.props.theme.selectTheme.fontColor_3
                                }}
                                // 'px-1 border border-gray-200 dark:border-gray-600 text-center py-2';
                                key={index.id}>
                                {this.props.dashboard.ordersTableData[index].created_at
                                  ? formatDate(
                                        'Amm-dd-yyyy',
                                        this.props.dashboard.ordersTableData[index].created_at
                                    )
                                  : formatDate('Amm-dd-yyyy')}
                              </TableCell>
                              <TableCell 
                                sx={{
                                  px:1,
                                  py:2,
                                  border:`1px ${grey[200]} solid`,
                                  textAlign:'center',
                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                  color: this.props.theme.selectTheme.fontColor_3
                                }}
                              >
                                <IconButton
                                  onClick={()=>{
                                    let favorite=!this.props.dashboard.ordersTableData[index].my_product
                                    this.handleUpdateFavorite(this.props.dashboard.ordersTableData[index].id,favorite)
                                  }}
                                >
                                  {this.props.dashboard.ordersTableData[index].my_product?
                                    (
                                      <Favorite 
                                        sx={{ color: red[500] }}
                                        size={24} 
                                      />
                                    ):(
                                      <Favorite 
                                      size={24} />
                                    )
                                  }
                                </IconButton>
                              </TableCell>
                              <TableCell 
                                sx={{
                                  px:1,
                                  py:2,
                                  border:`1px ${grey[200]} solid`,
                                  textAlign:'center',
                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                  color: this.props.theme.selectTheme.fontColor_3
                                }}
                              >
                                {this.props.dashboard.ordersTableData[index].product? this.props.dashboard.ordersTableData[index].product.name: ''}
                              </TableCell>
                              <TableCell 
                                sx={{
                                  px:1,
                                  py:2,
                                  border:`1px ${grey[200]} solid`,
                                  textAlign:'center',
                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                  color: this.props.theme.selectTheme.fontColor_3
                                }}
                              >
                                {this.props.dashboard.ordersTableData[index].product? this.props.dashboard.ordersTableData[index].product.s_k_u: ''}
                              </TableCell>
                              {/* Add Note column */}
                              <TableCell 
                                sx={{
                                  px:1,
                                  py:2,
                                  border:`1px ${grey[200]} solid`,
                                  textAlign:'center',
                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                  color: this.props.theme.selectTheme.fontColor_3
                                }}
                              >
                                {this.props.dashboard.ordersTableData[index].note || ''}
                              </TableCell>
                              <TableCell 
                                sx={{
                                  px:1,
                                  py:2,                          
                                  border:`1px ${grey[200]} solid`,
                                  textAlign:'center',
                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                  color: this.props.theme.selectTheme.fontColor_3
                                }}
                              >
                              {this.props.dashboard.ordersTableData[index].qty ?? ''}
                              </TableCell>
                              <TableCell 
                                sx={{
                                  px:1,
                                  py:2,
                                  border:`1px ${grey[200]} solid`,
                                  textAlign:'center',
                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                  color: this.props.theme.selectTheme.fontColor_3
                                }}
                              >
                                <Checkbox
                                  sx={{
                                    color:purple[400],
                                  }}
                                  checked={this.props.dashboard.ordersTableData[index].specific}
                                  // disabled
                                  />
                              </TableCell>
                              <TableCell 
                                sx={{
                                  px:1,
                                  py:2,
                                  border:`1px ${grey[200]} solid`,
                                  textAlign:'center',
                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                  color: this.props.theme.selectTheme.fontColor_3
                                }}
                              >
                                {this.props.dashboard.ordersTableData[index].vendor
                                    ? this.props.dashboard.ordersTableData[index].vendor.name
                                    : ''}
                              </TableCell>
                              <TableCell 
                                sx={{
                                  px:1,
                                  py:2,
                                  border:`1px ${grey[200]} solid`,
                                  textAlign:'center',
                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                  color: this.props.theme.selectTheme.fontColor_3
                                }}
                              >
                                <Checkbox
                                  sx={{
                                    color:purple[400],
                                  }}
                                  checked={
                                    this.props.dashboard.ordersTableData[index].urgent === true
                                    }
                                  />
                              </TableCell>            
                              <TableCell 
                                sx={{
                                  px:1,
                                  py:2,
                                  border:`1px ${grey[200]} solid`,
                                  textAlign:'center',
                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                  color: this.props.theme.selectTheme.fontColor_3
                                }}
                              >
                                  {
                                    orderStatusFn(this.props.dashboard.ordersTableData[index].status).name
                                    // if(this.state.order.initialOrderStatus)
                                      // orderStatusFn(this.state.ordersTableData[index].status)
                                      //     .name
                                  }
                              </TableCell>
                              <TableCell 
                                sx={{
                                  px:1,
                                  py:2,
                                  border:`1px ${grey[200]} solid`,
                                  textAlign:'center',
                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                  color: this.props.theme.selectTheme.fontColor_3
                                }}
                              >
                                <Checkbox
                                    sx={{
                                      color:purple[400],
                                    }}
                                    checked={this.props.dashboard.ordersTableData[index].received}
                                    // disabled
                                    // onChange={(e) =>
                                    //     handleInputChange(
                                    //         e,
                                    //         key,
                                    //         'received',
                                    //         order
                                    //     )
                                    // }
                                />
                              </TableCell>
                              <TableCell 
                                  sx={{
                                    px:1,
                                    py:2,
                                    border:`1px ${grey[200]} solid`,
                                    textAlign:'center',
                                    backgroundColor:this.props.theme.selectTheme.background_2,
                                  color: this.props.theme.selectTheme.fontColor_3
                                  }}
                                >
                                    {this.props.auth.user.is_admin && (
                                        <>
                                          <Tooltip
                                              content='Paid Order'
                                              // style='light'
                                          >
                                              <Button
                                                  // color='gray'
                                                  size='xs'
                                                  pill
                                                  inline='true'
                                                  className='m-0 p-0 inline mb-2'
                                                  onClick={() => {
                                                    this.handleStatus(3,this.props.dashboard.ordersTableData[index],index)
                                                  }}
                                                  // onClick={() => {
                                                  //   this.setState({
                                                  //     StatusPaid:true,
                                                  //     rowsItem:order
                                                  //   })
                                                  // }}
                                              >
                                                  <HiInformationCircle className='h-4 w-4 text-green-500' />
                                              </Button>
                                          </Tooltip>
                                         
                                        </>  
                                    )}
                                    {/* {this.state.ordersTableData[index].user_group>2? ( */}
                                    {this.props.auth.user.is_admin && (
                                      <>
                                        <Tooltip
                                          content='Delete Order'
                                          // style='light'
                                        >
                                          <Button
                                            // color='gray'
                                            size='xs'
                                            pill
                                            inline='true'
                                            className='m-0 p-0 inline'
                                            onClick={() => {
                                              this.handleStatus(4,this.props.dashboard.ordersTableData[index],index)
                                            }}
                                            // onClick={() => {
                                            //   this.setState({
                                            //     statusDelete:true,
                                            //     rowsItem:order
                                            //   })
                                            // }}
                                          >
                                            <TbTrash className='h-4 w-4 text-red-600' />
                                          </Button>
                                        </Tooltip>
                                      </>
                                    )}
                                    <Tooltip
                                      
                                        content='Edit Order'
                                        // style='light'
                                    >
                                        <Button
                                          // disabled={this.state.auth.user.user_group>2?true:false}
                                          size='medium'
                                          sx={{
                                            color:purple[400],
                                          }}
                                          pill
                                          // inline='true'
                                          onClick={() => {
                                            this.handleStatus(2,this.props.dashboard.ordersTableData[index],index)
                                            // this.setState({
                                            //   statusEdit:true,
                                            //   rowsItem:order
                                            // })
                                          }}
                                        >
                                            <TbEdit className='h-4 w-4 text-purple-600' />
                                        </Button>
                                    </Tooltip>
                                </TableCell>
                          </TableRow>
                         )
                      }
                      if(index===this.props.dashboard.ordersTableData.length-1 && controller===0){  
                        return(
                          <NoDataList colSpan={head.length} />
                        )
                      }
                    })
                  ):(
                      <NoDataList colSpan={head.length} />
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            {/* <Box 
              sx={{
                display:'flex',
                justifyContent:'flex-end',
                alignItems:'center',

              }}
            >
              <TablePagination
              disabled
                sx={{
                  width:'100%',
                  backgroundColor:this.props.theme.selectTheme.background,
                  color: this.props.theme.selectTheme.fontColor,
                  '&& .MuiTablePagination-selectLabel':{
                    margin:0
                  },
                  "&& .MuiTablePagination-displayedRows":{
                    margin:0
                  }
                }}
                rowsPerPageOptions={[5, 10, 25, 50, 100]}
              //   component="div"
                count={this.props.dashboard.ordersTableData.length}
                rowsPerPage={this.state.rowsPerPage}
                page={this.state.page}
                onPageChange={this.handleChangePage}
                onRowsPerPageChange={(e)=>{
                  this.handleChangeRowsPerPage(e)}}
              />
            </Box> */}
            </Paper>
            </Box>
            ))
          }
        </Box>
        {/* <Box
            sx={{
              width:'100%',
              minHeight:(this.props.theme.windowDimensions.height*30)/100,
              display:'block',
              backgroundColor:this.props.theme.selectTheme.background,
              // backgroundColor:"#F3F4F6"
            }}
          ></Box> */}
        </>
      
    )
  }
}
export default compose(
  themeHook,
  authHook,
  locationHook,
  dashboardHook
)(ReceivedScene)
