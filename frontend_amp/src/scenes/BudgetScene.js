import React, { Component } from 'react'
import { Box, Button, IconButton, Link, Paper, Skeleton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tooltip, Typography } from '@mui/material'
import OrderMenu from '../Components/OrderMenu'
import { compose } from 'redux'
import themeHook from '../HOK/ThemeHook'
import authHook from '../HOK/AuthHook'
import locationHook from '../HOK/LoctionHook'
import dashboardHook from '../HOK/DashboardHook'
import AlertNotes from '../Components/AlertNotes'
import NoDataList from '../Components/NoDataList'
import { blue, grey, purple, red } from '@mui/material/colors'
import { TbEyeCheck, TbTrash, TbUserEdit } from 'react-icons/tb'
import TableSkeleton from '../Components/TableSkeleton'
import { PieChart } from '@mui/x-charts'
import { TbEdit } from 'react-icons/tb'
import BudgetEdit from '../Components/budget/BudgetEdit'
import InvoiceAdd from '../Components/invoices/InvoiceAdd'
import InvoiceEdit from '../Components/invoices/InvoiceEdit'
import InvoiceDelete from '../Components/invoices/InvoiceDelete'
import { formatDate } from '../utils/functions'
import { FaCloudDownloadAlt } from 'react-icons/fa'
import { DEV_BASE_URL } from '../Environments'
import { SpaceBar } from '@mui/icons-material'
const head = [
  { name: 'Vendor', style: '10' },
  { name: 'Date', style: '10' },
  { name: 'Invoice Number', style: '10' },
  { name: 'Amount Paid', style: '10' },
  { name: 'Invoice File', style: '10' },
  { name: 'Action', style: '5' },
];
const summeryHead = [
  { name: 'Current', style: '1' },
  { name: 'Year', style: '2' },
  { name: 'Month', style: '2' },
  { name: 'Total Budget', style: '2' },
  { name: 'Paid', style: '2' },
  { name: 'Remainig Budget', style: '2' },

  // { name: 'Action', style: '5' },
];
const monthInfo={
  1:{name:'January'},
  2:{name:'February'},
  3:{name:'March'},
  4:{name:'April'},
  5:{name:'May'},
  6:{name:'June'},
  7:{name:'July'},
  8:{name:'August'},
  9:{name:'September'},
  10:{name:'October'},
  11:{name:'November'},
  12:{name:'December'},
}
const reverseMonthInfo={
  'January':1,
  'February':2,
  'March':3,
  'April':4,
  'May':5,
  'June':6,
  'July':7,
  'August':8,
  'September':9,
  'October':10,
  'November':11,
  'December':12,
}
class BudgetScene extends Component {
    constructor(props){
        super(props)
        this.state={
            theme:props.theme,
            auth:props.auth,
            dashboard:props.dashboard,
            invoicesTableData:Object(null),
            tableHeight:(this.props.theme.windowDimensions.height*85)/100,
            isloading:true,
            isloadingTotalTable:true,
            isLoadingInvoice:true,
            totalBudget:0,
            totalBudgetLeftData:[],
            budgetPercent:0,
            sendForAPI:false,
            rowsItem:0,
            // thismonth:new Date().getUTCMonth()+1,
            rest:0,
            radius:50,
            itemNb:2,
            skipAnimation:false,
            statusAdd:false,
            statusInvoiceEdit:false,
            statusEdit:false,
            statusDelete:false,
            showAlert:false,
            thisYear:new Date().getUTCFullYear(),
            thisMonth:new Date().getUTCMonth()+1,
            statusIndex:new Date().getUTCMonth()+1,
            statusSelectedOption:monthInfo[new Date().getUTCMonth()+1].name,
            // rowsItem:Object()|null,
            infoAlert:{ msg: '', status: undefined },
            data:[
              { label: 'Total Budget', value: 0 },
              { label: 'Remainig Budget', value: 0 },
            ]
        }
        this.handleStatus=this.handleStatus.bind(this)
        this.showAlertNotes=this.showAlertNotes.bind(this)
        this.handleCloseModal=this.handleCloseModal.bind(this)
    }
    async componentDidMount(){
      const thisYears=new Date().getUTCFullYear()
      const thisMonth=new Date().getUTCMonth()+1
      this.setState({
        theme:this.props.theme,
        auth:this.props.auth,
        dashboard:this.props.dashboard,
      })
      if (this.props.auth.user.is_admin){
        if(this.props.auth.selectUser){
          let response=await this.props.dashboard.getBudgetLeft(this.props.auth.token,this.props.auth.selectUser.id)
          if (response.status==200) {
            if(response.msg=="success"){
              let totalBudgetLeftData=this.props.dashboard.totalBudgetLeftData?this.props.dashboard.totalBudgetLeftData[thisYears][thisMonth].paid:0 
              let totalBudget=this.props.auth.user.is_admin?this.props.auth.selectUser.total_budget:this.props.auth.user.total_budget
              this.setState({
                totalBudgetLeftData:await this.props.dashboard.totalBudgetLeftData,
              })
              this.setState({
                totalBudget:totalBudget,
                data:[
                  { label: 'Paid', value: totalBudgetLeftData },
                  { label: 'Remainig Budget', value: parseFloat(totalBudget-totalBudgetLeftData).toFixed(2)},
                ]
              })
            }
          }
          this.setState({
            isloading:false,
            isloadingTotalTable:false,
          })
          let responseInvoice=await this.props.dashboard.getInvoices(this.props.auth.token,this.props.auth.selectUser.id)
          if (responseInvoice.status==200) {
            if(responseInvoice.msg=="success"){
              this.setState({
                invoicesTableData:await this.props.dashboard.invoicesTableData,
              })
            }
          }
          this.setState({
            isLoadingInvoice:false,
          })
        }
      }
      else{
        let user_id=0
        if(this.props.auth.user.user_group==3){
          user_id=this.props.auth.user.id
        }else if(this.props.auth.user.user_group==4){
          user_id=this.props.auth.user.ref_admin.id
        }
        let response=await this.props.dashboard.getBudgetLeft(this.props.auth.token,user_id)
        if (response.status==200) {
          if(response.msg=="success"){
            let totalBudgetLeftData=this.props.dashboard.totalBudgetLeftData?this.props.dashboard.totalBudgetLeftData[thisYears][thisMonth].paid:0 
            let totalBudget=this.props.auth.user.user_group==3?this.props.auth.user.total_budget:this.props.auth.user.ref_admin.total_budget
            this.setState({
              totalBudgetLeftData:await this.props.dashboard.totalBudgetLeftData,
            })
            this.setState({
              totalBudget:totalBudget,
              data:[
                { label: 'Paid', value:totalBudgetLeftData },
                { label: 'Remainig Budget', value: parseFloat((totalBudget-await this.props.dashboard.totalBudgetLeftData[this.state.thisYear][this.state.thisMonth].paid).toFixed(2))},
              ]
            })
          }
        }
        this.setState({
          isloading:false,
          isloadingTotalTable:false,
        })
        let responseInvoice=await this.props.dashboard.getInvoices(this.props.auth.token,user_id)
          if (responseInvoice.status==200) {
            if(responseInvoice.msg=="success"){
              this.setState({
                invoicesTableData:await this.props.dashboard.invoicesTableData,
              })
            }
          }
          this.setState({
            isLoadingInvoice:false,
          })
      }
      setInterval(async () => {
        if (this.state.sendForAPI){
          console.log("sendforApi")
          let totalBudget=this.props.auth.user.is_admin?this.props.auth.selectUser.total_budget:this.props.auth.user.total_budget
          this.setState({
            totalBudget:totalBudget,
            isloading:true,
            isloadingTotalTable:true,
            isLoadingInvoice:true,
            data:[
              { label: 'Paid', value: await this.props.dashboard.totalBudgetLeftData[this.state.thisYear].paid },
              { label: 'Remainig Budget', value: parseFloat((totalBudget-await this.props.dashboard.totalBudgetLeftData[this.state.thisYear][this.state.thisMonth].paid).toFixed(2)) },
            ],
            invoicesTableData:await this.props.dashboard.invoicesTableData,
          })
          this.shouldComponentUpdate=true
          this.forceUpdate()
        }
        this.setState({
          sendForAPI:false,
          isloading:false,
          isloadingTotalTable:false,
          isLoadingInvoice:false,
        })
      }, 3000);
      this.shouldComponentUpdate=true
      this.forceUpdate()
    }
    handleStatus(value,rowsItem,invoice){
      if(value===1){
        this.setState({
          statusAdd:true,
          // rowsItem:rowsItem
        })
      }if(value===2){
        this.setState({
          statusEdit:true,
          rowsItem:rowsItem,
          // indexRow:
        })
      }else if(value===3){
        this.setState({
          statusInvoiceEdit:true,
          rowsItem:rowsItem,
          indexRow:invoice
        })
      }else if(value===4){
        this.setState({
          statusDelete:true,      
          rowsItem:rowsItem,
          indexRow:invoice
        })
      }
    }
    showAlertNotes = () => {
      this.setState({
        showAlert:true,
      })
      setTimeout(() => {
        this.setState({
          showAlert:false,
          infoAlert:{ msg: '', status: undefined }
        })
      }, 3000);
  };
  handleCloseModal(){
    this.setState({
      statusAdd:false,
      statusInvoiceEdit:false,
      statusEdit:false,
      statusDelete:false,
      invoicesTableData:this.state.dashboard.invoicesTableData
    })
    this.shouldComponentUpdate=true
    this.forceUpdate()
  }
  render() {  
  
    return (
      <>
        {this.state.showAlert && (
          <AlertNotes 
            message={this.state.infoAlert.msg} 
            action={this.state.infoAlert.status}
          />
        )}
        {this.state.statusEdit && (
          <BudgetEdit
            onClose={this.handleCloseModal}
            item={Number(this.state.totalBudget)}
            // item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
            permission={this.state.auth.user.is_admin}
          />
          )}
        {this.state.statusAdd && (
            <InvoiceAdd
                // isLoadingOrders={this.state.isLoadingOrders}
                onClose={this.handleCloseModal}
                showAlertNotes={this.showAlertNotes}
                setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
                refetchOrder={(sendForAPI)=>{this.setState({
                    sendForAPI:sendForAPI
                })}}
                // onFormInsert={this.handleInsertOrder}
            />
        )}
        {this.state.statusInvoiceEdit && (
            <InvoiceEdit
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
                sendForAPI:sendForAPI
            })}}
            permission={this.state.auth.user.is_admin}
            />
        )}
        {this.state.statusDelete && (
            <InvoiceDelete
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
                sendForAPI:sendForAPI
            })}}
            />
        )}
          {this.state.isloading ?(
              <>

              </>
            ):(
              <Box
              sx={{
                display:'flex',
              height:'100%',
              minHeight: `${this.state.tableHeight}px` ,
              width:'100%',
              flexDirection:'row',
              // paddingLeft:5,
              // paddingX:4,
              borderTop:'1px solid #e5e7eb',
              justifyContent:'center',
              textAlign:'center',
              // alignItems:'center',
              backgroundColor:this.props.theme.selectTheme.background_3
            }}
          >
            
            <Box
              sx={{
              height:'100%',
              width:'100%',
              paddingX:4,
              borderTop:'1px solid #e5e7eb',
              backgroundColor:this.props.theme.selectTheme.background_3,
              color:this.props.theme.selectTheme.fontColor_3

            }}
            >
              {/* <Box 
                sx={{  
                  width:'30%',
                  marginTop:2,

                }}
              >
              <FormControl
                size='small'
                fullWidth
                sx={{
                  borderColor:this.props.theme.fontColor,
                  backgroundColor:this.props.theme.background,
                  color:this.props.theme.fontColor,
                  border:`1px solid ${this.state.statusChoiceColor}`
                }}
              >
                <InputLabel 
                    id="demo-simple-select-label"
                    sx={{
                        backgroundColor:this.props.theme.background,
                        color:this.props.theme.fontColor,
                        // width:'80%',
                        borderRight:`1px solid`
                    }}
                >Figure Month</InputLabel>
                <Select
                  value={this.state.statusSelectedOption}
                  label="Month"
                  // disabled={!this.state.checkReceived}
                  onChange={(e)=>{
                    this.setState({
                      statusIndex:reverseMonthInfo[e.target.value],
                      statusSelectedOption:e.target.value,
                      data:[
                        { label: 'Paid', value: this.state.totalBudgetLeftData[reverseMonthInfo[e.target.value]-1].paid },
                        { label: 'Remainig Budget', value: parseFloat((this.state.totalBudget-this.state.totalBudgetLeftData[reverseMonthInfo[e.target.value]-1].paid).toFixed(2))},
                      ]
                    })
                    }
                  }
                  sx={{
                      display:'flex',
                      flexDirection:'row',
                      backgroundColor:this.props.theme.background,
                      color:this.props.theme.fontColor,
                      '& .MuiSelect-select':{
                          display:'flex',
                          backgroundColor:this.props.theme.background,
                          color:this.props.theme.fontColor
                      },
                  }}
                  style={{display:'flex',flexDirection:'row',alignItems:'center'}}
              >
                {Object.keys(monthInfo).map((item,key)=>{
                    return (
                      <MenuItem 
                        key={item}
                        value={monthInfo[item].name}
                      >
                        <Typography>
                          {monthInfo[item].name}
                        </Typography>
                    </MenuItem>
                    )
                  })
                }
                </Select>
              </FormControl>
              </Box> */}
              <Box 
                sx={{
                  display:'flex',
                  flexDirection:'row',

                }}
              >
                {this.state.isloadingTotalTable?(
                  <>
                    <Box 
                      sx={{
                        display:'flex',
                        flexDirection:'row',
                        gap:1,
                        minHeight:this.state.tableHeight,
                        height:'100%',
                        width:'100%',
                        paddingTop:0,
                        marginTop:0,
                        backgroundColor:this.props.theme.selectTheme.background_2,
                      }}
                    >
                      <TableSkeleton />
                    </Box>
                  </>
                ):(
                  <Box
                    sx={{
                      width:'40%'
                    }}
                  >
                    <Paper 
                      sx={{ 
                        width: '100%',
                        height:'100%',
                        overflow: 'auto'
                      }}
                    >
                      <TableContainer 
                        sx={{ 
                          // height:'100%',
                          // minHeight: `${this.state.tableHeight}px` ,
                          backgroundColor:this.props.theme.selectTheme.background,
                          color: this.props.theme.selectTheme.fontColor,
                          overflow:'auto'

                        }}
                      >
                        <Table 
                          stickyHeader 
                          aria-label="sticky table" 
                          sx={{
                              overflow:'auto'
                          }}
                        >
                          <TableHead sx={{
                              marginBottom:2,
                              backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                              color: this.props.theme.selectTheme.headerfontColor
                          }}>
                            <TableRow
                              sx={{
                                // backgroundColor:this.props.theme.selectTheme.headerBg,
                                // color: this.props.theme.selectTheme.headerfontColor
                              }}
                            >
                              {summeryHead.map((item,index) => (
                                <TableCell
                                  key={index}
                                  align={item.align}
                                  sx={{
                                    width:`${item.style}%`,
                                    border:`1px ${grey[200]} solid`,
                                    textAlign:'center',
                                    backgroundColor:this.props.theme.selectTheme.background,
                                    color: this.props.theme.selectTheme.fontColor
                                  }}
                                  // style={{ 
                                  //   width: `${item.style}%`,
                                  //   border:`1px ${grey[200]} solid`,
                                  //   // borderColor:grey[200],
                                  //   textAlign:'center',
                                  //  }}
                                >
                                  {item.name}
                                </TableCell>
                              ))}
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {Object.keys(this.props.dashboard.totalBudgetLeftData).map((year,yearIndex)=>{
                                const item=this.props.dashboard.totalBudgetLeftData[year]
                                return(Object.keys(item).map((month,monthIndex)=>{
                                  return (
                                    <>
                                      <TableRow 
                                        sx={{
                                          padding:1
                                          // backgroundColor:this.props.theme.selectTheme.background,
                                          // color: this.props.theme.selectTheme.fontColor
                                        }}
                                        hover role="checkbox" tabIndex={-1} key={yearIndex}
                                      >
                                        <TableCell 
                                          colSpan={1}
                                          sx={{
                                            width:'8px',
                                            padding:1,
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                              color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                        >
                                          <IconButton
                                            aria-label="expand row"
                                            size="small"
                                            // onClick={() => {
                                            //   const rowReportDetail=this.state.rowReportDetail[month]
                                            //   rowReportDetail.collapse=!this.state.rowReportDetail[month].collapse
                                            //     this.setState({
                                            //       ...this.state.rowReportDetail[month],
                                            //       report:rowReportDetail,
                                            //       indexFocus:month
                                            //     })
                                            //   }
                                            // }
                                          >
                                            {/* {this.state.rowReportDetail[month].collapse ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />} */}
                                          </IconButton>
                                        </TableCell>
                                        <TableCell 
                                          colSpan={1} 
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                        >
                                          {year}
                                        </TableCell>
                                        <TableCell 
                                          colSpan={1} 
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                        >
                                          {item[month]['name']}
                                        </TableCell>
                                        <TableCell 
                                          colSpan={1} 
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                        >
                                          ${this.state.totalBudget}
                                        </TableCell>
                                        <TableCell 
                                          colSpan={1} 
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                        >
                                          {item[month]['paid']}
                                        </TableCell>
                                        <TableCell 
                                          colSpan={1} 
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                        >
                                          {parseFloat(this.state.totalBudget-item[month]['paid']).toFixed(2)}
                                        </TableCell>
                                      </TableRow>
                                    </>
                                  )
                                })
                              )
                              })
                            }
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Paper>    
                  </Box>
                )}
                <Box
                  sx={{
                    display:'flex',
                    width:'60%',
                    flexDirection:'column',
                    justifyContent:'center',
                    alignItems:'center',
                    marginBottom:2

                  }}
                > 
                  {this.state.data &&(
                    <Box
                      sx={{
                        display:'flex',
                        justifyContent:'center',
                        alignItems:'center'
                      }}
                    >
                      <PieChart
                        
                        height={400}
                        width={500}
                        series={[
                          {
                            data:this.state.data,
                            innerRadius: this.state.radius,
                            arcLabel: (params) => params.label ?? '',
                            color:this.props.theme.selectTheme.fontColor_3,
                            arcLabelMinAngle: 90,
                            // valueFormatter,
                          },
                        ]}
                        sx={{
                          color:this.props.theme.selectTheme.fontColor_3,
                          display:'flex',
                          justifyContent:'center',
                          textAlign:'center',
                          alignContent:'center'
                        }}
                        skipAnimation={this.state.skipAnimation}
                      />
                    </Box>
                  )}
                  <Box
                    sx={{
                      display:'flex',
                      width:'100%',
                      justifyContent:'space-evenly',
                      position:'relative',
                    }}
                  >
                    <Box
                      sx={{
                        display:'flex',
                        flexDirection:'column',
                        justifyContent:'center',
                        textAlign:'center',
                        paddingX:1,
                        // backgroundColor:this.props.theme.selectTheme.background_3,
                        color:this.props.theme.selectTheme.fontColor_3
                      }}
                    >
                      <Box
                        sx={{
                          display:'flex',
                        }}
                      >
                        <Typography
                          
                          // fontStyle={'oblique'} 
                          gutterBottom>
                          Total Budget
                        </Typography>
                        {this.state.auth.user.is_admin &&(
                        <Button
                          color={'warning'}
                          className='cursor-pointer inline-block rounded-full'
                          pill
                          size='small'
                          onClick={() => {
                            this.setState({
                              statusEdit:true,
                              rowsItem:this.state.totalBudget
                            })
                              // setStatusEdit(true);
                              // setRowsItem(budgetTotal);
                          }}
                        >
                          <TbEdit className='w-5 h-5 text-orange-500 dark:text-orange-300' />
                        </Button>
                        )}
                      </Box>
                      <Box
                        sx={{
                          display:'flex',
                          flexDirection:'row',
                          justifyContent:'space-evenly'
                        }}
                      >
                        <Typography  fontStyle={'oblique'} gutterBottom>
                          ${this.state.totalBudget}
                        </Typography>
                        <Typography/>
                      </Box>
                    </Box>
                    <Box
                      sx={{
                        display:'flex',
                        flexDirection:'column',
                        justifyContent:'center',
                        textAlign:'center',
                        paddingX:1,
                        color:this.props.theme.selectTheme.fontColor_3
                      }}
                    >
                      <Typography id="input-item-number" gutterBottom>
                        Remainig Budget
                      </Typography>
                      <Typography id="input-item-number" gutterBottom>
                        ${this.props.dashboard.totalBudgetLeftData?parseFloat(this.state.totalBudget-this.props.dashboard.totalBudgetLeftData[this.state.thisYear][this.state.thisMonth].paid).toFixed(2):this.state.totalBudget}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>
              {this.state.auth.user.is_admin? (
                <OrderMenu handleStatus={this.handleStatus}/>
              ):(
                <>
                  {/* <SpaceBar 
                    sx={{
                      height:"20px"
                    }}
                    /> */}
                </>
              )}
              {this.state.isLoadingInvoice ? 
                (
                  <>
                    <Box 
                      sx={{
                        display:'flex',
                        flexDirection:'row',
                        gap:1,
                        minHeight:this.state.tableHeight,
                        height:'100%',
                        width:'100%',
                        paddingTop:0,
                        marginTop:0,
                        backgroundColor:this.props.theme.selectTheme.background_2,
                      }}
                    >
                      <TableSkeleton />
                    </Box>
                  </>
                ):(this.props.dashboard.invoicesTableData&&(
                    <Box
                      sx={{
                        height:'100%',
                        borderTop:'1px solid #e5e7eb',
                        backgroundColor:this.props.theme.selectTheme.background_3
                      }}
                    >
                      <Paper 
                        sx={{ 
                          width: '100%',
                          height:'100%',
                          overflow: 'hidden'
                        }}
                      >
                        <TableContainer 
                          sx={{ 
                            height:'100%',
                            minHeight: `${this.state.tableHeight}px` ,
                            backgroundColor:this.props.theme.selectTheme.background,
                            color: this.props.theme.selectTheme.fontColor
                          }}
                        >
                          <Table stickyHeader aria-label="sticky table">
                            <TableHead sx={{
                                marginBottom:2,
                                backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                                color: this.props.theme.selectTheme.headerfontColor
                            }}>
                              <TableRow
                                sx={{
                                  // backgroundColor:this.props.theme.selectTheme.headerBg,
                                  // color: this.props.theme.selectTheme.headerfontColor
                                }}
                              >
                                {head.map((item,index) => (
                                  this.props.auth.user.is_admin?(
                                    <TableCell
                                      key={index}
                                      align={item.align}
                                      sx={{
                                        width:`${item.style}%`,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.props.theme.selectTheme.background,
                                        color: this.props.theme.selectTheme.fontColor
                                      }}
                                      >
                                      {item.name}
                                    </TableCell>
                                  ):(item.name=='Action' ?(
                                      <>
                                      </>
                                    ):(
                                      <TableCell
                                        key={index}
                                        align={item.align}
                                        sx={{
                                          width:`${item.style}%`,
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background,
                                          color: this.props.theme.selectTheme.fontColor
                                        }}
                                      >
                                        {item.name}
                                      </TableCell>
                                  ))
                                ))}
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {this.props.dashboard.invoicesTableData.length ? (
                              Object.keys(this.props.dashboard.invoicesTableData)
                                .map((invoice,index) => {
                                  const item=this.props.dashboard.invoicesTableData[invoice]
                                  return (
                                      <TableRow 
                                          sx={{
                                            // backgroundColor:this.props.theme.selectTheme.background,
                                            // color: this.props.theme.selectTheme.fontColor
                                          }}
                                        hover 
                                        role="checkbox" 
                                        tabIndex={-1} 
                                        key={index}
                                      >
                                        <TableCell 
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                          key={invoice.id}
                                          >
                                          {item.vendor}
                                        </TableCell>
                                        <TableCell 
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                          key={invoice.id}
                                          >
                                           {formatDate('Amm-dd-yyyy',item.date)}
                                        </TableCell>
                                        <TableCell 
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                          key={invoice.id}
                                          >
                                          {item.invoice_number}
                                        </TableCell>
                                        <TableCell 
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                          key={invoice.id}
                                          >
                                          {item.amount_paid}
                                        </TableCell>
                                        <TableCell 
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                          key={invoice.id}
                                          >
                                            <Tooltip
                                              // disableTouchListener={!item.invoice}
                                              title="See More"
                                            >
                                              <IconButton
                                                href={DEV_BASE_URL+'/'+item.invoice_file}
                                                target="_blank"
                                                disabled={item.invoice_file?false:true}
                                              >
                                                <FaCloudDownloadAlt size={24} color={item.invoice_file?blue[600]:grey[500]} />
                                              </IconButton>
                                            </Tooltip>
                                          {/* {item.invoice_file} */}
                                        </TableCell>
                                        {this.state.auth.user.is_admin && (
                                          <TableCell 
                                            sx={{
                                              px:1,
                                              py:2,
                                              border:`1px ${grey[200]} solid`,
                                              textAlign:'center',
                                              backgroundColor:this.props.theme.selectTheme.background_2,
                                              color: this.props.theme.selectTheme.fontColor_3
                                            }}
                                            key={invoice.id}
                                          >
                                            {item.id && (
                                              <Box
                                                sx={{
                                                  display:'flex',
                                                  justifyContent:'space-around'
                                                }} 
                                                >
                                                  <Tooltip
                                                      content='Edit Vendor'
                                                  >
                                                      <Button
                                                          size='medium'
                                                          sx={{
                                                            color:purple[400],
                                                          }}
                                                          pill
                                                          onClick={() => {
                                                            this.handleStatus(3,item)
                                                          }}
                                                      >
                                                          <TbUserEdit size={24} />
                                                      </Button>
                                                  </Tooltip>
                                                  <Tooltip
                                                      content='Delete Vendor'
                                                  >
                                                      <Button
                                                        size='medium'
                                                        sx={{
                                                          color:red[400],
                                                        }}
                                                        pill
                                                        inline='true'
                                                        onClick={() => {
                                                          this.handleStatus(4,item)
                                                        }}
                                                      >
                                                          <TbTrash size={24} />
                                                      </Button>
                                                  </Tooltip>
                                              </Box>
                                            )}
                                          </TableCell>
                                        )}
                                      </TableRow>
                                  )
                                })
                              ):(
                                <NoDataList colSpan={head.length}/>
                              )}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </Paper>
                    </Box>
                  )
                )
              }
            </Box>
          </Box>
            )
          }
      </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    locationHook,
    dashboardHook
  )(BudgetScene)