import React, { Component } from 'react'
import PropTypes, { string } from 'prop-types'
import { Box, Button, Divider, IconButton, Link, Tooltip, Typography } from '@mui/material'
import { compose } from 'redux'
import themeHook from '../HOK/ThemeHook'
import authHook from '../HOK/AuthHook'
import navigateHook from '../HOK/NavigateHook'
import dashboardHook from '../HOK/DashboardHook'
import { formatDate } from '../utils/functions'
import { DEV_BASE_URL } from '../Environments'
import { BiSolidLike } from "react-icons/bi";
import { BiSolidDislike } from "react-icons/bi";
import { Badge } from '@mui/joy'
import Comment from '../Components/blogfeedback/Comment'
import locationHook from '../HOK/LoctionHook'
import { FaCloudDownloadAlt } from 'react-icons/fa'
import BlogImage from '../Components/blogfeedback/‌BlogImage'
import BlogEdit from '../Components/blogfeedback/BlogEdit'
import { blue } from '@mui/material/colors'
import { TbEdit } from 'react-icons/tb'
class BlogByIdScene extends Component {
  constructor(props){
    super(props)
    // console.log(props.location.state.id)
    this.state={
      theme:props.theme,
      darkTheme:props.theme.darkTheme,
      tableHeight:(this.props.theme.windowDimensions.height*70)/100,
      auth:props.auth,
      sendForAPI:false,
      blogData:Object(null),
      countLike:0,
      countDisLike:0,
      statusEdit:false,
      showAlert:false,
      infoAlert:{ msg: '', status: undefined },
      rowsItem:Object(null),
      commentsData:Object(null),
      isLoading:true,
      id:0
    }
    this.showAlertNotes=this.showAlertNotes.bind(this)
    this.handleStatus=this.handleStatus.bind(this)
    this.handleCloseModal=this.handleCloseModal.bind(this)
  }
  async componentDidMount(prevProps){
    const path=this.props.location.pathname.slice(1,)
    const id=path.split("/")[1]
    this.setState({
      id:id
    })
    // console.log(id)
    if(await this.props.auth.user){
        if(await this.props.auth.user.is_admin){
          let responseblogs=await this.props.dashboard.getBlogById(await this.props.auth.token,this.props.location.state.id)
          if(responseblogs.status!=200){
          }else{
              if(responseblogs.msg=="success"){
                this.setState({
                  blogData:await this.props.dashboard.blogData
                })
                this.setState({
                  isLoading:false
                })
              }else{
              }
          }
        }else{
          let responseblogs=await this.props.dashboard.getBlogById(await this.props.auth.token,id)
          if(responseblogs.status!=200){
          }else{
              if(responseblogs.msg=="success"){
                this.setState({
                  blogData:await this.props.dashboard.blogData
                })
                this.setState({
                  isLoading:false
                })
              }else{
              }
          }
        }
        this.setState({
          isLoading:false
        })
        this.shouldComponentUpdate=true
        this.forceUpdate()
      }
  }
  showAlertNotes(){
    this.setState({
      showAlert:true
    })
    setTimeout(() => {
      this.setState({
        showAlert:false,
        infoAlert:{ msg: '', status: undefined }
      })
    }, 3000);
  }
  handleStatus(status){
    if (status==2){
      this.setState({
        statusEdit:true,
        // rowsItem:rowsItem,
        // indexRow:blog
      })
    }
  }
  handleCloseModal(){
    this.setState({
      statusEdit:false,
      blogsData:this.props.dashboard.blogsData
    })
    this.shouldComponentUpdate=true
    this.forceUpdate()
  }
  render() {
    return (this.state.isLoading?(
        <>
        </>
      ):(
        <>
        {this.state.statusEdit && this.props.auth.user.is_admin &&(
              <BlogEdit
              onClose={this.handleCloseModal}
              item={this.props.dashboard.blogData}
              showAlertNotes={this.showAlertNotes}
              setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
              refetchOrder={(sendForAPI)=>{this.setState({
                  sendForAPI:sendForAPI
              })}}
              />
          )}
          <Box
              sx={{
                  width:'100%',
                  display:'flex',
                  flexDirection:'column',
                  textAlign:'center',
                  justifyContent:'center',
                  alignContent:'center',
                  alignItems:'center',
                  borderBottom:'',
                  paddingTop:2,
                  backgroundColor:this.props.theme.selectTheme.background_2,
                  gap:1
              }}
          >
             <Box
              sx={{
                width:'100%',
                display:'flex',
                flexDirection:'column',
                textAlign:'center',
                justifyContent:'center',
                alignContent:'center',
                alignItems:'center',
                backgroundColor:this.props.theme.selectTheme.background_2,
                paddingTop:2,
                gap:1
              }}
            >
            <Box
              sx={{
                width:'90%',
                height:"100%",
                display:'flex',
                flexDirection:'row',
                justifyContent:'center',
                alignContent:'center',
              }}
            > 
                <BlogImage post_image={this.props.dashboard.blogData.post_image} alt="Post Image" />
            </Box>
            <Box
              sx={{
                width:'90%',
                minHeight:'400px',
                display:'flex',
                flexDirection:'column',
                justifyContent:'start',
                borderTop:'1px solid #e5e7eb',
                borderLeft:'1px solid #e5e7eb',
                borderRight:'1px solid #e5e7eb',
                backgroundColor:this.props.theme.selectTheme.background_3,
                alignContent:'start',
                paddingY:'5%',
                paddingX:'10%'
              }}
            >
              <Box
                sx={{
                    width:'100%',
                    display:'flex',
                    flexDirection:'row',
                    justifyContent:'space-between',
                    marginBottom:2,
                    paddingX:2
                }}
              >
                <Box 
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                  }}
                >
                  <Tooltip
                    title="Edit"
                    content='Edit Blog'
                  >
                    <Button
                        size='medium'
                        pill
                        sx={{
                            color:blue[400],
                        }}
                        inline='true'
                        onClick={() => {
                            this.handleStatus(2)
                        }}
                    >
                        <TbEdit  size={24}/>
                    </Button>
                  </Tooltip>
                  <Tooltip
                    title="Download"
                  >
                    <Link
                      style={{
                        alignContent:'center',
                        marginRight:4
                      }}
                      href={DEV_BASE_URL+'/'+this.props.dashboard.blogData.blog_file}
                      target="_blank"
                      >
                      <FaCloudDownloadAlt size={24} />
                    </Link>
                  </Tooltip>
                </Box>
                <Box 
                  sx={{
                    display:'flex',
                    flexDirection:'column',
                  }}
                >
                  <Typography 
                      variant="h4"
                  >
                    {this.props.dashboard.blogData.post_title} 
                  </Typography>
                  
                </Box>
                <Box 
                 sx={{
                  display:'flex',
                  flexDirection:'column',
                  justifyContent:'space-between',
                  alignContent:'center'
                 }}
                >
                  <Typography 
                    sx={{
                        fontSize:'10px',
                        alignContent:'center',
                        alignItems:'center'
                    }}
                    variant="p" 
                    color="text.primary"
                  >
                      {formatDate(
                      'Amm-dd-yyyy',this.props.dashboard.blogData.created_at)}
                  </Typography>
                  <Typography 
                    sx={{
                        fontSize:'10px',
                        paddingLeft:2,
                         alignContent:'center',
                        alignItems:'center'
                    }}
                    variant="p"
                    color="text.secondary"
                    >
                    {this.props.dashboard.blogData.post_author}<br/> 
                  </Typography>
                </Box>
              </Box>
              <Box
                sx={{
                  display:'flex',
                  justifyContent:'flex-start',
                  alignContent:'flex-start',
                  alignItems:'flex-start'
                }}
              >
                <Box
                  sx={{
                    width:'100%',
                    border: '1px solid #ccc',
                    p: 2,
                    mt: 1,
                    textAlign:'left',
                    whiteSpace: 'pre-wrap', // این خط مهمه
                  }}
                  dangerouslySetInnerHTML={{ __html: this.props.dashboard.blogData.post_content }}
                />
                {/* // <Typography variant="span">
                //   {this.props.dashboard.blogData.post_content}
                // </Typography> */}
              </Box>
            </Box>
            </Box>
          </Box>
        </>
      )
    )
  }
}
export default compose(
  themeHook,
  authHook,
  locationHook,
  dashboardHook
)(BlogByIdScene)