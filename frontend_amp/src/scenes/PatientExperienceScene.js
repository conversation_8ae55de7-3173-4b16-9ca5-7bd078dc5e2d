import React, { Component } from 'react'
import { Box, Button, Paper, Skeleton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tooltip } from '@mui/material'
import OrderMenu from '../Components/OrderMenu'
import { compose } from 'redux'
import themeHook from '../HOK/ThemeHook'
import authHook from '../HOK/AuthHook'
import locationHook from '../HOK/LoctionHook'
import dashboardHook from '../HOK/DashboardHook'
import AlertNotes from '../Components/AlertNotes'
import NoDataList from '../Components/NoDataList'
import { grey, purple, red } from '@mui/material/colors'
import { TbEyeCheck, TbTrash, TbUserEdit } from 'react-icons/tb'
import TableSkeleton from '../Components/TableSkeleton'
import PatientExperienceAdd from '../Components/patientexperience/PatientExperienceAdd'
import PatientExperienceEdit from '../Components/patientexperience/PatientExperienceEdit'
import PatientExperienceDelete from '../Components/patientexperience/PatientExperienceDelete'
import PatientExperienceShow from '../Components/patientexperience/PatientExperienceShow'

const head = [
  { name: 'Patiant name', style: '20' },
  { name: 'Phone Number', style: '10' },
  { name: 'Preferred Pronouns', style: '10' },
  { name: 'Email', style: '10' },
  { name: 'Last Visited', style: '10' },
  { name: 'Review', style: '10' },
  { name: 'Action', style: '10' },
];
class PatientExperienceScene extends Component {
  constructor(props){
      super(props)
      this.state={
          theme:props.theme,
          auth:props.auth,
          dashboard:props.dashboard,
          patientExperienceTableData:Object(null),
          tableHeight:(this.props.theme.windowDimensions.height*85)/100,
          sendForAPI:false,
          rowsItem:Object()|null,
          statusAdd:false,
          statusEdit:false,
          statusDelete:false,
          isLoadingPatientExperience:false,
          showAlert:false,
          statusShow:false,
          infoAlert:{ msg: '', status: undefined },
      }
      this.showAlertNotes=this.showAlertNotes.bind(this)
      this.handleStatus=this.handleStatus.bind(this)
      this.handleCloseModal=this.handleCloseModal.bind(this)
  }
  async componentDidMount(){
    this.props.theme.setWindowDimensions({width: window.innerWidth,height: window.innerHeight});
    const auth = this.props.auth;
    this.setState({
      theme:this.props.theme,
      auth:this.props.auth,
      dashboard:this.props.dashboard,
    })   
    if (this.props.auth.user.is_admin){
      if(this.props.auth.selectUser){
        let response=await this.props.dashboard.getPatientExperience(this.props.auth.token,parseInt(this.props.auth.selectUser.id))
        if (response.status==200) {
          if(response.msg=="success"){
            this.setState({
              patientExperienceTableData:await this.props.dashboard.patientExperienceTableData,
            })
          }
        }
        this.setState({
          isLoadingPatientExperience:false,
        })
      }
    }
    else{
      let user_id=0
        if(this.props.auth.user.user_group==3){
          user_id=this.props.auth.user.id
        }else if(this.props.auth.user.user_group==4){
          user_id=this.props.auth.user.ref_admin.id
        }
      let response=await this.props.dashboard.getPatientExperience(this.props.auth.token,user_id)
      if (response.status==200) {
        if(response.msg=="success"){
          this.setState({
            patientExperienceTableData:await this.props.dashboard.patientExperienceTableData,
          })
        }
      }
      this.setState({
        isLoadingPatientExperience:false,
      })
    }
    setInterval(async () => {
      if (this.state.sendForAPI){
        console.log("sendforApi")
        this.setState({
          patientExperienceTableData:await this.props.dashboard.patientExperienceTableData,
          isLoadingPatientExperience:true,
        })
        this.shouldComponentUpdate=true
        this.forceUpdate()
      }
      this.setState({
        sendForAPI:false,
        isLoadingPatientExperience:false,
      })
    }, 3000);
    this.shouldComponentUpdate=true
    this.forceUpdate()
  }
  showAlertNotes(){
    this.setState({
      showAlert:true
    })
    setTimeout(() => {
      this.setState({
        showAlert:false,
        infoAlert:{ msg: '', status: undefined }
      })
    }, 3000);
  }
  handleStatus(value,rowsItem,patientExperience){
    if(value===1){
      this.setState({
        statusAdd:true,
        // rowsItem:rowsItem
      })
    }else if(value===2){
      this.setState({
        statusEdit:true,
        rowsItem:rowsItem,
        indexRow:patientExperience
      })
    }else if(value===3){
      this.setState({
        statusShow:true,
        rowsItem:rowsItem
      })
    }
    else if(value===4){
      this.setState({
        statusDelete:true,      
        rowsItem:rowsItem,
        indexRow:patientExperience
      })
    }
  }
  handleCloseModal(){
    this.setState({
      statusAdd:false,
      statusEdit:false,
      statusDelete:false,
      statusShow:false
    })
    this.shouldComponentUpdate=true
    this.forceUpdate()
  }
  render() {
    return (
      <>
        {this.state.showAlert && (
          <AlertNotes 
            message={this.state.infoAlert.msg}
            action={this.state.infoAlert.status}
          />
        )}
        {this.state.statusAdd && (
          <PatientExperienceAdd
            // isLoadingOrders={this.state.isLoadingOrders}
            onClose={this.handleCloseModal}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
        {this.state.statusEdit && (
          <PatientExperienceEdit
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
        {this.state.statusDelete && (
          <PatientExperienceDelete
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
        {this.state.statusShow && (
          <PatientExperienceShow
            data={this.state.statusShow}
            item={this.state.rowsItem}
            onClose={this.handleCloseModal}
          />
        )}
        <Box
          sx={{
            width:'100%',
            height:'100%',
            display:'block',
            // backgroundColor:"#F3F4F6"
          }}
        >
            <OrderMenu handleStatus={this.handleStatus} />
            {this.state.isLoadingPatientExperience ? (
                  <>
                    <Box 
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    gap:1,
                    // textAlign:'center',
                    // alignItems:'center',
                    minHeight:this.state.tableHeight,
                    height:'100%',
                    width:'100%',
                    paddingTop:0,
                    marginTop:0,
                    backgroundColor:this.props.theme.selectTheme.background_2,
                    // paddingY:5,
                    // marginBottom:3,
                  }}
                >
                  <TableSkeleton />
                </Box>
                  </>
              ) : (this.props.dashboard.patientExperienceTableData&&(
                  <Box
                    sx={{
                    height:'100%',
                    // display:'flex',
                    // paddingLeft:5,
                    borderTop:'1px solid #e5e7eb',
                    // textAlign:'left',
                    // alignItems:'center',
                    backgroundColor:this.props.theme.selectTheme.background_3
                  }}
                >
                  <Paper 
                    sx={{ 
                      width: '100%',
                      height:'100%',
                      overflow: 'hidden'
                    }}
                  >
                  <TableContainer 
                    sx={{ 
                      height:'100%',
                      minHeight: `${this.state.tableHeight}px` ,
                      backgroundColor:this.props.theme.selectTheme.background,
                      color: this.props.theme.selectTheme.fontColor
                    }}
                  >
                    <Table stickyHeader aria-label="sticky table">
                      <TableHead sx={{
                          marginBottom:2,
                          backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                          color: this.props.theme.selectTheme.headerfontColor
                      }}>
                        <TableRow
                          sx={{
                            // backgroundColor:this.props.theme.selectTheme.headerBg,
                            // color: this.props.theme.selectTheme.headerfontColor
                          }}
                        >
                          {head.map((item,index) => (
                            <TableCell
                              key={index}
                              align={item.align}
                              sx={{
                                width:`${item.style}%`,
                                border:`1px ${grey[200]} solid`,
                                textAlign:'center',
                                backgroundColor:this.props.theme.selectTheme.background,
                                color: this.props.theme.selectTheme.fontColor
                              }}
                            >
                              {item.name}
                            </TableCell>
                          ))}
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {this.props.dashboard.patientExperienceTableData.length ? (
                        Object.keys(this.props.dashboard.patientExperienceTableData)
                          .map((patientExperience,index) => {
                            let item=this.props.dashboard.patientExperienceTableData[patientExperience]
                            return (
                                <TableRow 
                                    sx={{
                                      // backgroundColor:this.props.theme.selectTheme.background,
                                      // color: this.props.theme.selectTheme.fontColor
                                    }}
                                  hover 
                                  role="checkbox" 
                                  tabIndex={-1} 
                                  key={index}
                                >
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={patientExperience.id}
                                    >
                                    {item.patiant_name}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={patientExperience.id}
                                    >
                                    {item.phone_number}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={patientExperience.id}
                                    >
                                    {item.preferred_pronouns}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={patientExperience.id}
                                    >
                                    {item.email}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={patientExperience.id}
                                    >
                                    {item.last_visited}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={patientExperience.id}
                                    >
                                    {item.review}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={patientExperience.id}
                                  >
                                    {item.id && (
                                      <Box
                                        sx={{
                                          display:'flex',
                                          justifyContent:'space-evenly',
                                          alignItems:'center'
                                        }} 
                                        >
                                          <Tooltip
                                              content='Show Patient Experience'
                                          >
                                              <Button
                                                size='medium'
                                                sx={{
                                                  color:purple[400],
                                                }}
                                                pill
                                                onClick={() => {
                                                  this.handleStatus(3,item,patientExperience)
                                                }}
                                              >
                                                  <TbEyeCheck size={24} />
                                              </Button>
                                          </Tooltip>
                                          <Tooltip
                                              content='Edit Insurance'
                                          >
                                              <Button
                                                  size='medium'
                                                  sx={{
                                                    color:purple[400],
                                                  }}
                                                  pill
                                                  onClick={() => {
                                                    this.handleStatus(2,item)
                                                  }}
                                              >
                                                  <TbUserEdit size={24} />
                                              </Button>
                                          </Tooltip>
                                          {this.props.auth.user.is_admin && (
                                              <Tooltip
                                                  content='Delete Vendor'
                                              >
                                                  <Button
                                                    size='medium'
                                                    sx={{
                                                      color:red[400],
                                                    }}
                                                    pill
                                                    inline='true'
                                                    onClick={() => {
                                                      this.handleStatus(4,item)
                                                    }}
                                                  >
                                                      <TbTrash size={24} />
                                                  </Button>
                                              </Tooltip>
                                          )}
                                      </Box>
                                    )}
                                  </TableCell>
                                </TableRow>
                            )
                          })
                        ):(
                          <NoDataList colSpan={head.length}/>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Paper>
                  </Box>
                )
              )
            }  
        </Box>      
      </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    locationHook,
    dashboardHook
  )(PatientExperienceScene)