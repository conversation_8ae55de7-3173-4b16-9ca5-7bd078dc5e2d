import React, { Component } from 'react'
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { Avatar, Box, Button, Checkbox, Collapse, IconButton, Skeleton, TablePagination, Tooltip, Typography } from '@mui/material';
import { TbEyeCheck, TbPlus, TbTrash, TbUserEdit } from 'react-icons/tb';
import TableSkeleton from '../Components/TableSkeleton';
import themeHook from '../HOK/ThemeHook';
import authHook from '../HOK/AuthHook';
import { blue, green, grey, purple, red } from '@mui/material/colors'
import NoDataList from '../Components/NoDataList'
import locationHook from '../HOK/LoctionHook';
import OrderMenu from '../Components/OrderMenu';
import { compose } from 'redux';
import AlertNotes from '../Components/AlertNotes';
import UserAdd from '../Components/users/UserAdd';
import UserEdit from '../Components/users/UserEdit';
import UserShow from '../Components/users/UserShow';
import UserDelete from '../Components/users/UserDelete';
import SubUserAdd from '../Components/subuser/SubUserAdd';
import SubUserEdit from '../Components/subuser/SubUserEdit';
import SubUserShow from '../Components/subuser/SubUserShow';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
const UserHead = [
  { name: 'Status', style: '2' },
  { name: 'Name', style: '20' },
  { name: 'Email', style: '10' },
  { name: 'Mobile Number', style: '15' },
  // { name: 'Dental', style: '20' },
  { name: 'Total Budget', style: '10' },
  { name: 'IsAdmin', style: '5' },
  { name: 'Status', style: '10' },
  { name: 'Sub User', style: '10' },
  { name: 'Action', style: '10' },
]
const subUserHead = [
    { name: 'Name', style: '20' },
    { name: 'Email', style: '10' },
    { name: 'Mobile Number', style: '15' },
    // { name: 'Dental', style: '20' },
    { name: 'Total Budget', style: '10' },
    { name: 'IsAdmin', style: '5' },
    { name: 'Status', style: '10' },
    { name: 'Action', style: '10' },
]

class UserListScene extends Component {
    constructor(props){
        super(props)
        this.state={
          theme:props.theme,
          auth:props.auth,
          isLoadingUserList:true,
          userList:Object(null)||[],
          tableHeight:(this.props.theme.windowDimensions.height*85)/100,
          collapseOpen:[],
          rowsItem:Object()|null,
          sendForAPI:false,
          statusAdd:false,
          statusEdit:false,
          statusShow:false,
          statusDelete:false,
          statusSubUserAdd:false,
          statusSubUserEdit:false,
          statusSubUserShow:false,
          statusSubUserDelete:false,
          refUser:Object()|null,
          showAlert:false,
          infoAlert:{ msg: '', status: undefined },
          rowsPerPage:5,
          page:0,
        }
        this.showAlertNotes=this.showAlertNotes.bind(this)
        this.handleStatus=this.handleStatus.bind(this)
        this.handleCloseModal=this.handleCloseModal.bind(this)
    }
    async componentDidMount(){
      const auth = this.props.auth;
      if (auth.user.is_admin){
        if(auth.selectUser){
          let response=await auth.getUsersAction(this.props.auth.token)
          if (response.status==200) {
            if(response.msg=="success"){
              this.setState({
                userList:await auth.users,
              })
            }
          }
          let users=await this.props.auth.users 
          Object.keys(users).map(async(user,index)=>{
              const item_id=await users[user].id
              let row={id:item_id,collapse:false}
              let collapseOpen=this.state.collapseOpen
              collapseOpen.push(row)
              this.setState({
                ...this.state.collapseOpen,
                collapseOpen:collapseOpen
              })
            }
          )
          setInterval(async () => {
            if (this.state.sendForAPI){
              console.log("sendforApi")
              this.setState({
                userList:await this.props.auth.users,
                isLoadingUserList:true,
              })
              this.shouldComponentUpdate=true
              this.forceUpdate()
            }
            this.setState({
              sendForAPI:false,
              isLoadingUserList:false,
            })
          }, 3000);
          this.setState({
            isLoadingUserList:false
          })
        }
      }
      else{
        await auth.getUsersAction(this.props.auth.token)
          this.setState({
            userList:await auth.users,
            // isLoadingUserList:false
          })
          setInterval(async () => {
            if (this.state.sendForAPI){
              console.log("sendforApi")
              this.setState({
                userList:await this.props.auth.users,
                isLoadingUserList:true,
              })
              this.shouldComponentUpdate=true
              this.forceUpdate()
            }
            this.setState({
              sendForAPI:false,
              isLoadingUserList:false,
            })
          }, 3000);
          this.setState({
            isLoadingUserList:false
          })
      }
    }
    handleCollapse(userItem){
      let flag=false
      Object.keys(this.state.collapseOpen).map((index)=>{
          if(this.state.collapseOpen[index].id==userItem.id){
            if(this.state.collapseOpen[index].collapse){
              flag=true
            }
          }
        }
      )
      return flag
    }
    showAlertNotes(){
      this.setState({
        showAlert:true
      })
      setTimeout(() => {
        this.setState({
          showAlert:false,
          infoAlert:{ msg: '', status: undefined }
        })
      }, 3000);
    }
    handleStatus(value,rowsItem,user,type,ref_user){
      if(type==='sub_user'){
        if(value===1){
          this.setState({
            statusSubUserAdd:true,
            refUser:ref_user
            // rowsItem:rowsItem
          })
        }else if(value===2){
          this.setState({
            statusSubUserEdit:true,
            rowsItem:rowsItem,
            indexRow:user,
            refUser:ref_user
          })
        }else if(value===3){
          this.setState({
            statusSubUserShow:true,
            rowsItem:rowsItem,
            indexRow:user,
            refUser:ref_user
          })
        }
        else if(value===4){
          this.setState({
            statusSubUserDelete:true,      
            rowsItem:rowsItem,
            indexRow:user
          })
        }
      }else{
        if(value===1){
          this.setState({
            statusAdd:true,
            // rowsItem:rowsItem
          })
        }else if(value===2){
          this.setState({
            statusEdit:true,
            rowsItem:rowsItem,
            indexRow:user
          })
        }else if(value===3){
          this.setState({
            statusShow:true,
            rowsItem:rowsItem,
            indexRow:user
          })
        }else if(value===4){
          this.setState({
            statusDelete:true,      
            rowsItem:rowsItem,
            indexRow:user
          })
        }
      }

    }
    handleCloseModal(){
      this.setState({
        statusAdd:false,
        statusEdit:false,
        statusShow:false,
        statusDelete:false,
        statusSubUserAdd:false,
        statusSubUserEdit:false,
        statusSubUserShow:false,
        statusSubUserDelete:false,
      })
      this.shouldComponentUpdate=true
      this.forceUpdate()
    }
    handleChangePage=(event, newPage)=>{
      this.setState({
          page:newPage,
      })
    };
    handleChangeRowsPerPage(event){
      this.setState({
          rowsPerPage:+event.target.value,
          page:0
      });
    };
  render() {
    return (
        <>
        
          {this.state.showAlert && (
            <AlertNotes 
            message={this.state.infoAlert.msg} 
            action={this.state.infoAlert.status} 
            />
          )}
          {this.state.statusAdd && (
          <UserAdd
            data={this.state.statusAdd}
            onClose={this.handleCloseModal}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
          )}
          {this.state.statusEdit && (
          <UserEdit
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
            permission={this.state.auth.user.is_admin}
            data={this.state.statusEdit}
          />
          )}
          {this.state.statusDelete && (
            <UserDelete
                data={this.state.statusDelete}
                onClose={this.handleCloseModal}
                item={this.state.rowsItem}
                showAlertNotes={this.showAlertNotes}
                setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
                refetchOrder={(sendForAPI)=>{this.setState({
                  sendForAPI:sendForAPI
                })}}
                // refetch={refetchUsers}
            />
          )}
          {this.state.statusShow && (
          <UserShow
            data={this.state.statusShow}
            item={this.state.rowsItem}
            onClose={this.handleCloseModal}
          />
          )}
          {this.state.showAlert && (
            <AlertNotes 
            message={this.state.infoAlert.msg} 
            action={this.state.infoAlert.status} 
            />
          )}
          {this.state.statusSubUserAdd && (
            <SubUserAdd
              data={this.state.statusSubUserAdd}
              onClose={this.handleCloseModal}
              showAlertNotes={this.showAlertNotes}
              setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
              refetchOrder={(sendForAPI)=>{this.setState({
                sendForAPI:sendForAPI
              })}}
              refUser={this.state.refUser}
              groups={this.state.refUser.groups}
            />
          )}
          {this.state.statusSubUserEdit && (
            <SubUserEdit
              onClose={this.handleCloseModal}
              item={this.state.rowsItem}
              showAlertNotes={this.showAlertNotes}
              setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
              refetchOrder={(sendForAPI)=>{this.setState({
                sendForAPI:sendForAPI
              })}}
              permission={this.state.auth.user.is_admin}
              data={this.state.statusSubUserEdit}
              refUser={this.state.refUser}
              groups={this.state.refUser.groups}
              // {this.props.auth.user.is_admin?(
              //   this.state.refUser.groups
              // ):(
              //   this.state.refUser.groups
              // )}
            />
          )}
          {this.state.statusSubUserDelete && (
            <UserDelete
                data={this.state.statusSubUserDelete}
                onClose={this.handleCloseModal}
                item={this.state.rowsItem}
                showAlertNotes={this.showAlertNotes}
                setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
                refetchOrder={(sendForAPI)=>{this.setState({
                  sendForAPI:sendForAPI
                })}}
                // refetch={refetchUsers}
            />
          )}
          {this.state.statusSubUserShow && (
            <SubUserShow
              data={this.state.statusSubUserShow}
              item={this.state.rowsItem}
              onClose={this.handleCloseModal}
            />
          )}
        <Box
          sx={{
            width:'100%',
            height:'100%',
            display:'block',
            // backgroundColor:"#F3F4F6"
          }}
        >
            <OrderMenu handleStatus={this.handleStatus}/>
            {this.state.isLoadingUserList ? 
            (
              <>
                <Box 
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    gap:1,
                    // textAlign:'center',
                    // alignItems:'center',
                    minHeight:this.state.tableHeight,
                    height:'100%',
                    width:'100%',
                    paddingTop:0,
                    marginTop:0,
                    backgroundColor:this.props.theme.selectTheme.background_2,
                    // paddingY:5,
                    // marginBottom:3,
                  }}
                >
                  <TableSkeleton />
                </Box>
              </>
            ):(this.props.auth.user.is_admin? 
              (this.props.auth.users && (
                <Box
                    sx={{
                    height:'100%',
                    // display:'flex',
                    // paddingLeft:5,
                    borderTop:'1px solid #e5e7eb',
                    // textAlign:'left',
                    // alignItems:'center',
                    backgroundColor:this.props.theme.selectTheme.background_3,
                    overflow:'auto'
                    }}
                >
                <Paper 
                  sx={{ 
                    width: '100%',
                    height:'100%',
                    overflow: 'hidden'
                  }}
                >
                <TableContainer 
                  sx={{ 
                    height:'100%',
                    minHeight: `${this.state.tableHeight}px` ,
                    backgroundColor:this.props.theme.selectTheme.background,
                    color: this.props.theme.selectTheme.fontColor
                  }}
                >
                  <Table stickyHeader aria-label="sticky table">
                    <TableHead sx={{
                        marginBottom:2,
                        backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                        color: this.props.theme.selectTheme.headerfontColor
                    }}>
                      <TableRow
                        sx={{
                          // backgroundColor:this.props.theme.selectTheme.headerBg,
                          // color: this.props.theme.selectTheme.headerfontColor
                        }}
                      >
                        {UserHead.map((item,index) => (
                          <TableCell
                            key={index}
                            align={item.align}
                            sx={{
                              width:`${item.style}%`,
                              border:`1px ${grey[200]} solid`,
                              textAlign:'center',
                              backgroundColor:this.props.theme.selectTheme.background,
                              color: this.props.theme.selectTheme.fontColor
                            }}
                            // style={{ 
                            //   width: `${item.style}%`,
                            //   border:`1px ${grey[200]} solid`,
                            //   // borderColor:grey[200],
                            //   textAlign:'center',
                            //  }}
                          >
                            {item.name}
                          </TableCell>
                        ))}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {this.props.auth.users.length ? (
                      // this.props.auth.users.slice(this.state.page * this.state.rowsPerPage, this.state.page * this.state.rowsPerPage + this.state.rowsPerPage)
                      Object.keys(this.props.auth.users)
                          .map((user,index) => {
                            const userItem=this.props.auth.users[index]
                              return ( 
                                <>
                                  <TableRow 
                                    hover 
                                    role="checkbox" 
                                    tabIndex={-1} 
                                    key={index}
                                    sx={{ 
                                      // marginBottom:1,
                                      // paddingBottom:1,
                                      '& > *':{ 
                                        borderBottom: 'unset' 
                                        }
                                    }}
                                  >
                                    <TableCell colSpan={1}
                                      sx={{
                                        backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                    >
                                      <IconButton
                                        aria-label="expand row"
                                        size="small"
                                        onClick={() => {
                                            if (userItem.sub_user.length!=0){  
                                              Object.keys(this.state.collapseOpen).map((index)=>{
                                                if(this.state.collapseOpen[index].id==userItem.id){
                                                  let collapse=this.state.collapseOpen
                                                  collapse[index].collapse=!this.state.collapseOpen[index].collapse
                                                  this.setState({
                                                    ...this.state.collapseOpen,
                                                    collapseOpen:collapse
                                                  })
                                                }
                                              })
                                            }
                                          }
                                        }
                                      >
                                        {Object.keys(this.state.collapseOpen).map((index)=>{
                                              if(this.state.collapseOpen[index].id==userItem.id){
                                                if(this.state.collapseOpen[index].collapse){
                                                  return(
                                                    <KeyboardArrowUpIcon /> 
                                                  )
                                                }else{
                                                  return (
                                                      <KeyboardArrowDownIcon />
                                                    )
                                                }
                                              }
                                            }
                                          )
                                        }
                                      </IconButton>
                                    </TableCell>
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.props.theme.selectTheme.background_2,
                                        color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                    > 
                                      {userItem.first_name}
                                    </TableCell>
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,                          
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.props.theme.selectTheme.background_2,
                                        color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                    >
                                    {userItem.email ?? ''}
                                    </TableCell>
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.props.theme.selectTheme.background_2,
                                        color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                    >
                                      {userItem.phone_number}
                                    </TableCell>
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.props.theme.selectTheme.background_2,
                                        color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                    >
                                      ${userItem.total_budget}
                                    </TableCell>            
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.props.theme.selectTheme.background_2,
                                        color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                    > 
                                      <Checkbox
                                          sx={{
                                            color:purple[400],
                                          }}
                                          checked={userItem.is_admin}
                                      />
                                    </TableCell>
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.props.theme.selectTheme.background_2,
                                        color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                    >
                                      {userItem.is_active?
                                        (
                                          <Typography color={green[700]}>
                                            Active
                                          </Typography>
                                        ):(
                                          <Typography color={red[600]} >
                                            Deactive
                                          </Typography>
                                        )
                                      }
                                    </TableCell>
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.props.theme.selectTheme.background_2,
                                        color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                    >
                                      {userItem.sub_user.length}
                                    </TableCell>
                                    <TableCell 
                                        sx={{
                                          display:'flex',
                                          flexDirection:'row',
                                          alignItems:'center',
                                          height:'100%',
                                          px:1,
                                          py:4,
                                          border:`1px ${grey[200]} solid`,
                                          textAlign:'center',
                                          backgroundColor:this.props.theme.selectTheme.background_2,
                                          color: this.props.theme.selectTheme.fontColor_3
                                        }}
                                      >
                                          <>
                                          <Tooltip
                                                content='add sub user'
                                            >
                                              <Button
                                                  size='medium'
                                                  pill
                                                  inline='true'
                                                  className='m-0 p-0 inline mb-2'
                                                  onClick={() => {
                                                  this.handleStatus(1,userItem,user,'sub_user',userItem)
                                                  }}
                                              >
                                                <TbPlus size={24} color={blue[600]} />
                                              </Button>
                                            </Tooltip>
                                            <Tooltip
                                                content='show user'
                                            >
                                              <Button
                                                  size='medium'
                                                  pill
                                                  inline='true'
                                                  className='m-0 p-0 inline mb-2'
                                                  onClick={() => {
                                                  this.handleStatus(3,userItem,index)
                                                  }}
                                              >
                                                <TbEyeCheck size={24} color={blue[600]} />
                                              </Button>
                                            </Tooltip>
                                            <Tooltip
                                                content='Edit Order'
                                            >
                                              <Button
                                              size='medium'
                                              sx={{
                                                  color:purple[400],
                                              }}
                                              pill
                                              onClick={() => {
                                                  this.handleStatus(2,userItem,index)
                                                }}
                                              >
                                                  <TbUserEdit size={24} color={green[600]} />
                                              </Button>
                                            </Tooltip>
                                            <Tooltip
                                            content='Delete Order'
                                            >
                                              <Button
                                                size='medium'
                                                pill
                                                inline='true'
                                                className='m-0 p-0 inline'
                                                onClick={() => {
                                                  this.handleStatus(4,userItem,index)
                                                }}
                                              >
                                                  <TbTrash size={24} color={red[600]}/>
                                              </Button>
                                            </Tooltip>
                                          </>  
                                    </TableCell>
                                  </TableRow>
                                  <TableRow
                                    sx={{
                                      marginBottom:1,
                                    }}
                                  >
                                    <TableCell style={{ paddingBottom: 0, paddingTop: 0, height:'auto'}} colSpan={9}>
                                      <Collapse 
                                        in={this.handleCollapse(userItem)}
                                        timeout="auto" 
                                        unmountOnExit
                                        >
                                        <Box
                                          sx={{
                                            // height:'100%',
                                            height:'auto',
                                            // display:'flex',
                                            // paddingLeft:5,
                                            borderTop:'1px solid #e5e7eb',
                                            // textAlign:'left',
                                            // alignItems:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_3
                                          }}
                                        >
                                          <Paper 
                                            sx={{ 
                                              width: '100%',
                                              // height:'100%',
                                              overflow: 'hidden'
                                            }}
                                          >
                                            <TableContainer 
                                              sx={{ 
                                                height:'auto',
                                                paddingBottom:4,
                                                // minHeight: `${this.state.tableHeight}px` ,
                                                backgroundColor:this.props.theme.selectTheme.background,
                                                color: this.props.theme.selectTheme.fontColor
                                              }}
                                            >
                                              <Table stickyHeader aria-label="sticky table">
                                                <TableHead 
                                                  sx={{
                                                    marginBottom:2,
                                                    backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                                                    color: this.props.theme.selectTheme.headerfontColor
                                                  }}
                                                >
                                                  <TableRow
                                                    sx={{
                                                      // backgroundColor:this.props.theme.selectTheme.headerBg,
                                                      // color: this.props.theme.selectTheme.headerfontColor
                                                    }}
                                                  >
                                                    {subUserHead.map((item,index) => (
                                                      <TableCell
                                                        key={index}
                                                        align={item.align}
                                                        sx={{
                                                          width:`${item.style}%`,
                                                          border:`1px ${grey[200]} solid`,
                                                          textAlign:'center',
                                                          backgroundColor:this.props.theme.selectTheme.background,
                                                          color: this.props.theme.selectTheme.fontColor
                                                        }}
                                                        // style={{ 
                                                        //   width: `${item.style}%`,
                                                        //   border:`1px ${grey[200]} solid`,
                                                        //   // borderColor:grey[200],
                                                        //   textAlign:'center',
                                                        //  }}
                                                      >
                                                        {item.name}
                                                      </TableCell>
                                                    ))}
                                                  </TableRow>
                                                </TableHead>
                                                <TableBody>
                                                  {Object.keys(userItem.sub_user).map((subUser,index)=>{
                                                        const subUserItem=userItem.sub_user[subUser]
                                                        return(
                                                          <>
                                                            <TableRow>
                                                              <TableCell 
                                                                sx={{
                                                                  px:1,
                                                                  py:2,
                                                                  border:`1px ${grey[200]} solid`,
                                                                  textAlign:'center',
                                                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                                                  color: this.props.theme.selectTheme.fontColor_3
                                                                }}
                                                              > 
                                                                {subUserItem.first_name}
                                                              </TableCell>
                                                              <TableCell 
                                                                sx={{
                                                                  px:1,
                                                                  py:2,                          
                                                                  border:`1px ${grey[200]} solid`,
                                                                  textAlign:'center',
                                                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                                                  color: this.props.theme.selectTheme.fontColor_3
                                                                }}
                                                              >
                                                                {subUserItem.email ?? ''}
                                                              </TableCell>
                                                              <TableCell 
                                                                sx={{
                                                                  px:1,
                                                                  py:2,
                                                                  border:`1px ${grey[200]} solid`,
                                                                  textAlign:'center',
                                                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                                                  color: this.props.theme.selectTheme.fontColor_3
                                                                }}
                                                              >
                                                                {subUserItem.phone_number}
                                                              </TableCell>
                                                              <TableCell 
                                                                sx={{
                                                                  px:1,
                                                                  py:2,
                                                                  border:`1px ${grey[200]} solid`,
                                                                  textAlign:'center',
                                                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                                                  color: this.props.theme.selectTheme.fontColor_3
                                                                }}
                                                              >
                                                                ${subUserItem.total_budget}
                                                              </TableCell>            
                                                              <TableCell 
                                                                sx={{
                                                                  px:1,
                                                                  py:2,
                                                                  border:`1px ${grey[200]} solid`,
                                                                  textAlign:'center',
                                                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                                                  color: this.props.theme.selectTheme.fontColor_3
                                                                }}
                                                              > 
                                                                <Checkbox
                                                                    sx={{
                                                                      color:purple[400],
                                                                    }}
                                                                    checked={subUserItem.is_admin}
                                                                />
                                                              </TableCell>
                                                              <TableCell 
                                                                sx={{
                                                                  px:1,
                                                                  py:2,
                                                                  border:`1px ${grey[200]} solid`,
                                                                  textAlign:'center',
                                                                  backgroundColor:this.props.theme.selectTheme.background_2,
                                                                  color: this.props.theme.selectTheme.fontColor_3
                                                                }}
                                                              >
                                                                {subUserItem.is_active?
                                                                  (
                                                                    <Typography color={green[700]}>
                                                                      Active
                                                                    </Typography>
                                                                  ):(
                                                                    <Typography color={red[600]} >
                                                                      Deactive
                                                                    </Typography>
                                                                  )
                                                                }
                                                              </TableCell>
                                                              <TableCell 
                                                                  sx={{
                                                                    display:'flex',
                                                                    flexDirection:'row',
                                                                    alignItems:'center',
                                                                    height:'100%',
                                                                    px:1,
                                                                    py:4,
                                                                    border:`1px ${grey[200]} solid`,
                                                                    textAlign:'center',
                                                                    backgroundColor:this.props.theme.selectTheme.background_2,
                                                                    color: this.props.theme.selectTheme.fontColor_3
                                                                  }}
                                                                >
                                                                    <>
                                                                      <Tooltip
                                                                          content='show user'
                                                                      >
                                                                        <Button
                                                                            size='medium'
                                                                            pill
                                                                            inline='true'
                                                                            className='m-0 p-0 inline mb-2'
                                                                            onClick={() => {
                                                                            this.handleStatus(3,subUserItem,index,'sub_user',userItem)
                                                                            }}
                                                                        >
                                                                          <TbEyeCheck size={24} color={blue[600]} />
                                                                        </Button>
                                                                      </Tooltip>
                                                                      <Tooltip
                                                                          content='Edit Order'
                                                                      >
                                                                        <Button
                                                                        size='medium'
                                                                        sx={{
                                                                            color:purple[400],
                                                                        }}
                                                                        pill
                                                                        onClick={() => {
                                                                            this.handleStatus(2,subUserItem,index,'sub_user',userItem)
                                                                          }}
                                                                        >
                                                                            <TbUserEdit size={24} color={green[600]} />
                                                                        </Button>
                                                                      </Tooltip>
                                                                      <Tooltip
                                                                      content='Delete Order'
                                                                      >
                                                                        <Button
                                                                          size='medium'
                                                                          pill
                                                                          inline='true'
                                                                          className='m-0 p-0 inline'
                                                                          onClick={() => {
                                                                            this.handleStatus(4,subUserItem,index,'sub_user',userItem)
                                                                          }}
                                                                        >
                                                                            <TbTrash size={24} color={red[600]}/>
                                                                        </Button>
                                                                      </Tooltip>
                                                                    </>  
                                                              </TableCell>
                                                            </TableRow>
                                                          </>
                                                        )
                                                      }
                                                    )
                                                  }
                                                </TableBody>
                                              </Table>
                                            </TableContainer>
                                          </Paper>
                                        </Box>
                                      </Collapse>
                                    </TableCell>
                                  </TableRow>
                                </>
                              )
                          })
                      ):(
                        <>
                          <NoDataList colSpan={UserHead.length} />                        
                        </>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
                {/* <Box 
                  sx={{
                    display:'flex',
                    justifyContent:'flex-end',
                    alignItems:'center',

                  }}
                >
                  <TablePagination
                  disabled
                    sx={{
                      width:'100%',
                      backgroundColor:this.props.theme.selectTheme.background,
                      color: this.props.theme.selectTheme.fontColor,
                      '&& .MuiTablePagination-selectLabel':{
                        margin:0
                      },
                      "&& .MuiTablePagination-displayedRows":{
                        margin:0
                      }
                    }}
                    rowsPerPageOptions={[5, 10, 25, 50, 100]}
                  //   component="div"
                    count={this.props.auth.users.length}
                    rowsPerPage={this.state.rowsPerPage}
                    page={this.state.page}
                    onPageChange={this.handleChangePage}
                    onRowsPerPageChange={(e)=>{
                      this.handleChangeRowsPerPage(e)}}
                  />
                </Box> */}
                </Paper>
                </Box>
                )
              ):(this.props.auth.users && (
                <Box
                    sx={{
                    height:'100%',
                    // display:'flex',
                    // paddingLeft:5,
                    borderTop:'1px solid #e5e7eb',
                    // textAlign:'left',
                    // alignItems:'center',
                    backgroundColor:this.props.theme.selectTheme.background_3
                    }}
                >
                <Paper 
                  sx={{ 
                    width: '100%',
                    height:'100%',
                    overflow: 'hidden'
                  }}
                >
                <TableContainer 
                  sx={{ 
                    height:'100%',
                    minHeight: `${this.state.tableHeight}px` ,
                    backgroundColor:this.props.theme.selectTheme.background,
                    color: this.props.theme.selectTheme.fontColor
                  }}
                >
                  <Table stickyHeader aria-label="sticky table">
                    <TableHead sx={{
                        marginBottom:2,
                        backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                        color: this.props.theme.selectTheme.headerfontColor
                    }}>
                      <TableRow
                        sx={{
                          // backgroundColor:this.props.theme.selectTheme.headerBg,
                          // color: this.props.theme.selectTheme.headerfontColor
                        }}
                      >
                        {subUserHead.map((item,index) => (
                          <TableCell
                            key={index}
                            align={item.align}
                            sx={{
                              width:`${item.style}%`,
                              border:`1px ${grey[200]} solid`,
                              textAlign:'center',
                              backgroundColor:this.props.theme.selectTheme.background,
                              color: this.props.theme.selectTheme.fontColor
                            }}
                            // style={{ 
                            //   width: `${item.style}%`,
                            //   border:`1px ${grey[200]} solid`,
                            //   // borderColor:grey[200],
                            //   textAlign:'center',
                            //  }}
                          >
                            {item.name}
                          </TableCell>
                        ))}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {this.props.auth.users.length ? (
                      // this.props.auth.users.slice(this.state.page * this.state.rowsPerPage, this.state.page * this.state.rowsPerPage + this.state.rowsPerPage)
                      Object.keys(this.props.auth.users)
                        .map((user,index) => {
                          const userItem=this.props.auth.users[index]
                          // console.log(this.props.auth.users[user])
                            return ( 
                              <TableRow 
                                  sx={{
                                    // backgroundColor:this.props.theme.selectTheme.background,
                                    // color: this.props.theme.selectTheme.fontColor
                                  }}
                                  hover role="checkbox" tabIndex={-1} key={index}>
                                  
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  > 
                                    {userItem.first_name}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,                          
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  >
                                  {userItem.email ?? ''}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  >
                                    {userItem.phone_number}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  >
                                    ${userItem.total_budget}
                                  </TableCell>            
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  > 
                                    <Checkbox
                                        sx={{
                                          color:purple[400],
                                        }}
                                        checked={userItem.is_admin}
                                    />
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  >
                                    {userItem.is_active?
                                      (
                                        <Typography color={green[700]}>
                                          Active
                                        </Typography>
                                      ):(
                                        <Typography color={red[600]} >
                                          Deactive
                                        </Typography>
                                      )
                                    }
                                  </TableCell>
                                  <TableCell 
                                      sx={{
                                        display:'flex',
                                        flexDirection:'row',
                                        alignItems:'center',
                                        height:'100%',
                                        px:1,
                                        py:4,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.props.theme.selectTheme.background_2,
                                        color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                    >
                                        <>
                                          <Tooltip
                                              content='show user'
                                          >
                                            <Button
                                                size='medium'
                                                pill
                                                inline='true'
                                                className='m-0 p-0 inline mb-2'
                                                onClick={() => {
                                                this.handleStatus(3,userItem,user,'sub_user',this.props.auth.user)
                                                }}
                                            >
                                              <TbEyeCheck size={24} color={blue[600]} />
                                            </Button>
                                          </Tooltip>
                                          <Tooltip
                                              content='Edit Order'
                                          >
                                            <Button
                                            size='medium'
                                            sx={{
                                                color:purple[400],
                                            }}
                                            pill
                                            onClick={() => {
                                                this.handleStatus(2,userItem,user,'sub_user',this.props.auth.user)
                                              }}
                                            >
                                                <TbUserEdit size={24} color={green[600]} />
                                            </Button>
                                          </Tooltip>
                                          <Tooltip
                                          content='Delete Order'
                                          >
                                            <Button
                                              size='medium'
                                              pill
                                              inline='true'
                                              className='m-0 p-0 inline'
                                              onClick={() => {
                                                this.handleStatus(4,userItem,user,'sub_user',this.props.auth.user)
                                              }}
                                            >
                                                <TbTrash size={24} color={red[600]}/>
                                            </Button>
                                          </Tooltip>
                                        </>  
                                    </TableCell>
                              </TableRow>
                            )
                        })
                      ):(
                          <NoDataList colSpan={subUserHead.length} />
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
                {/* <Box 
                  sx={{
                    display:'flex',
                    justifyContent:'flex-end',
                    alignItems:'center',

                  }}
                >
                  <TablePagination
                  disabled
                    sx={{
                      width:'100%',
                      backgroundColor:this.props.theme.selectTheme.background,
                      color: this.props.theme.selectTheme.fontColor,
                      '&& .MuiTablePagination-selectLabel':{
                        margin:0
                      },
                      "&& .MuiTablePagination-displayedRows":{
                        margin:0
                      }
                    }}
                    rowsPerPageOptions={[5, 10, 25, 50, 100]}
                  //   component="div"
                    count={this.props.auth.users.length}
                    rowsPerPage={this.state.rowsPerPage}
                    page={this.state.page}
                    onPageChange={this.handleChangePage}
                    onRowsPerPageChange={(e)=>{
                      this.handleChangeRowsPerPage(e)}}
                  />
                </Box> */}
                </Paper>
                </Box>
                )
              )
            )
            }
        </Box>
        </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    locationHook,
)(UserListScene)