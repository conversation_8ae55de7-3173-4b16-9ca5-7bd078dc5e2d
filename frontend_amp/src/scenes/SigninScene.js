import React,{Component} from 'react';
import { connect } from 'react-redux';
import InputLabel from '@mui/material/InputLabel';
import Loader from '../Components/Loader';
import Cookies from 'js-cookie';
import bgLogin from '../static/img/login.svg';
import logo from '../static/img/logo.png';
import { Link,Card,Checkbox,Alert,TextField, Box, Typography, AlertTitle} from '@mui/material';
import { styled } from '@mui/material/styles';
import InfoIcon from '@mui/icons-material/Info';
import ImageIcon from '@mui/icons-material/Image';
import IconButton from '@mui/material/IconButton';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import { purple} from '@mui/material/colors';
import Button from '@mui/joy/Button';
import { CircularProgress } from '@mui/joy';
import '../styles/SigninStyles.scss'
// import {
//     signInRequest,
//   } from '../actions';
import { error } from 'jquery';
import { useNavigate } from 'react-router-dom';
import navigateHook from '../HOK/NavigateHook';
import { compose } from 'redux';
import authHook from '../HOK/AuthHook';
import themeHook from '../HOK/ThemeHook';
import dashboardHook from '../HOK/DashboardHook';
// import ImageIcon from '@mui/icons-material/Image';
// const [email, setEmail] = useState('');
//     const [password, setPassword] = useState('');
//     const [error, setError] = useState<string | null>(null);

//     // ******** 1! signin validation ************
//     const handleSignIn = async () => {
//         // Clear previous error message
//         setError(null);

//         // Attempt to sign in with email and password
//         const result = await signIn('credentials', {
//             email,
//             password,
//             redirect: false, // Prevent automatic redirection
//         });

//         if (result.error) {
//             // Handle authentication error
//             setError(result.error);
//         } else {
//             // Successful login; you can redirect the user to another page here
//             router.push('/order');
//         }
//     };
// const HOC = (props) => {
//     useEffect(() =>{Report(calllName)}, [])
//     return <WrappedComponent {...props} />
// }6C2BD9
const ColorButton = styled(Button)(({ theme }) => ({
    color: theme.palette.getContrastText(purple[700]),
    backgroundColor: '#6C2BD9',
    '&:hover': {
      backgroundColor: purple[900],
    },
  }));
class SigninScene extends Component{
    constructor(props) {
        super(props);
        this.state={
            email:'',
            password:'',
            showPassword:false,
            status:'unauthenticated',
            error:'',
            isLoading:false,
        };
        // this.handleSignIn.bind(this.handleSignIn)
    }
    componentDidMount(){
        this.props.theme.setWindowDimensions({width: window.innerWidth,height: window.innerHeight});
        
        // Check for tokenExpired parameter
        const urlParams = new URLSearchParams(window.location.search);
        const tokenExpired = urlParams.get('tokenExpired');
        
        if (tokenExpired === 'true') {
            this.setState({
                error: 'Your session has expired. Please sign in again.'
            });
            
            // Clean up the URL
            if (window.history && window.history.replaceState) {
                window.history.replaceState({}, document.title, '/signin');
            }
        }
    }
    handleShowPassword(arg){
        this.setState({
            showPassword:arg,
        })
    }
    async handleSignIn(){
        this.setState({
            isLoading: true,
            error: '' // Clear any previous errors
        });
        
        try {
            const navigate = this.props.navigate;
            const data = {
                username: this.state.email,
                password: this.state.password
            };
            
            let response = await this.props.auth.loginAction(data);
            
            if (response.status !== 200) {
                this.setState({
                    error: typeof response.msg === 'string' ? response.msg : 'Login failed. Please try again.',
                    isLoading: false,
                });
            } else {
                if (response.msg === 'success') {
                    this.setState({
                        status: 'loading',
                        sendForAPI: true,
                    });
                    
                    try {
                        let responseUserInfo = await this.props.auth.getUserInfoAction(this.props.auth.token);
                        
                        if (responseUserInfo.status !== 200) {
                            this.setState({
                                error: 'Failed to get user information',
                                isLoading: false,
                                status: 'unauthenticated'
                            });
                        } else {
                            // Successfully got user info, navigate to order page
                            this.setState({
                                isLoading: false,
                                sendForAPI: false,
                            });
                            
                            // Use a timeout to ensure state is updated before navigation
                            setTimeout(() => {
                                navigate('/order');
                            }, 100);
                            
                            return; // Exit early to prevent double navigation
                        }
                    } catch (err) {
                        this.setState({
                            error: 'Network error. Please check your connection.',
                            isLoading: false,
                            status: 'unauthenticated'
                        });
                    }
                } else {
                    this.setState({
                        isLoading: false,
                        error: 'Login failed. Please try again.'
                    });
                }
            }
        } catch (err) {
            this.setState({
                error: 'Network error. Please check your connection.',
                isLoading: false,
                status: 'unauthenticated'
            });
        }
    }
    render(){
        return(
            <section className=''>
                <Box
                    sx={{  width: '100%',
                    height: '100%',
                    display: 'grid',
                    marginTop:10,
                    marginBottom:10,
                    px:8,
                    marginX:'auto',
                    justifyItems:'center',
                    alignContent:'center',
                    textAlign:'center',
                    }}
                >
                <Card
                    sx={{
                        width:'65%',
                        padding:4,
                        textAlign:'center',
                        border:'gray',
                        shadow:true
                    }}
                >
                    {this.state.status === 'loading' ? (
                        <>
                            <Box
                                sx={{
                                    display:'flex',
                                    
                                    alignItems:'center',
                                    justifyContent:'center'
                                }}
                                className='flex items-center justify-between p-1 space-y-1 divide-y divide-gray-200 rounded animate-pulse dark:divide-gray-700'
                                role='status'
                            >
                                <div className='flex-1'>
                                    <div className='flex items-center justify-left p-1 space-y-1 divide-y divide-gray-200 rounded animate-pulse dark:divide-gray-700'>
                                        <ImageIcon className='w-20 h-10 text-gray-200 dark:text-gray-600' />
                                        <span className='w-[200px] h-4 bg-gray-300 rounded-lg dark:bg-gray-600'></span>
                                    </div>
                                    <div className='w-[30%] h-3 mb-1 mt-8 bg-gray-300 rounded-full dark:bg-gray-600' />
                                    <div className='w-full h-10 mb-3 bg-gray-300 rounded-xl dark:bg-gray-600' />
                                    <div className='w-[30%] h-3 mb-1 mt-8 bg-gray-300 rounded-full dark:bg-gray-600' />
                                    <div className='w-full h-10 mb-3 bg-gray-300 rounded-xl dark:bg-gray-600' />
                                    <div className='flex mt-10 items-center justify-between'>
                                        <div className='flex-1 h-10 mb-2 bg-gray-300 rounded-xl dark:bg-gray-600' />
                                    </div>
                                </div>

                                <div className='flex-1'>
                                    <ImageIcon className='w-[100%] h-[200px] text-gray-200 dark:text-gray-600' />
                                </div>
                            </Box>
                        </>
                    ) 
                    : this.state.status === 'unauthenticated' ? (
                        <>
                        <Box 
                            sx={{  
                                width: '100%',
                                height: '100%',
                                display: 'flex',
                                // flexDirection: 'column-reverse',
                                // position:'fixed',
                                // bottom:0,
                                padding:3,
                                justifyItems:'center',
                                alignContent:'center',
                                textAlign:'center',
                            }}
                        >
                            <Box 
                                sx={{  
                                    width: '100%',
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection:'column',
                                    textAlign:'left'
                                }}
                            >
                                <Typography 
                                    sx={{marginBottom:3}}
                                    variant="h5" color="text.primary">
                                    <Link
                                        underline="none"
                                        href='/'
                                        className='flex text-center items-center mb-6 text-2xl font-semibold text-gray-900 dark:text-white m-auto'
                                    >
                                        <img 
                                            className='w-10 h-8 mr-2' src={logo} alt='logo'
                                            style={{width:40,height:32}}
                                            />
                                        <span>A Medical Partner</span>
                                        </Link>
                                </Typography>
                                {this.state.error && (
                                    <Alert 
                                        variant="filled" 
                                        severity="error"
                                        onClose={() => {
                                            this.setState({
                                                error: ""
                                            })
                                        }}
                                    >
                                        <AlertTitle>
                                            Info alert!
                                        </AlertTitle>
                                        {typeof this.state.error === 'string' ? this.state.error : 'An error occurred. Please try again.'}
                                    </Alert>          
                                )}
                                <Box
                                    sx={{
                                        width: '100%',
                                        height: '100%',
                                        display: 'flex',
                                        flexDirection:'column',
                                        justifyContent:'space-between',
                                        textAlign:'left',
                                        gap:4,
                                    }}
                                >
                                    <Box
                                        sx={{
                                        width: '100%',
                                        display:'flex',
                                        flexDirection:'column', 
                                        marginTop:2,
                                        gap:4                                          
                                        }}
                                    >
                                        <TextField
                                            fullWidth
                                            // shrink
                                            size='medium'
                                            id='email1'
                                            label="Email or Username"
                                            placeholder='<EMAIL>'
                                            required
                                            type='email'
                                            value={this.state.email}
                                            onChange={(e) =>
                                                this.setState({
                                                    email:e.target.value
                                                })
                                            }
                                        />
                                        <Box 
                                            // height='60%'
                                            sx={{ 
                                            display: 'flex',
                                            position: 'relative' 
                                            }}
                                        >
                                            <TextField 
                                                required
                                                // shrink
                                                size='medium'
                                                id='password1'
                                                className='!focus:border-purple-700'
                                                label="Password" 
                                                placeholder='******'
                                                type={this.state.showPassword ? 'text' : 'password'}
                                                fullWidth
                                                value={this.state.password}
                                                onChange={(e) =>
                                                    this.setState({
                                                        password:e.target.value
                                                    })
                                                }
                                            />
                                            <IconButton
                                                onClick={() => this.handleShowPassword(!this.state.showPassword)} 
                                                sx={{ position: 'absolute',height:'100%',width:'15%',right: 0, top: 0 ,boxShadow:'none' }}>
                                                {this.state.showPassword ? <VisibilityOff /> : <Visibility />}
                                            </IconButton>
                                        </Box>
                                    </Box>
                                    {this.state.isLoading?(
                                        <>
                                        <Button 
                                            // color={'primary'}
                                            sx={{
                                                backgroundColor:purple[400],
                                                marginRight:1
                                                // marginTop:1
                                            }}
                                            startDecorator={
                                                <CircularProgress variant="solid"/>
                                            }
                                        >
                                            Loading…
                                        </Button>
                                        </>
                                    ):(
                                        <ColorButton 
                                        variant="contained"
                                        className='mt-4'
                                        onClick={() =>this.handleSignIn()}   
                                        >
                                            Sign In
                                        </ColorButton>
                                    )}
                                       
                                </Box>
                            </Box>
                            <Box 
                                sx={{  width: '100%',
                                height: '100%',
                                display: 'flex',
                                }}
                            >    
                                <Box
                                    sx={{
                                        backgroundImage: `url(${bgLogin})`,
                                        backgroundRepeat: "no-repeat",
                                        backgroundSize: "cover",
                                        height: "385px",
                                        width:'100%'
                                    }}
                                >
                                </Box>

                            </Box>
                        </Box>

                        </>
                    ) : (
                        <Loader />
                    )}
                </Card>

                </Box>
            {/* </div> */}
        </section>   
        );
    }
}
const mapStateToProps=({signin})=>{
    return{
        result: signin.result,
        status: signin.status,
        error: signin.error,
        loading:signin.loading
    }
};
const enhance = compose(
    navigateHook,
    authHook,
    themeHook,
    dashboardHook
    // connect(mapStateToProps,{signInRequest})
  )
export default enhance(SigninScene)
// export default connect(mapStateToProps,{
//     signInRequest
// })(navigateHook(SigninScene));
