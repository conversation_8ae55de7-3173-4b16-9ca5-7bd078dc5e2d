import React, { Component } from 'react'

export default class SupplyHubScene extends Component {
  constructor(props) {
    super(props)
    this.state = {
      isSupplyHubRoute: false,
      scriptLoaded: false,
      error: null
    }
  }

  componentDidMount() {
    console.log("SupplyHubScene mounted, path:", window.location.pathname);
    
    // Always load the script for testing purposes
    this.loadEcwidScript();
    
    // Set state to indicate we're on the supply hub route (for testing)
    this.setState({ isSupplyHubRoute: true });
  }
  
  loadEcwidScript() {
    console.log("Attempting to load Ecwid script");
    
    try {
      // Create and load the Ecwid script
      const script = document.createElement('script');
      script.src = 'https://app.ecwid.com/script.js?31447282&data_platform=code&data_date=2025-06-03';
      script.setAttribute('data-cfasync', 'false');
      script.type = 'text/javascript';
      script.charset = 'utf-8';
      script.async = true;
      
      // Add error handling
      script.onerror = (error) => {
        console.error("Error loading Ecwid script:", error);
        this.setState({ error: "Failed to load Ecwid script", scriptLoaded: false });
      };
      
      // Initialize the product browser after the script loads
      script.onload = () => {
        console.log("Ecwid script loaded successfully");
        this.setState({ scriptLoaded: true });
        
        // Wait a moment to ensure Ecwid is fully initialized
        setTimeout(() => {
          if (window.xProductBrowser) {
            console.log("Initializing xProductBrowser");
            try {
              window.xProductBrowser(
                "categoriesPerRow=3",
                "views=grid(20,3) list(60) table(60)",
                "categoryView=grid",
                "searchView=list",
                "id=my-store-31447282"
              );
              console.log("xProductBrowser initialized successfully");
            } catch (error) {
              console.error("Error initializing xProductBrowser:", error);
              this.setState({ error: "Failed to initialize product browser" });
            }
          } else {
            console.error("xProductBrowser function not found");
            this.setState({ error: "xProductBrowser function not available" });
          }
        }, 1000);
      };
      
      // Add the script to the document
      document.body.appendChild(script);
      console.log("Ecwid script added to document");
      
      // Store the script reference for cleanup
      this.ecwidScript = script;
    } catch (error) {
      console.error("Exception while loading Ecwid script:", error);
      this.setState({ error: "Exception occurred: " + error.message });
    }
  }
  
  componentWillUnmount() {
    console.log("SupplyHubScene unmounting");
    // Clean up the script when component unmounts
    if (this.ecwidScript && document.body.contains(this.ecwidScript)) {
      document.body.removeChild(this.ecwidScript);
      console.log("Ecwid script removed");
    }
  }

  render() {
    const { scriptLoaded, error } = this.state;
    
    return (
      <div className="supply-hub-container">
        <h1>Supply Hub</h1>
        <p>Welcome to our Supply Hub. Browse and order supplies directly from our store.</p>
        
        {/* Debug information */}
        {/* <div style={{ margin: '10px 0', padding: '10px', backgroundColor: '#f8f9fa', border: '1px solid #ddd' }}>
          <p><strong>Debug Info:</strong></p>
          <p>Script loaded: {scriptLoaded ? 'Yes' : 'No'}</p>
          {error && <p style={{ color: 'red' }}>Error: {error}</p>}
          <p>Current path: {window.location.pathname}</p>
        </div> */}
        
        {/* Ecwid store container */}
        <div id="my-store-31447282"></div>
      </div>
    )
  }
}
