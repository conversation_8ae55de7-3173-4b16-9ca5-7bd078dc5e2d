
import React, { Component } from 'react'
import { Box, Paper, Skeleton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tooltip } from '@mui/material'
import Header from '../Components/Header'
import SideBar from '../Components/SideBar'
import AlertUser from '../Components/AlertUser'
import OrderMenu from '../Components/OrderMenu'
import { compose } from 'redux'
import themeHook from '../HOK/ThemeHook'
import authHook from '../HOK/AuthHook'
import locationHook from '../HOK/LoctionHook'
import dashboardHook from '../HOK/DashboardHook'
import { green, grey, red } from '@mui/material/colors'
import NoDataList from '../Components/NoDataList'
import { formatDate } from '../utils/functions'
import { TbRefresh, TbTrash } from 'react-icons/tb'
import TableSkeleton from '../Components/TableSkeleton'
import MyProductRemove from '../Components/myproduct/MyProductRemove'
import AlertNotes from '../Components/AlertNotes'
import Button from '@mui/joy/Button';
import InventoryReorder from '../Components/inventory/InventoryReorder'

const head = [
  { name: 'Date', style: '8' },
  { name: 'Product', style: '40' },
  { name: 'SKU(Manufacturer Code)', style: '40' },
  { name: 'Action', style: '8' },
];
class MyProcductScene extends Component {
    constructor(props){
        super(props)
        this.state={
          theme:props.theme,
          auth:props.auth,
          dashboard:props.dashboard,
          tableHeight:(this.props.theme.windowDimensions.height*85)/100,
          ordersTableData:Object(null),
          isLoadingOrders:true,
          sendForAPI:false,
          statusEdit:false,
          statusOrderAdd:false,
          showAlert:false,
          rowsItem:Object()|null,
          infoAlert:{ msg: '', status: undefined },
        }
        this.showAlertNotes=this.showAlertNotes.bind(this)
        this.handleStatus=this.handleStatus.bind(this)
        this.handleCloseModal=this.handleCloseModal.bind(this)
    }
    async componentDidMount(){
      this.setState({
        theme:this.props.theme,
        auth:this.props.auth,
        dashboard:this.props.dashboard,
        ordersTableData:this.props.dashboard.ordersTableData
      })
      if (this.props.auth.user.is_admin){
        if(this.props.auth.selectUser){
          let response=await this.props.dashboard.getOrders(this.props.auth.token,this.props.auth.selectUser.id)
          if (response.status==200) {
            if(response.msg=="success"){
              this.setState({
                ordersTableData:await this.props.dashboard.ordersTableData,
              })
            }
          }
          this.setState({
            isLoadingOrders:false,
          })
        }
      }
      else{
        let user_id=0
        if(this.props.auth.user.user_group==3){
          user_id=this.props.auth.user.id
        }else if(this.props.auth.user.user_group==4){
          user_id=this.props.auth.user.ref_admin.id
        }
        let response=await this.props.dashboard.getOrders(this.props.auth.token,user_id)
        if (response.status==200) {
          if(response.msg=="success"){
            this.setState({
              ordersTableData:await this.props.dashboard.ordersTableData,
            })
          }
        }
        this.setState({
          isLoadingOrders:false,
        })
      }
      setInterval(async () => {
        if (this.state.sendForAPI){
          console.log("sendforApi")
          this.setState({
            ordersTableData:await this.props.dashboard.ordersTableData,
            isLoadingOrders:true,
          })
          this.shouldComponentUpdate=true
          this.forceUpdate()
        }
        this.setState({
          sendForAPI:false,
          isLoadingOrders:false,
        })
      }, 3000);
      this.shouldComponentUpdate=true
      this.forceUpdate()
    }
    showAlertNotes(){
      this.setState({
        showAlert:true
      })
      setTimeout(() => {
        this.setState({
          showAlert:false,
          infoAlert:{ msg: '', status: undefined }
        })
      }, 3000);
    }
    handleStatus(value,rowsItem,order){
      if(value===3){
        this.setState({
          statusOrderAdd:true,
          rowsItem:rowsItem
        })
      }
      if(value===4){
        this.setState({
          statusDelete:true,
          rowsItem:rowsItem
        })
      }
    }
    handleCloseModal(){
      this.setState({
        statusOrderAdd:false,
        statusDelete:false,
      })
      this.shouldComponentUpdate=true
      this.forceUpdate()
    }
  render() {
    let controller=0
    return (
      <>
        {this.state.showAlert && (
          <AlertNotes
              message={this.state.infoAlert.msg}
              action={this.state.infoAlert.status}
          />
        )}
        {this.state.statusDelete && (
          <MyProductRemove
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
        {this.state.statusOrderAdd && (
          <InventoryReorder
            item={this.state.rowsItem}
            onClose={this.handleCloseModal}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
              // onFormInsert={this.handleReorderInventory}
          />
        )}
        <Box
            sx={{
              width:'100%',
              height:'100%',
              display:'block',
              // backgroundColor:"#F3F4F6"
            }}
          >
            <OrderMenu handleStatus={this.handleStatus} />
            {this.state.isLoadingOrders ? (
                  <>
                    <Box 
                      sx={{
                        display:'flex',
                        flexDirection:'row',
                        gap:1,
                        // textAlign:'center',
                        // alignItems:'center',
                        minHeight:this.state.tableHeight,
                        height:'100%',
                        width:'100%',
                        paddingTop:0,
                        marginTop:0,
                        backgroundColor:this.props.theme.selectTheme.background_2,
                        // paddingY:5,
                        // marginBottom:3,
                      }}
                    >
                      <TableSkeleton />
                    </Box>
                  </>
                ) : (this.props.dashboard.ordersTableData&&(
                  <Box
                  sx={{
                    height:'100%',
                    // display:'flex',
                    // paddingLeft:5,
                    borderTop:'1px solid #e5e7eb',
                    // textAlign:'left',
                    // alignItems:'center',
                    backgroundColor:this.props.theme.selectTheme.background_3
                  }}
                >
                  <Paper 
                    sx={{ 
                      width: '100%',
                      height:'100%',
                      overflow: 'hidden'
                    }}
                  >
                    <TableContainer 
                      sx={{ 
                        height:'100%',
                        minHeight: `${this.state.tableHeight}px` ,
                        backgroundColor:this.props.theme.selectTheme.background,
                        color: this.props.theme.selectTheme.fontColor
                      }}
                    >
                      <Table stickyHeader aria-label="sticky table">
                        <TableHead sx={{
                            marginBottom:2,
                            backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                            color: this.props.theme.selectTheme.headerfontColor
                        }}>
                          <TableRow
                            sx={{
                              // backgroundColor:this.props.theme.selectTheme.headerBg,
                              // color: this.props.theme.selectTheme.headerfontColor
                            }}
                          >
                            {head.map((item,index) => (
                              <TableCell
                                key={index}
                                align={item.align}
                                sx={{
                                  width:`${item.style}%`,
                                  border:`1px ${grey[200]} solid`,
                                  textAlign:'center',
                                  backgroundColor:this.props.theme.selectTheme.background,
                                  color: this.props.theme.selectTheme.fontColor
                                }}
                              >
                                {item.name}
                              </TableCell>
                            ))}
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {this.props.dashboard.ordersTableData.length ? (
                          // rows.slice(this.state.page * this.state.rowsPerPage, this.state.page * this.state.rowsPerPage + this.state.rowsPerPage)
                          Object.keys(this.props.dashboard.ordersTableData)
                            .map((order,index) => {
                              let item=this.props.dashboard.ordersTableData[order]
                              if (item.my_product){
                                controller=controller+1
                                return (
                                  <>
                                    {item.my_product &&
                                    (
                                      <TableRow 
                                        sx={{
                                          // backgroundColor:this.props.theme.selectTheme.background,
                                          // color: this.props.theme.selectTheme.fontColor
                                        }}
                                        hover role="checkbox" tabIndex={-1} key={index}>
                                        <TableCell 
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                          // 'px-1 border border-gray-200 dark:border-gray-600 text-center py-2';
                                          key={order.id}>
                                          {item.created_at
                                            ? formatDate(
                                                  'Amm-dd-yy',
                                                  item.created_at
                                              )
                                            : formatDate('Amm-dd-yy')}
                                        </TableCell>
                                        <TableCell 
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                          // 'px-1 border border-gray-200 dark:border-gray-600 text-center py-2';
                                          key={order.id}>
                                          {item.product.name}
                                        </TableCell>
                                        <TableCell 
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                          // 'px-1 border border-gray-200 dark:border-gray-600 text-center py-2';
                                          key={order.id}>
                                          {item.product.s_k_u}
                                        </TableCell>
                                        <TableCell
                                          sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                          }}
                                          // 'px-1 border border-gray-200 dark:border-gray-600 text-center py-2';
                                          key={order.id}
                                        >
                                          <Tooltip
                                            title="Delete My product "
                                            content='Delete Order'
                                            // style='light'
                                          >
                                            <Button
                                              // color='gray'
                                              size='xs'
                                              pill
                                              inline='true'
                                              color='white'
                                              onClick={() => {
                                                this.handleStatus(4,item)
                                              }}
                                              // onClick={() => {
                                              //   this.setState({
                                              //     statusDelete:true,
                                              //     rowsItem:order
                                              //   })
                                              // }}
                                            >
                                              <TbTrash color={red[600]} />
                                            </Button>
                                          </Tooltip>
                                          <Tooltip
                                                title="ReOrder"
                                                content='Reorder'
                                            >
                                                <Button
                                                  size='xs'
                                                  pill
                                                  color='white'
                                                  inline='true'
                                                  onClick={() => {
                                                    this.handleStatus(3,item)
                                                    // this.handleStatus(3,this.props.dashboard.inventoriesTableData[inventory])
                                                  }}
                                                >
                                                    <TbRefresh  color={green[600]} />
                                                </Button>
                                            </Tooltip>
                                        </TableCell>
                                      </TableRow>
                                    )}
                                  </>
                                )
                              }
                              if(index==this.props.dashboard.ordersTableData.length-1 && controller==0){  
                                return(
                                  <NoDataList colSpan={head.length} />
                                )
                              }
                            })
                          ):(
                            <NoDataList colSpan={head.length} />
                        )}
                      </TableBody>
                    </Table>
                    </TableContainer>
                  </Paper>
                  </Box>
                  )
                )
              }
        </Box>        
      </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    locationHook,
    dashboardHook
  )(MyProcductScene)