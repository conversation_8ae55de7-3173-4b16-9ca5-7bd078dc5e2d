import React, { Component } from 'react'

import dashboardHook from '../HOK/DashboardHook';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TablePagination from '@mui/material/TablePagination';
import TableRow from '@mui/material/TableRow';
import { Box, Button, Checkbox, Skeleton, Tooltip } from '@mui/material';
import { formatDate, orderStatusFn } from '../utils/functions';
import { grey, purple } from '@mui/material/colors';
import { TbTrash } from 'react-icons/tb';
import { compose } from 'redux';
import authHook from '../HOK/AuthHook';
import themeHook from '../HOK/ThemeHook';
import locationHook from '../HOK/LoctionHook';
import FilterDate from '../Components/FilterDate';
import SearchBox from '../Components/SearchBox';
import AlertNotes from '../Components/AlertNotes';
import OrderMenu from '../Components/OrderMenu';
import OrderDelete from '../Components/order/OrderDelete';
import NoDataList from '../Components/NoDataList';
import TableSkeleton from '../Components/TableSkeleton';
const head = [
  { name: 'Date', style: '8' },
  { name: 'Product', style: '25' },
  { name: 'SKU(Manufacturer Code)', style: '10' },
  { name: 'Note', style: '20' },
  { name: 'QTY', style: '6' },
  { name: 'Specific', style: '4' },
  { name: 'Vendor', style: '7' },
  { name: 'Urgent', style: '4' },
  // { name: 'Exp. Delivery', style: '10' },
  { name: 'Status', style: '8' },
  { name: 'Received', style: '4' },
  { name: 'Action', style: '8' },
];

class ReturnScene extends Component {
    constructor(props){
        super(props)
        this.state={
            theme:props.theme,
            auth:props.auth,
            dashboard:props.dashboard,
            ordersTableData:Object(null),
            rowsItem:Object()|null,
            sendForAPI:false,
            indexRow:0,
            statusDelete:false,
            showAlert:false,
            isLoadingOrders:true,
            infoAlert:{ msg: '', status: undefined },
            tableHeight:(this.props.theme.windowDimensions.height*85)/100,
        }
        this.showAlertNotes=this.showAlertNotes.bind(this)
        this.handleStatus=this.handleStatus.bind(this)
        this.handleCloseModal=this.handleCloseModal.bind(this)
    }
    async componentDidMount(){
      this.setState({
        theme:this.props.theme,
        auth:this.props.auth,
        dashboard:this.props.dashboard,
      })
      if (this.props.auth.user.is_admin){
        if(this.props.auth.selectUser){
          let response=await this.props.dashboard.getOrders(this.props.auth.token,this.props.auth.selectUser.id)
          if (response.status==200) {
            if(response.msg=="success"){
                this.setState({
                  ordersTableData:await this.props.dashboard.ordersTableData,
                })
            }
          }
          this.setState({
            isLoadingOrders:false,
          })
        }
      }
      else{
        let user_id=0
        if(this.props.auth.user.user_group==3){
          user_id=this.props.auth.user.id
        }else if(this.props.auth.user.user_group==4){
          user_id=this.props.auth.user.ref_admin.id
        }
        let response=await this.props.dashboard.getOrders(this.props.auth.token,user_id)
        if (response.status==200) {
          if(response.msg=="success"){
            this.setState({
              ordersTableData:await this.props.dashboard.ordersTableData,
              isLoadingOrders:false,
            })
          }
        }
        this.setState({
          isLoadingOrders:false,
        })
      }
      setInterval(async () => {
        if (this.state.sendForAPI){
          console.log("sendforApi")
          this.setState({
            ordersTableData:await this.props.dashboard.ordersTableData,
            isLoadingOrders:true,
          })
          this.shouldComponentUpdate=true
          this.forceUpdate()
        }
        this.setState({
          sendForAPI:false,
          isLoadingOrders:false,
        })
      }, 3000);
      this.shouldComponentUpdate=true
      this.forceUpdate()
    }
    // async handleDeleteOrder(orderData){
    //   await this.state.dashboard.deleteOrder(this.state.auth.token,orderData)
    //   let response=await this.state.dashboard.deleteOrderStatus
    //   // console.log(response)
    //   if (response.status!==200) {
    //       this.setState({
    //         infoAlert:{
    //           msg: `Error deleteing order: ${response.msg}`,
    //           status: false,
    //         }
    //       })
    //       this.showAlertNotes();
    //   } else {
    //       if (response.msg === 'success') {
    //           this.setState({
    //             ordersTableData:this.state.ordersTableData.filter(a =>
    //               a.id !== orderData.id
    //             ),
    //             infoAlert:{
    //               msg: 'Order deleted successfully',
    //               status: true,
    //             }
    //           })
    //           this.showAlertNotes();
    //           this.handleCloseModal();
    //       } else {
    //         this.setState({
    //           infoAlert:{
    //               msg: `Failed to delete Order: ${response.msg}`,
    //               status: false,
    //             }
    //         })
    //         this.showAlertNotes();
    //       }
    //   }
    // }
    showAlertNotes(){
      this.setState({
        showAlert:true
      })
      setTimeout(() => {
        this.setState({
          showAlert:false,
          infoAlert:{ msg: '', status: undefined }
        })
      }, 3000);
    }
    handleStatus(value,rowsItem,order){
      // console.log(value,rowsItem,order)
      if(value===4){
        this.setState({
          statusDelete:true,      
          rowsItem:rowsItem,
          indexRow:order
        })
      }
    }
    handleCloseModal(){
      this.setState({
        statusDelete:false,
      })
      this.shouldComponentUpdate=true
      this.forceUpdate()
    }
  render() {
    let controller=0;
    const loderClass =
        'divide-y divide-gray-200 rounded-xl animate-pulse dark:divide-gray-700 bg-gray-300 rounded-2xl';
    return (
        <>
          {this.state.showAlert && (
          <AlertNotes
              message={this.state.infoAlert.msg}
              action={this.state.infoAlert.status}
          />
        )}
          {this.state.statusDelete && (
            <OrderDelete
                onClose={this.handleCloseModal}
                item={this.state.rowsItem}
                showAlertNotes={this.showAlertNotes}
                setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
                refetchOrder={(sendForAPI)=>{this.setState({
                  sendForAPI:sendForAPI
                })}}
            />
        )}
          <Box
            sx={{
              width:'100%',
              height:'100%',
              display:'block',
              // backgroundColor:"#F3F4F6"
            }}
          >
            <OrderMenu />
            {this.state.isLoadingOrders ? (
                <>
                  <Box 
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    gap:1,
                    // textAlign:'center',
                    // alignItems:'center',
                    minHeight:this.state.tableHeight,
                    height:'100%',
                    width:'100%',
                    paddingTop:0,
                    marginTop:0,
                    backgroundColor:this.props.theme.selectTheme.background_2,
                    // paddingY:5,
                    // marginBottom:3,
                  }}
                >
                  <TableSkeleton />
                </Box>
                </>
              ) : (this.props.dashboard.ordersTableData&&(
                <Box
                  sx={{
                    height:'100%',
                    // display:'flex',
                    // paddingLeft:5,
                    borderTop:'1px solid #e5e7eb',
                    // textAlign:'left',
                    // alignItems:'center',
                    backgroundColor:this.props.theme.selectTheme.background_3
                  }}
                >
                  <Paper 
                    sx={{ 
                      width: '100%',
                      height:'100%',
                      overflow: 'hidden'
                    }}
                  >
                  <TableContainer 
                    sx={{ 
                      height:'100%',
                      minHeight: `${this.state.tableHeight}px` ,
                      backgroundColor:this.props.theme.selectTheme.background,
                      color: this.props.theme.selectTheme.fontColor
                    }}
                  >
                    <Table stickyHeader aria-label="sticky table">
                      <TableHead sx={{
                          marginBottom:2,
                          backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                          color: this.props.theme.selectTheme.headerfontColor
                      }}>
                        <TableRow
                          sx={{
                            // backgroundColor:this.props.theme.selectTheme.headerBg,
                            // color: this.props.theme.selectTheme.headerfontColor
                          }}
                        >
                          {head.map((item,index) => (
                            this.state.auth.user.is_admin?
                            (
                                <TableCell
                                  key={index}
                                  align={item.align}
                                  sx={{
                                    width:`${item.style}%`,
                                    border:`1px ${grey[200]} solid`,
                                    textAlign:'center',
                                    backgroundColor:this.props.theme.selectTheme.background,
                                    color: this.props.theme.selectTheme.fontColor
                                  }}
                                >
                                  {item.name}
                                </TableCell>
                            ):(item.name=='Action' ?(
                                <>
                                </>
                              ):(
                                <TableCell
                                  key={index}
                                  align={item.align}
                                  sx={{
                                    width:`${item.style}%`,
                                    border:`1px ${grey[200]} solid`,
                                    textAlign:'center',
                                    backgroundColor:this.props.theme.selectTheme.background,
                                    color: this.props.theme.selectTheme.fontColor
                                  }}
                                >
                                  {item.name}
                                </TableCell>
                            ))
                          ))}
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {this.props.dashboard.ordersTableData.length ? (
                        // rows.slice(this.state.page * this.state.rowsPerPage, this.state.page * this.state.rowsPerPage + this.state.rowsPerPage)
                        Object.keys(this.props.dashboard.ordersTableData)
                          .map((order,index) => {
                            let item=this.props.dashboard.ordersTableData[order]
                            console.log(item.status)
                            if (item.received && item.status=='returned'){
                              controller=controller+1
                            return (
                              <>
                                <TableRow 
                                  sx={{
                                    // backgroundColor:this.props.theme.selectTheme.background,
                                    // color: this.props.theme.selectTheme.fontColor
                                  }}
                                  hover role="checkbox" tabIndex={-1} key={index}>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    // 'px-1 border border-gray-200 dark:border-gray-600 text-center py-2';
                                    key={order.id}>
                                    {item.created_at
                                      ? formatDate(
                                            'Amm-dd-yyyy',
                                            item.created_at
                                        )
                                      : formatDate('Amm-dd-yyyy')}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  >
                                    {item.product? item.product.name: ''}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  >
                                    {item.product? item.product.s_k_u: ''}
                                  </TableCell>
                                  {/* Add Note column */}
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  >
                                    {item.note || ''}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,                          
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  >
                                  {item.qty ?? ''}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  >
                                    <Checkbox
                                      sx={{
                                        color:purple[400],
                                      }}
                                      checked={item.specific}
                                      // disabled
                                      />
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  >
                                    {item.vendor
                                        ? item.vendor.name
                                        : ''}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  >
                                    <Checkbox
                                      sx={{
                                        color:purple[400],
                                      }}
                                      checked={
                                        item.urgent === true
                                        }
                                      />
                                  </TableCell>            
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  >
                                      {
                                        orderStatusFn(item.status).name
                                        // if(this.state.order.initialOrderStatus)
                                          // orderStatusFn(this.state.ordersTableData[order].status)
                                          //     .name
                                      }
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                  >
                                    <Checkbox
                                        sx={{
                                          color:purple[400],
                                        }}
                                        checked={item.received}
                                        // disabled
                                        // onChange={(e) =>
                                        //     handleInputChange(
                                        //         e,
                                        //         key,
                                        //         'received',
                                        //         order
                                        //     )
                                        // }
                                    />
                                  </TableCell>
                                  {this.props.auth.user.is_admin && (
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                    >
                                      <Tooltip
                                          content='Delete Order'
                                          // style='light'
                                        >
                                          <Button
                                            // color='gray'
                                            size='xs'
                                            pill
                                            inline='true'
                                            className='m-0 p-0 inline'
                                            onClick={() => {
                                              this.handleStatus(4,item,order)
                                            }}
                                            // onClick={() => {
                                            //   this.setState({
                                            //     statusDelete:true,
                                            //     rowsItem:order
                                            //   })
                                            // }}
                                          >
                                            <TbTrash className='h-4 w-4 text-red-600' />
                                          </Button>
                                      </Tooltip>  
                                    </TableCell>
                                  )}
                                </TableRow>
                              </>
                            )}
                            if(index===this.props.dashboard.ordersTableData.length-1 && controller===0){  
                              return(
                                <NoDataList colSpan={head.length} />
                              )
                            }
                          })
                        ):(
                            <NoDataList colSpan={head.length} />
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  {/* <Box 
                    sx={{
                      display:'flex',
                      justifyContent:'flex-end'
                    }}
                  >
                    <TablePagination
                      rowsPerPageOptions={[5, 10, 25, 50, 100]}
                    //   component="div"

                      count={this.state.ordersTableData.length}
                      rowsPerPage={this.state.rowsPerPage}
                      page={this.state.page}
                      onPageChange={(e)=>{this.handleChangePage(e)}}
                      onRowsPerPageChange={(e)=>{this.handleChangeRowsPerPage(e)}}
                    />
                  </Box> */}
                  </Paper>
                </Box>
                )
              )
            }
          </Box>
        </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    locationHook,
    dashboardHook
  )(ReturnScene)
