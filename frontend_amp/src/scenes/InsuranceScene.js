
import React, { Component } from 'react'
import { Box, Button, Paper, Skeleton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tooltip } from '@mui/material'
import OrderMenu from '../Components/OrderMenu'
import { compose } from 'redux'
import themeHook from '../HOK/ThemeHook'
import authHook from '../HOK/AuthHook'
import locationHook from '../HOK/LoctionHook'
import dashboardHook from '../HOK/DashboardHook'
import AlertNotes from '../Components/AlertNotes'
import NoDataList from '../Components/NoDataList'
import { blue, grey, purple, red } from '@mui/material/colors'
import { TbEyeCheck,TbTrash, TbEdit } from 'react-icons/tb'
import TableSkeleton from '../Components/TableSkeleton'
import InsuranceAdd from '../Components/insurance/InsuranceAdd'
import InsuranceEdit from '../Components/insurance/InsuranceEdit'
import InsuranceDelete from '../Components/insurance/InsuranceDelete'
import { formatDate } from '../utils/functions'
import InsuranceShow from '../Components/insurance/InsuranceShow'
const head = [
  { name: 'Patiant Name', style: '20' },
  { name: 'Group', style: '10' },
  { name: 'Cert', style: '10' },
  { name: 'Date birth', style: '10' },
  { name: 'Plan holder birth', style: '10' },
  { name: 'Plan holder', style: '10' },
  { name: 'Address', style: '10' },
  { name: 'Relation subscriber', style: '10' },
  { name: 'Notes', style: '10' },
  { name: 'Breakdown', style: '10' },
  { name: 'Action', style: '10' },
];
class InsuranceScene extends Component {
    constructor(props){
        super(props)
        this.state={
          theme:props.theme,
          auth:props.auth,
          dashboard:props.dashboard,
          insuranceTableData:Object(null),
          tableHeight:(this.props.theme.windowDimensions.height*85)/100,
          isLoadingInsurance:false,
          sendForAPI:false,
          rowsItem:Object()|null,
          statusAdd:false,
          statusEdit:false,
          statusDelete:false,
          statusShow:false,
          showAlert:false,
          infoAlert:{ msg: '', status: undefined },
        }
        this.showAlertNotes=this.showAlertNotes.bind(this)
        this.handleStatus=this.handleStatus.bind(this)
        this.handleCloseModal=this.handleCloseModal.bind(this)
    }
    async componentDidMount(){
      this.props.theme.setWindowDimensions({width: window.innerWidth,height: window.innerHeight});
      this.setState({
        theme:this.props.theme,
        auth:this.props.auth,
        dashboard:this.props.dashboard,
      })   
      if (this.props.auth.user.is_admin){
        if(this.props.auth.selectUser){
          let response=await this.props.dashboard.getInsurance(this.props.auth.token,parseInt(this.props.auth.selectUser.id))
          if (response.status==200) {
            if(response.msg=="success"){
              this.setState({
                insuranceTableData:await this.props.dashboard.insuranceTableData,
              })
            }
          }
          this.setState({
            isLoadingInsurance:false
          })
        }
      }
      if(!this.props.auth.user.is_admin){
        let user_id=0
        if(this.props.auth.user.user_group==3){
          user_id=this.props.auth.user.id
        }else if(this.props.auth.user.user_group==4){
          user_id=this.props.auth.user.ref_admin.id
        }
        let response=await this.props.dashboard.getInsurance(this.props.auth.token,user_id)
        if (response.status==200) {
          if(response.msg=="success"){
            this.setState({
              insuranceTableData:await this.props.dashboard.insuranceTableData,
            })
          }
        }
        this.setState({
          isLoadingInsurance:false
        })
      }
      setInterval(async () => {
        if (this.state.sendForAPI){
          console.log("sendforApi")
          this.setState({
            insuranceTableData:await this.props.dashboard.insuranceTableData,
            isLoadingInsurance:true,
          })
          this.shouldComponentUpdate=true
          this.forceUpdate()
        }
        this.setState({
          sendForAPI:false,
          isLoadingInsurance:false,
        })
      }, 3000);
      this.shouldComponentUpdate=true
      this.forceUpdate()
    }
    showAlertNotes(){
      this.setState({
        showAlert:true
      })
      setTimeout(() => {
        this.setState({
          showAlert:false,
          infoAlert:{ msg: '', status: undefined }
        })
      }, 3000);
    }
    handleStatus(value,rowsItem,insurance){
      // console.log(value,rowsItem,insurance)
      if(value===1){
        this.setState({
          statusAdd:true,
          // rowsItem:rowsItem
        })
      }else if(value===2){
        this.setState({
          statusEdit:true,
          rowsItem:rowsItem,
          indexRow:insurance
        })
      }else if(value===3){
        this.setState({
          statusShow:true,
          rowsItem:rowsItem
        })
      }else if(value===4){
        this.setState({
          statusDelete:true,      
          rowsItem:rowsItem,
          indexRow:insurance
        })
      }
    }
    handleCloseModal(){
      this.setState({
        statusAdd:false,
        statusEdit:false,
        statusDelete:false,
        statusShow:false,
      })
      this.shouldComponentUpdate=true
      this.forceUpdate()
    }
  render() {
    return (
      <>
       {this.state.showAlert && (
          <AlertNotes 
            message={this.state.infoAlert.msg}
            action={this.state.infoAlert.status}
          />
        )}
        {this.state.statusAdd && (
          <InsuranceAdd
            // isLoadingOrders={this.state.isLoadingOrders}
            onClose={this.handleCloseModal}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
        {this.state.statusEdit && (
          <InsuranceEdit
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
        {this.state.statusDelete && (
          <InsuranceDelete
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
        {this.state.statusShow && (
          <InsuranceShow
            data={this.state.statusShow}
            item={this.state.rowsItem}
            onClose={this.handleCloseModal}
          />
        )}
        <Box
            sx={{
              width:'100%',
              height:'100%',
              display:'block',
              // backgroundColor:"#F3F4F6"
            }}
          >
            <OrderMenu handleStatus={this.handleStatus} />
            {this.state.isLoadingInsurance ? (
                  <>
                    <Box 
                      sx={{
                        display:'flex',
                        flexDirection:'row',
                        gap:1,
                        // textAlign:'center',
                        // alignItems:'center',
                        minHeight:this.state.tableHeight,
                        height:'100%',
                        width:'100%',
                        paddingTop:0,
                        marginTop:0,
                        backgroundColor:this.props.theme.selectTheme.background_2,
                        // paddingY:5,
                        // marginBottom:3,
                      }}
                    >
                      <TableSkeleton />
                    </Box>
                  </>
              ) : (this.props.dashboard.insuranceTableData&&(
                  <Box
                    sx={{
                    height:'100%',
                    // display:'flex',
                    // paddingLeft:5,
                    borderTop:'1px solid #e5e7eb',
                    // textAlign:'left',
                    // alignItems:'center',
                    backgroundColor:this.props.theme.selectTheme.background_3
                  }}
                >
                  <Paper 
                    sx={{ 
                      width: '100%',
                      height:'100%',
                      overflow: 'hidden'
                    }}
                  >
                  <TableContainer 
                    sx={{ 
                      height:'100%',
                      minHeight: `${this.state.tableHeight}px` ,
                      backgroundColor:this.props.theme.selectTheme.background,
                      color: this.props.theme.selectTheme.fontColor
                    }}
                  >
                    <Table stickyHeader aria-label="sticky table">
                      <TableHead sx={{
                          marginBottom:2,
                          backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                          color: this.props.theme.selectTheme.headerfontColor
                      }}>
                        <TableRow
                          sx={{
                            // backgroundColor:this.props.theme.selectTheme.headerBg,
                            // color: this.props.theme.selectTheme.headerfontColor
                          }}
                        >
                          {head.map((item,index) => (
                            <TableCell
                              key={index}
                              align={item.align}
                              sx={{
                                width:`${item.style}%`,
                                border:`1px ${grey[200]} solid`,
                                textAlign:'center',
                                backgroundColor:this.props.theme.selectTheme.background,
                                color: this.props.theme.selectTheme.fontColor
                              }}
                            >
                              {item.name}
                            </TableCell>
                          ))}
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {this.props.dashboard.insuranceTableData.length ? (
                        Object.keys(this.props.dashboard.insuranceTableData)
                          .map((insurance,index) => {
                            let item=this.props.dashboard.insuranceTableData[insurance]
                            return (
                                <TableRow 
                                    sx={{
                                      // backgroundColor:this.props.theme.selectTheme.background,
                                      // color: this.props.theme.selectTheme.fontColor
                                    }}
                                  hover 
                                  role="checkbox" 
                                  tabIndex={-1} 
                                  key={index}
                                >
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={insurance.id}
                                    >
                                    {item.patiant_name}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={insurance.id}
                                    >
                                    {item.group}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={insurance.id}
                                    >
                                    {item.cert}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={insurance.id}
                                    >
                                      {item.date_of_birth
                                          ? formatDate(
                                                'Amm-dd-yyyy',
                                                item.date_of_birth
                                            )
                                          : ''}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={insurance.id}
                                    >
                                      {item.plan_holder_date_of_birth
                                          ? formatDate(
                                                'Amm-dd-yyyy',
                                                item.plan_holder_date_of_birth
                                            )
                                          : ''}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={insurance.id}
                                    >
                                    {item.plan_holder}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={insurance.id}
                                    >
                                    {item.address}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={insurance.id}
                                    >
                                    {item.relation_to_subscriber}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={insurance.id}
                                    >
                                    {item.notes}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={insurance.id}
                                    >
                                    {item.breakdown}
                                  </TableCell>
                                  <TableCell 
                                    sx={{
                                      px:1,
                                      py:2,
                                      border:`1px ${grey[200]} solid`,
                                      textAlign:'center',
                                      backgroundColor:this.props.theme.selectTheme.background_2,
                                      color: this.props.theme.selectTheme.fontColor_3
                                    }}
                                    key={insurance.id}
                                  >
                                    {item.id && (
                                      <Box
                                        sx={{
                                          display:'flex'
                                        }} 
                                        >
                                          <Tooltip
                                              content='Show Insurance'
                                          >
                                              <Button
                                                size='medium'
                                                sx={{
                                                  color:purple[400],
                                                }}
                                                pill
                                                onClick={() => {
                                                  this.handleStatus(3,item,insurance)
                                                }}
                                              >
                                                  <TbEyeCheck size={24} />
                                              </Button>
                                          </Tooltip>
                                          <Tooltip
                                              content='Edit Vendor'
                                          >
                                              <Button
                                                  size='medium'
                                                  sx={{
                                                    color:purple[400],
                                                  }}
                                                  pill
                                                  onClick={() => {
                                                    this.handleStatus(2,item,insurance)
                                                  }}
                                              >
                                                  <TbEdit size={24} color={blue[600]} />
                                              </Button>
                                          </Tooltip>
                                          {this.props.auth.user.is_admin && (
                                              <Tooltip
                                                  content='Delete Vendor'
                                              >
                                                  <Button
                                                    size='medium'
                                                    sx={{
                                                      color:purple[400],
                                                    }}
                                                    pill
                                                    inline='true'
                                                    onClick={() => {
                                                      this.handleStatus(4,item,insurance)
                                                    }}
                                                  >
                                                      <TbTrash size={24} color={red[600]} />
                                                  </Button>
                                              </Tooltip>
                                          )}
                                      </Box>
                                    )}
                                  </TableCell>
                                </TableRow>
                            )
                          })
                        ):(
                          <NoDataList colSpan={head.length}/>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Paper>
                  </Box>
                )
              )
            }
        </Box>
      </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    locationHook,
    dashboardHook
  )(InsuranceScene)