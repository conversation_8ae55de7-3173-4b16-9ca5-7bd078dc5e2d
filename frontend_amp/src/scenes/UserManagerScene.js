import React, { Component } from 'react'
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { Avatar, Box, Button, Checkbox, IconButton, Skeleton, Tooltip, Typography } from '@mui/material';
import { TbEyeCheck, TbTrash, TbUserEdit } from 'react-icons/tb';
import TableSkeleton from '../Components/TableSkeleton';
import themeHook from '../HOK/ThemeHook';
import authHook from '../HOK/AuthHook';
import { blue, green, grey, purple, red } from '@mui/material/colors'
import NoDataList from '../Components/NoDataList'
import locationHook from '../HOK/LoctionHook';
import OrderMenu from '../Components/OrderMenu';
import { compose } from 'redux';
import AlertNotes from '../Components/AlertNotes';
import SubUserAdd from '../Components/subuser/SubUserAdd';
import SubUserEdit from '../Components/subuser/SubUserEdit';
import SubUserShow from '../Components/subuser/SubUserShow';
import UserDelete from '../Components/users/UserDelete';
const head = [
    { name: 'Name', style: '20' },
    { name: 'Email', style: '10' },
    { name: 'Mobile Number', style: '15' },
    // { name: 'Dental', style: '20' },
    { name: 'Total Budget', style: '10' },
    { name: 'IsAdmin', style: '5' },
    { name: 'Status', style: '10' },
    { name: 'Action', style: '10' },
]
class UserListScene extends Component {
    constructor(props){
        super(props)
        this.state={
          theme:props.theme,
          auth:props.auth,
          isLoadingUserList:true,
          userList:Object(null)||[],
          tableHeight:(this.props.theme.windowDimensions.height*85)/100,

          rowsItem:Object()|null,
          sendForAPI:false,
          statusAdd:false,
          statusEdit:false,
          statusShow:false,
          statusDelete:false,
          showAlert:false,
          infoAlert:{ msg: '', status: undefined },
        }
        this.showAlertNotes=this.showAlertNotes.bind(this)
        this.handleStatus=this.handleStatus.bind(this)
        this.handleCloseModal=this.handleCloseModal.bind(this)
    }
    async componentDidMount(){
      const auth = this.props.auth;
      if (auth.user.is_admin){
        if(auth.selectUser){
          await auth.getUsersAction(this.props.auth.token)
          this.setState({
            userList:await auth.users,
            // isLoadingUserList:false
          })
          setInterval(async () => {
            if (this.state.sendForAPI){
              console.log("sendforApi")
              this.setState({
                userList:await this.props.auth.users,
                isLoadingUserList:true,
              })
              this.shouldComponentUpdate=true
              this.forceUpdate()
            }
            this.setState({
              sendForAPI:false,
              isLoadingUserList:false,
            })
          }, 3000);
          this.setState({
            isLoadingUserList:false
          })
        }
      }
      else{
        await auth.getUsersAction(this.props.auth.token)
          this.setState({
            userList:await auth.users,
            // isLoadingUserList:false
          })
          setInterval(async () => {
            if (this.state.sendForAPI){
              console.log("sendforApi")
              this.setState({
                userList:await this.props.auth.users,
                isLoadingUserList:true,
              })
              this.shouldComponentUpdate=true
              this.forceUpdate()
            }
            this.setState({
              sendForAPI:false,
              isLoadingUserList:false,
            })
          }, 3000);
          this.setState({
            isLoadingUserList:false
          })
      }
    }
    showAlertNotes(){
      this.setState({
        showAlert:true
      })
      setTimeout(() => {
        this.setState({
          showAlert:false,
          infoAlert:{ msg: '', status: undefined }
        })
      }, 3000);
    }
    handleStatus(value,rowsItem,user){
      // console.log(value,rowsItem,order)
      if(value===1){
        this.setState({
          statusAdd:true,
          // rowsItem:rowsItem
        })
      }else if(value===2){
        this.setState({
          statusEdit:true,
          rowsItem:rowsItem,
          indexRow:user
        })
      }else if(value===3){
        this.setState({
          statusShow:true,
          rowsItem:rowsItem,
          indexRow:user
        })
      }else if(value===4){
        this.setState({
          statusDelete:true,      
          rowsItem:rowsItem,
          indexRow:user
        })
      }
    }
    handleCloseModal(){
      this.setState({
        statusAdd:false,
        statusEdit:false,
        statusShow:false,
        statusDelete:false,
      })
      this.shouldComponentUpdate=true
      this.forceUpdate()
    }
  render() {
    return (
        <>
        {this.state.showAlert && (
            <AlertNotes 
            message={this.state.infoAlert.msg} 
            action={this.state.infoAlert.status} 
            />
        )}
        {this.state.statusAdd && (
          <SubUserAdd
            data={this.state.statusAdd}
            onClose={this.handleCloseModal}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
        {this.state.statusEdit && (
          <SubUserEdit
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
            permission={this.state.auth.user.is_admin}
            data={this.state.statusEdit}
          />
        )}
        {this.state.statusDelete && (
            <UserDelete
                data={this.state.statusDelete}
                onClose={this.handleCloseModal}
                item={this.state.rowsItem}
                showAlertNotes={this.showAlertNotes}
                setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
                refetchOrder={(sendForAPI)=>{this.setState({
                  sendForAPI:sendForAPI
                })}}
                // refetch={refetchUsers}
            />
        )}
        {this.state.statusShow && (
          <SubUserShow
            data={this.state.statusShow}
            item={this.state.rowsItem}
            onClose={this.handleCloseModal}
          />
        )}
        <Box
          sx={{
            width:'100%',
            height:'100%',
            display:'block',
            // backgroundColor:"#F3F4F6"
          }}
        >
            <OrderMenu handleStatus={this.handleStatus}/>
            {this.state.isLoadingUserList ? 
            (
              <>
                <Box 
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    gap:1,
                    // textAlign:'center',
                    // alignItems:'center',
                    minHeight:this.state.tableHeight,
                    height:'100%',
                    width:'100%',
                    paddingTop:0,
                    marginTop:0,
                    backgroundColor:this.props.theme.selectTheme.background_2,
                    // paddingY:5,
                    // marginBottom:3,
                  }}
                >
                  <TableSkeleton />
                </Box>
              </>
            ):(this.props.auth.users && (
                        <Box
                            sx={{
                            height:'100%',
                            // display:'flex',
                            // paddingLeft:5,
                            borderTop:'1px solid #e5e7eb',
                            // textAlign:'left',
                            // alignItems:'center',
                            backgroundColor:this.props.theme.selectTheme.background_3
                            }}
                        >
                        <Paper 
                        sx={{ 
                            width: '100%',
                            height:'100%',
                            overflow: 'hidden'
                        }}
                        >
                        <TableContainer 
                        sx={{ 
                            height:'100%',
                            minHeight: `${this.state.tableHeight}px` ,
                            backgroundColor:this.props.theme.selectTheme.background,
                            color: this.props.theme.selectTheme.fontColor
                        }}
                        >
                        <Table stickyHeader aria-label="sticky table">
                            <TableHead sx={{
                                marginBottom:2,
                                backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                                color: this.props.theme.selectTheme.headerfontColor
                            }}>
                            <TableRow
                                sx={{
                                // backgroundColor:this.props.theme.selectTheme.headerBg,
                                // color: this.props.theme.selectTheme.headerfontColor
                                }}
                            >
                                {head.map((item,index) => (
                                <TableCell
                                    key={index}
                                    align={item.align}
                                    sx={{
                                    width:`${item.style}%`,
                                    border:`1px ${grey[200]} solid`,
                                    textAlign:'center',
                                    backgroundColor:this.props.theme.selectTheme.background,
                                    color: this.props.theme.selectTheme.fontColor
                                    }}
                                    // style={{ 
                                    //   width: `${item.style}%`,
                                    //   border:`1px ${grey[200]} solid`,
                                    //   // borderColor:grey[200],
                                    //   textAlign:'center',
                                    //  }}
                                >
                                    {item.name}
                                </TableCell>
                                ))}
                            </TableRow>
                            </TableHead>
                            <TableBody>
                            {this.props.auth.users.length ? (
                            // rows.slice(this.state.page * this.state.rowsPerPage, this.state.page * this.state.rowsPerPage + this.state.rowsPerPage)
                            Object.keys(this.props.auth.users)
                                .map((user,index) => {
                                // console.log(this.props.auth.users[user])
                                    return ( 
                                    <TableRow 
                                        sx={{
                                            // backgroundColor:this.props.theme.selectTheme.background,
                                            // color: this.props.theme.selectTheme.fontColor
                                        }}
                                        hover role="checkbox" tabIndex={-1} key={index}>
                                        
                                        <TableCell 
                                            sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                            }}
                                        > 
                                            {this.props.auth.users[user].first_name}
                                        </TableCell>
                                        <TableCell 
                                            sx={{
                                            px:1,
                                            py:2,                          
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                            }}
                                        >
                                        {this.props.auth.users[user].email ?? ''}
                                        </TableCell>
                                        <TableCell 
                                            sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                            }}
                                        >
                                            {this.props.auth.users[user].phone_number}
                                        </TableCell>
                                        {/* <TableCell 
                                            sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                            }}
                                        >
                                            <Box
                                            sx={{
                                                display:'flex',
                                                width:'100%',
                                                justifyContent:'center',
                                                alignItems:'center',
                                                textAlign:'center'
                                            }}
                                            >

                                            <Avatar 
                                                src={this.state.userList[user].dental_logo}/>
                                            </Box>
                                        
                                        </TableCell> */}
                                        <TableCell 
                                            sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                            }}
                                        >
                                            ${this.props.auth.users[user].total_budget}
                                        </TableCell>            
                                        <TableCell 
                                            sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                            }}
                                        > 
                                            <Checkbox
                                                sx={{
                                                color:purple[400],
                                                }}
                                                checked={this.props.auth.users[user].is_admin}
                                            />
                                        </TableCell>
                                        <TableCell 
                                            sx={{
                                            px:1,
                                            py:2,
                                            border:`1px ${grey[200]} solid`,
                                            textAlign:'center',
                                            backgroundColor:this.props.theme.selectTheme.background_2,
                                            color: this.props.theme.selectTheme.fontColor_3
                                            }}
                                        >
                                            {this.props.auth.users[user].is_active?
                                            (
                                                <Typography color={green[700]}>
                                                Active
                                                </Typography>
                                            ):(
                                                <Typography color={red[600]} >
                                                Deactive
                                                </Typography>
                                            )
                                            }
                                        </TableCell>
                                        <TableCell 
                                            sx={{
                                                display:'flex',
                                                flexDirection:'row',
                                                alignItems:'center',
                                                height:'100%',
                                                px:1,
                                                py:4,
                                                border:`1px ${grey[200]} solid`,
                                                textAlign:'center',
                                                backgroundColor:this.props.theme.selectTheme.background_2,
                                                color: this.props.theme.selectTheme.fontColor_3
                                            }}
                                            >
                                                <>
                                                <Tooltip
                                                    content='show user'
                                                >
                                                    <Button
                                                        size='medium'
                                                        pill
                                                        inline='true'
                                                        className='m-0 p-0 inline mb-2'
                                                        onClick={() => {
                                                        this.handleStatus(3,this.props.auth.users[user],user)
                                                        }}
                                                    >
                                                    <TbEyeCheck size={24} color={blue[600]} />
                                                    </Button>
                                                </Tooltip>
                                                <Tooltip
                                                    content='Edit Order'
                                                >
                                                    <Button
                                                    size='medium'
                                                    sx={{
                                                        color:purple[400],
                                                    }}
                                                    pill
                                                    onClick={() => {
                                                        this.handleStatus(2,this.props.auth.users[user],user)
                                                    }}
                                                    >
                                                        <TbUserEdit size={24} color={green[600]} />
                                                    </Button>
                                                </Tooltip>
                                                <Tooltip
                                                content='Delete Order'
                                                >
                                                    <Button
                                                    size='medium'
                                                    pill
                                                    inline='true'
                                                    className='m-0 p-0 inline'
                                                    onClick={() => {
                                                        this.handleStatus(4,this.props.auth.users[user],user)
                                                    }}
                                                    >
                                                        <TbTrash size={24} color={red[600]}/>
                                                    </Button>
                                                </Tooltip>
                                                </>  
                                            </TableCell>
                                    </TableRow>
                                    )
                                })
                            ):(
                                <NoDataList colSpan={head.length} />
                            )}
                            </TableBody>
                        </Table>
                        </TableContainer>
                        {/* <Box 
                        sx={{
                            display:'flex',
                            justifyContent:'flex-end'
                        }}
                        >
                        <TablePagination
                            rowsPerPageOptions={[5, 10, 25, 50, 100]}
                        //   component="div"

                            count={this.state.ordersTableData.length}
                            rowsPerPage={this.state.rowsPerPage}
                            page={this.state.page}
                            onPageChange={(e)=>{this.handleChangePage(e)}}
                            onRowsPerPageChange={(e)=>{this.handleChangeRowsPerPage(e)}}
                        />
                        </Box> */}
                        </Paper>
                        </Box>
                    )
                )
            }
        </Box>
        </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    locationHook,
)(UserListScene)