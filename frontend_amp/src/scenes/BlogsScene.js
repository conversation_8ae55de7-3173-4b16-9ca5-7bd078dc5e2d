import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { Avatar, Box, Card, CardActions, CardContent, CardMedia, Grid, IconButton,Tooltip,Button, Link, ListItemIcon, MenuItem, Typography, CardHeader, FormControl, InputLabel, Select } from '@mui/material';
import { GoRead } from 'react-icons/go';
import { blue, green, pink, purple, red } from '@mui/material/colors';
import { formatDate } from '../utils/functions';
import themeHook from '../HOK/ThemeHook';
import authHook from '../HOK/AuthHook';
import navigateHook from '../HOK/NavigateHook';
import dashboardHook from '../HOK/DashboardHook';
import { compose } from 'redux';
import { DEV_BASE_URL } from '../Environments';
// import { Button, Tooltip } from '@mui/joy';
import { CiRead } from "react-icons/ci";
import { TbEdit, TbTrash } from 'react-icons/tb';
import { MdOpenInNew } from "react-icons/md";
import BlogAdd from '../Components/blogfeedback/BlogAdd';
import BlogEdit from '../Components/blogfeedback/BlogEdit';
import BlogDelete from '../Components/blogfeedback/BlogDelete';
import BlogPreview from '../Components/blogfeedback/BlogPreview';
import AlertNotes from '../Components/AlertNotes';
import { FaCloudDownloadAlt } from "react-icons/fa";
import { ArrowDropDownCircle } from '@mui/icons-material';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/navigation';
import { Navigation } from 'swiper/modules';
const post_status={
    'Published':1,
    'Hidden':2,
    'Draft':3,
}
class BlogsScene extends Component {
    constructor(props){
        super(props)
        this.state={
            theme:props.theme,
            darkTheme:props.theme.darkTheme,
            tableHeight:(this.props.theme.windowDimensions.height*85)/100,
            auth:props.auth,
            sendForAPI:false,
            newsQuantity:0,
            newsOpen:false,
            newsAnchorEl:null,
            newsStatusShow:false,
            newsShowIndex:0,
            statusAdd:false,
            statusEdit:false,
            statusPreview:false,
            statusDelete:false,
            showAlert:false,
            infoAlert:{ msg: '', status: undefined },
            rowsItem:Object(null),
            blogsData:Object(null),
            isLoading:true,
        }
        this.showAlertNotes=this.showAlertNotes.bind(this)
        this.handleStatus=this.handleStatus.bind(this)
        this.handleCloseModal=this.handleCloseModal.bind(this)
    }
  async componentDidMount(){
    if(await this.props.auth.user){
        if(await this.props.auth.user.is_admin){
            let responseblogs=await this.props.dashboard.getBlog(await this.props.auth.token)
          if(responseblogs.status!=200){
          }else{
              if(responseblogs.msg=="success"){
                this.setState({
                  // auth:this.props.auth,
                  blogsData:await this.props.dashboard.blogsData
                })
                let q=0
                Object.keys(this.props.dashboard.blogsData).map((blog)=>{
                  // if(this.props.dashborad.blogData[blog].unread==true){
                    q=q+1
                  // }
                })
                this.setState({
                  newsQuantity:q,
                  isLoading:false
                })
              }else{
              }
          }
        }else{
          let responseblogs=await this.props.dashboard.getBlog(await this.props.auth.token)
          if(responseblogs.status!=200){
          }else{
              if(responseblogs.msg=="success"){
                this.setState({
                  // auth:this.props.auth,
                  blogsData:await this.props.dashboard.blogsData
                })
                let q=0
                Object.keys(this.props.dashboard.blogsData).map((blog)=>{
                  // if(this.props.dashborad.blogData[blog].unread==true){
                    q=q+1
                  // }
                })
                this.setState({
                  newsQuantity:q,
                  isLoading:false
                })
              }else{
              }
          }
        }
        this.setState({
          isLoading:false
        })
        this.shouldComponentUpdate=true
        this.forceUpdate()
      }
  }
  async updateBlogStatus(rowItem,status,blog){
    const blogDate={
        id:rowItem.id,
        post_status:status
    }
    let response=await this.props.dashboard.editBlogStatus(this.props.auth.token,blogDate)
      if (response.status!=200) {
          this.setState({
            sendForAPI:true,
            infoAlert:{
              msg: `Error,Can\'t Update Status : ${response.msg}`,
              status: false,
            }
          })
          this.showAlertNotes();
      } else {
          if (response.msg == 'success') {
              // refetchOrder();
              this.setState({
                sendForAPI:true,
                infoAlert:{
                  msg: 'Blog status Updated',
                  status: true,
                }
              })
              this.showAlertNotes();
              this.handleCloseModal();
          } else {
            this.setState({
              sendForAPI:true,
              infoAlert:{
                  msg: `Failed,Can\'t Update Blog Status: ${response.msg}`,
                  status: false,
                }
            })
            this.showAlertNotes();
          }
      }

  }
  showAlertNotes(){
    this.setState({
      showAlert:true
    })
    setTimeout(() => {
      this.setState({
        showAlert:false,
        infoAlert:{ msg: '', status: undefined }
      })
    }, 3000);
  }
  handleStatus(value,rowsItem,blog){
    // console.log(value,rowsItem,order)
    if(value===1){
      this.setState({
        statusAdd:true,
        // rowsItem:rowsItem
      })
    }else if(value===2){
      this.setState({
        statusEdit:true,
        rowsItem:rowsItem,
        indexRow:blog
      })
    }else if(value===3){
      this.setState({
        statusPreview:true,
        rowsItem:rowsItem,
        indexRow:blog
      })
    }else if(value===4){
      this.setState({
        statusDelete:true,      
        rowsItem:rowsItem,
        indexRow:blog
      })
    }
  }
  handleCloseModal(){
    this.setState({
      statusAdd:false,
      statusEdit:false,
      statusPaid:false,
      statusDelete:false,
      blogsData:this.props.dashboard.blogsData
    })
    this.shouldComponentUpdate=true
    this.forceUpdate()
  }
  render() {
    return (
      <>
        {this.state.showAlert && (
            <AlertNotes
                message={this.state.infoAlert.msg}
                action={this.state.infoAlert.status}
            />
        )}
        {this.state.statusAdd && (
            <BlogAdd
                // isLoadingOrders={this.state.isLoadingOrders}
                onClose={this.handleCloseModal}
                showAlertNotes={this.showAlertNotes}
                setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
                refetchOrder={(sendForAPI)=>{this.setState({
                    sendForAPI:sendForAPI
                })}}
                // onFormInsert={this.handleInsertOrder}
            
            />
        )}
        {this.state.statusEdit && (
            <BlogEdit
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
                sendForAPI:sendForAPI
            })}}
            permission={this.state.auth.user.is_admin}
            />
        )}
        {/* {this.state.statusPreview && (
            <BlogPreview
                item={this.state.rowsItem}
                data={this.state.statusPreview}
                onClose={this.handleCloseModal}
                permission={this.state.auth.user.is_admin}
                showAlertNotes={this.showAlertNotes}
                setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
                refetchOrder={(sendForAPI)=>{this.setState({
                sendForAPI:sendForAPI
                })}}
            />
        )} */}
        {this.state.statusDelete && (
            <BlogDelete
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
                sendForAPI:sendForAPI
            })}}
            />
        )}
        <Box
            sx={{
                width:'100%',
                minHeight:this.state.tableHeight,
                paddingY:2,
                backgroundColor:this.props.theme.selectTheme.background_2,
            }}
        >
            <Box
                sx={{
                    width:'100%',
                    backgroundColor:this.props.theme.selectTheme.background_2,
                    gap:2
                }}
            >
                <Typography
                    variant='h4'
                    sx={{
                        paddingLeft:5
                    }}
                >
                    Blog
                </Typography>
                <Typography
                    sx={{
                        paddingLeft:5
                    }}
                >
                    Stay in the loop with the latest about our products
                </Typography>
                {
                this.props.dashboard.blogsData&&
                    (this.props.dashboard.blogsData.length?(
                        Object.keys(this.props.dashboard.blogsData).map((blog)=>{
                        const item=this.props.dashboard.blogsData[blog]
                        return(
                           
                            <Card 
                                sx={{ 
                                    width:'95%',
                                    display:'flex',
                                    flexDirection:'column',
                                    //justifyContent:'space-between',
                                    marginTop:2,
                                    marginBottom:2,
                                    marginX:2,
                                    backgroundColor:this.props.theme.selectTheme.background,
                                    padding:2,
                                }} 
                                key={blog}
                            >   
                                {this.props.auth.user.is_admin&&(
                                    <CardHeader
                                         title={
                                            <>
                                            <FormControl
                                                size='small'
                                                fullWidth
                                                sx={{
                                                    borderColor:this.props.theme.fontColor,
                                                    backgroundColor:this.props.theme.background,
                                                    color:this.props.theme.fontColor,
                                                }}
                                            >
                                            <InputLabel
                                                id="demo-simple-select-label"
                                                sx={{
                                                    backgroundColor:this.props.theme.background,
                                                    color:this.props.theme.fontColor,
                                                    // width:'80%',
                                                    // borderRight:`1px solid`
                                                }}
                                            >Status</InputLabel>
                                            <Select
                                                value={item.post_status}
                                                label="Status"
                                                onChange={(e)=>{
                                                    this.props.dashboard.blogsData[blog].post_status=e.target.value
                                                    this.updateBlogStatus(this.props.dashboard.blogsData[blog],e.target.value,blog)
                                                    console.log(e.target.value)
                                                    }
                                                }
                                                // components={CustomOption}
                                                sx={{
                                                    display:'flex',
                                                    flexDirection:'row',
                                                    backgroundColor:this.props.theme.background,
                                                    color:this.props.theme.fontColor,
                                                    '& .MuiSelect-select':{
                                                        display:'flex',
                                                        backgroundColor:this.props.theme.background,
                                                        color:this.props.theme.fontColor
                                                    },
                                                }}
                                                style={{display:'flex',flexDirection:'row',alignItems:'center'}}
                                            >
                                            {Object.keys(post_status).map((item,key)=>{
                                                return (
                                                    <MenuItem 
                                                    key={key}
                                                    value={item}
                                                    >
                                                    <Typography>
                                                        {item}
                                                    </Typography>
                                                </MenuItem>
                                                )
                                                })
                                            }
                                            </Select>
                                            </FormControl>
                                            </>
                                         }      
                                        subheader={
                                            <>
                                            </>
                                        }                              
                                    />
                                )}
                                <Box 
                                    sx={{
                                        width:'100%',
                                        display:'flex',
                                        flexDirection:'row',
                                        justifyContent:'space-between',
                                        // marginTop:2,
                                        // marginBottom:2,
                                        // marginX:2,
                                        backgroundColor:this.props.theme.selectTheme.background,
                                        padding:3,
                                    }}
                                >
                                    <CardActions
                                        sx={{
                                            display:'flex',
                                            flexDirection:'column',
                                            justifyContent:'space-evenly',
                                            border: '1px solid #ccc',
                                            borderLeft: 0,
                                            marginRight:2,
                                            padding:0
                                        }}
                                    >
                                        <Tooltip
                                            title="See More"
                                        >
                                            <Button
                                                size='small'
                                                pill
                                                sx={{
                                                    color:green[400],
                                                    padding:0
                                                }}
                                                inline='true'
                                                onClick={()=>{
                                                    // this.props.navigate("/blogs-by-id/"+item.id)
                                                    this.props.navigate("/blogs-by-id/"+item.id,{
                                                                                            state: {
                                                                                            id: item.id,
                                                                                            },
                                                                                            replace:true
                                                                                        })
                                                }}
                                                >
                                                <CiRead size={24} />
                                            </Button>
                                        </Tooltip>
                                        <Tooltip
                                            title="Download"
                                        >
                                            <Link
                                                href={DEV_BASE_URL+'/'+item.blog_file}
                                                target="_blank"
                                                >
                                                <FaCloudDownloadAlt size={24} />
                                            </Link>
                                        </Tooltip>
                                        {this.props.auth.user.is_admin&&(
                                            <>
                                             <Tooltip
                                                    title="Edit"
                                                    content='Delete Order'
                                                >
                                                    <Button
                                                        size='small'
                                                        pill
                                                        sx={{
                                                            color:blue[400],
                                                            padding:0
                                                        }}
                                                        inline='true'
                                                        onClick={() => {
                                                            this.handleStatus(2,item,blog)
                                                        }}
                                                    >
                                                        <TbEdit  size={24}/>
                                                    </Button>
                                                </Tooltip>
                                                <Tooltip
                                                    title="Delete"
                                                    content='Delete Order'
                                                >
                                                    <Button
                                                        size='small'
                                                        pill
                                                        inline='true'
                                                        sx={{
                                                            color:red[400],
                                                            padding:0
                                                        }}
                                                        onClick={() => {
                                                            this.handleStatus(4,item,blog)
                                                        }}
                                                    >
                                                        <TbTrash size={24} />
                                                    </Button>
                                                </Tooltip>
                                            </>
                                        )}
                                    </CardActions>
                                    <Box
                                        sx={{
                                            width:'80%',
                                            paddingRight:2
                                        }}
                                    >      
                                        <CardHeader
                                            sx={{
                                                display:'flex',
                                                alignContent:'center',
                                                alignItems:'center'
                                            }}
                                         //avatar={
                                          //  <Avatar sx={{ bgcolor: red[500] }} aria-label="recipe">
                                           //   Admin
                                           // </Avatar>
                                          //}
                                          action={
                                            <Box
                                                sx={{
                                                    display:'flex',
                                                    flexDirection:'column',
                                                    justifyContent:'center',
                                                    textAlign:'center',
                                                    alignContent:'center',
                                                    alignItems:'center'
                                                }}
                                            >
                                                <Typography 
                                                    sx={{
                                                        fontSize:'10px',
                                                        alignContent:'center',
                                                    }}
                                                    variant="p" 
                                                    color="text.primary"
                                                >
                                                    {formatDate(
                                                    'Amm-dd-yyyy',item.created_at)}
                                                </Typography>
                                                <Typography 
                                                    sx={{
                                                        fontSize:'10px',
                                                        paddingLeft:1
                                                    }}
                                                    variant="p"
                                                    color="text.secondary">
                                                    {item.post_author}<br/> 
                                                </Typography>
                                            </Box>
                                          }
                                          title={
                                            <IconButton
                                                sx={{
                                                    color:'text.primary',
                                                    textAlign:'center'
                                                }}
                                                onClick={()=>{
                                                    this.props.navigate("/blogs-by-id/"+item.id,{
                                                    state: {
                                                        id: item.id,
                                                    },
                                                    replace:true
                                                    })
                                                }}
                                            >
                                                <Typography
                                                    variant='h3'
                                                    sx={{
                                                        display:'flex',
                                                        justifyContent:'center',
                                                        textAlign:'center',
                                                    }}
                                                >
                                                    {item.post_title}
                                                </Typography>
                                            </IconButton>
                                          }
                                          subheader={
                                            <>
                                                
                                            </>
                                          }
                                        />                          
                                        <CardContent 
                                            sx={{
                                                width:'100%',
                                                position:'relative'
                                            }}
                                        >
                                            <Box
                                                sx={{
                                                    borderTop: '1px solid #ccc',
                                                    borderLeft: '1px solid #ccc',
                                                    borderRight: '1px solid #ccc',
                                                    padding:2
                                                }}
                                            >
                                                <Typography 
                                                    sx={{
                                                        whiteSpace:'pre-wrap',
                                                    }}
                                                    variant="span"
                                                    >
                                                    {item.post_description}
                                                </Typography>
                                            </Box>
                                        </CardContent>
                                    </Box>
                                    <Box 
                                        sx={{
                                            width:'20%'
                                        }}
                                    >
                                        {/* <CardMedia
                                            sx={{ 
                                                boxShadow: '0 0 5px 5px gray'
                                            }}
                                            component="img"
                                            height="250"
                                            // src={DEV_BASE_URL+'/'+item.post_image[]}
                                            // src={"media/"+item.post_image}
                                            // image="static/img/shiroodi.jpg"
                                            alt="Paella dish"
                                            onClick={()=>{
                                                this.props.navigate("/blogs-by-id/"+item.id)
                                            }}
                                        >    */}
                                        <Swiper
                                            modules={[Navigation]}
                                            navigation
                                            spaceBetween={10}
                                            slidesPerView={1}
                                            style={{ borderRadius: '8px' }}
                                        >
                                            {Object.keys(item.post_image).map((media, index) => {
                                                console.log(item.post_image[media]["img_url "])
                                                    return(
                                                        <SwiperSlide key={index}>
                                                            <CardMedia
                                                            component="img"
                                                            height="200"
                                                            image={DEV_BASE_URL+'/'+item.post_image[media]["img_url"]}
                                                            alt={`slide-${index}`}
                                                            sx={{ objectFit: 'cover' }}
                                                            />
                                                        </SwiperSlide>
                                                    )
                                                })
                                            }
                                        </Swiper>
                                        {/* </CardMedia>        */}
                                    </Box>
                                </Box>
                            </Card>
                        )
                        })
                        ):(
                            <>
                                <MenuItem 
                                sx={{
                                    width:'100%',
                                    borderBottom:'1px solid #e5e7eb',
                                    textAlign:'center',
                                    paddingX:'2',
                                    marginBottom:'2',
                                    justifyContent: 'space-between'
                                }}
                                key={"no-data"}
                                >
                                <Typography>
                                    Noting
                                </Typography>
                                </MenuItem>
                            </>
                        )
                    )
                }
            </Box>
        </Box>
      </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    navigateHook,
    dashboardHook
  )(BlogsScene)