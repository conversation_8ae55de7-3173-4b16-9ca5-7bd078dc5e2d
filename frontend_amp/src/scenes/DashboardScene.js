import React, { Children, Component } from 'react'
import PropTypes from 'prop-types'
import { Box, Typography } from '@mui/material'
import Header from '../Components/Header'
import SideBar from '../Components/SideBar'
import AlertUser from '../Components/AlertUser'
import locationHook from '../HOK/LoctionHook'
import { compose } from 'redux'
import navigateHook from '../HOK/NavigateHook'
// import {userInfoAction, userListInfoAction } from '../actions/DashboardActions'
import { connect } from 'react-redux'
import Loader from '../Components/Loader';
import authHook from '../HOK/AuthHook'
import themeHook from '../HOK/ThemeHook'
class DashboardScene extends Component {
  constructor (props){
    super(props)
    this.state={
      isLoading:true,
      auth:props.auth
      // darkTheme:false
    }
  }
  // toggleTheme(newTheme) {
  //   console.log(this.state.theme,newTheme)
  //   this.setState({
  //     theme: 'dark'
  //   });
  // }
  componentDidMount(){
    // console.log(this.props.location.state.token)
    // this.props.userInfoAction(this.props.location.state.token)
    // this.props.userListInfoAction(this.props.location.state.token)
    this.setState({
      isLoading:false
      // token:this.props.token
    })
    this.forceUpdate()
    // this.shouldComponentUpdate=true
    // const { navigate } = this.props;
    // navigate('/order');
    // this.props.orderInitial(this.props.location.state.token,this.props.location.state.token_type)
  }
  render() {
    return (
        <>
            
        </>
    )
  }
}
const mapStateToProps=({dashboard})=>{
    return{
        userInfo: dashboard.userinfo,
        userListInfo: dashboard.userlistinfo,
        status: dashboard.status,
        error: dashboard.error,
        loading:dashboard.loading
    }
};
const enhance = compose(
  navigateHook,
  authHook,
  // connect(mapStateToProps,{userInfoAction,userListInfoAction})
)
export default enhance(DashboardScene)
// export default (locationHook(DashboardScene));