import React, { Component } from 'react'
import { Box, FormControl, IconButton, InputAdornment, OutlinedInput, Paper, Skeleton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, Tooltip, Typography } from '@mui/material'
import Header from '../Components/Header'
import SideBar from '../Components/SideBar'
import AlertUser from '../Components/AlertUser'
import OrderMenu from '../Components/OrderMenu'
import { compose } from 'redux'
import themeHook from '../HOK/ThemeHook'
import authHook from '../HOK/AuthHook'
import locationHook from '../HOK/LoctionHook'
import dashboardHook from '../HOK/DashboardHook'
import TableSkeleton from '../Components/TableSkeleton'
import { green, grey, purple, red } from '@mui/material/colors'
import { TbEyeCheck, TbTrash, TbUserEdit ,TbRefresh,TbRefresh<PERSON>lert, TbEdit} from 'react-icons/tb'
import NoDataList from '../Components/NoDataList'
import AlertNotes from '../Components/AlertNotes'
import { HiMinusCircle, HiPlusCircle } from 'react-icons/hi'
import InventoryAdd from '../Components/inventory/InventoryAdd'
import InventoryRemove from '../Components/inventory/InventoryRemove'
import InventoryReorder from '../Components/inventory/InventoryReorder'
import { ColorButton } from '../Components/ColorButton'
// import { Minimize, PlusOne } from '@mui/icons-material'
import DoneIcon from '@mui/icons-material/Done';
import { CancelSharp } from '@mui/icons-material'
import Button from '@mui/joy/Button';
import { CircularProgress } from '@mui/joy';
import { diff_months_count, formatDate } from '../utils/functions'
import InventoryEdit from '../Components/inventory/InventoryEdit'
import FilterDate from '../Components/FilterDate'
const head = [
  { name: 'product', style: '30' },
  { name: 'SKU(Manufacturer Code)', style: '10' },
  { name: 'QTY', style: '10' },
  { name: 'Min QTY', style: '10' },
  { name: 'notes', style: '20' },
  { name: 'action', style: '15' },
];
class InventoryScene extends Component {
    constructor(props){
        super(props)
        this.state={
          theme:props.theme,
          auth:props.auth,
          dashboard:props.dashboard,
          tableHeight:(this.props.theme.windowDimensions.height*85)/100,
          showAlert:false,
          rowsItem:Object()|null,
          sendForAPI:false,
          statusAdd:false,
          statusEdit:false,
          statusDelete:false,
          statusOrderAdd:false,
          statusFilter:false,
          infoAlert:{ msg: '', status: undefined },
          inventoriesTableData:[],
          isLoadingInventoties:true,
          qty:0,
          min_qty:0,
          notes:'',
          changeQty:false,
          changeMinQty:false,
          changeQtyRow:Object()|null,
          changeMinQtyRow:Object()|null,
          indexChange:[],
          statusNotedEdit:false
          // rowBackgroundColorController:false,
          // rowBackgroundColor:red[700],
        }
        this.handleDataChanged=this.handleDataChanged.bind(this)
        this.pushFilter=this.pushFilter.bind(this)
        this.handleStatus=this.handleStatus.bind(this)
        this.showAlertNotes=this.showAlertNotes.bind(this)
        this.handleCloseModal=this.handleCloseModal.bind(this)
    }
    delay = ms => new Promise(
      resolve => setTimeout(resolve, ms)
    );
    async componentDidMount(){
      this.setState({
        theme:this.props.theme,
        auth:this.props.auth,
        dashboard:this.props.dashboard,
      })
      if (this.props.auth.user.is_admin){
        if(this.props.auth.selectUser){
          await this.props.dashboard.getInventory(this.props.auth.token,this.props.auth.selectUser.id,this.handleDataChanged)
          // this.setState({
          //   inventoriesTableData:await this.state.inventoriesTableData,
          // })
        }
      }
      else{
        let user_id=0
        if(this.props.auth.user.user_group==3){
          user_id=this.props.auth.user.id
        }else if(this.props.auth.user.user_group==4){
          user_id=this.props.auth.user.ref_admin.id
        }
        await this.props.dashboard.getInventory(this.props.auth.token,user_id,this.handleDataChanged)
        // this.setState({
        //   inventoriesTableData:await this.state.inventoriesTableData,
        // })
      }
      setInterval(async () => {
        if (this.state.sendForAPI){
          console.log("sendforApi")
          let user_id=""
          if (this.props.auth.user.is_admin){
            user_id=this.props.auth.selectUser.id
          }else{
            user_id=this.props.auth.user.id
          }
          await this.props.dashboard.getInventory(this.props.auth.token,user_id,this.handleDataChanged)
          this.setState({
            // inventoriesTableData:await this.state.inventoriesTableData,
            isLoadingInventoties:true,
          })
          this.shouldComponentUpdate=true
          this.forceUpdate()
        }
        this.setState({
          sendForAPI:false,
          isLoadingInventoties:false,
        })
    }, 3000);
      this.setState({
        isLoadingInventoties:false,
      })
      this.shouldComponentUpdate=true
      this.forceUpdate()
    }
    async handleUpdateInventory(inventoryData){
      let response=await this.state.dashboard.updateInventory(this.props.auth.token,inventoryData)
      if (response.status!=200) {
          this.setState({
            sendForAPI:true,
            infoAlert:{
              msg: `Error updating inventory: ${response.msg}`,
              status: false,
            }
          })
          this.showAlertNotes();
      } else {
          if (response.msg == 'success') {
              // refetchOrder();
              this.setState({
                sendForAPI:true,
                infoAlert:{
                  msg: 'inventory updated successfully',
                  status: true,
                }
              })
              this.showAlertNotes();
              this.handleCloseModal();
          } else {
            this.setState({
              sendForAPI:true,
              infoAlert:{
                  msg: `Failed to update inventory: ${response.msg}`,
                  status: false,
                }
            })
            this.showAlertNotes();
          }
      }
    }
    async handleUpdateNoted(inventoryData){
      this.setState({
        statusNotedEdit:true
      })
      let response=await this.state.dashboard.updateInventory(this.props.auth.token,inventoryData)
      if (response.status!=200) {
          this.setState({
            sendForAPI:true,
            infoAlert:{
              msg: `Error updating inventory: ${response.msg}`,
              status: false,
            },
            statusNotedEdit:false
          })
          this.showAlertNotes();
      } else {
          if (response.msg == 'success') {
              // refetchOrder();
              this.setState({
                sendForAPI:true,
                infoAlert:{
                  msg: 'inventory updated successfully',
                  status: true,
                },
                statusNotedEdit:false
              })
              this.showAlertNotes();
              this.handleCloseModal();
          } else {
            this.setState({
              sendForAPI:true,
              infoAlert:{
                  msg: `Failed to update inventory: ${response.msg}`,
                  status: false,
                },
                statusNotedEdit:false
            })
            this.showAlertNotes();
          }
      }
      this.setState({
        statusNotedEdit:false
      })
    }
    handleDataChanged = (inventoryData) => {
      this.setState({
        inventoriesTableData:inventoryData,
        isLoadingInventoties:true,

      })
    }
    pushFilter(value){
      if(value==1){
        this.setState({
          inventoriesTableData:this.props.dashboard.inventoriesTableData
        })
      }else if(value==2){
        console.log(this.props.dashboard.sortedByExpiration)
        this.setState({
          inventoriesTableData:this.props.dashboard.sortedByExpiration
        })
      }else if(value==3){
        console.log(this.props.dashboard.sortedByQty)
        this.setState({
          inventoriesTableData:this.props.dashboard.sortedByQty
        })
      }else if(value==4){
        console.log(this.props.dashboard.sortedByExpirationAndQty)
        this.setState({
          inventoriesTableData:this.props.dashboard.sortedByExpirationAndQty
        })
      }
    }
    handleStatus(value,rowsItem,inventory){
      if(value===1){
        this.setState({
          statusAdd:true,
          // rowsItem:rowsItem
        })
      }else if(value===2){
        this.setState({
          statusEdit:true,
          rowsItem:rowsItem,
          indexChange:inventory
        })
      }
      else if(value===3){
        this.setState({
          statusOrderAdd:true,
          rowsItem:rowsItem
        })
      }else if(value===4){
        this.setState({
          statusDelete:true,      
          rowsItem:rowsItem
        })
      }else if(value==5){
        this.setState({
          statusFilter:true,      
          // rowsItem:rowsItem
        })
      }
    }
    showAlertNotes(){
      this.setState({
        showAlert:true
      })
      setTimeout(() => {
        this.setState({
          showAlert:false,
          infoAlert:{ msg: '', status: undefined },
        })
      }, 3000);
    }
    handleCloseModal(){
      this.setState({
        statusAdd:false,
        statusEdit:false,
        statusDelete:false,
        statusOrderAdd:false,
      })
      this.shouldComponentUpdate=true
      this.forceUpdate()
    }
  render() {
    return (
      <>
        {this.state.showAlert && (
          <AlertNotes
            message={this.state.infoAlert.msg}
            action={this.state.infoAlert.status}
          />
        )}
        {this.state.statusAdd && (
          <InventoryAdd
            onClose={this.handleCloseModal}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
                })
              }
            }
          />
        )}
        {this.state.statusEdit && (
          <InventoryEdit
            onClose={this.handleCloseModal}
            showAlertNotes={this.showAlertNotes}
            item={this.state.rowsItem}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
                })
              }
            }
          />
        )}
        {this.state.statusDelete && (
          <InventoryRemove
            onClose={this.handleCloseModal}
            item={this.state.rowsItem}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
          />
        )}
        {this.state.statusOrderAdd && (
          <InventoryReorder
            item={this.state.rowsItem}
            onClose={this.handleCloseModal}
            showAlertNotes={this.showAlertNotes}
            setInfoAlert={(InfoAlert)=>{this.setState({infoAlert:InfoAlert})}}
            refetchOrder={(sendForAPI)=>{this.setState({
              sendForAPI:sendForAPI
            })}}
              // onFormInsert={this.handleReorderInventory}
          />
        )}
        {/* {this.state.statusFilter &&(
          <FilterDate 


          />
        )} */}
        <Box
          sx={{
            width:'100%',
            height:'100%',
            display:'block',
            // backgroundColor:"#F3F4F6"
          }}
        >
          <OrderMenu handleStatus={this.handleStatus} applyFilter={this.pushFilter}/>
          {this.state.isLoadingInventoties ? 
            (
              <>
                <Box 
                  sx={{
                    display:'flex',
                    flexDirection:'row',
                    gap:1,
                    // textAlign:'center',
                    // alignItems:'center',
                    minHeight:this.state.tableHeight,
                    height:'100%',
                    width:'100%',
                    paddingTop:0,
                    marginTop:0,
                    backgroundColor:this.props.theme.selectTheme.background_2,
                    // paddingY:5,
                    // marginBottom:3,
                  }}
                >
                  <TableSkeleton />
                </Box>
              </>
            ):(this.state.inventoriesTableData && (
              <Box
                sx={{
                  height:'100%',
                  borderTop:'1px solid #e5e7eb',
                  backgroundColor:this.props.theme.selectTheme.background_3
                }}
              > 
                <Paper 
                  sx={{ 
                    width: '100%',
                    height:'100%',
                  }}
                >
                  <TableContainer 
                    sx={{ 
                      height:'100%',
                      minHeight: `${this.state.tableHeight}px` ,
                      backgroundColor:this.props.theme.selectTheme.background,
                      color: this.props.theme.selectTheme.fontColor
                    }}
                  >
                    <Table stickyHeader aria-label="sticky table">
                      <TableHead sx={{
                          marginBottom:2,
                          backgroundColor:this.props.theme.selectTheme.tableheaderBg,
                          color: this.props.theme.selectTheme.headerfontColor
                      }}>
                        <TableRow
                          sx={{
                            // backgroundColor:this.props.theme.selectTheme.headerBg,
                            // color: this.props.theme.selectTheme.headerfontColor
                          }}
                        >
                          {head.map((item,index) => (
                            <TableCell
                              key={index}
                              align={item.align}
                              sx={{
                                width:`${item.style}%`,
                                border:`1px ${grey[200]} solid`,
                                textAlign:'center',
                                backgroundColor:this.props.theme.selectTheme.background,
                                color: this.props.theme.selectTheme.fontColor
                              }}
                            >
                              {item.name}
                            </TableCell>
                          ))}
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {this.state.inventoriesTableData.length ? (
                          Object.keys(this.state.inventoriesTableData)
                            .map((inventory,index) => {
                              return (
                                  <TableRow
                                    key={index}
                                  >
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.state.inventoriesTableData[inventory].qty<this.state.inventoriesTableData[inventory].min_qty?
                                        ( 
                                          this.props.theme.selectTheme.lowProductRowColor
                                        ):(
                                          this.props.theme.selectTheme.background_2
                                        ),
                                        color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                      key={inventory.id}
                                      >
                                        <Box
                                          sx={{
                                            display: 'flex',
                                            flexDirection:'column',
                                            justifyContent:'center',
                                            alignItems:'center',
                                            textAlign:'center',
                                            gap:2
                                          }}
                                        >
                                          <Typography>
                                            {this.state.inventoriesTableData[inventory].product.name}
                                          </Typography>
                                          {/* {console.log(new Date().toISOString().slice(0, 10))} */}
                                          {/* {console.log(this.state.inventoriesTableData[inventory].expiration_date)} */}
                                          <Box 
                                            sx={{
                                              width:'30%',
                                              display:'flex',
                                              displayDirection:'row',
                                              justifyContent:'center',
                                              backgroundColor:(diff_months_count(this.state.inventoriesTableData[inventory].expiration_date))?
                                              ( 
                                                this.props.theme.selectTheme.expiringProductRowColor
                                              ):(
                                                'inherit'
                                              ),
                                            }}
                                          >
                                            <Typography
                                              sx={{
                                                fontSize:'11px'
                                              }}
                                            >
                                              exp :
                                            </Typography>
                                            <Typography
                                              sx={{
                                                fontSize:'11px',
                                                paddingLeft:1
                                              }}
                                            >
                                              {this.state.inventoriesTableData[inventory].expiration_date
                                                ? formatDate(
                                                    'Amm-dd-yyyy',
                                                    this.state.inventoriesTableData[inventory].expiration_date
                                                  ): 'Not Set'}
                                            </Typography>
                                          </Box>
                                        </Box>
                                    </TableCell>
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.state.inventoriesTableData[inventory].qty<this.state.inventoriesTableData[inventory].min_qty?
                                        ( 
                                          this.props.theme.selectTheme.lowProductRowColor
                                        ):(
                                          this.props.theme.selectTheme.background_2
                                        ),
                                        color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                      key={inventory.id}
                                      >
                                        {this.state.inventoriesTableData[inventory].product.s_k_u}
                                      </TableCell>
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.state.inventoriesTableData[inventory].qty<this.state.inventoriesTableData[inventory].min_qty?
                                        ( 
                                          this.props.theme.selectTheme.lowProductRowColor
                                        ):(
                                          this.props.theme.selectTheme.background_2
                                        ),
                                        color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                      key={inventory.id}
                                      >
                                        <Box
                                          sx={{
                                            width:'100%',
                                            height:'100%',
                                            display:'flex',
                                            flexDirection:'row',
                                            alignItems:'center'
                                          }}
                                        >
                                          <Tooltip
                                            content='decreese'
                                          >
                                            <IconButton
                                              size='small'
                                              sx={{
                                                color:red[400],
                                              }}
                                              pill
                                              onClick={() => {
                                                let qty=this.state.inventoriesTableData[inventory].qty
                                                this.state.inventoriesTableData[inventory].qty=qty-1
                                                let inventoryData={
                                                  id:this.state.inventoriesTableData[inventory].id,
                                                  admin_id:null,
                                                  product:this.state.inventoriesTableData[inventory].product.name,
                                                  s_k_u:this.state.inventoriesTableData[inventory].product.s_k_u,
                                                  qty:this.state.inventoriesTableData[inventory].qty,
                                                  min_qty:this.state.inventoriesTableData[inventory].min_qty,
                                                  notes:this.state.inventoriesTableData[inventory].notes
                                                }
                                                if (this.props.auth.user.user_group==2){
                                                  inventoryData.admin_id=this.props.auth.selectUser.id
                                                }else if(this.props.auth.user.user_group==3){
                                                  inventoryData.admin_id=this.props.auth.user.id
                                                }else if(this.props.auth.user.user_group==4){
                                                  inventoryData.admin_id=this.props.auth.user.ref_admin.id
                                                }
                                                else{
                                            
                                                }
                                                this.handleUpdateInventory(inventoryData)
                                              }}
                                            >
                                              <HiMinusCircle/>
                                            </IconButton>
                                          </Tooltip>
                                          <TextField
                                            // contentEditable={'false'}
                                            size='medium'
                                            sx={{
                                              alignItems:'center'
                                            }}
                                            inputProps={{
                                              style: { textAlign: "center" }
                                            }}
                                            onChange={(e)=>{
                                              let qty=e.target.value
                                              this.state.inventoriesTableData[inventory].qty=qty
                                              let inventoryData={
                                                id:this.state.inventoriesTableData[inventory].id,
                                                admin_id:null,
                                                product:this.state.inventoriesTableData[inventory].product.name,
                                                s_k_u:this.state.inventoriesTableData[inventory].product.s_k_u,
                                                qty:this.state.inventoriesTableData[inventory].qty,
                                                min_qty:this.state.inventoriesTableData[inventory].min_qty,
                                                notes:this.state.inventoriesTableData[inventory].notes
                                              }
                                              if (this.props.auth.user.user_group==2){
                                                inventoryData.admin_id=this.props.auth.selectUser.id
                                              }else if(this.props.auth.user.user_group==3){
                                                inventoryData.admin_id=this.props.auth.user.id
                                              }else if(this.props.auth.user.user_group==4){
                                                inventoryData.admin_id=this.props.auth.user.ref_admin.id
                                              }
                                              else{
                                          
                                              }
                                              this.handleUpdateInventory(inventoryData)
                                            }}
                                            value={this.state.inventoriesTableData[inventory].qty}
                                          />
                                          <Tooltip
                                            content='increase'
                                          >
                                            <IconButton
                                              size='small'
                                              sx={{
                                                color:green[400],
                                              }}
                                              pill
                                              onClick={() => {
                                                let qty=this.state.inventoriesTableData[inventory].qty
                                                this.state.inventoriesTableData[inventory].qty=qty+1
                                                let inventoryData={
                                                  id:this.state.inventoriesTableData[inventory].id,
                                                  admin_id:null,
                                                  product:this.state.inventoriesTableData[inventory].product.name,
                                                  s_k_u:this.state.inventoriesTableData[inventory].product.s_k_u,
                                                  qty:this.state.inventoriesTableData[inventory].qty,
                                                  min_qty:this.state.inventoriesTableData[inventory].min_qty,
                                                  notes:this.state.inventoriesTableData[inventory].notes
                                                }
                                                if (this.props.auth.user.user_group==2){
                                                    inventoryData.admin_id=this.props.auth.selectUser.id
                                                  }else if(this.props.auth.user.user_group==3){
                                                    inventoryData.admin_id=this.props.auth.user.id
                                                  }else if(this.props.auth.user.user_group==4){
                                                    inventoryData.admin_id=this.props.auth.user.ref_admin.id
                                                  }
                                                  else{
                                              
                                                  }
                                                this.handleUpdateInventory(inventoryData)
                                              }}
                                            >
                                              <HiPlusCircle/>
                                            </IconButton>
                                          </Tooltip>
                                        </Box>
                                    </TableCell>
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.state.inventoriesTableData[inventory].qty<this.state.inventoriesTableData[inventory].min_qty?
                                        ( 
                                          this.props.theme.selectTheme.lowProductRowColor
                                        ):(
                                          this.props.theme.selectTheme.background_2
                                        ),
                                        color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                      key={inventory.id}
                                      >
                                        <Box
                                          sx={{
                                            display:'flex',
                                            flexDirection:'row',
                                            alignItems:'center',
                                            // justifyContent:'space-between'
                                          }}
                                        >
                                          <Tooltip
                                            content='decrease'
                                        >
                                            <IconButton
                                              size='small'
                                              sx={{
                                                color:red[400],
                                                // paddingX:0,
                                              }}
                                              pill
                                              onClick={() => {
                                                let min_qty=this.state.inventoriesTableData[inventory].min_qty
                                                this.state.inventoriesTableData[inventory].min_qty=min_qty-1
                                                let inventoryData={
                                                  id:this.state.inventoriesTableData[inventory].id,
                                                  admin_id:null,
                                                  product:this.state.inventoriesTableData[inventory].product.name,
                                                  s_k_u:this.state.inventoriesTableData[inventory].product.s_k_u,
                                                  qty:this.state.inventoriesTableData[inventory].qty,
                                                  min_qty:this.state.inventoriesTableData[inventory].min_qty,
                                                  notes:this.state.inventoriesTableData[inventory].notes
                                                }
                                                if (this.props.auth.user.user_group==2){
                                                  inventoryData.admin_id=this.props.auth.selectUser.id
                                                }else if(this.props.auth.user.user_group==3){
                                                  inventoryData.admin_id=this.props.auth.user.id
                                                }else if(this.props.auth.user.user_group==4){
                                                  inventoryData.admin_id=this.props.auth.user.ref_admin.id
                                                }
                                                else{
                                            
                                                }
                                                this.handleUpdateInventory(inventoryData)
                                              }}
                                            >
                                              <HiMinusCircle/>
                                            </IconButton>
                                          </Tooltip>
                                          <TextField
                                            // contentEditable={'false'}
                                            size='medium'
                                            sx={{
                                              alignItems:'center',
                                              textAlign:'center',
                                              justifyContent:'center'
                                            }}
                                            inputProps={{
                                              style: { textAlign: "center" }
                                            }}
                                            onChange={(e)=>{
                                              let min_qty=e.target.value
                                              this.state.inventoriesTableData[inventory].min_qty=min_qty
                                              let inventoryData={
                                                id:this.state.inventoriesTableData[inventory].id,
                                                admin_id:null,
                                                product:this.state.inventoriesTableData[inventory].product.name,
                                                s_k_u:this.state.inventoriesTableData[inventory].product.s_k_u,
                                                qty:this.state.inventoriesTableData[inventory].qty,
                                                min_qty:this.state.inventoriesTableData[inventory].min_qty,
                                                notes:this.state.inventoriesTableData[inventory].notes
                                              }
                                              if (this.props.auth.user.user_group==2){
                                                inventoryData.admin_id=this.props.auth.selectUser.id
                                              }else if(this.props.auth.user.user_group==3){
                                                inventoryData.admin_id=this.props.auth.user.id
                                              }else if(this.props.auth.user.user_group==4){
                                                inventoryData.admin_id=this.props.auth.user.ref_admin.id
                                              }
                                              else{
                                          
                                              }
                                              this.handleUpdateInventory(inventoryData)
                                            }}
                                            value={this.state.inventoriesTableData[inventory].min_qty}
                                          />
                                          <Tooltip
                                            content='increase'
                                          >
                                            <IconButton
                                              size='small'
                                              sx={{
                                                color:green[400],
                                              }}
                                              pill
                                              onClick={() => {
                                                let min_qty=this.state.inventoriesTableData[inventory].min_qty
                                                this.state.inventoriesTableData[inventory].min_qty=min_qty+1
                                                let inventoryData={
                                                  id:this.state.inventoriesTableData[inventory].id,
                                                  admin_id:null,
                                                  product:this.state.inventoriesTableData[inventory].product.name,
                                                  s_k_u:this.state.inventoriesTableData[inventory].product.s_k_u,
                                                  qty:this.state.inventoriesTableData[inventory].qty,
                                                  min_qty:this.state.inventoriesTableData[inventory].min_qty,
                                                  notes:this.state.inventoriesTableData[inventory].notes
                                                }
                                                if (this.props.auth.user.user_group==2){
                                                  inventoryData.admin_id=this.props.auth.selectUser.id
                                                }else if(this.props.auth.user.user_group==3){
                                                  inventoryData.admin_id=this.props.auth.user.id
                                                }else if(this.props.auth.user.user_group==4){
                                                  inventoryData.admin_id=this.props.auth.user.ref_admin.id
                                                }
                                                else{
                                            
                                                }
                                                this.handleUpdateInventory(inventoryData)
                                              }}
                                            >
                                              <HiPlusCircle/>
                                            </IconButton>
                                          </Tooltip>
                                        </Box>
                                    </TableCell>
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',

                                        backgroundColor:this.state.inventoriesTableData[inventory].qty<this.state.inventoriesTableData[inventory].min_qty?
                                        ( 
                                          this.props.theme.selectTheme.lowProductRowColor
                                        ):(
                                          this.props.theme.selectTheme.background_2
                                        ),
                                        color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                      key={inventory.id}
                                      >
                                        <FormControl 
                                        fullWidth sx={{border:'none' }}>
                                        {/* <InputLabel htmlFor="outlined-adornment-amount">Total Budget</InputLabel> */}
                                        <OutlinedInput
                                          sx={{
                                            border:'none'
                                          }}
                                          value={this.state.inventoriesTableData[inventory].notes}
                                          onChange={(e) =>{
                                              this.state.inventoriesTableData[inventory].notes=e.target.value
                                              this.setState({
                                                indexChange:inventory
                                              })
                                            }
                                          }
                                          id="outlined-adornment-amount"
                                          endAdornment={
                                            <InputAdornment
                                              position="end">
                                                {(this.state.statusNotedEdit&& this.state.indexChange==inventory)?(
                                                  <Button 
                                                    sx={{
                                                      marginRight:1
                                                    }}
                                                    startDecorator={
                                                      <CircularProgress variant="solid"/>
                                                    }
                                                  >
                                                    Loading…
                                                  </Button>
                                                ):(
                                                  <IconButton
                                                    onClick={()=>{
                                                      let inventoryData={
                                                        id:this.state.inventoriesTableData[inventory].id,
                                                        admin_id:null,
                                                        product:this.state.inventoriesTableData[inventory].product.name,
                                                        s_k_u:this.state.inventoriesTableData[inventory].product.s_k_u,
                                                        qty:this.state.inventoriesTableData[inventory].qty,
                                                        min_qty:this.state.inventoriesTableData[inventory].min_qty,
                                                        notes:this.state.inventoriesTableData[inventory].notes
                                                      }
                                                      if (this.props.auth.user.user_group==2){
                                                        inventoryData.admin_id=this.props.auth.selectUser.id
                                                      }else if(this.props.auth.user.user_group==3){
                                                        inventoryData.admin_id=this.props.auth.user.id
                                                      }else if(this.props.auth.user.user_group==4){
                                                        inventoryData.admin_id=this.props.auth.user.ref_admin.id
                                                      }
                                                      else{
                                                  
                                                      }
                                                      this.handleUpdateNoted(inventoryData)
                                                      // this.handleStatus(2,this.state.inventoriesTableData[inventory],inventory)
                                                    }}
                                                  >
                                                    <DoneIcon
                                                      color="success"
                                                    />
                                                  </IconButton>
                                                )}
                                            </InputAdornment>
                                          }
                                          />
                                      </FormControl>
                                    </TableCell>
                                    <TableCell 
                                      sx={{
                                        px:1,
                                        py:2,
                                        border:`1px ${grey[200]} solid`,
                                        textAlign:'center',
                                        backgroundColor:this.state.inventoriesTableData[inventory].qty<this.state.inventoriesTableData[inventory].min_qty?
                                        ( 
                                          this.props.theme.selectTheme.lowProductRowColor
                                        ):(
                                          this.props.theme.selectTheme.background_2
                                        ),
                                        color: this.props.theme.selectTheme.fontColor_3
                                      }}
                                      key={inventory.id}
                                    >
                                      {this.state.inventoriesTableData[inventory].id && (
                                        <Box
                                          sx={{
                                            display:'flex',
                                            flexDirection:"row",
                                            justifyContent:'space-evenly',
                                          }} 
                                          >
                                              <Tooltip
                                                title="Edit"
                                                content='Paid Order'
                                              >
                                                <Button
                                                  size='xs'
                                                  color='white'
                                                  pill
                                                  inline='true'
                                                  onClick={() => {
                                                    this.handleStatus(2,this.state.inventoriesTableData[inventory])
                                                  }}
                                                >
                                                  <TbEdit color={purple[500]} />
                                                </Button>
                                              </Tooltip>
                                             <Tooltip
                                                title="Delete"
                                                content='Delete Order'
                                                // style='light'
                                              >
                                                <Button
                                                  size='xs'
                                                  pill
                                                  color='white'
                                                  inline='true'
                                                  onClick={() => {
                                                    this.handleStatus(4,this.state.inventoriesTableData[inventory])
                                                  }}
                                                >
                                                  <TbTrash color={red[600]} />
                                                </Button>
                                              </Tooltip>
                                            <Tooltip
                                                title="ReOrder"
                                                content='Reorder'
                                            >
                                                <Button
                                                  size='xs'
                                                  pill
                                                  color='white'
                                                  inline='true'
                                                  onClick={() => {
                                                    this.handleStatus(3,this.state.inventoriesTableData[inventory])
                                                  }}
                                                >
                                                    <TbRefresh  color={green[600]} />
                                                </Button>
                                            </Tooltip>
                                        </Box>
                                      )}
                                    </TableCell>
                                  </TableRow>
                              )
                            }
                          )
                        ):(
                          <NoDataList colSpan={head.length}/>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Paper>
              </Box>
            ))
          }
        </Box>
      </>
    )
  }
}
export default compose(
    themeHook,
    authHook,
    locationHook,
    dashboardHook
  )(InventoryScene)