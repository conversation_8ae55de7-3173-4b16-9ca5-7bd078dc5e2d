import {
    SIGNIN_RESULT,
} from '../types';

const SIGIN_STATE = {
    result: [],
    status: 200,
    // searchResults:[],
    // contactStatus: '',
    error: '',
    loading: false
};

export default (state = SIGIN_STATE, action) => {
    switch (action.type) {
        case SIGNIN_RESULT:
            return { ...state, result: action.payload,status:action.status };
        default:
            return state;
    }
};
