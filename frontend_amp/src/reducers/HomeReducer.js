// import {
//     HOME_SEARCH_RESULTS,
//     HOME_WEB_POSTS,
//     HOME_INSTAGRAM_POSTS,
//     HOME_PRODUCTS,
//     // HOME_CONTACT_STATUS
// } from '../types';

// const INITIAL_STATE = {
//     webPosts: [],
//     products: [],
//     instagramPosts: [],
//     // searchResults:[],
//     // contactStatus: '',
//     error: '',
//     loading: false
// };

// export default (state = INITIAL_STATE, action) => {
//     switch (action.type) {
//         case HOME_WEB_POSTS:
//             return { ...state, webPosts: action.payload };
//         case HOME_PRODUCTS:
//             return { ...state, products: action.payload };
//         case HOME_INSTAGRAM_POSTS:
//             return { ...state, instagramPosts: action.payload };
//         // case HOME_SEARCH_RESULTS:
//         //     return { ...state, searchResults: action.payload };
//         // case HOME_CONTACT_STATUS:
//         //     return { ...state, contactStatus: action.payload };
//         default:
//             return state;
//     }
// };
