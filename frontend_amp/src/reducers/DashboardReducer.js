import {
    USERINFO,
    USERLISTINFO,
} from '../types';

const DASHBOARD_STATE = {
    userinfo: [],
    userlistinfo: [],
    status: 200,
    // searchResults:[],
    // contactStatus: '',
    error: '',
    loading: false
};

export default (state = DASHBOARD_STATE, action) => {
    switch (action.type) {
        case USERINFO:
            return { ...state, userinfo: action.payload,status:action.status };
        case USERLISTINFO:
            return { ...state, userlistinfo: action.payload,status:action.status };
        default:
            return state;
    }
};
