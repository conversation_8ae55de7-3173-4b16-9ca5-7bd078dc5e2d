// Environment-specific configuration
import { DEV_BASE_URL, BASE_URL, API_URL as ENV_API_URL, WS_URL as ENV_WS_URL, DEBUG } from './Environments';

// Determine if we're in development or production
const isDevelopment = process.env.NODE_ENV === 'development';

// Set the API URL based on environment
export const API_URL = ENV_API_URL;

// Other configuration settings
export const CONFIG = {
  // API URL with fallback
  API_URL: ENV_API_URL,
  
  // WebSocket URL with fallback
  wsUrl: ENV_WS_URL,
  
  // API request timeout in milliseconds
  requestTimeout: 30000,
  
  // Pagination defaults
  defaultPageSize: 10,
  
  // Authentication
  tokenStorageKey: 'amp.amedicalpartner.com.token',
  refreshTokenStorageKey: 'amp.amedicalpartner.com.refreshToken',
  
  // Feature flags
  features: {
    enableNotifications: true,
    enableChat: true,
    enableDarkMode: true
  },
  
  // Debug mode
  debug: DEBUG
};

// Log configuration in debug mode
if (CONFIG.debug) {
  console.log('App Configuration:');
  console.log('- API_URL:', CONFIG.API_URL);
  console.log('- wsUrl:', CONFIG.wsUrl);
  console.log('- isDevelopment:', isDevelopment);
}

// Add a debug function to test WebSocket connection
export const testWebSocketConnection = () => {
  console.log('Testing WebSocket connection...');
  const wsUrl = `${CONFIG.wsUrl}/ws/chat/test`;
  console.log(`Connecting to: ${wsUrl}`);
  
  try {
    const ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
      console.log('WebSocket connection successful!');
      ws.send(JSON.stringify({ type: 'ping', message: 'Hello server!' }));
    };
    
    ws.onmessage = (event) => {
      console.log('Received message:', event.data);
    };
    
    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
    
    ws.onclose = (event) => {
      console.log(`WebSocket closed with code ${event.code}:`, event.reason);
    };
    
    return ws;
  } catch (error) {
    console.error('Failed to create WebSocket connection:', error);
    return null;
  }
};

export default CONFIG;
