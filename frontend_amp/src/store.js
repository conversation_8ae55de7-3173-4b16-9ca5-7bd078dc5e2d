import { configureStore } from '@reduxjs/toolkit'
import SigninReducer from './reducers/SigninReducer'
import OrderReducer from './reducers/OrderReducer' 
import DashboardReducer from './reducers/DashboardReducer' 

// import HomeReducer from './reducers/HomeReducer';
// import SingleProductReducer from './reducers/SingleProductReducer';
// import AdvertisingReducer from './reducers/AdvertisingReducer';
// import AgenciesReducer from './reducers/AgenciesReducer';
// import AgenciesSingleReducer from './reducers/AgenciesSingleReducer';

export default configureStore({
  reducer: {
    signin: SigninReducer,
    order:OrderReducer,
    dashboard:DashboardReducer
    // home: HomeReducer,
    // singleProduct: SingleProductReducer,
    // agencies: AgenciesReducer,
    // agenciessingledetail:AgenciesSingleReducer,
    // realestatesingleagencies:
  },
})
