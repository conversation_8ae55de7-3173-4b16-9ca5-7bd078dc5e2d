// Development environment
export const DEV_BASE_URL = 'https://dev-amp.amedicalpartner.com';

// Production environment
export const BASE_URL = 'https://amp.amedicalpartner.com';

// API URL - can be different from base URL if needed
export const API_URL = process.env.REACT_APP_API_URL || 
  (process.env.NODE_ENV === 'development' ? `${DEV_BASE_URL}/` : `${BASE_URL}/`);

// WebSocket URL - derived from API URL
export const WS_URL = process.env.REACT_APP_WS_URL || 
  (process.env.NODE_ENV === 'development' 
    ? DEV_BASE_URL.replace('https://', 'wss://').replace('http://', 'ws://') 
    : BASE_URL.replace('https://', 'wss://').replace('http://', 'ws://'));

// Debug flag
export const DEBUG = process.env.NODE_ENV === 'development';

// Log environment settings
if (DEBUG) {
  console.log('Environment Settings:');
  console.log('- NODE_ENV:', process.env.NODE_ENV);
  console.log('- BASE_URL:', process.env.NODE_ENV === 'development' ? DEV_BASE_URL : BASE_URL);
  console.log('- API_URL:', API_URL);
  console.log('- WS_URL:', WS_URL);
}
// REACT_APP_ECWID_TOKEN=