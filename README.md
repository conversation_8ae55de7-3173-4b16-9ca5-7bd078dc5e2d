# FastAPI with Django ORM and Admin

## Overview

This is a template repository for implementing the server using Django functionality for the DB area (admin, ORM) and FastAPI for the API area.

## Prerequisites

### Poetry

Dependency management for Python files is done using POETRY.

1. <https://python-poetry.org/docs/#installation>
1. `python -m venv venv`
1. `source venv/bin/activate`
2. `pip install --upgrade pip` (if needed)
3. `poetry install`

### pre-commit (for developers)

This tool defines commands to be executed before committing. It is already defined in `.pre-commit-config.yaml`, so you need to configure it in your environment. Please follow the steps below.

1. <https://pre-commit.com/#installation>
1. `pre-commit install`

## Usage

1. Clone this repository

   ```sh
    git clone https://github.com/kathmandu777/fastapi-django-template.git
    ```

1. Create fastapi.env with reference to fastapi.env.tmpl

1. Build

    ```sh
    docker-compose build
    ```
https://amp.amedicalpartner.com
sudo docker compose -f docker-compose.prod.yml up -d
sudo docker compose -f docker-compose.dev.yml up -d


1. Dependency install
    for Production
    ```sh
     sudo docker compose -f docker-compose.prod.yml run --rm fastapi_prod poetry install
    ```
    for development
    sudo docker-compose -f docker-compose.dev.yml run --rm fastapi_dev poetry install

1. Setup Static Files

    ```sh
    sudo docker compose -f docker-compose.prod.yml run --rm fastapi_prod poetry run python manage.py collectstatic --noinput

    ```
2. Setup Makemigrations

    sudo docker compose -f docker-compose.prod.yml run --rm fastapi_prod poetry run python manage.py makemigrations
    
    sudo docker-compose -f docker-compose.dev.yml run --rm fastapi_dev poetry run python manage.py makemigrations

1. Migrate

    ```sh
    sudo docker compose -f docker-compose.prod.yml run --rm fastapi_prod poetry run python manage.py migrate

    ```
    sudo docker-compose -f docker-compose.dev.yml run --rm fastapi_dev poetry run python manage.py migrate

1. Create Super User for Admin Page

    ```sh
    sudo docker compose -f docker-compose.prod.yml run --rm fastapi_prod poetry run python manage.py createsuperuser
    ```
    sudo docker-compose -f docker-compose.dev.yml run --rm fastapi_dev poetry run python manage.py migrate
1. Start Server

    ```sh
    sudo docker compose -f docker-compose.prod.yml up
    ```
    sudo docker-compose -f docker-compose.dev.yml up
## alias for frequently used commands

```sh
source alias.sh
```
