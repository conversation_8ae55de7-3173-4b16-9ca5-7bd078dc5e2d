version: "3.7"
services:
  fastapi_dev:
    container_name: dev_backend_amp
    build:
      context: "./backend_amp"
      target: "dev"
    environment:
      DEBUG : "true"
      PYTHONUNBUFFERED : 1
#      CELERY_BROKER : redis://broker:6379/0
#      CELERY_BACKEND : redis://broker:6379/
#      - APP_BASIC_AUTH_PASSWORD=adPswd12*
#      - APP_BASIC_AUTH_USER=admin
#      - APP_TOKEN_AUTH=NHEC_UTILITY
#      - VTN_API_URL=vtn_app:8000
#      - VTN_API_TOKEN=NHECAPP_UTILITY
#      - SQL_PORT=5000
      DB_NAME: amp_dev
      DB_USER: amp_dev
      DB_PASSWORD: amp_dev_pass_123456
      DB_HOST: postgres_dev
      DB_PORT: 5432
    ports:
      - 4000:4000
      - 4001:4001

    depends_on:
        - postgres_dev
#        - celery-worker
#        - broker
    env_file:
      - backend_amp/fastapi.env
    links:
      - postgres_dev
    command: ./scripts/dev/runlocalserver.sh
#    command: ./scripts/dev/startserver.sh
    volumes:
      - ./backend_amp:/src
    networks:
      - app-web-net
#  app_test:
#    build: .
#    command: python manage.py test
#    container_name: app_test
#    environment:
#      - DEBUG=True
#      - PYTHONUNBUFFERED=1
#    volumes:
#      - .:/code
#    depends_on:
#      - db
  postgres_dev:
    container_name: dev_backend_amp_postgres
    image: postgres:12
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: amp_dev
      POSTGRES_PASSWORD: amp_dev_pass_123456
      POSTGRES_DB: amp_dev
    ports:
      - 54432:5432
    networks:
      - app-web-net
#  celery-worker:
#    build: .
#    command: celery -A app worker -l DEBUG
#    depends_on:
#      - postgres_dev
#      - broker
#    environment:
#      CELERY_BROKER_URL: redis://broker:6379/0
#    networks:
#      - app-web-net
#  broker:
#      image: redis:6-alpine
#      ports:
#        - 6379:6379
#      networks:
#        - app-web-net

volumes:
  postgres_data_dev:

networks:
  app-web-net:
    driver: bridge