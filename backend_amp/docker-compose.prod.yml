version: "3.7"
services:
  fastapi_prod:
    container_name: prod_backend_amp
    build:
      context: "./backend_amp"
      target: "prod"
    volumes:
      - ./backend_amp:/src
    ports:
      - 8000:8000
      - 8001:8001
    environment:
      DB_NAME: amp_prod
      DB_USER: amp_prod
      DB_PASSWORD: amp_prod_pass_123456
      DB_HOST: postgres_prod
      DB_PORT: 5432
    env_file:
      - backend_amp/fastapi.env
    links:
      - postgres_prod
#    command: ./scripts/runlocalserver.sh
    command: ./scripts/startserver.sh
  postgres_prod:
    container_name: prod_backend_amp_postgres
    image: postgres:12
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: amp_prod
      POSTGRES_PASSWORD: amp_prod_pass_123456
      POSTGRES_DB: amp_prod
    ports:
      - 5433:5432
volumes:
  postgres_data:
