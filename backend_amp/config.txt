/etc/systemctl/docker-compose.service
[Unit]
Description=amp_docker_service
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=/bin/bash -c "docker compose -f /root/public_html/amp/docker-compose.yml up --detach"
ExecStop=/bin/bash -c "docker compose -f /root/public_html/amp/docker-compose.yml stop"

[Install]
WantedBy=multi-user.target


#The Nginx server instance
server {
    listen 80;
    server_name Amp.amedicalpartner.com;

    location / {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header Host $http_host;
      proxy_set_header X-NginX-Proxy true;
        add_header 'Content-Security-Policy' 'upgrade-insecure-requests';
      proxy_set_header Access-Control-Allow-Origin *;
#add_header Access-Control-Allow-Origin *;
      proxy_pass http://localhost:8000/;
      proxy_redirect off;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection 'upgrade';
      proxy_set_header Host $host;
      proxy_cache_bypass $http_upgrade;
    }
 }
