repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-toml
      - id: check-added-large-files
        args: ["--maxkb=100000"]
      - id: detect-private-key
      - id: no-commit-to-branch
        args: [--branch, master]
      - id: pretty-format-json
        args: [--autofix]
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.8.0.4
    hooks:
      - id: shellcheck
  - repo: https://github.com/myint/autoflake
    rev: v1.5.1
    hooks:
      - id: autoflake
        args:
          - --in-place
          - --remove-unused-variables
          - --remove-all-unused-imports
          - --ignore-init-module-imports
  - repo: https://gitlab.com/pycqa/flake8
    rev: 3.9.2
    hooks:
      - id: flake8
        entry: pflake8
        additional_dependencies:
          [flake8-bugbear, flake8-builtins, pep8-naming, pyproject-flake8]
  - repo: https://github.com/pycqa/isort
    rev: 5.10.1
    hooks:
      - id: isort
  - repo: https://github.com/psf/black
    rev: 22.6.0
    hooks:
      - id: black
        language_version: python3
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v0.971
    hooks:
      - id: mypy
        additional_dependencies:
          - "pydantic"
          - "types-PyYAML"
          - "types-pytz"
        args: ["--no-warn-return-any"]
  - repo: https://github.com/myint/docformatter
    rev: v1.5.0
    hooks:
      - id: docformatter
        args: [--in-place]
  - repo: https://github.com/markdownlint/markdownlint
    rev: v0.11.0
    hooks:
      - id: markdownlint
        args: [-r, ~MD013] # ignore line length of makrdownlint
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.32.2
    hooks:
      - id: markdownlint-fix
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v2.7.1
    hooks:
      - id: prettier
        files: \.(json|yml|yaml)$
        args: ["--write"]
