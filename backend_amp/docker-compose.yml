version: "3.7"
services:
  fastapi_dev:
    image: python:3.10.5
    container_name: dev_backend_amp
    build:
      no_cache: true
      context: "./backend_amp"
      target: "dev"
    volumes:
      - ./backend_amp:/dev
    ports:
      - 4000:4000
      - 4001:4001
    environment:
      DB_NAME: fastapi-django-template
      DB_USER: fastapi
      DB_PASSWORD: fastapi
      DB_HOST: postgres_dev
      DB_PORT: 54432
    env_file:
      - backend_amp/fastapi.env
    links:
      - postgres_dev
    command: ./scripts/runlocalserver.sh
#    command: ./scripts/startserver.sh
  postgres_dev:
    container_name: dev_backend_amp_postgres
    image: postgres:12
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: fastapi
      POSTGRES_PASSWORD: fastapi
      POSTGRES_DB: fastapi-django-template
    ports:
      - 54433:54432
volumes:
  postgres_data:
