"""
Test script to verify imports are working correctly.
Run this with: python test_imports.py
"""

import os
import sys

# Print current environment
print(f"Current directory: {os.getcwd()}")
print(f"Python path: {sys.path}")

try:
    # Try importing the ASGI module
    from config.asgi import fastapi_app, django_app
    print("Successfully imported fastapi_app and django_app from config.asgi")
except ImportError as e:
    print(f"Import error: {e}")
    
    # Try to diagnose the issue
    try:
        import config
        print("Successfully imported config module")
        
        # Check if asgi.py exists
        config_dir = os.path.dirname(config.__file__)
        asgi_path = os.path.join(config_dir, 'asgi.py')
        print(f"Looking for asgi.py at: {asgi_path}")
        print(f"File exists: {os.path.exists(asgi_path)}")
        
    except ImportError as e2:
        print(f"Cannot import config module: {e2}")
        
        # Check directory structure
        print("\nDirectory structure:")
        for root, dirs, files in os.walk('.', topdown=True, maxdepth=3):
            for name in dirs:
                print(os.path.join(root, name))