"""
Test script to verify Celery imports are working correctly.
Run this with: python test_celery_imports.py
"""

import os
import sys

# Print current environment
print(f"Current directory: {os.getcwd()}")
print(f"Python path: {sys.path}")

# Try importing from app.celery
try:
    from app.celery import app as celery_app
    print("Successfully imported celery_app from app.celery")
    print(f"celery_app: {celery_app}")
except ImportError as e:
    print(f"Import error from app.celery: {e}")
    
    # Check if app/celery.py exists
    celery_path = os.path.join('app', 'celery.py')
    print(f"Looking for app/celery.py: {os.path.exists(celery_path)}")
    
    # Check if app/celery/__init__.py exists
    celery_init_path = os.path.join('app', 'celery', '__init__.py')
    print(f"Looking for app/celery/__init__.py: {os.path.exists(celery_init_path)}")

# Try importing from config.celery
try:
    from config.celery import app as config_celery_app
    print("Successfully imported app from config.celery")
    print(f"config_celery_app: {config_celery_app}")
except ImportError as e:
    print(f"Import error from config.celery: {e}")