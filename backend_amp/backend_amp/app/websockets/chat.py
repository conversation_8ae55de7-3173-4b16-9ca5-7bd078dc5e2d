import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from app.models.chat import Conversation, Message, ConversationParticipant
from app.models.user import Admin

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.user_id = self.scope['url_route']['kwargs']['user_id']
        self.user_group_name = f'chat_{self.user_id}'
        
        # Join user group
        await self.channel_layer.group_add(
            self.user_group_name,
            self.channel_name
        )
        
        await self.accept()
    
    async def disconnect(self, close_code):
        # Leave user group
        await self.channel_layer.group_discard(
            self.user_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        data = json.loads(text_data)
        message_type = data.get('type')
        
        if message_type == 'read_messages':
            conversation_id = data.get('conversation_id')
            await self.mark_messages_as_read(conversation_id)
        
    async def chat_message(self, event):
        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': event['message']
        }))
    
    @database_sync_to_async
    def mark_messages_as_read(self, conversation_id):
        try:
            user = Admin.objects.get(id=self.user_id)
            # Mark all messages in this conversation as read
            Message.objects.filter(
                conversation_id=conversation_id,
                is_read=False
            ).exclude(sender=user).update(is_read=True)
            return True
        except Exception as e:
            print(f"Error marking messages as read: {e}")
            return False