from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from app.models.user import Admin

class Conversation(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'conversations'
        ordering = ['-updated_at']

    def __str__(self):
        participants = ConversationParticipant.objects.filter(conversation=self)
        participant_names = [p.user.username for p in participants]
        return f"Conversation between {', '.join(participant_names)}"

class ConversationParticipant(models.Model):
    conversation = models.ForeignKey(
        Conversation, 
        on_delete=models.CASCADE, 
        related_name='participants'
    )
    user = models.ForeignKey(
        Admin,
        on_delete=models.CASCADE, 
        related_name='conversations'
    )
    joined_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'conversation_participants'
        unique_together = ('conversation', 'user')

    def __str__(self):
        return f"{self.user.username} in conversation {self.conversation.id}"

class Message(models.Model):
    conversation = models.ForeignKey(
        Conversation, 
        on_delete=models.CASCADE, 
        related_name='messages'
    )
    sender = models.ForeignKey(
        Admin,
        on_delete=models.CASCADE, 
        related_name='sent_messages'
    )
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)

    class Meta:
        db_table = 'messages'
        ordering = ['created_at']

    def __str__(self):
        return f"Message from {self.sender.username} in conversation {self.conversation.id}"
