from django.db import models
from app.models.user import Admin
from django.utils.translation import gettext_lazy as _


class PatientExperienceModel(models.Model):
    admin = models.ForeignKey(Admin, on_delete=models.CASCADE, null=False)
    patiant_name = models.TextField(null=False)
    preferred_pronouns = models.TextField()
    phone_number = models.CharField(max_length=255)
    email = models.EmailField(_("email address"))
    review = models.TextField(default='')
    last_visited = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(
        verbose_name=_("created_at"),
        # db_index=True,
        auto_now_add=True,
        # default=date.today
    )
    updated_at = models.DateTimeField(
        verbose_name=_("updated_at"),
        # db_index=True,
        auto_now=True,
    )

    class Meta:
        db_table = 'patient_experience'
