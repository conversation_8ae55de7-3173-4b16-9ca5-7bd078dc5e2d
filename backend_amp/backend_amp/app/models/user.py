from django.contrib.auth.models import Abstract<PERSON><PERSON><PERSON><PERSON>, BaseUserManager, PermissionsMixin

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import FileExtensionValidator
from django.conf import settings as django_settings, settings
from .base import BaseModelMixin


class UserManager(BaseUserManager):
    use_in_migrations = True

    def _create_user(
            self, username: str, email: str, password: str, **extra_fields: dict
    ) -> "Admin":
        """Create and save a user with the given username, email, and
        password."""
        if not username:
            raise ValueError("The given username must be set.")

        email = self.normalize_email(email)
        username = self.model.normalize_username(username)
        user = self.model(username=username, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(  # type: ignore
            self, username: str, email: str, password: str, **extra_fields
    ) -> "User":
        extra_fields.setdefault("is_staff", False)
        extra_fields.setdefault("is_superuser", False)
        return self._create_user(username, email, password, **extra_fields)

    def create_superuser(  # type: ignore
            self, username: str, email: str, password: str, **extra_fields
    ) -> "User":
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")
        return self._create_user(username, email, password, **extra_fields)


GlobalStatus = (
    (1, 'active'),
    (2, 'inactive')
)
ADMIN_GROUP = (
    (1, 'SuperAdmin'),
    (2, 'Admin'),
    (3, 'User'),
    (4,'SubUser')
)
SERVICES_GROUP=(
    (1,'Procurement_Hub'),
    (1,'Insurance_Verification'),
    (1,'Patient_Experience'),
)

class Admin(AbstractBaseUser, PermissionsMixin, BaseModelMixin):
    objects = UserManager()
    email = models.EmailField(_("email address"), unique=True)
    first_name = models.CharField(_('first name'), max_length=255, null=False)
    last_name = models.CharField(_('last name'), max_length=255, null=False)
    avatar = models.FileField(upload_to='documents/profile_pictures',
                              default='documents/profile_pictures/user.jpg',
                              null=True, blank=True,
                              validators=[
                                  FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif'])])
    user_group = models.IntegerField(choices=ADMIN_GROUP, default=3)
    phone_number = models.CharField(max_length=255)
    dental_name = models.CharField(max_length=255)
    dental_logo = models.FileField(upload_to='documents/dental_pictures',
                                   default='documents/dental_pictures/user.jpg',
                                   null=True, blank=True,
                                   validators=[
                                       FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif'])])
    total_budget = models.FloatField(null=False, default=0)
    status = models.IntegerField(choices=GlobalStatus, null=False, default=1)
    ref_admin = models.ForeignKey('Admin', on_delete=models.SET_NULL, null=True, blank=True)
    # created_at = models.DateTimeField(
    #     verbose_name=_("created_at"),
    #     db_index=True,
    #     auto_now_add=True,
    # )
    # updated_at = models.DateTimeField(
    #     verbose_name=_("updated_at"),
    #     db_index=True,
    #     auto_now=True,
    # )
    MIN_LENGTH_USERNAME = 1
    MAX_LENGTH_USERNAME = 20
    username = models.CharField(
        _("username"),
        max_length=MAX_LENGTH_USERNAME,
    )

    # permissions
    is_admin = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    is_superuser = models.BooleanField(default=False)
    is_staff = models.BooleanField(
        _("staff status"),
        default=False,
        help_text=_("Designates whether the user can log into this admin site."),
    )

    USERNAME_FIELD = "email"
    EMAIL_FIELD = "email"
    REQUIRED_FIELDS = ["username"]

    class Meta:
        # verbose_name = _("user")
        verbose_name_plural = _("users")
        ordering = ["-created_at"]
        db_table = 'admin'

    def __unicode__(self):
        return self.date_joined

    def __str__(self):
        return "".join(self.first_name if self.first_name else "")

    def get_absolute_url(self):
        pass
        # return "".join("/users/%s/" % urlquote(self.email))

    def get_full_name(self):
        full_name = '%s %s' % (self.first_name, self.last_name)
        return "".join(full_name.strip())

    def get_short_name(self):
        return "".join(self.first_name if self.first_name else "None")

    def get_avatar(self):
        return self.avatar.__str__()

    def get_dental_logo(self):
        return self.dental_logo.__str__()
    # def get_data_join(self):
    #     return self.date_joined.__str__()
    # def email_user(self, subject, message, from_email=None):
    #     send_mail(subject, message, from_email, [self.email])

    # @background(schedule=60)
    # def notify_user(self, user_id):
    #     # lookup user by id and send them a message
    #
    #     user = CustomUser.objects.get(pk=user_id)
    #     if user:
    #         if self.email:
    #             user.email_user('Here is a notification', 'You have been notified')

    # def save(self, *args, **kwargs):
    #     self.notify_user(self.pk)
    #     super(CustomUser, self).save(args, kwargs)
