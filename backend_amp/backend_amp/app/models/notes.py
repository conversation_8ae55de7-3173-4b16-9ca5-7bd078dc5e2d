
from django.db import models
from app.models.user import Admin
from django.utils.translation import gettext_lazy as _





class NotesModel(models.Model):
    admin = models.ForeignKey(Admin, on_delete=models.CASCADE, null=False)
    notes = models.TextField()
    created_at = models.DateTimeField(
        verbose_name=_("created_at"),
        # db_index=True,
        auto_now_add=True,
        # default=date.today
    )
    updated_at = models.DateTimeField(
        verbose_name=_("updated_at"),
        # db_index=True,
        auto_now=True,
    )

    class Meta:
        db_table = 'notes'
