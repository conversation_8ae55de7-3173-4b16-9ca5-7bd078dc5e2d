import os

from django.db import models
from django.utils.translation import gettext_lazy as _

from app.models.user import Admin
from django.core.validators import FileExtensionValidator


class InvoicesModel(models.Model):
    admin = models.ForeignKey(Admin, on_delete=models.SET_NULL, null=True, blank=True)
    vendor = models.CharField(max_length=255, null=True, blank=True)
    date = models.DateTimeField(null=True, blank=True)
    invoice_number = models.CharField(max_length=255, null=True, blank=True)
    amount_paid = models.FloatField(null=False, default=0)
    created_at = models.DateTimeField(
        verbose_name=_("created_at"),
        db_index=True,
        auto_now_add=True,
    )
    updated_at = models.DateTimeField(
        verbose_name=_("updated_at"),
        db_index=True,
        auto_now=True,
    )

    class Meta:
        db_table = 'invoices'
class InvoicesFileModel(models.Model):
    invoice = models.ForeignKey(
        InvoicesModel, on_delete=models.CASCADE, null=True, blank=True
    )
    invoice_file = models.FileField(
        upload_to='documents/invoices', null=True, blank=True,
        validators=[FileExtensionValidator(allowed_extensions=['pdf', 'docx','doc'])])
    def delete(self, *args, **kwargs):
        os.remove(self.invoice_file.path)
        self.invoice_file.delete(False)
        super(InvoicesFileModel, self).delete(*args, **kwargs)

    class Meta:
        db_table = 'invoices_file'