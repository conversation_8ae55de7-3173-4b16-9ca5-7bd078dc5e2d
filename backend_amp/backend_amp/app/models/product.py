from django.core.validators import FileExtensionValidator
from django.conf import settings as django_settings, settings
from django.utils.translation import gettext_lazy as _

from django.db import models

from app.models import GlobalStatus


class ProductsModel(models.Model):
    name = models.CharField(max_length=1000, null=False)
    image = models.FileField(upload_to='profile_pictures',
                             default=django_settings.MEDIA_URL + 'profile_pictures/user.jpg',
                             null=True, blank=True,
                             validators=[
                                 FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif'])])
    status = models.IntegerField(choices=GlobalStatus, null=False, default=1)
    s_k_u=models.CharField(max_length=200,null=True,blank=True,default="")
    created_at = models.DateTimeField(
        verbose_name=_("created_at"),
        db_index=True,
        auto_now_add=True,
    )
    updated_at = models.DateTimeField(
        verbose_name=_("updated_at"),
        db_index=True,
        auto_now=True,
    )
    class Meta:
        db_table = 'products'
