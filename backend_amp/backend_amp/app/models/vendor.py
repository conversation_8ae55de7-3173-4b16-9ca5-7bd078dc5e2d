from django.db import models
from django.utils.translation import gettext_lazy as _


from app.models.user import Admin


class VendorsModel(models.Model):
    admin = models.ForeignKey(Admin, on_delete=models.SET_NULL, null=True, blank=True)
    name = models.Char<PERSON>ield(max_length=255, null=False)
    account_number = models.Char<PERSON>ield(max_length=255)
    contact_name = models.Char<PERSON>ield(max_length=255)
    how_to_order = models.CharField(max_length=255)
    phone = models.Char<PERSON>ield(max_length=255)
    email = models.Char<PERSON>ield(max_length=255)
    website = models.Char<PERSON>ield(max_length=255)
    user_name = models.Char<PERSON>ield(max_length=255)
    password = models.Char<PERSON>ield(max_length=255)
    description = models.TextField()
    created_at = models.DateTimeField(
        verbose_name=_("created_at"),
        db_index=True,
        auto_now_add=True,
    )
    updated_at = models.DateTimeField(
        verbose_name=_("updated_at"),
        db_index=True,
        auto_now=True,
    )

    class Meta:
        verbose_name_plural = _("vendors")
        db_table = 'vendors'
