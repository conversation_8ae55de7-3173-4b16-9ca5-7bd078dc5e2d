from django.db import models
from django.utils.translation import gettext_lazy as _

from app.models.user import Admin, GlobalStatus
from app.models.product import ProductsModel
from app.models.vendor import VendorsModel
from app.models.order import OrdersModel, OrderStatus
#
#
ReturnStatus = (
    (1, 'in_progress'),
    (2, 'returned')
)


class ReturnModel(models.Model):
    admin = models.ForeignKey(Admin, on_delete=models.CASCADE, null=False)
    product = models.ForeignKey(ProductsModel, on_delete=models.CASCADE, null=False)
    vendor = models.ForeignKey(VendorsModel, on_delete=models.CASCADE, null=False)
    qty = models.FloatField(null=False, default=1)
    specific = models.BooleanField(null=False, default=False)
    communication_note = models.TextField()
    status = models.IntegerField(choices=ReturnStatus, null=False, default=1)
    created_at = models.DateTimeField(
        verbose_name=_("created_at"),
        db_index=True,
        auto_now_add=True,
    )
    updated_at = models.DateTimeField(
        verbose_name=_("updated_at"),
        db_index=True,
        auto_now=True,
    )

    class Meta:
        db_table = 'returns'
