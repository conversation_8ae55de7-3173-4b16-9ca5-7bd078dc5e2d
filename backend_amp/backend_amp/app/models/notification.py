from swapper import swappable_setting
from django.db import models

from .base import AbstractNotification, notify_handler  # noqa
from .base import AbstractNotification
from .user import ADMIN_GROUP


class Notification(AbstractNotification):
    recipient_group = models.IntegerField(choices=ADMIN_GROUP, default=3)

    class Meta(AbstractNotification.Meta):
        abstract = False
        swappable = swappable_setting('app', 'Notification')

    def naturalday(self):
        """
        Shortcut for the ``humanize``.
        Take a parameter humanize_type. This parameter control the which humanize method use.
        Return ``today``, ``yesterday`` ,``now``, ``2 seconds ago``etc.
        """
        from django.contrib.humanize.templatetags.humanize import naturalday
        return naturalday(self.timestamp)

    def naturaltime(self):
        from django.contrib.humanize.templatetags.humanize import naturaltime
        return naturaltime(self.timestamp)
