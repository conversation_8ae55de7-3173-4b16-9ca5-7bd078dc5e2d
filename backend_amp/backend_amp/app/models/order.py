from django.db import models
from django.utils.translation import gettext_lazy as _

from app.models.user import Admin
from app.models.product import ProductsModel
from app.models.vendor import VendorsModel
# from app.models.notification import NotificationModel
#
OrderStatus = (
    (1, 'back_order'),
    (2, 'incoming'),
    (3, 'in_progress'),
    (4, 'confirmed'),
    (5, 'partially_delivered'),
    (6, 'delivered'),
    (7, 'in_storage'),
    (8, 'returned'),
    (9, 'partially_returned'),
    (10, 'not_available'),
)
class OrdersModel(models.Model):
    admin = models.ForeignKey(Admin, on_delete=models.SET_NULL, null=True, blank=True)
    product = models.ForeignKey(ProductsModel, on_delete=models.SET_NULL, null=True, blank=True)
    vendor = models.ForeignKey(VendorsModel, on_delete=models.SET_NULL, null=True, blank=True)
    note = models.TextField()
    qty = models.IntegerField(null=False, default=0)
    specific = models.BooleanField(null=False, default=False)
    urgent = models.BooleanField(null=False, default=False)
    exp_delivery = models.CharField(max_length=255)
    timeline = models.DateTimeField(null=True, blank=True)
    received = models.BooleanField(null=False, default=False)
    received_date = models.DateTimeField(auto_now_add=True)
    my_product = models.BooleanField(null=False, default=False)
    status = models.IntegerField(choices=OrderStatus, null=False, default=1)
    would_paid = models.FloatField(null=False, default=0)
    paid = models.FloatField(null=False, default=0)
    saved = models.FloatField(null=False, default=0)
    # notification_order = models.BooleanField(default=False)
    # notification_orders = models.ManyToManyField(NotificationModel, through="MeatInParcel")
    created_at = models.DateTimeField(
        verbose_name=_("created_at"),
        # db_index=True,
        auto_now_add=True,
    )
    updated_at = models.DateTimeField(
        verbose_name=_("updated_at"),
        # db_index=True,
        auto_now=True,
    )

    class Meta:
        db_table = 'orders'

    def get_admin(self):
        return self.admin.username

    def get_product(self):
        return self.product.name

    def get_vendor(self):
        return self.vendor.name if self.vendor else None

    def get_status(self):
        for index, status in enumerate(OrderStatus):
            if self.status == status[0]:
                return status[1]

    def get_datatime(self):
        desired_format = '%Y-%m-%dT%H-%M'
        if self.timeline:
            return self.timeline.strftime('%m-%d-%Y')
        else:
            return ''

class OrderLogModel(models.Model):
    admin = models.ForeignKey(Admin, on_delete=models.CASCADE, null=False)
    order = models.ForeignKey(OrdersModel, on_delete=models.CASCADE, null=False)
    old_status = models.IntegerField(choices=OrderStatus, null=False)
    new_status = models.IntegerField(choices=OrderStatus, null=False)
    created_at = models.DateTimeField(
        verbose_name=_("created_at"),
        db_index=True,
        auto_now_add=True,
    )

    # updated_at = models.DateTimeField(
    #     verbose_name=_("updated_at"),
    #     db_index=True,
    #     auto_now=True,
    # )
    class Meta:
        db_table = 'order_logs'



# class NotificationModel(models.Model):
#     admin = models.ForeignKey(Admin, on_delete=models.CASCADE, null=False)
#     order = models.ForeignKey(OrdersModel, on_delete=models.CASCADE)
#     quantity = models.IntegerField(default=0)
#     notification_content = models.CharField(max_length=255, null=True, blank=True)
#     created_at = models.DateTimeField(
#         verbose_name=_("created_at"),
#         db_index=True,
#         auto_now_add=True,
#     )
#     updated_at = models.DateTimeField(
#         verbose_name=_("updated_at"),
#         db_index=True,
#         auto_now=True,
#     )
#
#     class Meta:
#         verbose_name_plural = _("notification")
#         db_table = 'notifications'
