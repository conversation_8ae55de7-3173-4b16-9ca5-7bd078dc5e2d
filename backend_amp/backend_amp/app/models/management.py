from django.db import models
from django.core.validators import FileExtensionValidator
from django.utils.translation import gettext_lazy as _

TaskStatus = (
    (1, 'incoming'),
    (2, 'in_progress'),
    (3, 'Failed'),
    (4, 'Done'),
)


class ManagementModel(models.Model):
    file_upload = models.FileField(upload_to='management/upload',
                                   null=True, blank=True,
                                   validators=[
                                       FileExtensionValidator(allowed_extensions=['xls','xlsx', 'csv'])])
    file_status = models.IntegerField(choices=TaskStatus, null=False, default=1)
    created_at = models.DateTimeField(
        verbose_name=_("created_at"),
        # db_index=True,
        auto_now_add=True,
    )
    updated_at = models.DateTimeField(
        verbose_name=_("updated_at"),
        # db_index=True,
        auto_now=True,
    )

    class Meta:
        db_table = 'management'


class ManagementTaskModel(models.Model):
    task_name = models.CharField(_('task name'), max_length=255, null=False)
    task_id = models.CharField(_('task id'), max_length=255, null=False)
    sudo_permission = models.BooleanField(default=False)
    task_command = models.CharField(_('task command'), max_length=255)
    task_status = models.IntegerField(choices=TaskStatus, null=False, default=1)
    created_at = models.DateTimeField(
        verbose_name=_("created_at"),
        # db_index=True,
        auto_now_add=True,
    )
    updated_at = models.DateTimeField(
        verbose_name=_("updated_at"),
        # db_index=True,
        auto_now=True,
    )

    class Meta:
        db_table = 'management_task'
