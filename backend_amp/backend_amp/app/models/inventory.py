from django.db import models
from django.utils.translation import gettext_lazy as _

from app.models.user import Admin
from app.models.product import ProductsModel


class InventoryModel(models.Model):
    admin = models.ForeignKey(Admin, on_delete=models.SET_NULL, null=True, blank=True)
    product = models.ForeignKey(ProductsModel, on_delete=models.SET_NULL, null=True, blank=True)
    qty = models.IntegerField(null=False, default=0)
    min_qty = models.IntegerField(null=False, default=0)
    notes = models.TextField()
    expiration_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(
        verbose_name=_("created_at"),
        db_index=True,
        auto_now_add=True,
    )
    updated_at = models.DateTimeField(
        verbose_name=_("updated_at"),
        db_index=True,
        auto_now=True,
    )

    class Meta:
        db_table = 'inventories'


class InventoryLogModel(models.Model):
    inventory = models.ForeignKey(InventoryModel, on_delete=models.CASCADE, null=False)
    qty = models.IntegerField(null=False, default=1)

    class Meta:
        db_table = 'inventory_logs'
