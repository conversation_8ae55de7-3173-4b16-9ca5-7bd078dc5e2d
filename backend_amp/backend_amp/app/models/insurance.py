from django.db import models
from app.models.user import Admin
from django.utils.translation import gettext_lazy as _
from datetime import date


class InsuranceVerificationModel(models.Model):
    admin = models.ForeignKey(Admin, on_delete=models.CASCADE, null=False)
    patiant_name = models.TextField(null=False)
    group = models.TextField()
    cert = models.TextField()
    date_of_birth = models.DateTimeField(null=True, blank=True)
    plan_holder_date_of_birth = models.DateTimeField(null=True, blank=True)
    plan_holder = models.CharField(max_length=255)
    address = models.TextField()
    relation_to_subscriber = models.CharField(max_length=255)
    notes = models.TextField()
    breakdown = models.Char<PERSON>ield(max_length=255)
    created_at = models.DateTimeField(
        verbose_name=_("created_at"),
        # db_index=True,
        auto_now_add=True,
        # default=date.today
    )
    updated_at = models.DateTimeField(
        verbose_name=_("updated_at"),
        # db_index=True,
        auto_now=True,
    )

    class Meta:
        db_table = 'insurance_verifications'
