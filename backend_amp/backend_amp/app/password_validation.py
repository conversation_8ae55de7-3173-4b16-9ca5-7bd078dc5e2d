from re import compile as recompile
from django.utils.translation import gettext_lazy as _

from django.core.exceptions import ValidationError


class PinValidator:
    pin_regex = recompile('\d{6}')

    def get_help_text(self):
        pass

    def validate(self, password, user=None):
        if not self.pin_regex.fullmatch(password):
            raise ValidationError(
                _('The password must contain exactly six digit'),
                code='pin_6_digits',
            )
