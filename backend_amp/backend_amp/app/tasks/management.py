from __future__ import absolute_import, unicode_literals
from celery.decorators import task
from datetime import datetime, date
from django.utils import timezone
import os
import subprocess

from celery import Celery
from django.conf import settings
from celery.utils.log import get_task_logger

from app.models import ManagementTaskModel

logger = get_task_logger(__name__)


@task(name="DataBaseBackup")
def db_backup():
#      docker exec -t dev_backend_amp_postgres pg_dumpall -c -U amp_dev > dump_`date +%Y-%m-%d"_"%H_%M_%S`.sql
    pass
@task(name="exec_thread_task")
def exec_thread_task(chanel):
    """sends an email when feedback form is filled successfully"""
    logger.info("Sent feedback email")
    pid = bg_task(chanel)
    logger.info(pid)
    return pid


def bg_task(channel):
    # if 'TC-Bot/src' not in os.getcwd():
    #     os.chdir("TC-Bot/src")
    print(os.getcwd())
    cmd = 'ls -la'
    process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE)
    pid = process.pid
    # channel_object_id = ChannelChat.objects.values().filter(channel=channel)[0].__getitem__('id')
    ManagementTaskModel.objects.create(**{
        # 'channel': ChannelChat.objects.get(id=channel_object_id),
        'proc_id': pid + 1
    })
    print(pid)
    process.wait()
    return pid


@task(name="kill_thread_task")
def kill_thread_task(proc_id):
    # if 'TC-Bot/src' not in os.getcwd():
    #     os.chdir("TC-Bot/src")
    cmd = 'kill -s 9 ' + proc_id.__str__()
    cmd2 = 'kill -s 9 ' + (int(proc_id)-1).__str__()
    process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE)
    process = subprocess.Popen(cmd2, shell=True, stdout=subprocess.PIPE)
    return