from typing import Any

from app.models import Admin, ProductsModel
from asgiref.sync import sync_to_async
from fastapi import HTT<PERSON>Ex<PERSON>, Request

from app.schemas import CreateProductSchema

from config.exceptions import UpdateOrCreateObject


class ProductAPI:
    @classmethod
    async def create(cls, request: Request, schema: CreateProductSchema, current_user: Admin) -> ProductsModel:
        schema.status = int(schema.status)
        exist_object = await ProductsModel.objects.filter(name=schema.name).afirst()
        if exist_object:
            raise HTTPException(status_code=200, detail="object is create to product model")
        return await sync_to_async(ProductsModel.objects.create)(**schema.dict())
