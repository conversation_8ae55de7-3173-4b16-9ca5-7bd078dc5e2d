from app.models import Admin, NotesModel
from asgiref.sync import sync_to_async
from fastapi import HTTP<PERSON>x<PERSON>, Request
from config.exceptions import PermissionDeniedException

from app.schemas import CreateNotesSchema,EditNotesSchema,DeleteNotesSchema

from config.exceptions import UpdateOrCreateObject


class NotesAPI:
    @classmethod
    async def get_notes(cls, request: Request, user_id: str, current_user: Admin)->list[NotesModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        notes = []
        if admin_object.user_group <= 2:
            async for insurance in NotesModel.objects.filter(admin=user_id).all().select_related(
            'admin').order_by(
            '-created_at'):
                notes.append(insurance)
        else:
            if admin_object.id == int(user_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id, ref_admin__id=user_id).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            async for note in NotesModel.objects.filter(admin=user_id).all().select_related(
            'admin').order_by( '-created_at'):
                notes.append(note)
        return notes

    @classmethod
    async def create_notes(cls, request: Request, schema: CreateNotesSchema,
                               current_user: Admin) -> list[NotesModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        schema.admin_id = int(schema.admin_id)
        exist_object = await NotesModel.objects.filter(notes=schema.notes).afirst()
        notes = []
        if admin_object.user_group <= 2:
            if exist_object:
                raise HTTPException(status_code=409, detail="object is Exist to Notes model")
        else:
            if admin_object.id == int(schema.admin_id):
               pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id, ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if exist_object:
                raise HTTPException(status_code=409, detail="object is Exist to Notes model")
        await sync_to_async(NotesModel.objects.create)(**schema.dict())
        async for note in NotesModel.objects.filter(admin=schema.admin_id).all().select_related(
                'admin').order_by('-created_at'):
            notes.append(note)
        return notes
    @classmethod
    async def update_notes(cls, request: Request, schema: EditNotesSchema, current_user: Admin) -> \
            list[NotesModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        schema.admin_id = int(schema.admin_id)
        notes = []
        exist_object = await NotesModel.objects.filter(id=int(schema.id)).afirst()
        if admin_object.user_group <= 2:
            if not exist_object:
                del schema.id
                await sync_to_async(NotesModel.objects.create)(**schema.dict())
        else:
            if admin_object.id == int(schema.admin_id):
                if not exist_object:
                    del schema.id
                    await sync_to_async(NotesModel.objects.create)(**schema.dict())
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
                if not exist_object:
                    del schema.id
                    await sync_to_async(NotesModel.objects.create)(**schema.dict())
        await NotesModel.objects.filter(pk=schema.id).aupdate(**schema.dict())
        async for note in NotesModel.objects.filter(admin=int(schema.admin_id)).all().select_related(
                'admin').order_by(
            '-created_at'):
            notes.append(note)
        return notes
    @classmethod
    async def delete_notes(cls, request: Request, schema: DeleteNotesSchema, current_user: Admin) -> \
            list[NotesModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        exist_object = await NotesModel.objects.filter(pk=int(schema.id)).afirst()
        notes = []
        if admin_object.user_group <= 2:
            if not exist_object:
                raise HTTPException(status_code=404, detail="Notes is not found")
        else:
            if admin_object.id == int(schema.admin_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if  not exist_object:
                raise HTTPException(status_code=409, detail="object is Exist to Notes model")
        await NotesModel.objects.filter(pk=int(schema.id),admin=int(schema.admin_id)).adelete()
        async for note in NotesModel.objects.filter(
                admin=int(schema.admin_id),).all().select_related(
            'admin').order_by(
            '-created_at'):
            notes.append(note)
        return notes
