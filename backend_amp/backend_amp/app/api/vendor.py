from typing import Any
from logging import getLogger

from app.models import Admin, VendorsModel
from asgiref.sync import sync_to_async
from fastapi import HTTP<PERSON>x<PERSON>, Request
from config.exceptions import PermissionDeniedException

from app.schemas import Create<PERSON>endors<PERSON>chema, EditVendorSchema, ReadVendorsSchema, DeleteVendorSchema
logger = getLogger(__name__)


class VendorAPI:
    @classmethod
    async def get(cls, request: Request, user_id: str, current_user: Admin) -> list[VendorsModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        vendors = []
        if admin_object.user_group <= 2:
            async for vendor in VendorsModel.objects.filter(admin=user_id).all().select_related('admin').order_by(
                    '-created_at'):
                vendors.append(vendor)
        else:
            if admin_object.id == int(user_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id, ref_admin__id=user_id).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            async for vendor in VendorsModel.objects.filter(admin=user_id).all().select_related('admin').order_by(
                    '-created_at'):
                vendors.append(vendor)
        return vendors


    @classmethod
    async def create(cls, request: Request, schema: CreateVendorsSchema,
                     current_user: Admin) -> list[VendorsModel]:
        schema.admin_id = int(schema.admin_id)
        admin_object = await Admin.objects.aget(email=current_user.email)
        exist_object = await VendorsModel.objects.filter(name=schema.name).afirst()
        vendors = []
        if admin_object.user_group <= 2:
            if exist_object:
                raise HTTPException(status_code=404, detail="Vendor object is Exist")
        else:
            if admin_object.id == int(schema.admin_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if exist_object:
                raise HTTPException(status_code=409, detail="Vendor object is Exist")
        await sync_to_async(VendorsModel.objects.create)(**schema.dict())
        async for vendor in VendorsModel.objects.filter(admin=schema.admin_id).all().select_related('admin').order_by(
                '-created_at'):
            vendors.append(vendor)
        return vendors

    @classmethod
    async def update(cls, request: Request, schema: EditVendorSchema, current_user: Admin) -> list[VendorsModel]:
        schema.admin_id = int(schema.admin_id)
        admin_object = await Admin.objects.aget(email=current_user.email)
        vendor_object = await VendorsModel.objects.filter(pk=schema.id).afirst()
        vendors = []
        if admin_object.user_group <= 2:
            if not vendor_object:
                raise HTTPException(status_code=404, detail="Vendor object dose not Exist")
        else:
            if admin_object.id == int(schema.admin_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if not vendor_object:
                raise HTTPException(status_code=409, detail="Vendor object dose not Exist")
        await VendorsModel.objects.filter(pk=int(schema.id),admin=int(schema.admin_id)).aupdate(**schema.dict())
        async for order in VendorsModel.objects.filter(admin=int(schema.admin_id)).all().select_related(
                'admin').order_by(
                '-created_at'):
            vendors.append(order)
        return vendors

    @classmethod
    async def delete_vendor(cls, request: Request, schema: DeleteVendorSchema, current_user: Admin) -> list[
        VendorsModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        vendor = await VendorsModel.objects.filter(pk=int(schema.id)).afirst()
        if admin_object.user_group <= 2:
            if not vendor:
                raise HTTPException(status_code=404, detail="Vendor object dose not Exist")
        else:
            if admin_object.id == int(schema.admin_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if not vendor:
                raise HTTPException(status_code=409, detail="Vendor object dose not Exist")
        await VendorsModel.objects.filter(pk=int(schema.id),admin=int(schema.admin_id)).adelete()
        vendors = []
        async for vendor in VendorsModel.objects.filter(admin=int(schema.admin_id)).all().select_related(
                'admin').order_by(
            '-created_at'):
            vendors.append(vendor)
        return vendors