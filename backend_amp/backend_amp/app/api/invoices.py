
import datetime
import os
from typing import List, Any, Dict

from asgiref.sync import sync_to_async
from django.utils import timezone
from datetime import date
from dateutil.relativedelta import relativedelta

from fastapi import HTTPException, Request, UploadFile, Form, File
from django.conf import settings as django_settings
from fastapi.responses import JSONResponse


from app.models import InvoicesModel,InvoicesFileModel, Admin  ##, NotificationModel
from app.schemas import InvoiceReadSchema, CreateInvoiceSchema, EditInvoiceSchema,DeleteInvoiceSchema

from config.exceptions import PermissionDeniedException

month_to_str = {
    1: "January",
    2: "February",
    3: "March",
    4: "April",
    5: "May",
    6: "June",
    7: "July",
    8: "August",
    9: "September",
    10: "October",
    11: "November",
    12: "December",
}

class InvoicesAPI:
    @classmethod
    async def get_invoices(cls,request:Request,user_id: str,current_user:Admin)->list[InvoicesModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        invoices = []
        today = date.today()
        six_months = today - relativedelta(months=6)
        if admin_object.user_group <= 2:
            async for invoice in (InvoicesModel.objects.filter(admin_id=int(user_id),
                                                           created_at__gte=six_months)
                                                            .all().select_related('admin')
                                                            .order_by('-created_at')):  # .exclude(Q(quantity=0):
                invoiceFile = await InvoicesFileModel.objects.filter(invoice_id=invoice.id).afirst()
                invoices.append({
                    "id": invoice.id,
                    "admin_id":invoice.admin.id,
                    "vendor": invoice.vendor,
                    "invoice_number": invoice.invoice_number,
                    "amount_paid": invoice.amount_paid,
                    "invoice_file": str(invoiceFile.invoice_file) if invoiceFile else '',
                    "date": invoice.date,
                    "created_at": invoice.created_at,
                })
        else:
            if admin_object.id == int(user_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id, ref_admin__id=user_id).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            async for invoice in (InvoicesModel.objects.filter(admin_id=int(user_id),
                                                               created_at__gte=six_months)
                                                               .all().select_related('admin')
                                                                .order_by('-created_at')):  # .exclude(Q(quantity=0):
                invoiceFile = await InvoicesFileModel.objects.filter(invoice_id=invoice.id).afirst()
                invoices.append({
                    "id": invoice.id,
                    "admin_id": invoice.admin.id,
                    "vendor": invoice.vendor,
                    "invoice_number": invoice.invoice_number,
                    "amount_paid": invoice.amount_paid,
                    "invoice_file": str(invoiceFile.invoice_file) if invoiceFile else '',
                    "date": invoice.date,
                    "created_at": invoice.created_at,
                })
        return invoices

    @classmethod
    async def insert_invoice(cls,request:Request,schema:CreateInvoiceSchema,current_user:Admin):
        admin_object = await Admin.objects.aget(email=current_user.email)
        invoice = await InvoicesModel.objects.filter(pk=int(schema.invoice_number)).afirst()
        if admin_object.user_group <= 2:
            if invoice:
                raise HTTPException(status_code=404, detail="Invoice object is Exist")
        else:
            if admin_object.id == int(schema.admin_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if invoice:
                raise HTTPException(status_code=409, detail="Invoice object is Exist")
        invoices = []
        invoice_file = schema.invoice_file
        del schema.invoice_file
        await sync_to_async(InvoicesModel.objects.create)(**schema.dict())
        await sync_to_async(InvoicesFileModel.objects.create)(**{
            "invoice": await InvoicesModel.objects.aget(invoice_number=schema.invoice_number),
            "invoice_file": invoice_file,
        })
        async for invoice in (InvoicesModel.objects.filter(admin_id=int(schema.admin_id),
                                                           created_at__gte=datetime.datetime.today().replace(
                                                               day=1,
                                                               hour=0,
                                                               minute=0,
                                                               second=0,
                                                               microsecond=0)
                                                           )
                                                            .all().select_related('admin')
                                                            .order_by('-created_at')):  # .exclude(Q(quantity=0):
            invoiceFile = await InvoicesFileModel.objects.filter(invoice_id=invoice.id).afirst()
            invoices.append({
                "id": invoice.id,
                "admin_id":invoice.admin.id,
                "vendor": invoice.vendor,
                "invoice_number": invoice.invoice_number,
                "amount_paid": invoice.amount_paid,
                "invoice_file": str(invoiceFile.invoice_file) if invoiceFile else '',
                "date": invoice.date,
                "created_at": invoice.created_at,
            })
        return invoices
    @classmethod
    async def edit_invoice(cls, request: Request, schema: EditInvoiceSchema, current_user: Admin) -> list[InvoicesModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        invoice = await InvoicesModel.objects.filter(pk=int(schema.id)).afirst()
        if admin_object.user_group <= 2:
            if not invoice:
                raise HTTPException(status_code=404, detail="Invoice object is Exist")
        else:
            if admin_object.id == int(schema.admin_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if not invoice:
                raise HTTPException(status_code=409, detail="Invoice object is Exist")
        invoices = []
        invoice_file = schema.invoice_file
        del schema.invoice_file
        await InvoicesModel.objects.filter(pk=int(schema.id)).aupdate(**schema.dict())
        invoiceFile=await InvoicesFileModel.objects.aget(invoice_id=schema.id)
        if str(invoiceFile.invoice_file)!=(invoice_file.split("/")[-1]):
            if invoiceFile :
                await InvoicesFileModel.objects.filter(invoice_id=schema.id).aupdate(**{
                    # "invoice": await InvoicesModel.objects.aget(id=schema.id),
                    "invoice_file": invoice_file
                })
            else:
                await sync_to_async(InvoicesFileModel.objects.create)(**{
                    "invoice": await InvoicesModel.objects.aget(id=schema.id),
                    "invoice_file": invoice_file
                })
        async for invoice in (InvoicesModel.objects.filter(admin_id=int(schema.admin_id),
                                                           created_at__gte=datetime.datetime.today().replace(
                                                               day=1,
                                                               hour=0,
                                                               minute=0,
                                                               second=0,
                                                               microsecond=0)
                                                           )
                                                            .all().select_related('admin')
                                                            .order_by('-created_at')):  # .exclude(Q(quantity=0):
            invoiceFile = await InvoicesFileModel.objects.filter(invoice_id=invoice.id).afirst()
            invoices.append({
                "id": invoice.id,
                "admin_id": invoice.admin.id,
                "vendor": invoice.vendor,
                "invoice_number": invoice.invoice_number,
                "amount_paid": invoice.amount_paid,
                "invoice_file": str(invoiceFile.invoice_file) if invoiceFile else '',
                "date": invoice.date,
                "created_at": invoice.created_at,
            })
        return invoices

    @classmethod
    async def get_total_budget_left(cls, request: Request, user_id: str, current_user: Admin) -> Any:
        admin_object = await Admin.objects.aget(email=current_user.email)
        time_index=[]
        if admin_object.user_group <= 2:
            pass
        else:
            if admin_object.id == int(user_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(user_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
        orders = {}
        today = date.today()
        six_months = today - relativedelta(months=6)
        for month_index in range(0,6):
            time_split=str(today - relativedelta(months=month_index)).split('-')
            last_year=time_split[0]
            last_month=time_split[1]
            time_index.append({"year":last_year,"month":last_month,"month_index":month_index})
        async for invoice in InvoicesModel.objects.filter(admin_id=int(user_id),
                                                          date__gte=six_months
                                                      # created_at__gte=datetime.datetime.today().replace(day=1,
                                                      #                                                   hour=0,
                                                      #                                                   minute=0,
                                                      #                                                   second=0,
                                                      #                                                   microsecond=0),

                                                      ).all():
            date_at = invoice.date.__str__()
            year = int(date_at.split('-')[0])
            month = int(date_at.split('-')[1])
            if year not in orders.keys():
                orders[year] = {}
            if month not in orders[year].keys():
                    orders[year][month] = {"month_id": month, "name": month_to_str[month], "paid": 0}
            sum_paid=orders[year][month]["paid"]
            sum_paid = sum_paid + invoice.amount_paid
            orders[year][month]={"month_id": month, "name": month_to_str[month], "paid": sum_paid}

        for times in time_index:
            last_year=int(times["year"])
            last_month=int(times["month"])
            if last_year not in orders.keys():
                orders[last_year] = {}
            if last_month not in orders[last_year].keys():
                orders[last_year][last_month] = {"month_id": last_month, "name": month_to_str[last_month], "paid": 0}
        return orders
    @classmethod
    async def delete_invoice(cls, request: Request, schema: DeleteInvoiceSchema, current_user: Admin) -> list[InvoicesModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        invoice = await InvoicesModel.objects.filter(pk=int(schema.id)).afirst()
        if admin_object.user_group <= 2:
            if not invoice:
                raise HTTPException(status_code=404, detail="Invoice object is Exist")
        else:
            if admin_object.id == int(schema.admin_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if not invoice:
                raise HTTPException(status_code=409, detail="Invoice object is Exist")
        await InvoicesModel.objects.filter(pk=int(schema.id)).adelete()
        invoices = []
        async for invoice in (InvoicesModel.objects.filter(admin_id=int(schema.admin_id),
                                                           created_at__gte=datetime.datetime.today().replace(
                                                               day=1,
                                                               hour=0,
                                                               minute=0,
                                                               second=0,
                                                               microsecond=0)
                                                           )
                                                            .all().select_related('admin')
                                                            .order_by('-created_at')):  # .exclude(Q(quantity=0):
            invoiceFile = await InvoicesFileModel.objects.filter(invoice_id=invoice.id).afirst()
            invoices.append({
                "id": invoice.id,
                "admin_id": invoice.admin.id,
                "vendor": invoice.vendor,
                "invoice_number": invoice.invoice_number,
                "amount_paid": invoice.amount_paid,
                "invoice_file": str(invoiceFile.invoice_file) if invoiceFile else '',
                "date": invoice.date,
                "created_at": invoice.created_at,
            })
        return invoices

    @classmethod
    async def upload_invoice_file(cls,request:Request,file:UploadFile,vendor:str,current_user: Admin):
        ext = os.path.splitext(file.filename)[1].lower()
        valid_extensions = ['.pdf', '.docx',]
        # print(file)
        pass_name = ''
        if not vendor:
            pass_name = rand(1, 100)
        if ext in valid_extensions:
            # print(ext)
            filename = django_settings.MEDIA_ROOT + '/documents/invoices/' + vendor + '-' + pass_name + '_tmp'+ext
            # print(filename)
            with open(filename, 'wb+') as destination:
                destination.write(file.file.read())
                destination.close()
                # destination.write(chunk)
            # CustomUser.objects.values().filter(phone_number=phone_number_request,
            #                                    ip_address=ip_address).update(
            #     picture_profile=filename)
        else:
            raise HTTPException(status_code=404, detail="you'r file format is incorrect")
        return JSONResponse(content={"filename": filename, "detail": "success"})