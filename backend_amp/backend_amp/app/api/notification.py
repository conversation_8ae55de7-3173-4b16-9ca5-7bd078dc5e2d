from datetime import datetime, timezone
import datetime

from typing import List, Any, Dict
from asgiref.sync import sync_to_async
from django.utils.dateparse import parse_datetime
from fastapi import Request, HTTPException
from logging import getLogger
from django.utils import timezone
from datetime import timedelta
from datetime import date
from dateutil.relativedelta import relativedelta
from django.db.models import Q
from django.db.models.functions import Trunc
from app.models import Admin, OrdersModel, InventoryModel
from config.exceptions import PermissionDeniedException
from django.conf import settings

from app.schemas import UserReadNotificationSchema, EditNotificationSchema, ReadInventorySchema,EditSingleUserNotificationSchema

logger = getLogger(__name__)

from swapper import load_model

Notification = load_model('app', 'Notification')
CONFIG_DEFAULTS = {
    'PAGINATE_BY': 20,
    'USE_JSONFIELD': False,
    'SOFT_DELETE': False,
    'NUM_TO_FETCH': 10,
    'CACHE_TIMEOUT': 2,
}


def get_config():
    user_config = getattr(settings, 'DJANGO_NOTIFICATIONS_CONFIG', {})
    config = CONFIG_DEFAULTS.copy()
    config.update(user_config)
    return config


class NotificationAPI:
    def __init__(self):
        pass

    @classmethod
    async def all_notifications_list(cls, request: Request, current_user: Admin):
        admin_object = await Admin.objects.aget(email=current_user.email)
        notifications = []
        if admin_object.user_group <= 2:
            print("hi notification")
            user = await Admin.objects.aget(email=current_user.email)
            async for notification in Notification.objects.values().filter(recipient_id=user.id, unread=True):
                print(notification)
        else:
            print("hi notification")
            user = await Admin.objects.aget(email=current_user.email)
            async for notification in Notification.objects.values().filter(recipient_id=user.id, unread=True):
                print(notification)
        # if get_config()['SOFT_DELETE']:
        #     qset = self.request.user.notifications.active()
        # else:
        #     qset = self.request.user.notifications.all()
        # return qset

    @classmethod
    async def get_notification(cls, request: Request, current_user: Admin):
        admin_object = await Admin.objects.aget(email=current_user.email)
        notifications = []
        if admin_object.user_group <= 2:
            async for notification in Notification.objects.filter(recipient_group=2,
                                                                  created_at__gte=datetime.datetime.today().replace(
                                                                      day=1,
                                                                      hour=0,
                                                                      minute=0,
                                                                      second=0,
                                                                      microsecond=0),
                                                                  ).all().order_by(
                    '-timestamp'):  # .exclude(Q(quantity=0):
                admin_info = None
                async for admin in Admin.objects.filter(id=notification.recipient_id).all():
                    admin_info = admin
                notifications.append({
                    "id": notification.id,
                    "recipient": admin_info,
                    "unread": notification.unread,
                    "verb": notification.verb,
                    "description": notification.description,
                    "target_content_type_id": notification.target_content_type_id,
                    "target_object_id": notification.target_object_id,
                    # "inventory_object": inventory_info,
                    "timestamp": notification.timestamp,
                    "public": notification.public,
                })
            return notifications
        else:
            user_id=current_user.id
            if admin_object.user_group ==4:
                async for admin in Admin.objects.filter(id=admin_object.ref_admin_id).all():#.select_related('ref'):
                    user_id = admin.id
            async for notification in Notification.objects.filter(actor_object_id=user_id, recipient_group=3,
                                                                  created_at__gte=datetime.datetime.today().replace(
                                                                      day=1,
                                                                      hour=0,
                                                                      minute=0,
                                                                      second=0,
                                                                      microsecond=0),
                                                                  ):
                inventory_info = None
                async for inventory in InventoryModel.objects.filter(
                        id=notification.target_object_id).all().select_related('product'):
                    inventory_info = inventory
                notifications.append({
                    "id": notification.id,
                    # "recipient": notification.recipient_id,
                    "unread": notification.unread,
                    "verb": notification.verb,
                    "description": notification.description,
                    "target_content_type_id": notification.target_content_type_id,
                    "target_object_id": notification.target_object_id,
                    "inventory_object": inventory_info,
                    "timestamp": notification.timestamp,
                    "public": notification.public,
                })
            return notifications

    @classmethod
    async def read_notification(cls, request: Request, schema: EditNotificationSchema, current_user: Admin) -> list[
        Any]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        notifications = []
        notification = await Notification.objects.filter(id=schema.id).afirst()
        if not notification:
            HTTPException(status_code=404, detail="Notification dose not exist !!!!")
        if admin_object.user_group <= 2:
            await Notification.objects.filter(id=schema.id).aupdate(**{
                "unread": False
            })
            async for notification in Notification.objects.filter(recipient_group=2,
                                                                  created_at__gte=datetime.datetime.today().replace(
                                                                      day=1,
                                                                      hour=0,
                                                                      minute=0,
                                                                      second=0,
                                                                      microsecond=0),
                                                                  ).all().order_by(
                    '-timestamp'):  # .exclude(Q(quantity=0):
                admin_info = None
                async for admin in Admin.objects.filter(id=notification.recipient_id).all():
                    admin_info = admin
                notifications.append({
                    "id": notification.id,
                    "recipient": admin_info,
                    "unread": notification.unread,
                    "verb": notification.verb,
                    "description": notification.description,
                    "target_content_type_id": notification.target_content_type_id,
                    "target_object_id": notification.target_object_id,
                    # "inventory_object": inventory_info,
                    "timestamp": notification.timestamp,
                    "public": notification.public,
                })
        else:
            if admin_object.id == notification.recipient_id:
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id, ref_admin__id=notification.recipient_id).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            await Notification.objects.filter(id=schema.id).aupdate(**{
                "unread": False
            })
            async for notification in Notification.objects.filter(actor_object_id=notification.recipient_id, recipient_group=3,
                                                                  created_at__gte=datetime.datetime.today().replace(
                                                                      day=1,
                                                                      hour=0,
                                                                      minute=0,
                                                                      second=0,
                                                                      microsecond=0),
                                                                  ):
                inventory_info = None
                async for inventory in InventoryModel.objects.filter(
                        id=notification.target_object_id).all().select_related('product'):
                    inventory_info = inventory
                notifications.append({
                    "id": notification.id,
                    "recipient_id": notification.recipient_id,
                    "unread": notification.unread,
                    "verb": notification.verb,
                    "description": notification.description,
                    "target_content_type_id": notification.target_content_type_id,
                    "target_object_id": notification.target_object_id,
                    "inventory_object": inventory_info,
                    "timestamp": notification.timestamp,
                    "public": notification.public,
                })
        return notifications

    @classmethod
    async def read_single_user_notification(cls, request: Request, schema: EditSingleUserNotificationSchema,
                                            current_user: Admin) -> list[
        Any]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        notifications = []
        if admin_object.user_group <= 2:
            async for single_user_notification in Notification.objects.filter(recipient_id=schema.admin_id).all():
                await Notification.objects.filter(id=single_user_notification.id).aupdate(**{
                    "unread": False
                })
            async for notification in Notification.objects.filter(recipient_group=2,
                                                                  created_at__gte=datetime.datetime.today().replace(
                                                                      day=1,
                                                                      hour=0,
                                                                      minute=0,
                                                                      second=0,
                                                                      microsecond=0),
                                                                  ).all().order_by(
                '-timestamp'):  # .exclude(Q(quantity=0):
                admin_info = None
                async for admin in Admin.objects.filter(id=notification.recipient_id).all():
                    admin_info = admin
                notifications.append({
                    "id": notification.id,
                    "recipient": admin_info,
                    "unread": notification.unread,
                    "verb": notification.verb,
                    "description": notification.description,
                    "target_content_type_id": notification.target_content_type_id,
                    "target_object_id": notification.target_object_id,
                    # "inventory_object": inventory_info,
                    "timestamp": notification.timestamp,
                    "public": notification.public,
                })
        else:
            raise PermissionDeniedException
        return notifications

    @classmethod
    async def read_mark_notification(cls, request: Request, schema: EditNotificationSchema, current_user: Admin) -> list[
        Any]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        notifications = []
        notification = await Notification.objects.filter(id=schema.id).afirst()
        if not notification:
            HTTPException(status_code=404, detail="Notification dose not exist !!!!")
        if admin_object.user_group <= 2:
            await Notification.objects.filter(id=schema.id).aupdate(**{
                "unread": False
            })
            async for notification in Notification.objects.filter(recipient_group=2,
                                                                  created_at__gte=datetime.datetime.today().replace(
                                                                      day=1,
                                                                      hour=0,
                                                                      minute=0,
                                                                      second=0,
                                                                      microsecond=0),
                                                                  ).all().order_by(
                    '-timestamp'):  # .exclude(Q(quantity=0):
                admin_info = None
                async for admin in Admin.objects.filter(id=notification.recipient_id).all():
                    admin_info = admin
                print(admin_info)
                notifications.append({
                    "id": notification.id,
                    "recipient": admin_info,
                    "unread": notification.unread,
                    "verb": notification.verb,
                    "description": notification.description,
                    "target_content_type_id": notification.target_content_type_id,
                    "target_object_id": notification.target_object_id,
                    # "inventory_object": inventory_info,
                    "timestamp": notification.timestamp,
                    "public": notification.public,
                })
        else:
            if admin_object.id != notification.recipient_id:
                raise PermissionDeniedException
            await Notification.objects.filter(id=schema.id).aupdate(**{
                "unread": False
            })
            async for notification in Notification.objects.filter(actor_object_id=current_user.id, recipient_group=3,
                                                                  created_at__gte=datetime.datetime.today().replace(
                                                                      day=1,
                                                                      hour=0,
                                                                      minute=0,
                                                                      second=0,
                                                                      microsecond=0),
                                                                  ):
                inventory_info = None
                async for inventory in InventoryModel.objects.filter(
                        id=notification.target_object_id).all().select_related('product'):
                    inventory_info = inventory
                notifications.append({
                    "id": notification.id,
                    "recipient_id": notification.recipient_id,
                    "unread": notification.unread,
                    "verb": notification.verb,
                    "description": notification.description,
                    "target_content_type_id": notification.target_content_type_id,
                    "target_object_id": notification.target_object_id,
                    "inventory_object": inventory_info,
                    "timestamp": notification.timestamp,
                    "public": notification.public,
                })
        return notifications
    @classmethod
    async def read_all_notification(cls, request: Request, current_user: Admin) -> list[
        Any]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        notifications = []
        # notification = await Notification.objects.filter(id=schema.id).afirst()
        # if not notification:
        #     HTTPException(status_code=404, detail="Notification dose not exist !!!!")
        if admin_object.user_group <= 2:
            async for notification in Notification.objects.filter(recipient_group=2).all():
                await Notification.objects.filter(id=notification.id).aupdate(**{
                    "unread": False
                })
            async for notification in Notification.objects.filter(recipient_group=2,
                                                                  created_at__gte=datetime.datetime.today().replace(
                                                                      day=1,
                                                                      hour=0,
                                                                      minute=0,
                                                                      second=0,
                                                                      microsecond=0),
                                                                  ).all().order_by(
                    '-timestamp'):  # .exclude(Q(quantity=0):
                admin_info = None
                async for admin in Admin.objects.filter(id=notification.recipient_id).all():
                    admin_info = admin
                notifications.append({
                    "id": notification.id,
                    "recipient": admin_info,
                    "unread": notification.unread,
                    "verb": notification.verb,
                    "description": notification.description,
                    "target_content_type_id": notification.target_content_type_id,
                    "target_object_id": notification.target_object_id,
                    # "inventory_object": inventory_info,
                    "timestamp": notification.timestamp,
                    "public": notification.public,
                })
        else:
            async for notification in Notification.objects.filter(actor_object_id=current_user.id, recipient_group=3).all():
                await Notification.objects.filter(id=notification.id).aupdate(**{
                    "unread": False
                })
            async for notification in Notification.objects.filter(actor_object_id=current_user.id, recipient_group=3,
                                                                  created_at__gte=datetime.datetime.today().replace(
                                                                      day=1,
                                                                      hour=0,
                                                                      minute=0,
                                                                      second=0,
                                                                      microsecond=0),
                                                                  ):
                inventory_info = None
                async for inventory in InventoryModel.objects.filter(
                        id=notification.target_object_id).all().select_related('product'):
                    inventory_info = inventory
                notifications.append({
                    "id": notification.id,
                    "recipient_id": notification.recipient_id,
                    "unread": notification.unread,
                    "verb": notification.verb,
                    "description": notification.description,
                    "target_content_type_id": notification.target_content_type_id,
                    "target_object_id": notification.target_object_id,
                    "inventory_object": inventory_info,
                    "timestamp": notification.timestamp,
                    "public": notification.public,
                })
        return notifications
