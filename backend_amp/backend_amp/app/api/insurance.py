from app.models import Admin, InsuranceVerificationModel
from asgiref.sync import sync_to_async
from fastapi import HTTP<PERSON>x<PERSON>, Request
from config.exceptions import PermissionDeniedException
from app.schemas import CreateInsuranceVerificationSchema, EditInsuranceVerificationSchema, ReadInsuranceVerification, \
    DeleteInsuranceVerificationSchema
from datetime import datetime, timezone
from logging import getLogger

logger = getLogger(__name__)


class InsuranceAPI:
    @classmethod
    async def get_insurance(cls, request: Request, user_id: str, current_user: Admin) -> list[
        InsuranceVerificationModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        i_admin = await sync_to_async(Admin.objects.get)(**{
            "email": current_user.email
        })
        async for group in i_admin.groups.all():
            if group.name == "Insurance_Verification":
                insurances = []
                if admin_object.user_group <= 2:
                    async for insurance in InsuranceVerificationModel.objects.filter(
                            admin=user_id).all().select_related(
                            'admin').order_by(
                        '-created_at'):
                        insurances.append(insurance)
                else:
                    if admin_object.id == int(user_id):
                        pass
                    else:
                        find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                                   ref_admin__id=user_id).afirst()
                        if not find_sub_user:
                            raise PermissionDeniedException
                    async for insurance in InsuranceVerificationModel.objects.filter(
                            admin=user_id).all().select_related(
                            'admin').order_by(
                        '-created_at'):
                        insurances.append(insurance)
                return insurances
        raise PermissionDeniedException

    @classmethod
    async def create_insurance(cls, request: Request, schema: CreateInsuranceVerificationSchema,
                               current_user: Admin) -> InsuranceVerificationModel:
        schema.admin_id = int(schema.admin_id)
        admin_object = await Admin.objects.aget(email=current_user.email)
        i_admin = await sync_to_async(Admin.objects.get)(**{
            "email": current_user.email
        })
        async for group in i_admin.groups.all():
            if group.name == "Insurance_Verification":
                exist_object = await InsuranceVerificationModel.objects.filter(
                    patiant_name=schema.patiant_name).afirst()
                insurances = []
                if admin_object.user_group <= 2:
                    if exist_object:
                        raise HTTPException(status_code=409, detail="Insurance  is Exist")
                else:
                    if admin_object.id == int(schema.admin_id):
                        pass
                    else:
                        find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                                   ref_admin__id=int(schema.admin_id)).afirst()
                        if not find_sub_user:
                            raise PermissionDeniedException
                    if exist_object:
                        raise HTTPException(status_code=409, detail="Insurance  is Exist")
                if not schema.date_of_birth:
                    del schema.date_of_birth
                if not schema.plan_holder_date_of_birth:
                    del schema.plan_holder_date_of_birth
                await sync_to_async(InsuranceVerificationModel.objects.create)(**schema.dict())
                async for insurance in InsuranceVerificationModel.objects.filter(
                        admin=schema.admin_id).all().select_related(
                        'admin').order_by(
                    '-created_at'):
                    insurances.append(insurance)
                return insurances
        raise PermissionDeniedException

    @classmethod
    async def update_insurance(cls, request: Request, schema: EditInsuranceVerificationSchema, current_user: Admin) -> \
            list[InsuranceVerificationModel]:
        schema.admin_id = int(schema.admin_id)
        admin_object = await Admin.objects.aget(email=current_user.email)
        i_admin = await sync_to_async(Admin.objects.get)(**{
            "email": current_user.email
        })
        async for group in i_admin.groups.all():
            if group.name == "Insurance_Verification":
                insurances = []
                exist_object = await InsuranceVerificationModel.objects.filter(id=int(schema.id)).afirst()
                if not schema.date_of_birth:
                    del schema.date_of_birth
                if not schema.plan_holder_date_of_birth:
                    del schema.plan_holder_date_of_birth
                if admin_object.user_group <= 2:
                    if not exist_object:
                        raise HTTPException(status_code=404, detail="Insurance dose not Exist")
                else:
                    if admin_object.id == int(schema.admin_id):
                        pass
                    else:
                        find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                                   ref_admin__id=int(schema.admin_id)).afirst()
                        if not find_sub_user:
                            raise PermissionDeniedException
                    if not exist_object:
                        raise HTTPException(status_code=404, detail="Insurance dose not Exist")

                await InsuranceVerificationModel.objects.filter(pk=schema.id).aupdate(**schema.dict())
                async for order in InsuranceVerificationModel.objects.filter(
                        admin=int(schema.admin_id)).all().select_related(
                        'admin').order_by(
                    '-created_at'):
                    insurances.append(order)
                return insurances
        raise PermissionDeniedException

    @classmethod
    async def delete_insurance(cls, request: Request, schema: DeleteInsuranceVerificationSchema, current_user: Admin) -> \
            list[InsuranceVerificationModel]:
        i_admin = await sync_to_async(Admin.objects.get)(**{
            "email": current_user.email
        })
        async for group in i_admin.groups.all():
            if group.name == "Insurance_Verification":
                exist_object = await InsuranceVerificationModel.objects.filter(pk=int(schema.id)).afirst()
                admin_object = await Admin.objects.aget(email=current_user.email)
                insurances = []
                if admin_object.user_group <= 2:
                    if not exist_object:
                        raise HTTPException(status_code=404, detail="Insurance dose not Exist")
                else:
                    if admin_object.id == int(schema.admin_id):
                        pass
                    else:
                        find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                                   ref_admin__id=int(schema.admin_id)).afirst()
                        if not find_sub_user:
                            raise PermissionDeniedException
                    if not exist_object:
                        raise HTTPException(status_code=404, detail="Insurance dose not Exist")
                await InsuranceVerificationModel.objects.filter(pk=int(schema.id)).adelete()
                async for insurance in InsuranceVerificationModel.objects.filter(
                        admin=int(schema.admin_id)).all().select_related(
                    'admin').order_by(
                    '-created_at'):
                    insurances.append(insurance)
                return insurances
        raise PermissionDeniedException
