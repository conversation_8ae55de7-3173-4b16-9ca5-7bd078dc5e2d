from datetime import datetime, timezone
from typing import List, Any, Dict

from asgiref.sync import sync_to_async
from django.utils.dateparse import parse_datetime
from fastapi import Request, HTTPException
from logging import getLogger
from django.utils import timezone
from datetime import timedelta
from datetime import date
from dateutil.relativedelta import relativedelta

from django.db.models.functions import Trunc
from app.models import Admin, ProductsModel, InventoryModel
from config.exceptions import PermissionDeniedException

from app.schemas import ReadInventorySchema, CreateInventorySchema, CreateProductSchema, ReadProductSchema, \
    DeleteInventorySchema, EditInventorySchema
from app.signals import notify

logger = getLogger(__name__)

from swapper import load_model


Notification = load_model('app', 'Notification')


class InventoryAPI:
    @classmethod
    async def get_by_id(cls, request: Request, inventory_id: id, current_user: Admin) -> InventoryModel:
        inventory_id = int(inventory_id)
        admin_object = await Admin.objects.aget(email=current_user.email)
        inventory = await InventoryModel.objects.filter(pk=inventory_id).select_related('product', 'admin').afirst()
        if inventory:
            return inventory
        else:
            HTTPException(status_code=404, detail="inventory dose not exist !!!!")

    @classmethod
    async def get(cls, request: Request, user_id: str, current_user: Admin) -> list[InventoryModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        is_admin = await Admin.objects.values().filter(email=current_user.email).afirst()
        inventories = []
        if admin_object.user_group <= 2:
            async for inventory in InventoryModel.objects.filter(admin=int(user_id)).all().select_related('product',
                                                                                                          'admin').order_by(
                '-created_at'):
                # order.status = order.get_status()
                inventories.append(inventory)
            return inventories
        else:
            if admin_object.id == int(user_id):
                pass
            else:
                find_sub_user=await Admin.objects.filter(id=admin_object.id,ref_admin__id=user_id).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            async for inventory in InventoryModel.objects.filter(admin=int(user_id)).all().select_related('product',
                                                                                                          'admin').order_by(
                '-created_at'):
                inventories.append(inventory)
            return inventories


    @classmethod
    async def create(cls, request: Request, schema: CreateInventorySchema, current_user: Admin) -> list[InventoryModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        product_schema = CreateProductSchema(name=schema.product, status=1,s_k_u=schema.s_k_u)
        product_object = await ProductsModel.objects.filter(name=product_schema.name,s_k_u=product_schema.s_k_u).afirst()
        if admin_object.user_group <= 2:
            if not product_object:
                product_object = await sync_to_async(ProductsModel.objects.create)(**product_schema.dict())
        else:
            if admin_object.id == int(schema.admin_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if not product_object:
                product_object = await sync_to_async(ProductsModel.objects.create)(**product_schema.dict())
        del schema.s_k_u
        # product_object = await ProductsModel.objects.filter(name=product_schema.name).afirst()
        # if not product_object:
        #     product_object = await sync_to_async(ProductsModel.objects.create)(**product_schema.dict())
        schema.product = product_object
        if not schema.expiration_date:
            del schema.expiration_date
        await sync_to_async(InventoryModel.objects.create)(**schema.dict())
        inventories = []
        async for order in (InventoryModel.objects.filter(admin=int(schema.admin_id))
                .all().select_related('product', 'admin')
                .order_by('-created_at')):
            inventories.append(order)
        return inventories

    @classmethod
    async def delete_inventory(cls, request: Request, schema: DeleteInventorySchema, current_user: Admin) -> list[
        InventoryModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        inventory = await InventoryModel.objects.filter(pk=int(schema.id)).afirst()
        if admin_object.user_group <= 2:
            if not inventory:
                raise HTTPException(status_code=404, detail="Inventory dose not Exist")
        else:
            if admin_object.id == int(schema.admin_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if not inventory:
                raise HTTPException(status_code=404, detail="Inventory dose not Exist")
        await InventoryModel.objects.filter(pk=int(schema.id)).adelete()
        inventories = []
        async for inventory in (InventoryModel.objects.filter(admin=int(schema.admin_id))
                .all().select_related('product', 'admin')
                .order_by('-created_at')):
            inventories.append(inventory)
        return inventories

    @classmethod
    async def update_inventory(cls, request: Request, schema: EditInventorySchema, current_user: Admin) -> list[
        InventoryModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        inventory = await InventoryModel.objects.filter(pk=int(schema.id),admin=int(schema.admin_id)).afirst()
        product_schema = CreateProductSchema(name=schema.product, status=1, s_k_u=schema.s_k_u)
        product_object = await ProductsModel.objects.filter(name=product_schema.name,
                                                            s_k_u=product_schema.s_k_u).afirst()
        if admin_object.user_group <= 2:
            if not product_object:
                product_object = await sync_to_async(ProductsModel.objects.create)(**product_schema.dict())
            if not inventory:
                raise HTTPException(status_code=404, detail="Inventory dose not Exist")
        else:
            if admin_object.id == int(schema.admin_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if not product_object:
                product_object = await sync_to_async(ProductsModel.objects.create)(**product_schema.dict())
            if not inventory:
                raise HTTPException(status_code=404, detail="Inventory dose not Exist")
        del schema.s_k_u
        schema.product=product_object.id
        if schema.qty < schema.min_qty:
            if admin_object.is_admin:
                admin_object = await Admin.objects.aget(pk=schema.admin_id)
            notify_check = await Notification.objects.filter(target_object_id=int(schema.id)).afirst()
            if notify_check:
                await Notification.objects.filter(target_object_id=int(schema.id)).aupdate(**{
                    "unread": True
                })
            else:
                await sync_to_async(notify.send)(**{
                    "sender": await Admin.objects.aget(pk=schema.admin_id),
                    "recipient": await Admin.objects.aget(pk=schema.admin_id),
                    "verb": 'Low Inventory Alert',
                    "target": inventory,
                    "description": f"Inventory for {product_object.name} is below minimum quantity. Current: {schema.qty}, Minimum: {schema.min_qty}",
                    "recipient_group": 3
                })
        if not schema.expiration_date:
            del schema.expiration_date
        await InventoryModel.objects.filter(id=int(schema.id),admin=int(schema.admin_id)).aupdate(**schema.dict())
        inventories = []
        async for inventory in (InventoryModel.objects.filter(admin=int(schema.admin_id))
                .all().select_related('product', 'admin')
                .order_by('-created_at')):
            inventories.append(inventory)
        return inventories

