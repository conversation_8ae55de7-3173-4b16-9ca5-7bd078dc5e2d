from logging import getLogger

from app.models import Admin, PatientExperienceModel
from asgiref.sync import sync_to_async
from fastapi import HTTP<PERSON>x<PERSON>, Request
from config.exceptions import PermissionDeniedException
from app.schemas import ReadPatientExperienceSchema, CreatePatientExperienceSchema, EditPatientExperienceSchema, DeletePatientExperienceSchema
from datetime import datetime, timezone

from logging import getLogger

logger = getLogger(__name__)


class PatientExperienceAPI:
    @classmethod
    async def get_patient_experience(cls, request: Request, user_id: str, current_user: Admin) -> list[
        PatientExperienceModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        is_admin = await Admin.objects.values().filter(email=current_user.email).afirst()
        i_admin = await sync_to_async(Admin.objects.get)(**{
            "email": current_user.email
        })
        # groups=await sync_to_async(i_admin.groups.all)()
        # print(groups)
        async for group in i_admin.groups.all():
            if group.name == "Patient_Experience":
                patients_experience = []
                if admin_object.user_group <= 2:
                    async for patient_experience in PatientExperienceModel.objects.filter(admin=user_id).all().select_related(
                            'admin').order_by(
                        '-created_at'):
                        patients_experience.append(patient_experience)
                else:
                    if admin_object.id == int(user_id):
                        pass
                    else:
                        find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                                   ref_admin__id=user_id).afirst()
                        if not find_sub_user:
                            raise PermissionDeniedException
                    async for patient_experience in PatientExperienceModel.objects.filter(admin=user_id).all().select_related(
                            'admin').order_by(
                        '-created_at'):
                        patients_experience.append(patient_experience)
                return patients_experience
        raise PermissionDeniedException

    @classmethod
    async def create_patient_experience(cls, request: Request, schema: CreatePatientExperienceSchema,
                                  current_user: Admin) -> list[PatientExperienceModel]:
        schema.admin_id = int(schema.admin_id)
        i_admin = await sync_to_async(Admin.objects.get)(**{
            "email": current_user.email
        })
        async for group in i_admin.groups.all():
            if group.name == "Patient_Experience":
                exist_object = await PatientExperienceModel.objects.filter(patiant_name=schema.patiant_name).afirst()
                patients_experience = []
                admin_object = await Admin.objects.aget(email=current_user.email)
                if admin_object.user_group <= 2:
                    if exist_object:
                        raise HTTPException(status_code=404, detail="PatientExperience is Exist")
                else:
                    if admin_object.id == int(schema.admin_id):
                        pass
                    else:
                        find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                                   ref_admin__id=int(schema.admin_id)).afirst()
                        if not find_sub_user:
                            raise PermissionDeniedException
                    if exist_object:
                        raise HTTPException(status_code=404, detail="PatientExperience is Exist")
                if not schema.last_visited:
                    del schema.last_visited
                await sync_to_async(PatientExperienceModel.objects.create)(**schema.dict())
                async for patient_experience in PatientExperienceModel.objects.filter(admin=schema.admin_id).all().select_related(
                        'admin').order_by(
                    '-created_at'):
                    patients_experience.append(patient_experience)
                return patients_experience
        raise PermissionDeniedException

    @classmethod
    async def update_patient_experience(cls, request: Request, schema: EditPatientExperienceSchema,
                                  current_user: Admin) -> \
            list[PatientExperienceModel]:
        schema.admin_id = int(schema.admin_id)
        i_admin = await sync_to_async(Admin.objects.get)(**{
            "email": current_user.email
        })
        async for group in i_admin.groups.all():
            if group.name == "Patient_Experience":
                patients_experience = []
                exist_object = await PatientExperienceModel.objects.filter(pk=schema.id).afirst()
                if not schema.last_visited:
                    del schema.last_visited
                admin_object = await Admin.objects.aget(email=current_user.email)
                if admin_object.user_group <= 2:
                    if not exist_object:
                        raise HTTPException(status_code=404, detail="PatientExperience dose not Exist")
                else:
                    if admin_object.id == int(schema.admin_id):
                        pass
                    else:
                        find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                                   ref_admin__id=int(schema.admin_id)).afirst()
                        if not find_sub_user:
                            raise PermissionDeniedException
                    if not exist_object:
                        raise HTTPException(status_code=404, detail="PatientExperience dose not  Exist")
                await PatientExperienceModel.objects.filter(pk=schema.id).aupdate(**schema.dict())
                async for patient_experience in PatientExperienceModel.objects.filter(admin=int(schema.admin_id)).all().select_related(
                        'admin').order_by(
                    '-created_at'):
                    patients_experience.append(patient_experience)
                return patients_experience
        raise PermissionDeniedException

    @classmethod
    async def delete_patient_experience(cls, request: Request, schema: DeletePatientExperienceSchema,
                                  current_user: Admin) -> \
            list[PatientExperienceModel]:
        i_admin = await sync_to_async(Admin.objects.get)(**{
            "email": current_user.email
        })
        async for group in i_admin.groups.all():
            if group.name == "Patient_Experience":
                exist_object = await PatientExperienceModel.objects.filter(pk=int(schema.id)).afirst()
                patients_experience = []
                admin_object = await Admin.objects.aget(email=current_user.email)
                if admin_object.user_group <= 2:
                    if not exist_object:
                        raise HTTPException(status_code=404, detail="PatientExperience dose not Exist")
                else:
                    if admin_object.id == int(schema.admin_id):
                        pass
                    else:
                        find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                                   ref_admin__id=int(schema.admin_id)).afirst()
                        if not find_sub_user:
                            raise PermissionDeniedException
                    if not exist_object:
                        raise HTTPException(status_code=404, detail="PatientExperience dose not  Exist")

                await PatientExperienceModel.objects.filter(pk=int(schema.id)).adelete()
                async for patient_experience in PatientExperienceModel.objects.filter(
                        admin=int(exist_object.admin_id)).all().select_related(
                    'admin').order_by(
                    '-created_at'):
                    patients_experience.append(patient_experience)
                return patients_experience
        raise PermissionDeniedException