import os
import uuid

from logging import getLogger
from typing import Any
from django.contrib.auth.models import Group
from app.models import Admin, SERVICES_GROUP
from app.schemas import Create<PERSON>serSchema, EditUserBudgetSchema, DeleteUserSchema, EditUserSchema, ReadUsersSchema, \
    UploadFilesSchema
from asgiref.sync import sync_to_async, async_to_sync
from config.password import hash_password
from fastapi.responses import JSONResponse
from jose import JWTError
from fastapi import HTTPException, Request, UploadFile, Form
from django.conf import settings as django_settings
from PIL import Image, UnidentifiedImageError
from io import BytesIO
from config.exceptions import PermissionDeniedException

logger = getLogger(__name__)


class UserAPI:
    @classmethod
    async def get(cls, request: Request, current_user: Admin) -> Admin:
        admin_object = await Admin.objects.aget(email=current_user.email)  # .select_related('ref_admin')
        i_admin = await sync_to_async(Admin.objects.get)(**{
            "email": current_user.email
        })
        subscription = []
        async for group in i_admin.groups.all():
            subscription.append(group.name)
        user = {
            "id": admin_object.id,
            "uuid": admin_object.uuid,
            "username": admin_object.username,
            "first_name": admin_object.first_name,
            "last_name": admin_object.last_name,
            "email": admin_object.email,
            "is_active": admin_object.is_active,
            "is_admin": admin_object.is_admin,
            "dental_logo": str(admin_object.dental_logo),
            "user_group": admin_object.user_group,
            "total_budget": admin_object.total_budget,
            "groups": subscription
        }
        if admin_object.user_group == 4:
            ref_obj = await Admin.objects.aget(id=admin_object.ref_admin_id)
            i_admin = await sync_to_async(Admin.objects.get)(**{
                "email": ref_obj.email
            })
            groups = []
            async for group in i_admin.groups.all():
                groups.append(group.name)
            user["ref_admin"] = {
                "id": ref_obj.id,
                "uuid": ref_obj.uuid,
                "username": ref_obj.username,
                "first_name": ref_obj.first_name,
                "last_name": ref_obj.last_name,
                "email": ref_obj.email,
                "is_active": ref_obj.is_active,
                "is_admin": ref_obj.is_admin,
                "dental_logo": str(ref_obj.dental_logo),
                "user_group": ref_obj.user_group,
                "total_budget": ref_obj.total_budget,
                "groups": groups
            }
        return user

    @classmethod
    async def get_subscription(cls, request: Request, current_user: Admin) -> list[Any]:
        i_admin = await sync_to_async(Admin.objects.get)(**{
            "email": current_user.email
        })
        subscription = []
        async for group in i_admin.groups.all():
            subscription.append(group.name)
        return subscription

    @classmethod
    async def create(cls, request: Request, schema: CreateUserSchema, current_user: Admin) -> list[Admin]:
        is_admin = await Admin.objects.values().filter(email=current_user.email).afirst()
        user_check = await Admin.objects.filter(email=schema.email).afirst()
        groups = schema.groups
        del schema.groups
        users = []
        if user_check:
            raise HTTPException(status_code=400, detail="Email already registered")
        schema.password = hash_password(schema.password)
        if is_admin["user_group"] <= 2:
            if schema.ref_admin:
                schema.ref_admin = await Admin.objects.aget(pk=schema.ref_admin)
                schema.user_group = 4
            else:
                del schema.ref_admin
            await sync_to_async(Admin.objects.create)(**schema.dict())
            i_admin = await sync_to_async(Admin.objects.get)(**{
                "email": schema.email
            })
            for group in groups:
                group_object = await sync_to_async(Group.objects.get)(**{
                    'name': group
                })
                await sync_to_async(i_admin.groups.add)(group_object)
            async for user in Admin.objects.filter(user_group=3).all().select_related('ref_admin'):
                user.avatar = user.avatar.__str__()
                user.dental_logo = user.dental_logo.__str__()
                i_admin = await sync_to_async(Admin.objects.get)(**{
                    "email": user.email
                })
                groups = []
                async for group in i_admin.groups.all():
                    groups.append(group.name)
                sub_users = []
                async for subuser in Admin.objects.filter(ref_admin__id=user.id).all():
                    object_subuser = await sync_to_async(Admin.objects.get)(**{
                        "email": subuser.email
                    })
                    subuser_groups = []
                    async for group in object_subuser.groups.all():
                        subuser_groups.append(group.name)
                    sub_users.append({
                        "id": subuser.id,
                        "email": subuser.email,
                        "first_name": subuser.first_name,
                        "last_name": subuser.last_name,
                        "phone_number": subuser.phone_number,
                        "avatar": subuser.avatar.__str__(),
                        "dental_name": subuser.dental_name,
                        "is_active": subuser.is_active,
                        "is_admin": subuser.is_admin,
                        "total_budget": subuser.total_budget,
                        "groups": subuser_groups,
                    })
                users.append({
                    "id": user.id,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "phone_number": user.phone_number,
                    "avatar": user.avatar.__str__(),
                    "dental_name": user.dental_name,
                    "dental_logo": str(user.dental_logo),
                    "is_active": user.is_active,
                    "is_admin": user.is_admin,
                    "total_budget": user.total_budget,
                    "ref_admin": user.ref_admin,
                    "groups": groups,
                    "sub_user": sub_users
                })
                # users.append(user)
        else:
            # raise PermissionDeniedException
            schema.ref_admin = await Admin.objects.aget(email=current_user.email)
            await sync_to_async(Admin.objects.create)(**schema.dict())
            await Admin.objects.filter(email=schema.email).aupdate(**{
                "user_group": 4
            })
            i_admin = await sync_to_async(Admin.objects.get)(**{
                "email": schema.email
            })
            for group in groups:
                group_object = await sync_to_async(Group.objects.get)(**{
                    'name': group
                })
                await sync_to_async(i_admin.groups.add)(group_object)
            async for user in Admin.objects.filter(
                    ref_admin=await Admin.objects.aget(email=current_user.email)).all().select_related('ref_admin'):
                user.avatar = user.avatar.__str__()
                user.dental_logo = user.dental_logo.__str__()
                i_admin = await sync_to_async(Admin.objects.get)(**{
                    "email": user.email
                })
                groups = []
                async for group in i_admin.groups.all():
                    groups.append(group.name)
                users.append({
                    "id": user.id,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "phone_number": user.phone_number,
                    "avatar": user.avatar.__str__(),
                    "dental_name": user.dental_name,
                    "dental_logo": str(user.dental_logo),
                    "is_active": user.is_active,
                    "is_admin": user.is_admin,
                    "total_budget": user.total_budget,
                    "ref_admin": user.ref_admin,
                    "groups": groups
                })
        return users

    @classmethod
    async def create_subuser(cls, request: Request, schema: CreateUserSchema, current_user: Admin) -> list[Admin]:
        is_admin = await Admin.objects.values().filter(email=current_user.email).afirst()
        user_check = await Admin.objects.filter(email=schema.email).afirst()
        groups = schema.groups
        del schema.groups
        users = []
        if user_check:
            raise HTTPException(status_code=400, detail="Email already registered")
        schema.password = hash_password(schema.password)
        if is_admin["user_group"] <= 2:
            schema.ref_admin = await Admin.objects.aget(email=current_user.email)
            await sync_to_async(Admin.objects.create)(**schema.dict())
            i_admin = await sync_to_async(Admin.objects.get)(**{
                "email": schema.email
            })
            for group in groups:
                group_object = await sync_to_async(Group.objects.get)(**{
                    'name': group
                })
                await sync_to_async(i_admin.groups.add)(group_object)
            async for user in Admin.objects.filter(user_group__range=[2, 3]).all().select_related('ref_admin'):
                user.avatar = user.avatar.__str__()
                user.dental_logo = user.dental_logo.__str__()
                i_admin = await sync_to_async(Admin.objects.get)(**{
                    "email": user.email
                })
                groups = []
                async for group in i_admin.groups.all():
                    groups.append(group.name)
                users.append({
                    "id": user.id,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "phone_number": user.phone_number,
                    "avatar": user.avatar.__str__(),
                    "dental_name": user.dental_name,
                    "is_active": user.is_active,
                    "is_admin": user.is_admin,
                    "total_budget": user.total_budget,
                    "ref_admin": user.ref_admin,
                    "groups": groups
                })
        else:
            schema.ref_admin = await Admin.objects.aget(email=current_user.email)
            await sync_to_async(Admin.objects.create)(**schema.dict())
            await Admin.objects.filter(email=schema.email).aupdate(**{
                "user_group": 4
            })
            i_admin = await sync_to_async(Admin.objects.get)(**{
                "email": schema.email
            })
            for group in groups:
                group_object = await sync_to_async(Group.objects.get)(**{
                    'name': group
                })
                await sync_to_async(i_admin.groups.add)(group_object)
            async for user in Admin.objects.filter(
                    ref_admin=await Admin.objects.aget(email=current_user.email)).all().select_related('ref_admin'):
                user.avatar = user.avatar.__str__()
                user.dental_logo = user.dental_logo.__str__()
                i_admin = await sync_to_async(Admin.objects.get)(**{
                    "email": user.email
                })
                groups = []
                async for group in i_admin.groups.all():
                    groups.append(group.name)
                users.append({
                    "id": user.id,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "phone_number": user.phone_number,
                    "avatar": user.avatar.__str__(),
                    "dental_name": user.dental_name,
                    "is_active": user.is_active,
                    "is_admin": user.is_admin,
                    "total_budget": user.total_budget,
                    "ref_admin": user.ref_admin,
                    "groups": groups
                })
        return users

    @classmethod
    async def get_user_list(cls, request: Request, current_user: Admin) -> list[Admin]:
        is_admin = await Admin.objects.filter(email=current_user.email).afirst()
        if is_admin:
            users = []
            async for user in Admin.objects.filter(is_active=True, user_group=3).all():
                user.dental_logo = user.dental_logo.__str__()
                i_admin = await sync_to_async(Admin.objects.get)(**{
                    "email": user.email
                })
                groups = []
                async for group in i_admin.groups.all():
                    groups.append(group.name)
                users.append({
                    "id": user.id,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "phone_number": user.phone_number,
                    "dental_name": user.dental_name,
                    "dental_logo": str(user.dental_logo),
                    "is_active": user.is_active,
                    "is_admin": user.is_admin,
                    "total_budget": user.total_budget,
                    "groups": groups
                })
                # users.append(user)
            return users
        raise PermissionDeniedException

    @classmethod
    async def update_user(cls, request: Request, schema: EditUserSchema, current_user: Admin) -> list[Any]:
        user = await Admin.objects.aget(pk=int(schema.id))
        is_admin = await Admin.objects.filter(email=current_user.email).afirst()
        if not user:
            raise HTTPException(status_code=404, detail="user is not found")
        else:
            if is_admin.user_group <= 2:
                groups = schema.groups
                del schema.groups
                await Admin.objects.filter(pk=int(schema.id)).aupdate(**schema.dict())
                i_admin = await sync_to_async(Admin.objects.get)(**{
                    "email": schema.email
                })
                for group in SERVICES_GROUP:
                    group_object = await sync_to_async(Group.objects.get)(**{
                        'name': group[1]
                    })
                    if group[1] in groups:
                        await sync_to_async(group_object.user_set.add)(i_admin)
                    else:
                        await sync_to_async(group_object.user_set.remove)(i_admin)
                users = []
                async for user in (Admin.objects.filter(user_group=3).all().select_related('ref_admin')):
                    user.avatar = user.avatar.__str__()
                    user.dental_logo = user.dental_logo.__str__()
                    i_admin = await sync_to_async(Admin.objects.get)(**{
                        "email": user.email
                    })
                    groups = []
                    async for group in i_admin.groups.all():
                        groups.append(group.name)
                    sub_users = []
                    async for subuser in Admin.objects.filter(ref_admin__id=user.id).all():
                        object_subuser = await sync_to_async(Admin.objects.get)(**{
                            "email": subuser.email
                        })
                        subuser_groups = []
                        async for group in object_subuser.groups.all():
                            subuser_groups.append(group.name)
                        sub_users.append({
                            "id": subuser.id,
                            "email": subuser.email,
                            "first_name": subuser.first_name,
                            "last_name": subuser.last_name,
                            "phone_number": subuser.phone_number,
                            "avatar": subuser.avatar.__str__(),
                            "dental_logo": str(subuser.dental_logo),
                            "dental_name": subuser.dental_name,
                            "is_active": subuser.is_active,
                            "is_admin": subuser.is_admin,
                            "total_budget": subuser.total_budget,
                            "groups": subuser_groups,
                        })
                    users.append({
                        "id": user.id,
                        "email": user.email,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "phone_number": user.phone_number,
                        "avatar": user.avatar.__str__(),
                        "dental_logo": str(user.dental_logo),
                        "dental_name": user.dental_name,
                        "is_active": user.is_active,
                        "is_admin": user.is_admin,
                        "total_budget": user.total_budget,
                        "ref_admin": user.ref_admin,
                        "groups": groups,
                        "sub_user": sub_users
                    })
                    # users.append(user)
            else:
                if user.user_group == 4 and user.ref_admin_id == is_admin.id:
                    # schema.ref_admin = await Admin.objects.aget(email=current_user.email)
                    groups = schema.groups
                    del schema.groups
                    await Admin.objects.filter(pk=int(schema.id)).aupdate(**schema.dict())
                    i_admin = await sync_to_async(Admin.objects.get)(**{
                        "email": schema.email
                    })
                    for group in SERVICES_GROUP:
                        group_object = await sync_to_async(Group.objects.get)(**{
                            'name': group[1]
                        })
                        if group[1] in groups:
                            await sync_to_async(group_object.user_set.add)(i_admin)
                        else:
                            await sync_to_async(group_object.user_set.remove)(i_admin)
                    users = []
                    async for user in (Admin.objects.filter(
                            ref_admin=await Admin.objects.aget(email=current_user.email)).all().select_related(
                            'ref_admin')):
                        user.avatar = user.avatar.__str__()
                        user.dental_logo = user.dental_logo.__str__()
                        i_admin = await sync_to_async(Admin.objects.get)(**{
                            "email": user.email
                        })
                        groups = []
                        async for group in i_admin.groups.all():
                            groups.append(group.name)
                        users.append({
                            "id": user.id,
                            "email": user.email,
                            "first_name": user.first_name,
                            "last_name": user.last_name,
                            "phone_number": user.phone_number,
                            "avatar": user.avatar.__str__(),
                            "dental_logo": str(user.dental_logo),
                            "dental_name": user.dental_name,
                            "is_active": user.is_active,
                            "is_admin": user.is_admin,
                            "total_budget": user.total_budget,
                            "ref_admin": user.ref_admin,
                            "groups": groups
                        })
                    # users.append(user)
                else:
                    raise PermissionDeniedException
            return users

    @classmethod
    async def update_budget(cls, request: Request, schema: EditUserBudgetSchema, current_user: Admin) -> Admin:
        user_exist = await Admin.objects.filter(pk=int(schema.id), is_active=True).afirst()
        if user_exist:
            await Admin.objects.filter(pk=int(schema.id)).aupdate(**schema.dict())
            # return await Admin.objects.filter(pk=int(schema.id)).afirst()
            return JSONResponse(content={"detail": "success", })
        else:
            raise HTTPException(status_code=404, detail="user dose not exist")

    @classmethod
    async def get_users(cls, request: Request, current_user: Admin) -> list[Any]:
        is_admin = await Admin.objects.values().filter(email=current_user.email).afirst()
        users = []
        if is_admin["user_group"] <= 2:
            async for user in Admin.objects.filter(user_group=3).all():
                user.avatar = user.avatar.__str__()
                i_admin = await sync_to_async(Admin.objects.get)(**{
                    "email": user.email
                })
                groups = []
                async for group in i_admin.groups.all():
                    groups.append(group.name)
                sub_users = []
                async for subuser in Admin.objects.filter(ref_admin__id=user.id).all():
                    object_subuser = await sync_to_async(Admin.objects.get)(**{
                        "email": subuser.email
                    })
                    subuser_groups = []
                    async for group in object_subuser.groups.all():
                        subuser_groups.append(group.name)
                    sub_users.append({
                        "id": subuser.id,
                        "email": subuser.email,
                        "first_name": subuser.first_name,
                        "last_name": subuser.last_name,
                        "phone_number": subuser.phone_number,
                        "dental_logo": str(subuser.dental_logo),
                        "avatar": subuser.avatar.__str__(),
                        "dental_name": subuser.dental_name,
                        "is_active": subuser.is_active,
                        "is_admin": subuser.is_admin,
                        "total_budget": subuser.total_budget,
                        "groups": subuser_groups,
                    })
                # user.dental_logo = user.get_dental_logo()
                # schema=ReadUsersSchema(user)
                # schema.groups=groups
                users.append({
                    "id": user.id,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "phone_number": user.phone_number,
                    "avatar": user.avatar.__str__(),
                    "dental_logo": str(user.dental_logo),
                    "dental_name": user.dental_name,
                    "is_active": user.is_active,
                    "is_admin": user.is_admin,
                    "total_budget": user.total_budget,
                    "groups": groups,
                    "sub_user": sub_users
                })
        else:
            async for user in Admin.objects.filter(ref_admin=await Admin.objects.aget(email=current_user.email)).all():
                user.avatar = user.avatar.__str__()
                i_admin = await sync_to_async(Admin.objects.get)(**{
                    "email": user.email
                })
                groups = []
                async for group in i_admin.groups.all():
                    groups.append(group.name)
                # user.dental_logo = user.get_dental_logo()
                # schema=ReadUsersSchema(user)
                # schema.groups=groups
                users.append({
                    "id": user.id,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "phone_number": user.phone_number,
                    "avatar": user.avatar.__str__(),
                    "dental_logo": str(user.dental_logo),
                    "dental_name": user.dental_name,
                    "is_active": user.is_active,
                    "is_admin": user.is_admin,
                    "total_budget": user.total_budget,
                    "groups": groups
                })
        return users

    @classmethod
    async def delete_user(cls, request: Request, schema: DeleteUserSchema, current_user: Admin) -> list[Admin]:
        user = await Admin.objects.aget(pk=int(schema.id))
        is_admin = await Admin.objects.filter(email=current_user.email).afirst()
        if user:
            users = []
            if is_admin.user_group <= 2:
                await Admin.objects.filter(pk=int(schema.id)).adelete()
                async for user in (Admin.objects.filter(user_group=3).all().select_related('ref_admin')):
                    user.avatar = user.avatar.__str__()
                    user.dental_logo = user.dental_logo.__str__()
                    i_admin = await sync_to_async(Admin.objects.get)(**{
                        "email": user.email
                    })
                    groups = []
                    async for group in i_admin.groups.all():
                        groups.append(group.name)
                    sub_users = []
                    async for subuser in Admin.objects.filter(ref_admin__id=user.id).all():
                        object_subuser = await sync_to_async(Admin.objects.get)(**{
                            "email": subuser.email
                        })
                        subuser_groups = []
                        async for group in object_subuser.groups.all():
                            subuser_groups.append(group.name)
                        sub_users.append({
                            "id": subuser.id,
                            "email": subuser.email,
                            "first_name": subuser.first_name,
                            "last_name": subuser.last_name,
                            "phone_number": subuser.phone_number,
                            "avatar": subuser.avatar.__str__(),
                            "dental_logo": str(subuser.dental_logo),
                            "dental_name": subuser.dental_name,
                            "is_active": subuser.is_active,
                            "is_admin": subuser.is_admin,
                            "total_budget": subuser.total_budget,
                            "groups": subuser_groups,
                        })
                    users.append({
                        "id": user.id,
                        "email": user.email,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "phone_number": user.phone_number,
                        "avatar": user.avatar.__str__(),
                        "dental_logo": str(user.dental_logo),
                        "dental_name": user.dental_name,
                        "is_active": user.is_active,
                        "is_admin": user.is_admin,
                        "total_budget": user.total_budget,
                        "ref_admin": user.ref_admin,
                        "groups": groups,
                        "sub_user": sub_users
                    })
            else:
                if user.user_group == 4 and user.ref_admin_id == is_admin.id:
                    await Admin.objects.filter(pk=int(schema.id)).adelete()
                    async for user in (
                    Admin.objects.filter(ref_admin=await Admin.objects.aget(email=current_user.email)).all()):
                        user.avatar = user.avatar.__str__()
                        user.dental_logo = user.dental_logo.__str__()
                        i_admin = await sync_to_async(Admin.objects.get)(**{
                            "email": user.email
                        })
                        groups = []
                        async for group in i_admin.groups.all():
                            groups.append(group.name)
                        users.append({
                            "id": user.id,
                            "email": user.email,
                            "first_name": user.first_name,
                            "last_name": user.last_name,
                            "phone_number": user.phone_number,
                            "avatar": user.avatar.__str__(),
                            "dental_logo": str(user.dental_logo),
                            "dental_name": user.dental_name,
                            "is_active": user.is_active,
                            "is_admin": user.is_admin,
                            "total_budget": user.total_budget,
                            "groups": groups
                        })
                else:
                    raise PermissionDeniedException
            return users
        else:
            raise HTTPException(status_code=404, detail="user is not found")

    @classmethod
    async def upload_picture(cls, request: Request, file: UploadFile, first_name: str, current_user: Admin):
        ALLOWED_EXTENSIONS = ['.png', '.jpg', '.jpeg']
        ext = os.path.splitext(file.filename)[-1].lower()
        if ext not in ALLOWED_EXTENSIONS:
            raise HTTPException(status_code=400, detail="Unsupported file type. Only JPG, JPEG, and PNG allowed.")

        try:
            # تلاش برای باز کردن تصویر برای بررسی معتبر بودن
            image = Image.open(BytesIO(await file.read())).convert("RGB")
        except UnidentifiedImageError:
            raise HTTPException(status_code=400, detail="Invalid image file.")
            # ریسایز کردن با حفظ نسبت تصویر
        target_width, target_height = 250, 80
        image.thumbnail((target_width, target_height), Image.ANTIALIAS)
        new_image = Image.new("RGB", (target_width, target_height), (255, 255, 255))
        left = (target_width - image.width) // 2
        top = (target_height - image.height) // 2
        new_image.paste(image, (left, top))

        # ساخت مسیر ذخیره‌سازی
        file_name = f"{uuid.uuid4().hex}.jpg"
        relative_path = os.path.join("documents", "dental_pictures", file_name)
        absolute_path = os.path.join(django_settings.MEDIA_ROOT, relative_path)
        os.makedirs(os.path.dirname(absolute_path), exist_ok=True)

        # ذخیره تصویر
        new_image.save(absolute_path, format="JPEG", quality=95)
        return JSONResponse(content={"filename": absolute_path, "detail": "success"})
