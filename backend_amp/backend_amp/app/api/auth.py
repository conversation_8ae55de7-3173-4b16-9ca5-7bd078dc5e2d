from logging import get<PERSON>ogger
from typing import Any

from app.models import Admin
from config.exceptions import (
    InvalidCredentialsException,
    InvalidEmailOrPasswordException,
)
from config.jwt import create_access_token_response
from config.password import verify_password

from fastapi import Request
from fastapi.security import OAuth2PasswordRequestForm

logger = getLogger(__name__)


class AuthAPI:
    @classmethod
    async def login(
            cls, request: Request, form_data: OAuth2PasswordRequestForm
    ) -> Any:
        credentials = {"email": form_data.username, "password": form_data.password}
        if all(credentials.values()):
            user = await cls()._authenticate_user(**credentials)
        else:
            raise InvalidCredentialsException()
        return create_access_token_response(
            {"sub": str(user.uuid)})
            # {"sub": str(user.uuid), "is_active": user.is_active, "user_group": user.user_group"is_active": user.is_active, "user_group": user.user_group})

    async def _authenticate_user(self, email: str, password: str) -> Admin:
        user = await Admin.objects.filter(email=email).afirst()
        if not user:
            raise InvalidEmailOrPasswordException()
        if not verify_password(password, user.password) or not user.is_active:
            raise InvalidEmailOrPasswordException()
        return user

    @classmethod
    async def validate_token(cls, request: Request, current_user: Admin) -> Any:
        """
        Simple endpoint to validate if a token is still valid.
        If this endpoint is reached with the current_user dependency, the token is valid.
        """
        return {"status": "valid", "user_id": str(current_user.uuid)}
