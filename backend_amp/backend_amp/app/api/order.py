from __future__ import annotations

from datetime import datetime, timezone
from typing import List, Any, Dict
import datetime
from asgiref.sync import sync_to_async
from django.utils.dateparse import parse_datetime
from fastapi import Request, HTTPException
from logging import getLogger
from django.utils import timezone
from datetime import timedelta
from datetime import date
from dateutil.relativedelta import relativedelta

from django.db.models.functions import Trunc
from app.models import Admin, OrdersModel, ProductsModel, VendorsModel, InventoryModel  # , NotificationModel
from config.exceptions import PermissionDeniedException

from app.schemas import CreateOrdersSchema, CreateProductSchema, CreateVendorFromOrderSchema, CreateVendorsSchema, \
    ReadProductSchema, \
    EditOrderSchema, EditOrderPaidSchema, EditFavoriteSchema, DeleteOrder
from app.signals import notify
from app.email import sender_notification_to_admin

from django.core.mail import EmailMessage
from django.template.loader import render_to_string

logger = getLogger(__name__)

month_to_str = {
    1: "January",
    2: "February",
    3: "March",
    4: "April",
    5: "May",
    6: "June",
    7: "July",
    8: "August",
    9: "September",
    10: "October",
    11: "November",
    12: "December",
}


class OrderAPI:
    @classmethod
    async def get(cls, request: Request, user_id: str | None, current_user: Admin) -> list[OrdersModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        orders = []
        if user_id is not None:
            if admin_object.user_group <= 2:
                async for order in (
                        OrdersModel.objects.filter(admin=int(user_id)).all().select_related('product', 'vendor')
                                .order_by('-created_at')):
                    order.status = order.get_status()
                    orders.append(order)
                return orders
            else:
                if admin_object.id == int(user_id):
                    pass
                else:
                    find_sub_user = await Admin.objects.filter(id=admin_object.id, ref_admin__id=user_id).afirst()
                    if not find_sub_user:
                        raise PermissionDeniedException
                async for order in OrdersModel.objects.filter(admin=int(user_id)).all().select_related('product',
                                                                                                       'vendor').order_by(
                    '-created_at'):
                    order.status = order.get_status()
                    orders.append(order)
                return orders
        else:
            return orders

    @classmethod
    async def create(cls, request: Request, schema: CreateOrdersSchema, current_user: Admin) -> list[OrdersModel]:
        # try:
        admin_object = await Admin.objects.aget(email=current_user.email)
        product_schema = CreateProductSchema(name=schema.product, status=1,s_k_u=schema.s_k_u)
        product_object = await ProductsModel.objects.filter(name=product_schema.name,s_k_u=product_schema.s_k_u).afirst()
        if not product_object:
            product_object = await sync_to_async(ProductsModel.objects.create)(**product_schema.dict())
        vendor_object = None
        if schema.vendor:
            vendor_schema = CreateVendorFromOrderSchema(admin_id=int(schema.admin_id), name=schema.vendor)
            vendor_object = await VendorsModel.objects.filter(name=vendor_schema.name).afirst()
            if not vendor_object:
                vendor_object = await sync_to_async(VendorsModel.objects.create)(**vendor_schema.dict())
        schema.product = product_object
        schema.vendor = vendor_object
        del schema.s_k_u
        if not schema.urgent:
            del schema.timeline
        order = await sync_to_async(OrdersModel.objects.create)(**schema.dict())
        if admin_object.user_group > 2:
            await sync_to_async(notify.send)(**{
                "sender": admin_object,
                "recipient": admin_object,
                "verb": 'New Order Created',
                "target": order,
                "description": f"New order #{order.id} created for {product_object.name}",
                "recipient_group": 2
            })
        # if schema.admin_id == current_user.id:
        #     print('sender email')
        #     async for admin in Admin.objects.filter(user_group=1).all():
        #         sender = await Admin.objects.aget(id=schema.admin_id)
        #         await sync_to_async(sender_notification_to_admin)(
        #             **{
        #                 "sender": sender.first_name,
        #                 "order_content": schema.product.name,
        #                 "admin": {'name': admin.first_name, 'email': admin.email}
        #             })
        orders = []
        async for order in (OrdersModel.objects.filter(admin=int(schema.admin_id))
                .all().select_related('product', 'vendor')
                .order_by('-created_at')):
            order.status = order.get_status()
            orders.append(order)
        return orders

    # except Exception as e:
    #     print(e)
    #     HTTPException(status_code=404, detail="can't create order")

    @classmethod
    async def update(cls, request: Request, schema: EditOrderSchema, current_user: Admin) -> list[OrdersModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        order = await OrdersModel.objects.filter(pk=int(schema.id), admin=int(schema.admin_id)).afirst()
        if admin_object.user_group <= 2:
            if not order:
                raise HTTPException(status_code=404, detail="Order object dose not Exist")
        else:
            if admin_object.id == int(schema.admin_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if not order:
                raise HTTPException(status_code=409, detail="Order object dose not Exist")
        product_schema = CreateProductSchema(name=schema.product, status=1,s_k_u=schema.s_k_u)
        product_object = await ProductsModel.objects.filter(name=product_schema.name,s_k_u=product_schema.s_k_u).afirst()
        if not product_object:
            product_object = await sync_to_async(ProductsModel.objects.create)(**product_schema.dict())
        vendor_object = None
        if schema.vendor:
            vendor_schema = CreateVendorFromOrderSchema(admin_id=int(schema.admin_id), name=schema.vendor)
            vendor_object = await VendorsModel.objects.filter(name=vendor_schema.name).afirst()
            if not vendor_object:
                vendor_object = await sync_to_async(VendorsModel.objects.create)(**vendor_schema.dict())
        del schema.s_k_u
        schema.product = product_object
        schema.vendor = vendor_object
        if not schema.urgent:
            del schema.timeline
        #     timeline = datetime.fromisoformat(schema.timeline[:-1]).astimezone(timezone.utc)
        #     schema.timeline = timeline
        # else:
        if schema.received:
            inventory = await InventoryModel.objects.filter(admin=int(schema.admin_id),
                                                            product=product_object.id).afirst()
            if schema.status == 8:  # Order Returned
                # Notify all admins with user_group 2
                async for admin in Admin.objects.filter(user_group=2).all():
                    await sync_to_async(notify.send)(
                        sender=admin_object,
                        recipient=admin,
                        verb='Order Returned',
                        target=order,
                        description=f"Order #{order.id} for {product_object.name} has been returned by {admin_object.first_name}",
                        recipient_group=2
                    )

                    # Send email notification - removed sync_to_async since function is already async
                    await sender_notification_to_admin(
                        sender=admin_object.first_name,
                        order_content=f"Order {order.id} has been returned",
                        admin={'name': admin.first_name, 'email': admin.email}
                    )
            elif schema.status == 5:  # Partially Delivered
                qty = schema.qty
                qty_delivered = schema.qty_delivered
                qty_pending = schema.qty_pending
                del schema.qty_pending, schema.qty_delivered
                schema.qty = int(qty_delivered)
                await OrdersModel.objects.filter(pk=int(order.id)).aupdate(**schema.dict())
                if inventory:
                    await InventoryModel.objects.filter(admin=int(schema.admin_id),
                                                        product=product_object.id).aupdate(**{
                        "qty": inventory.qty + schema.qty,
                    })
                else:
                    await sync_to_async(InventoryModel.objects.create)(**{
                        "admin_id": int(schema.admin_id),
                        "product_id": product_object.id,
                        "qty": schema.qty,
                        "notes": "From AMP"
                    })
                schema.qty = int(qty_pending)
                schema.status = 3
                schema.received = False
                del schema.id
                await sync_to_async(OrdersModel.objects.create)(**schema.dict())
                
                # Notify about partial delivery
                async for admin in Admin.objects.filter(user_group=2).all():
                    await sync_to_async(notify.send)(
                        sender=admin_object,
                        recipient=admin,
                        verb='Partial Delivery',
                        target=order,
                        description=f"Order #{order.id} for {product_object.name} has been partially delivered ({qty_delivered} of {qty} units)",
                        recipient_group=2
                    )
            else:
                if inventory:
                    await InventoryModel.objects.filter(admin=int(schema.admin_id),
                                                        product=product_object.id).aupdate(**{
                        "qty": inventory.qty + schema.qty,
                    })
                else:
                    await sync_to_async(InventoryModel.objects.create)(**{
                        "admin_id": int(schema.admin_id),
                        "product_id": product_object.id,
                        "qty": schema.qty,
                        "notes": "From AMP"
                    })
                del schema.qty_pending, schema.qty_delivered
                await OrdersModel.objects.filter(pk=int(order.id)).aupdate(**schema.dict())
        else:
            del schema.qty_pending, schema.qty_delivered
            await OrdersModel.objects.filter(pk=int(order.id)).aupdate(**schema.dict())
        if admin_object.user_group==2:
            await OrdersModel.objects.filter(pk=int(order.id)).aupdate(**{
                "created_at": timezone.now()
            })
        orders = []
        async for order in OrdersModel.objects.filter(admin=int(schema.admin_id)).all().select_related(
                'product',
                'vendor').order_by(
            '-created_at'):
            order.status = order.get_status()
            orders.append(order)
        return orders
    
    @classmethod
    async def update_favorite(cls, request: Request, schema: EditFavoriteSchema, current_user: Admin) -> list[OrdersModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        order = await OrdersModel.objects.filter(pk=int(schema.id),admin=int(schema.admin_id)).afirst()
        if admin_object.user_group <= 2:
            if not order:
                raise HTTPException(status_code=404, detail="Order object dose not Exist")
        else:
            if admin_object.id == int(schema.admin_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if not order:
                raise HTTPException(status_code=409, detail="Order object dose not Exist")
        await OrdersModel.objects.filter(pk=int(order.id),admin=int(schema.admin_id)).aupdate(**schema.dict())
        orders = []
        async for order in OrdersModel.objects.filter(admin=int(schema.admin_id)).all().select_related(
                'product',
                'vendor').order_by(
            '-created_at'):
            order.status = order.get_status()
            orders.append(order)
        return orders


    @classmethod
    async def update_paid(cls, request: Request, schema: EditOrderPaidSchema, current_user: Admin) -> list[OrdersModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        order = await OrdersModel.objects.filter(pk=int(schema.id),admin=int(schema.admin_id)).afirst()
        if admin_object.user_group <= 2:
            if not order:
                raise HTTPException(status_code=404, detail="Order object dose not Exist")
        else:
            if admin_object.id == int(schema.admin_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if not order:
                raise HTTPException(status_code=404, detail="Order object dose not Exist")
        await sync_to_async(OrdersModel.objects.filter(pk=int(schema.id),admin=int(schema.admin_id)).update)(**schema.dict())
        orders = []
        async for order in (OrdersModel.objects.filter(admin=int(order.admin_id))
                .all().select_related('product', 'vendor')
                .order_by('-created_at')):
            order.status = order.get_status()
            orders.append(order)
        return orders

    @classmethod
    async def delete_order(cls, request: Request, schema: DeleteOrder, current_user: Admin) -> list[OrdersModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        order = await OrdersModel.objects.filter(pk=int(schema.id),admin=int(schema.admin_id)).afirst()
        if admin_object.user_group <= 2:
            if not order:
                raise HTTPException(status_code=404, detail="Order object dose not Exist")
        else:
            if admin_object.id == int(schema.admin_id):
                pass
            else:
                find_sub_user = await Admin.objects.filter(id=admin_object.id,
                                                           ref_admin__id=int(schema.admin_id)).afirst()
                if not find_sub_user:
                    raise PermissionDeniedException
            if not order:
                raise HTTPException(status_code=409, detail="Order object dose not Exist")
        await OrdersModel.objects.filter(pk=int(schema.id),admin=int(schema.admin_id)).adelete()
        orders = []
        async for order in (OrdersModel.objects.filter(admin=int(schema.admin_id))
                .all().select_related('product', 'vendor')
                .order_by('-created_at')):
            order.status = order.get_status()
            orders.append(order)
        return orders

    @classmethod
    async def summery_reports(cls, request, user_id:int, current_user):
        user_object = await Admin.objects.aget(id=current_user.id)
        orders = {}
        
        # Permission check
        if user_object.user_group > 2 and user_object.id != int(user_id):
            find_sub_user = await Admin.objects.filter(id=user_object.id,
                                                       ref_admin__id=int(user_id)).afirst()
            if not find_sub_user:
                raise PermissionDeniedException
        
        # Get the user whose data we're retrieving
        target_user = await Admin.objects.aget(id=int(user_id))
        
        # Get all orders for this user
        reports = OrdersModel.objects.filter(
            admin_id=int(user_id),
            created_at__gte=target_user.created_at
        ).values(
            'paid', 'would_paid', 'saved', 'created_at', 'qty'
        )
        
        # Process orders data
        async for report in reports:
            created_at = report['created_at'].__str__()
            year = int(created_at.split('-')[0])
            month = int(created_at.split('-')[1])

            if year not in orders:
                orders[year] = {}
            if month not in orders[year]:
                orders[year][month] = {"month_id": month, "name": month_to_str[month], "paid": 0, "would_paid": 0,
                                       "saved": 0, 'qty': 0, 'count_report': 0}

            orders[year][month]["paid"] += report['paid']*report['qty']
            orders[year][month]["would_paid"] += report['would_paid']*report['qty']
            orders[year][month]["saved"] += report['saved']*report['qty']
            orders[year][month]["qty"] += report['qty']
            orders[year][month]["count_report"] += 1
        
        # Fill in all months since user joined
        join_date = target_user.created_at
        # Use django's timezone.now() instead of datetime.now()
        from django.utils import timezone

        current_date = timezone.now()
        
        # Start from the join date
        current_year = join_date.year
        current_month = join_date.month
        
        # Loop through all months until current date
        while (current_year < current_date.year) or (current_year == current_date.year and current_month <= current_date.month):
            if current_year not in orders:
                orders[current_year] = {}
                
            if current_month not in orders[current_year]:
                orders[current_year][current_month] = {
                    "month_id": current_month, 
                    "name": month_to_str[current_month], 
                    "paid": 0, 
                    "would_paid": 0,
                    "saved": 0, 
                    'qty': 0, 
                    'count_report': 0
                }
            
            # Move to next month
            current_month += 1
            if current_month > 12:
                current_month = 1
                current_year += 1
        return orders

