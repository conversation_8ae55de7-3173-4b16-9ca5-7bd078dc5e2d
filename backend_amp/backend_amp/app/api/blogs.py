import os
import uuid
from typing import List

from asgiref.sync import sync_to_async
import datetime

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request, UploadFile, Form, File
from django.conf import settings as django_settings
from fastapi.responses import <PERSON><PERSON>NResponse

from PIL import Image, UnidentifiedImageError
from io import BytesIO

from app.models import Admin, BlogModel, BlogPictureModel,BlogFileModel  # , NotificationModel

from app.schemas import CreateBlogSchema, DeleteBlogSchema, EditBlogSchema, EditBlogStatusSchema, BlogByIdSchema


class BlogsAPI:
    @classmethod
    async def get_blogs(cls, request: Request, current_user: Admin) -> list[BlogModel]:
        admin_object = await Admin.objects.aget(email=current_user.email)
        blogs = []
        # if admin_object.user_group >= 3:
        async for blog in (BlogModel.objects.filter(
                                                    created_at__gte=datetime.datetime.today()
                                                        .replace(
                                                            day=1,
                                                            hour=0,
                                                            minute=0,
                                                            second=0,
                                                            microsecond=0),
                                                        )
                                                    .all().select_related('post_author')
                                                    .order_by('-created_at')):  # .exclude(Q(quantity=0):
            blogPicture = []
            async for blog_img in BlogPictureModel.objects.filter(blog_id=blog.id).all():
                blogPicture.append({"id":blog_img.id,"img_url":blog_img.image.__str__()})
            blogFile = await BlogFileModel.objects.filter(blog_id=blog.id).afirst()
            blogs.append({
                "id": blog.id,
                "post_title": blog.post_title,
                "post_description": blog.post_description,
                "post_content": blog.post_content,
                "post_status":blog.post_status,
                "post_author": blog.post_author.first_name,
                "post_image": blogPicture,
                "blog_file": str(blogFile.blog_file)if blogFile else '',
                "created_at": blog.created_at,
            })
        return blogs

    @classmethod
    async def blog_by_id(cls, request: Request, blog_id: str | None, current_user: Admin) -> BlogByIdSchema:
        admin_object = await Admin.objects.aget(email=current_user.email)
        # if admin_object.user_group >= 3:
        blog = {}
        async for blog in BlogModel.objects.filter(id=int(blog_id)).all().select_related(
                'post_author'):  # .exclude(Q(quantity=0):
            blogPicture = []
            blog_file=''
            async for blog_img in BlogPictureModel.objects.filter(blog_id=blog.id).all():
                blogPicture.append({"id":blog_img.id,"img_url":blog_img.image.__str__()})
            # blogPicture = await BlogPictureModel.objects.filter(blog_id=blog.id).afirst()
            blogFile = await BlogFileModel.objects.filter(blog_id=blog.id).afirst()
            blog={
                "id": blog.id,
                "post_title": blog.post_title,
                "post_description": blog.post_description,
                "post_content": blog.post_content,
                "post_image": blogPicture,
                "blog_file": str(blogFile.blog_file)if blogFile else '',
                "post_status": blog.post_status,
                "post_author": blog.post_author.first_name,
                "created_at": blog.created_at,
            }
        return blog

    @classmethod
    async def insert_blog(cls, request: Request, schema: CreateBlogSchema, current_user: Admin) -> list[BlogModel]:
        admin_object = await Admin.objects.values().aget(email=current_user.email)
        if admin_object.user_group >= 3:
            raise HTTPException(status_code=401,detail="you don't have enough permission to create blog")
        blogs = []
        blog = await BlogModel.objects.filter(post_title=schema.post_title).afirst()
        if blog:
            raise HTTPException(status_code=400, detail="blog already inserted")
        post_image = schema.post_image
        blog_file = schema.blog_file
        del schema.post_image
        del schema.blog_file
        schema.post_author = await Admin.objects.aget(email=current_user.email)
        blog,status=await sync_to_async(BlogModel.objects.create)(**schema.dict())
        for img in schema.post_content:
            await sync_to_async(BlogPictureModel.objects.create)(**{
                "blog": blog.id,
                "image": img,
            })
        await sync_to_async(BlogFileModel.objects.create)(**{
            "blog": await BlogModel.objects.aget(post_title=schema.post_title),
            "blog_file": blog_file,
        })
        async for blog in BlogModel.objects.filter(created_at__gte=datetime.datetime.today()
                                                        .replace(
                                                            day=1,
                                                            hour=0,
                                                            minute=0,
                                                            second=0,
                                                            microsecond=0),
                                                        ).all().select_related('post_author').order_by('-created_at'):  # .exclude(Q(quantity=0):
            blogPicture = []
            async for blog_img in BlogPictureModel.objects.filter(blog_id=blog.id).all():
                blogPicture.append({"id":blog_img.id,"img_url":blog_img.image.__str__()})
            blogFile = await BlogFileModel.objects.filter(blog_id=blog.id).afirst()
            blogs.append({
                "id": blog.id,
                "post_title": blog.post_title,
                "post_description": blog.post_description,
                "post_content": blog.post_content,
                "post_author": blog.post_author.first_name,
                "post_image": blogPicture,
                "blog_file": str(blogFile.blog_file)if blogFile else '',
                "created_at": blog.created_at,
            })
        return blogs

    @classmethod
    async def upload_blog_picture(cls, request: Request, files: List[UploadFile], post_title: str, current_user: Admin):
        files_name = []
        for file in files:
            ALLOWED_EXTENSIONS = ['.png', '.jpg', '.jpeg']
            ext = os.path.splitext(file.filename)[-1].lower()
            if ext not in ALLOWED_EXTENSIONS:
                raise HTTPException(status_code=400, detail="Unsupported file type. Only JPG, JPEG, and PNG allowed.")
            try:
                # تلاش برای باز کردن تصویر برای بررسی معتبر بودن
                image = Image.open(BytesIO(await file.read())).convert("RGB")
            except UnidentifiedImageError:
                raise HTTPException(status_code=400, detail="Invalid image file.")
                # ریسایز کردن با حفظ نسبت تصویر
            target_width, target_height = 250, 80
            image.thumbnail((target_width, target_height), Image.ANTIALIAS)
            new_image = Image.new("RGB", (target_width, target_height), (255, 255, 255))
            left = (target_width - image.width) // 2
            top = (target_height - image.height) // 2
            new_image.paste(image, (left, top))
            # ساخت مسیر ذخیره‌سازی
            file_name = f"{uuid.uuid4().hex}.jpg"
            relative_path = os.path.join("documents", "blogs", file_name)
            absolute_path = os.path.join(django_settings.MEDIA_ROOT, relative_path)
            os.makedirs(os.path.dirname(absolute_path), exist_ok=True)
            # ذخیره تصویر
            new_image.save(absolute_path, format="JPEG", quality=95)
            files_name.append(absolute_path)
        return JSONResponse(content={"files_name": files_name, "detail": "success"})

    @classmethod
    async def upload_blog_file(cls,request:Request,file:UploadFile,post_title:str,current_user: Admin):
        ext = os.path.splitext(file.filename)[1].lower()
        valid_extensions = ['.pdf', '.docx',]
        print(file)
        pass_name = ''
        if not post_title:
            pass_name = rand(1, 100)
        if ext in valid_extensions:
            print(ext)
            filename = django_settings.MEDIA_ROOT + '/documents/blogs/doc/' + post_title + '-' + pass_name + '_tmp'+ext
            print(filename)
            with open(filename, 'wb+') as destination:
                destination.write(file.file.read())
                destination.close()
                # destination.write(chunk)
            # CustomUser.objects.values().filter(phone_number=phone_number_request,
            #                                    ip_address=ip_address).update(
            #     picture_profile=filename)
        else:
            raise HTTPException(status_code=404, detail="you'r file format is incorrect")
        return JSONResponse(content={"filename": filename, "detail": "success"})
    @classmethod
    async def edit_blog_status(cls,request:Request,schema:EditBlogStatusSchema,current_user:Admin):
        admin_object = await Admin.objects.aget(email=current_user.email)
        if admin_object.user_group >= 3:
            raise HTTPException(status_code=401, detail="you don't have enough permission to create blog")
        blogs = []
        blog = await BlogModel.objects.filter(id=schema.id).afirst()
        if not blog:
            raise HTTPException(status_code=400, detail="blog dose not exists")
        await BlogModel.objects.filter(id=schema.id).aupdate(**{
            "post_status": schema.post_status,
        })
        async for blog in (BlogModel.objects.filter(
                                                    created_at__gte=datetime.datetime.today()
                                                        .replace(
                                                            day=1,
                                                            hour=0,
                                                            minute=0,
                                                            second=0,
                                                            microsecond=0),
                                                        )
                                                    .all().select_related('post_author')
                                                    .order_by('-created_at')):  # .exclude(Q(quantity=0):
            blogPicture = []
            async for blog_img in BlogPictureModel.objects.filter(blog_id=blog.id).all():
                blogPicture.append({"id": blog_img.id, "img_url": blog_img.image.__str__()})
            blogFile = await BlogFileModel.objects.filter(blog_id=blog.id).afirst()
            blogs.append({
                "id": blog.id,
                "post_title": blog.post_title,
                "post_description": blog.post_description,
                "post_content": blog.post_content,
                "post_status":blog.post_status,
                "post_author": blog.post_author.first_name,
                "post_image": blogPicture,
                "blog_file": str(blogFile.blog_file)if blogFile else '',
                "created_at": blog.created_at,
            })
        return blogs
    @classmethod
    async def edit_blog(cls,request:Request,schema:EditBlogSchema,current_user:Admin)-> list[BlogModel]:
        is_admin = await Admin.objects.values().filter(email=current_user.email).afirst()
        blog = await BlogModel.objects.filter(pk=int(schema.id)).afirst()
        if blog:
            blogs = []
            post_image = schema.post_image
            post_file = schema.blog_file
            del schema.post_image,schema.blog_file
            schema.post_author = await Admin.objects.aget(email=current_user.email)
            # async for pic in BlogPictureModel.objects.values('image').filter(blog=blog).all():
            #     print(pic)
                # for image in post_image:

                #     if image==blog_picture.image:
            await BlogModel.objects.filter(pk=int(schema.id)).aupdate(**schema.dict())
            # post_image = schema.post_image
            # blog_file = schema.blog_file
            # await sync_to_async(BlogPictureModel.objects.create)(**{
            #     "blog": await BlogModel.objects.aget(post_title=schema.post_title),
            #     "image": post_image,
            #     "blog_file": blog_file
            # })
            # await sync_to_async(BlogPictureModel.objects.create)(**{
            #     "blog": await BlogModel.objects.aget(post_title=schema.id),
            #     "image": post_image
            # })
            async for blog in BlogModel.objects.filter( created_at__gte=datetime.datetime.today()
                                                        .replace(
                                                            day=1,
                                                            hour=0,
                                                            minute=0,
                                                            second=0,
                                                            microsecond=0),
                                                        ).all().select_related('post_author').order_by(
                    '-created_at'):
                blogPicture = []
                async for blog_img in BlogPictureModel.objects.filter(blog_id=blog.id).all():
                    blogPicture.append({"id": blog_img.id, "img_url": blog_img.image.__str__()})
                blogFile = await BlogFileModel.objects.filter(blog_id=blog.id).afirst()
                blogs.append({
                    "id": blog.id,
                    "post_title": blog.post_title,
                    "post_description": blog.post_description,
                    "post_content": blog.post_content,
                    "post_status":blog.post_status,
                    "post_author": blog.post_author.first_name,
                    "post_image": blogPicture,
                    "blog_file": str(blogFile.blog_file),
                    "created_at": blog.created_at,
                })
            return blogs

        else:
            raise HTTPException(status_code=404, detail="Blog dose not exist !!!!")

    @classmethod
    async def delete_blog(cls,request:Request,schema:DeleteBlogSchema,current_user:Admin)-> list[BlogModel]:
        blog = await BlogModel.objects.filter(pk=int(schema.id)).afirst()
        if blog:
            await BlogModel.objects.filter(pk=int(schema.id)).adelete()
            await BlogPictureModel.objects.filter(blog_id=int(schema.id)).adelete()
            await BlogFileModel.objects.filter(blog_id=int(schema.id)).adelete()
            blogs = []
            async for blog in BlogModel.objects.filter( created_at__gte=datetime.datetime.today()
                                                        .replace(
                                                            day=1,
                                                            hour=0,
                                                            minute=0,
                                                            second=0,
                                                            microsecond=0),
                                                        ).all().select_related('post_author').order_by(
                    '-created_at'):  # .exclude(Q(quantity=0):
                blogPicture = []
                async for blog_img in BlogPictureModel.objects.filter(blog_id=blog.id).all():
                    blogPicture.append({"id":blog_img.id,"img_url":blog_img.image.__str__()})
                blogFile = await BlogFileModel.objects.filter(blog_id=blog.id).afirst()
                blogs.append({
                    "id": blog.id,
                    "post_title": blog.post_title,
                    "post_description": blog.post_description,
                    "post_content": blog.post_content,
                    "post_author": blog.post_author.first_name,
                    "post_image": blogPicture,
                    "blog_file": str(blogFile.blog_file),
                    "created_at": blog.created_at,
                })
            return blogs
        else:
            raise HTTPException(status_code=404, detail="Blog dose not exist !!!!")
