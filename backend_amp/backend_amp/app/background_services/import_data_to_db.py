import os
from datetime import datetime
import pandas as pd
from django.conf import settings as django_settings
from swapper import load_model

OrderStatus = (
    (1, 'back_order', 'BackOrder'),
    (2, 'incoming', 'Incoming'),
    (3, 'in_progress', 'In-Progress'),
    (4, 'confirmed', 'Confirmed'),
    (5, 'partially_delivered', 'Partially Delivered'),
    (6, 'delivered', 'Delivered'),
    (7, 'in_storage', 'In Storage'),
    (8, 'returned', 'Returned'),
    (9, 'partially_returned', 'Partially Returned'),
)
row_count = 59
sheet_name = {
    0: "Order Sheet",
    1: "Received",
    2: "Budget"
}
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
import django

django.setup()
Admin = load_model('app', 'Admin')

# ProductsModel = load_model('app', 'ProductsModel')
# VendorsModel = load_model('app', 'VendorsModel')
# OrdersModel = load_model('app', 'OrdersModel')
# InventoryModel = load_model('app', 'InventoryModel')
PatientExperienceModel = load_model('app', 'PatientExperienceModel')
InsuranceVerificationModel = load_model('app', 'InsuranceVerificationModel')

order_auth = Admin.objects.get(id=3)


# from backend_amp.app.models import Admin
def import_order():
    df = pd.read_excel('media/management/upload/test-data-mock_5srfYym.xlsx', sheet_name=0, skiprows=2, )
    headers = df.columns.tolist()
    df.dropna(subset=['DATE'], inplace=True)
    r = 0
    for index, row in df.iterrows():
        if row['DATE'] is not None:
            r = r + 1
            check_nan_value = row.isna()
            if check_nan_value.iloc[1]:
                continue
            row_status = 0
            for index, status in enumerate(OrderStatus):
                if status[2] == row.iloc[7]:
                    row_status = status[0]
            product_object = ProductsModel.objects.get_or_create(**{"name": row.iloc[1]})
            if not check_nan_value.iloc[4]:
                vendor_object = VendorsModel.objects.get_or_create(**{'admin': order_auth, 'name': row.iloc[4]})
            else:
                vendor_object = None
            object_create = {
                'admin': order_auth,
                'product': product_object[0],
                'vendor': vendor_object[0] if vendor_object else None,
                'qty': row.iloc[2] if not check_nan_value.iloc[2] else 1,
                'specific': row.iloc[3] if not check_nan_value.iloc[3] else False,
                'urgent': row.iloc[5] if not check_nan_value.iloc[5] else False,
                'timeline': row.iloc[6] if not check_nan_value.iloc[6] else '',
                'status': row_status if not check_nan_value.iloc[7] else 2,
                'received': row.iloc[8] if not check_nan_value.iloc[8] else False
            }
            if not object_create['timeline']:
                object_create.__delitem__('timeline')
            if object_create['vendor'] is None:
                object_create.__delitem__('vendor')
            print(object_create)
            OrdersModel.objects.update_or_create(**object_create)
            if not check_nan_value.iloc[8] and row.iloc[8]:
                InventoryModel.objects.create(**{
                    "admin": order_auth,
                    "product": product_object[0],
                    "qty": row.iloc[2] if not check_nan_value.iloc[2] else 1,
                    "notes": "From excel"
                })

        print(r)


class Insurance(object):
    def __init__(self, row: pd, check: pd):
        self.admin = order_auth
        self.patiant_name = row.iloc[0] if not check.iloc[0] else ''
        self.group = row.iloc[2] if not check.iloc[2] else ''
        self.cert = row.iloc[3] if not check.iloc[3] else ''
        if not check.iloc[4]:
            self.date_of_birth = row.iloc[4] if not check.iloc[4] else ''
        if not check.iloc[5]:
            self.plan_holder_date_of_birth = row.iloc[5] if not check.iloc[5] else ''
        self.plan_holder = row.iloc[6] if not check.iloc[6] else ''
        self.address = row.iloc[7] if not check.iloc[7] else ''
        self.relation_to_subscriber = row.iloc[8] if not check.iloc[8] else ''
        self.notes = row.iloc[9] if not check.iloc[9] else ''
        self.breakdown = row.iloc[10] if not check.iloc[10] else ''

    def get_object(self):
        return self.__dict__


class Patient(object):
    def __init__(self, row: pd, check: pd, index: int):
        self.admin = order_auth
        self.patiant_name = row.iloc[0] if not check.iloc[0] else ''
        if index > 5247:
            self.preferred_pronouns = ''
            self.phone_number = row.iloc[1] if not check.iloc[1] else ''
            self.email = row.iloc[2] if not check.iloc[2] else ''
            if not check.iloc[3]:
                self.last_visited = row.iloc[3] if not check.iloc[3] else ''
            self.review = row.iloc[6] if not check.iloc[6] else ''
        else:
            self.preferred_pronouns = row.iloc[1] if not check.iloc[1] else ''
            self.phone_number = row.iloc[2] if not check.iloc[2] else ''
            self.email = row.iloc[3] if not check.iloc[3] else ''
            if not check.iloc[4]:
                self.last_visited = row.iloc[4] if not check.iloc[4] else ''
            self.review = row.iloc[7] if not check.iloc[7] else ''

    def get_object(self):
        return self.__dict__


def import_insurance():
    df = pd.read_excel('media/management/upload/test-data-mock_XHql7Vw.xlsx', sheet_name=9, skiprows=[1, 2], )
    # df = pd.read_excel('test-data-mock.xlsx', sheet_name=7, skiprows=[1, 2], )
    for index, row in df.iterrows():
        check_nan_value = row.isna()
        if not check_nan_value.iloc[0]:
            if row.count() > 1:
                insurance_object = Insurance(row, check_nan_value)
                try:
                    InsuranceVerificationModel.objects.update_or_create(**(insurance_object.get_object()))
                    # break
                except Exception as e:
                    insurance_object_dict=insurance_object.get_object()
                    print(datetime.strptime(insurance_object_dict['date_of_birth'], '%d-%m-%Y').date())
                    # insurance_object_dict['date_of_birth'] = datetime.datetime(insurance_object_dict['date_of_birth'])
                    print(insurance_object_dict)
                    # InsuranceVerificationModel.objects.update_or_create(**insurance_object_dict)
                    print(index, "failed ", e)
        else:
            continue


def import_patient():
    df = pd.read_excel('media/management/upload/test-data-mock_5srfYym.xlsx', sheet_name=7, skiprows=[1, 2], )
    # df = pd.read_excel('test-data-mock.xlsx', sheet_name=7, skiprows=[1, 2], )
    for index, row in df.iterrows():
        check_nan_value = row.isna()
        if not check_nan_value.iloc[0]:
            if row.count() > 1:
                try:
                    patient_object = Patient(row, check_nan_value, index)
                    PatientExperienceModel.objects.update_or_create(**(patient_object.get_object()))
                    # print(index, "success")
                except Exception as e:
                    print(index, "failed ", e)
                # break
        else:
            continue


import_insurance()
# import_patient()
print("--------------------------------")
print("--------------------------------")
# print(df)
