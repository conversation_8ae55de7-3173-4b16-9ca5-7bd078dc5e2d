from django.test import TestCase

from app.models import Admin


class AdminTestCase(TestCase):
    def setUp(self):
        self.user = Admin.objects.create_user(username='admin',
                                              email='<EMAIL>',
                                              password='test1234',
                                              first_name="Admin",
                                              last_name="Test",
                                              is_admin=True
                                              )

    def test_admin_is_exist(self):
        admin = Admin.objects.get(email="<EMAIL>")
        self.assertEqual(admin.first_name, 'Admin')

    def test_create_post(self):
        """ Tests that create user from admin """
        self.assertEqual(self.post.title, 'My blog post')
        self.assertEqual(self.post.author, self.user)

    def test_post_str(self):
        """ Tests the __str__ of the Post model"""

        self.assertEqual(str(self.post), 'My blog post | by user123')

    def test_creates_a_slug(self):
        """ Tests a slug is automatically created """

        self.assertEqual(self.post.slug, 'my-blog-post')

    def test_slugs_are_unique(self):
        """ Tests two posts with identical titles from the same author receive different slugs """

        second_title = Post.objects.create(
            title='My blog post',
            body='This is my second post',
            author=self.user,
        )

        self.assertNotEqual(self.post.slug, second_title.slug)
