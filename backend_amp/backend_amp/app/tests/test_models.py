from django.test import TestCase

# Create your tests here.
# import pytest
from app.models import Admin

#
# @pytest.fixture
# def user_fixture():
#     return Admin.objects.create_user(username='admin', email='<EMAIL>', password='test1234')

from django.test import TestCase


#
#
class AdminTestCase(TestCase):
    def setUp(self):
        Admin.objects.create_user(username='admin',
                                  email='<EMAIL>',
                                  password='test1234',
                                  first_name="Admin",
                                  last_name="Test",
                                  is_admin=True
                                  )

    def test_admin_is_exist(self):
        admin = Admin.objects.get(email="<EMAIL>")
        self.assertEqual(admin.first_name, 'Admin')


import pytest

from app.models import Admin


@pytest.fixture
def user_A(db) -> Admin:
    return Admin.objects.create_user(username='admin',
                                     email='<EMAIL>',
                                     password='test1234',
                                     first_name="Admin",
                                     last_name="Test",
                                     is_admin=True
                                     )


def test_should_check_password(db, user_A: Admin) -> None:
    user_A = Admin.objects.get(email='<EMAIL>')
    #     user_A.set_password("secret")
    assert user_A.check_password("test1234") is True


# def test_should_not_check_unusable_password(db, user_A: User) -> None:
#     user_A.set_password("secret")
#     user_A.set_unusable_password()
#     assert user_A.check_password("secret") is False
