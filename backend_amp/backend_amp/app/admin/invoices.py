from django.contrib import admin


from app.models import InvoicesModel,InvoicesFileModel

class FileInline(admin.TabularInline):
    model = InvoicesFileModel
class InvoicesAdmin(admin.ModelAdmin):
    list_display = ['admin', 'vendor', 'date', 'invoice_number',
                    'amount_paid',]
    list_filter = ['invoice_number', ]
    search_fields = ['invoice_number','admin']
    # date_hierarchy = ''
    fields = ['admin', 'vendor', 'date', 'invoice_number',
                    'amount_paid',]
    inlines = [
        FileInline
    ]
    # form = BlogForm
    change_form_template = 'admin/change_form.html'

    class Media:
        css = {
            'all': (
                'css/admin.css',
            )
        }
admin.site.register(InvoicesModel, InvoicesAdmin)
