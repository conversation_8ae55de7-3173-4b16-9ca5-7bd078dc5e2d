from django.contrib import admin

from app.models import OrdersModel


@admin.register(OrdersModel)
class OrdersAdmin(admin.ModelAdmin):
    # fieldsets = (
    #     (
    #         None,
    #         {
    #             "fields": (
    #                 "admin",
    #                 "product",
    #                 "vendor",
    #                 "qty",
    #                 "specific",
    #                 "urgent",
    #                 "timeline",
    #                 "received",
    #                 "my_product",
    #                 "status",
    #                 "would_paid",
    #                 "paid",
    #                 "saved",
    #                 "created_at"
    #             )
    #         },
    #
    #     ),
    # )
    fields = (
        "admin",
        "product",
        "vendor",
        "qty",
        "specific",
        "urgent",
        "timeline",
        "received",
        "my_product",
        "status",
        "would_paid",
        "paid",
        "saved",
        # "created_at"
    )
    list_display = (
        "pk",
        "get_admin",
        "get_product",
        "get_vendor",
        "would_paid",
        "paid",
        "saved",
        "qty",
        "specific",
        "urgent",
        "received",
        'status'
    )
