from datetime import date

from pydantic import BaseModel
from app.schemas.product import ReadProductSchema


class InventorySchema(BaseModel):
    id: int
    admin_id: int
    product: ReadProductSchema = None

    qty: int
    min_qty: int
    notes: str
    created_at: date = None
    expiration_date: date = None
    updated_at: date = None


class ReadInventorySchema(BaseModel):
    id: int
    admin_id: int
    product: ReadProductSchema = None
    qty: int
    min_qty: int
    notes: str
    expiration_date: date = None

    class Config:
        orm_mode = True


class CreateInventorySchema(BaseModel):
    admin_id: int
    product: str
    s_k_u:str
    qty: int
    notes: str
    expiration_date: str = ''

    class Config:
        orm_mode = True


class DeleteInventorySchema(BaseModel):
    id: int
    admin_id: int

    # product: str
    # qty: int
    # notes: str

    class Config:
        orm_mode = True


class EditInventorySchema(BaseModel):
    id: int
    admin_id: int
    product: str
    s_k_u:str
    qty: int
    min_qty: int
    notes: str = ''
    expiration_date: str = ''

