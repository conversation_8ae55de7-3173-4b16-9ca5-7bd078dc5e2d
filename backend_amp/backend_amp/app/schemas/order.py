from datetime import datetime, date
from pydantic import BaseModel, validator

from app.schemas.product import ReadProductSchema

from app.schemas.vendor import ReadVendorsSchema


class OrderSchema(BaseModel):
    id: int
    admin_id: int

    class Config:
        orm_mode = True


class ReadOrderSchema(BaseModel):
    id: int
    admin_id: int
    product: ReadProductSchema = None
    vendor: ReadVendorsSchema = None
    note: str
    qty: int
    specific: bool
    urgent: bool
    exp_delivery: str
    my_product: bool
    received: bool
    timeline: date = None
    would_paid: float
    paid: float
    saved: float
    status: str
    created_at: date = None
    updated_at: date = None

    class Config:
        orm_mode = True
        orig_model = ReadProductSchema


class ReadOrderTotalBudgetLeftSchema(BaseModel):
    id: int
    paid: float

    class Config:
        orm_mode = True


class EditOrderSchema(BaseModel):
    id: int
    admin_id: int
    product: str
    s_k_u:str
    note: str
    vendor: str
    qty: int
    qty_pending: int = 0
    qty_delivered: int = 0
    specific: bool
    urgent: bool
    timeline: str = ''
    # exp_delivery: str
    received: bool = False
    my_product: bool = False
    status: int

    class Config:
        orm_mode = True


class CreateOrdersSchema(BaseModel):
    admin_id: int
    product: str
    s_k_u:str
    note: str
    vendor: str
    qty: int
    specific: bool = False
    urgent: bool = False
    timeline: str = ''

    status: int


class EditOrderPaidSchema(BaseModel):
    id: int
    admin_id:int
    would_paid: float
    paid: float
    saved: float

class EditFavoriteSchema(BaseModel):
    id:int
    admin_id:int
    my_product: bool = False


class DeleteOrder(BaseModel):
    id: int
    admin_id:int