from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

class ParticipantResponse(BaseModel):
    user_id: int
    username: str
    email: str
    is_admin: bool

class MessageBase(BaseModel):
    content: str

class MessageCreate(MessageBase):
    pass

class MessageResponse(MessageBase):
    id: int
    conversation_id: int
    sender_id: int
    sender_name: str
    created_at: datetime
    is_read: bool

class LastMessageResponse(BaseModel):
    id: int
    content: str
    sender_id: int
    sender_name: str
    created_at: datetime
    is_read: bool

class ConversationBase(BaseModel):
    pass

class ConversationCreate(BaseModel):
    user_id: int

class ConversationResponse(ConversationBase):
    id: int
    created_at: datetime
    updated_at: datetime
    unread_count: int
    participants: List[ParticipantResponse]
    last_message: Optional[LastMessageResponse] = None
