from pydantic import BaseModel
from datetime import datetime, date


class PatientExperience(BaseModel):
    id: int
    admin_id: int
    patiant_name: str
    preferred_pronouns: str
    phone_number: str
    email: str
    last_visited: str


class ReadPatientExperienceSchema(BaseModel):
    id: int
    admin_id: int
    patiant_name: str
    preferred_pronouns: str
    phone_number: str
    email: str
    last_visited: date = None
    review:str

    class Config:
        orm_mode = True


class CreatePatientExperienceSchema(BaseModel):
    admin_id: int
    patiant_name: str
    preferred_pronouns: str
    phone_number: str
    email: str
    last_visited: str = ''
    review:str = ''


class EditPatientExperienceSchema(BaseModel):
    id: int
    admin_id: int
    patiant_name: str
    preferred_pronouns: str
    phone_number: str
    email: str
    last_visited: str = ''
    review:str = ''


class DeletePatientExperienceSchema(BaseModel):
    id: int
    admin_id:int
