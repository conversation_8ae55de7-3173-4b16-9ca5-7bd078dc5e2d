from pydantic import BaseModel
from datetime import date


class VendorsSchema(BaseModel):
    id: int
    admin_id: int
    name: str
    account_number: str
    contact_name: str
    how_to_order: str
    phone: str
    email: str
    website: str
    user_name: str
    password: str
    description: str
    created_at: date = None
    updated_at: date = None


class ReadVendorsSchema(BaseModel):
    id: int
    admin_id: int
    name: str
    account_number: str
    contact_name: str
    how_to_order: str
    phone: str
    email: str
    website: str
    user_name: str
    password: str

    class Config:
        orm_mode = True


class DeleteVendorSchema(BaseModel):
    id: int
    admin_id: int


class EditVendorSchema(BaseModel):
    id: int
    admin_id: int
    name: str
    account_number: str
    contact_name: str
    how_to_order: str
    phone: str
    email: str
    website: str
    user_name: str
    password: str

    class Config:
        orm_mode = True


class CreateVendorsSchema(BaseModel):
    admin_id: str
    name: str
    account_number: str
    contact_name: str
    how_to_order: str
    phone: str
    email: str
    website: str
    user_name: str
    password: str


class CreateVendorFromOrderSchema(BaseModel):
    admin_id: str
    name: str
