from datetime import date

from pydantic import BaseModel


class ProductSchema(BaseModel):
    id: int
    name: str
    image: str
    status: str
    s_k_u:str
    created_at: date = None
    updated_at: date = None


class ReadProductSchema(BaseModel):
    id: int
    name: str
    status: str
    s_k_u:str
    class Config:
        orm_mode = True


class CreateProductSchema(BaseModel):
    name: str
    status: str
    s_k_u:str
