from datetime import date
from uuid import UUID

from pydantic import BaseModel
from pydantic import Extra

class Admin(BaseModel):
    id: int
    first_name: str
    last_name: str
    email: str
    password: str
    is_admin: bool
    user_group: int
    is_superuser: bool
    avatar: str
    phone_number: str
    dental_name: str
    dental_logo: str
    total_budget: float
    status: int
    created_at: date = None
    updated_at: date = None

    class Config:
        orm_mode = True

class ReadUserInfoSchema(BaseModel):
    id:int
    first_name: str
    class Config:
        orm_mode = True
        extra = Extra.allow
class ReadUserSchema(BaseModel):
    id: int
    uuid: UUID
    username: str
    first_name: str
    last_name: str
    email: str
    is_active: bool
    is_admin: bool
    dental_logo: str
    user_group: int
    # is_superuser: bool
    total_budget: float
    groups:list = []

    class Config:
        orm_mode = True
        extra = Extra.allow

class ReadUsersSchema(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str
    phone_number: str
    sub_user:list=[]
    dental_name: str
    dental_logo: str = ''
    is_active: bool
    is_admin: bool
    total_budget: float
    groups:list = []

    class Config:
        orm_mode = True


class ReadUserListSchema(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str
    phone_number: str
    dental_name: str
    dental_logo: str
    is_active: bool
    total_budget: float
    groups:list = []

    class Config:
        orm_mode = True



class EditUserBudgetSchema(BaseModel):
    id: int
    total_budget: float


class EditUserSchema(BaseModel):
    id: int
    first_name: str
    last_name: str
    email: str
    phone_number: str
    dental_name: str
    dental_logo: str = ''
    total_budget: float
    is_active: bool
    groups:list = []


class CreateUserSchema(BaseModel):
    first_name: str
    last_name: str
    email: str
    password: str
    # avatar: str = ''
    ref_admin:str=''
    phone_number: str
    dental_name: str
    dental_logo: str = ''
    total_budget: float
    is_active: bool
    groups:list = []

    class Config:
        extra = Extra.allow
class UploadFilesSchema(BaseModel):
    first_name:str=''
class DeleteUserSchema(BaseModel):
    id: int


class Token(BaseModel):
    token: str
    # username: str
    # is_admin: bool
    # is_active: bool
    # user_group: int
    # is_superuser: bool
    token_type: str
