from datetime import date

from pydantic import BaseModel


class NotesSchema(BaseModel):
    id: int
    admin_id: int
    notes: str
    created_at: date = None
    updated_at: date = None

class ReadNotesSchema(BaseModel):
    id:int
    admin_id:int
    notes:str
    created_at: date = None
    updated_at: date = None
    class Config:
        orm_mode = True

class EditNotesSchema(BaseModel):
    id:int
    admin_id:int
    notes:str
    class Config:
        orm_mode = True

class CreateNotesSchema(BaseModel):
    admin_id:int
    notes:str
    class Config:
        orm_mode = True

class DeleteNotesSchema(BaseModel):
    id:int
    admin_id:int

    class Config:
        orm_mode = True
