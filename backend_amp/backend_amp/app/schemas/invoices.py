from pydantic import BaseModel
from datetime import date


class InvoicesSchema(BaseModel):
    id: int
    admin_id: int
    vendor: str
    date: date = None
    invoice_number: str
    amount_paid: float
    invoice_file: str
    created_at: date = None
    updated_at: date = None


class InvoiceReadSchema(BaseModel):
    id: int
    admin_id: int
    vendor: str
    date: date
    invoice_number: str
    amount_paid: float
    invoice_file: str
    created_at: date = None

    class Config:
        orm_mode = True

class CreateInvoiceSchema(BaseModel):
    admin_id: int
    vendor: str
    date: str
    invoice_number: str
    invoice_file: str = ''
    amount_paid: float


class EditInvoiceSchema(BaseModel):
    id: int
    admin_id: int
    vendor: str
    date: str
    invoice_number: str
    invoice_file: str
    amount_paid: float
    class Config:
        orm_mode = True

class DeleteInvoiceSchema(BaseModel):
    id:int
    admin_id: int
    class Config:
        orm_mode = True