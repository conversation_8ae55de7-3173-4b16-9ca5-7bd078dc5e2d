from datetime import datetime, date
from pydantic import BaseModel, validator
from app.schemas.inventory import ReadInventorySchema
from app.schemas.admin import ReadUserInfoSchema


class NotificationsSchema(BaseModel):
    id: int
    level: str
    # recipient:None
    unread: bool
    verb:str
    description:str
    public:bool
    deleted:bool
    created_at: date = None
    updated_at: date = None

    class Config:
        orm_mode = True


class ReadNotificationSchema(BaseModel):
    id: int
    recipient: ReadUserInfoSchema=None
    unread: bool
    verb: str
    description:str
    target_content_type_id:int
    target_object_id:int
    inventory_object:ReadInventorySchema
    order_list: list = []

    class Config:
        orm_mode = True
class UserReadNotificationSchema(BaseModel):
    id: int
    recipient: ReadUserInfoSchema=None
    unread: bool
    verb: str
    description:str
    target_content_type_id:int
    target_object_id:int
    inventory_object:ReadInventorySchema=None
    public:bool
    timestamp:date = None
    # timestamp:str
    class Config:
        orm_mode = True


class EditNotificationSchema(BaseModel):
    id: int

class EditSingleUserNotificationSchema(BaseModel):
    admin_id:int
class EditMarkNotificationSchema(BaseModel):
    ids:list[int]
# class EditMarkNotificationSchema(BaseModel):
#     ids:list[int]
# class CreateNotificationSchema(BaseModel):
#     admin_id: int
#     quantity: int = 0
