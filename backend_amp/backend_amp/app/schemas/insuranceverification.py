from pydantic import BaseModel
from datetime import datetime, date


class InsuranceVerification(BaseModel):
    id: int
    admin_id: int
    patiant_name: str
    group: str
    cert: str
    date_of_birth: str
    plan_holder_date_of_birth: str
    plan_holder: str
    address: str
    relation_to_subscriber: str
    notes: str
    breakdown: str


class ReadInsuranceVerification(BaseModel):
    id: int
    admin_id: int
    patiant_name: str
    group: str
    cert: str
    date_of_birth: date = None
    plan_holder_date_of_birth: date = None
    plan_holder: str
    address: str
    relation_to_subscriber: str
    notes: str
    breakdown: str

    class Config:
        orm_mode = True


class CreateInsuranceVerificationSchema(BaseModel):
    admin_id: int
    patiant_name: str
    group: str
    cert: str
    date_of_birth: str = ''
    plan_holder_date_of_birth: str = ''
    plan_holder: str
    address: str
    relation_to_subscriber: str
    notes: str
    breakdown: str

    class Config:
        orm_mode = True


class EditInsuranceVerificationSchema(BaseModel):
    id: int
    admin_id: int
    patiant_name: str
    group: str
    cert: str
    date_of_birth: str = ''
    plan_holder_date_of_birth: str = ''
    plan_holder: str
    address: str
    relation_to_subscriber: str
    notes: str
    breakdown: str

    class Config:
        orm_mode = True


class DeleteInsuranceVerificationSchema(BaseModel):
    id: int
    admin_id:int
