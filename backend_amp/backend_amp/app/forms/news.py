from django import forms

from app.models import BlogModel


class BlogForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super(BlogForm, self).__init__(*args, **kwargs)
        # self.fields['category'].queryset = ProjectCategory.objects.all()
        # self.fields['category'].label_from_instance = lambda obj: "%s" % obj.name
        # self.fields['name_author'].queryset = CustomUser.objects.all()
        # self.fields['name_author'].label_from_instance = lambda obj: "%s" % obj.first_name

    class Meta:
        model = BlogModel
        fields = '__all__'
