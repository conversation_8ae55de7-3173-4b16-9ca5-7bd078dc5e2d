

from typing import Any

from fastapi import APIRouter, Depends, Request

from app.dependencies.auth import get_current_user
from app.models import Admin, NotesModel
from app.api.notes import NotesAPI

from app.schemas import ReadNotesSchema,EditNotesSchema,CreateNotesSchema,DeleteNotesSchema
notes_router=APIRouter()
@notes_router.get("/get_notes/{user_id}",
                   response_model=list[ReadNotesSchema]
                   )
async def get(request: Request, user_id: int, current_user: Admin = Depends(get_current_user)) -> list[NotesModel]:
    print('notes/get_notes')
    return await NotesAPI.get_notes(request, user_id, current_user)


@notes_router.post("/create_notes",
                   response_model=list[ReadNotesSchema]
                   )
async def create_notes(request: Request, schema: CreateNotesSchema, current_user: Admin = Depends(get_current_user)) -> list[NotesModel]:
    print('notes/create')
    return await NotesAPI.create_notes(request, schema, current_user)
@notes_router.put("/update_notes",
                  response_model=list[ReadNotesSchema]
                  )
async def update_notes(request: Request, schema: EditNotesSchema, current_user: Admin = Depends(get_current_user)) -> list[NotesModel]:
    print('notes/update')
    return await NotesAPI.update_notes(request, schema, current_user)
@notes_router.put("/delete_notes",
                  response_model=list[ReadNotesSchema]
                  )
async def delete_notes(request: Request, schema: DeleteNotesSchema, current_user: Admin = Depends(get_current_user)) -> list[NotesModel]:
    print('notes/delete')
    return await NotesAPI.delete_notes(request, schema, current_user)