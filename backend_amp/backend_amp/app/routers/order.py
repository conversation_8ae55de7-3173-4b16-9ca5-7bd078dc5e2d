from typing import Any

from fastapi import APIRouter, Depends, Request

from app.dependencies.auth import get_current_user,get_current_admin_user
from app.models import OrdersModel, Admin
from app.api.order import OrderAPI

from app.schemas import ReadOrderSchema, CreateOrdersSchema, EditOrderSchema, EditFavoriteSchema, ReadOrderTotalBudgetLeftSchema, \
    EditOrderPaidSchema,DeleteOrder

order_router = APIRouter()


@order_router.post("/list_user_orders/{user_id}",
                   response_model=list[ReadOrderSchema]
                   )
async def get(request: Request, user_id: int, current_user: Admin = Depends(get_current_user)) -> list[OrdersModel]:
    print('order/userid')
    return await OrderAPI.get(request, user_id, current_user)


@order_router.post("/create_order", response_model=list[ReadOrderSchema])
async def create(request: Request, schema: CreateOrdersSchema,
                 current_user: Admin = Depends(get_current_user)) -> list[OrdersModel]:
    return await OrderAPI.create(request, schema, current_user)


@order_router.put("/update_order",
                  response_model=list[ReadOrderSchema])
async def update(request: Request, schema: EditOrderSchema,
                 current_user: Admin = Depends(get_current_user)) -> list[OrdersModel]:
    return await OrderAPI.update(request, schema, current_user)

@order_router.put("/update_favorite",
                    response_model=list[ReadOrderSchema]
                  )
async def update_favorite(request: Request, schema: EditFavoriteSchema,
                 current_user: Admin = Depends(get_current_user)) -> list[OrdersModel]:
    return await OrderAPI.update_favorite(request, schema, current_user)

@order_router.put("/update_paid",
                  response_model=list[ReadOrderSchema]
                  )
async def update_paid(request: Request, schema: EditOrderPaidSchema,
                           current_user: Admin = Depends(get_current_admin_user)) ->list[OrdersModel]:
    return await OrderAPI.update_paid(request, schema, current_user)


@order_router.post("/delete_order", response_model=list[ReadOrderSchema])
async def delete_order(request: Request, schema: DeleteOrder,
                 current_user: Admin = Depends(get_current_user)) -> list[OrdersModel]:
    return await OrderAPI.delete_order(request, schema, current_user)

@order_router.get('/summery_reports/{user_id}',

                  )
async def get_summery_reports(request: Request,user_id:int,current_user: Admin = Depends(get_current_user)) -> Any:


    return await OrderAPI.summery_reports(request,user_id,current_user)
