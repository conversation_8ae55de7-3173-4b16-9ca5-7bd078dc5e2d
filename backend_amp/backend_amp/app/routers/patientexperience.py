from app.dependencies.auth import get_current_user
from app.models import <PERSON><PERSON>, PatientExperienceModel
from fastapi import APIRouter, Depends, Request
from app.api.patientexperience import Patient<PERSON>xperience<PERSON>I

from app.schemas import ReadPatientExperienceSchema, CreatePatientExperienceSchema, EditPatientExperienceSchema, DeletePatientExperienceSchema

patient_experience_router = APIRouter()


@patient_experience_router.post("/list_user_patient_experience/{user_id}",
                          response_model=list[ReadPatientExperienceSchema]
                          )
async def get(request: Request, user_id: int, current_user: Admin = Depends(get_current_user)) -> list[PatientExperienceModel]:
    return await PatientExperienceAPI.get_patient_experience(request, user_id, current_user)


@patient_experience_router.post(
    "/create_patient_experience",
    response_model=list[ReadPatientExperienceSchema],
)
async def create(request: Request, schema: CreatePatientExperienceSchema,
                 current_user: Admin = Depends(get_current_user)) -> list[PatientExperienceModel]:
    return await PatientExperienceAPI.create_patient_experience(request, schema, current_user)


@patient_experience_router.put("/update_patient_experience",
                         response_model=list[ReadPatientExperienceSchema])
async def update(request: Request, schema: EditPatientExperienceSchema,
                 current_user: Admin = Depends(get_current_user)) -> list[PatientExperienceModel]:
    return await PatientExperienceAPI.update_patient_experience(request, schema, current_user)


@patient_experience_router.put("/delete_patient_experience",
                         response_model=list[ReadPatientExperienceSchema])
async def delete(request: Request, schema: DeletePatientExperienceSchema,
                 current_user: Admin = Depends(get_current_user)) -> list[PatientExperienceModel]:
    return await PatientExperienceAPI.delete_patient_experience(request, schema, current_user)
