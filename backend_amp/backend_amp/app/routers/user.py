from typing import Any, List

from app.api import UserAPI
from app.dependencies.auth import get_current_user, get_current_admin_user
from app.models import Admin
from app.schemas import CreateUserSchema, ReadUserSchema, ReadUserListSchema, EditUserBudgetSchema, EditUserSchema, \
    ReadUsersSchema, \
    DeleteUserSchema,UploadFilesSchema
from fastapi import APIRouter, Depends, Request,UploadFile,File,Form

user_router = APIRouter()


@user_router.post("/", response_model=ReadUserSchema)
async def get(request: Request, current_user: Admin = Depends(get_current_user)) -> Admin:
    return await UserAPI.get(request, current_user)

@user_router.get("/subscription_list",
                 )
async def get_subscription(request: Request, current_user: Admin = Depends(get_current_user)) -> list[Any]:
    return await UserAPI.get_subscription(request, current_user)

@user_router.post(
    "/create_user",
    response_model=list[ReadUsersSchema],
)
async def create(request: Request, schema: CreateUserSchema, current_user: Admin = Depends(get_current_user)) -> \
        list[Admin]:
    return await UserAPI.create(request, schema, current_user)
@user_router.post(
    "/create_subuser",
    response_model=list[ReadUsersSchema],
)
async def create_subuser(request: Request, schema: CreateUserSchema, current_user: Admin = Depends(get_current_user)) -> \
        list[Admin]:
    return await UserAPI.create_subuser(request, schema, current_user)


@user_router.put("/update_user",
                 response_model=list[ReadUsersSchema],
                 )
async def update(request: Request, schema: EditUserSchema, current_user: Admin = Depends(get_current_user)) -> \
        list[Any]:
    return await UserAPI.update_user(request, schema, current_user)


@user_router.get(
    "/user_list",
    response_model=list[ReadUserListSchema]
)
async def get(request: Request, current_user: Admin = Depends(get_current_user)) -> list[Admin]:
    return await UserAPI.get_user_list(request, current_user)


@user_router.put(
    "/update_budget",
    response_model=ReadUserSchema
)
async def update_budget(request: Request, schema: EditUserBudgetSchema,
                        current_user: Admin = Depends(get_current_admin_user)) -> Admin:
    return await UserAPI.update_budget(request, schema, current_user)


@user_router.post("/users",
                  response_model=list[ReadUsersSchema]
                  )
async def users(request: Request,
                current_user: Admin = Depends(get_current_user)) -> list[Any]:
    return await UserAPI.get_users(request, current_user)


@user_router.delete("/delete_user",
                    response_model=list[ReadUsersSchema]
                    )
async def delete_user(request: Request, schema: DeleteUserSchema,
                      current_user: Admin = Depends(get_current_user)) -> list[Admin]:
    return await UserAPI.delete_user(request, schema, current_user)

@user_router.post("/upload_picture")
async def upload_picture(request: Request,file: UploadFile,first_name: str = Form(...),current_user: Admin = Depends(get_current_admin_user)):
    return await UserAPI.upload_picture(request,file,first_name,current_user)