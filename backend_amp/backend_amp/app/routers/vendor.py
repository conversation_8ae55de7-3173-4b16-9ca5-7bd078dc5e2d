from typing import Any

from app.api import VendorAP<PERSON>
from app.dependencies.auth import get_current_user
from app.models import Admin, VendorsModel
from fastapi import APIRouter, Depends, Request

from app.schemas import CreateVendorsSchema, ReadVendorsSchema, EditVendorSchema, DeleteVendorSchema

vendor_router = APIRouter()


@vendor_router.post("/list_user_vendors/{user_id}",
                    response_model=list[ReadVendorsSchema]
                    )
async def get(request: Request, user_id: int, current_user: Admin = Depends(get_current_user)) -> list[VendorsModel]:
    return await VendorAPI.get(request, user_id, current_user)


@vendor_router.post(
    "/create_vendor",
    response_model=list[ReadVendorsSchema],
)
async def create(request: Request, schema: CreateVendorsSchema,
                 current_user: Admin = Depends(get_current_user)) -> list[VendorsModel]:
    return await VendorAPI.create(request, schema, current_user)


@vendor_router.put("/update_vendor",
                   response_model=list[ReadVendorsSchema])
async def update(request: Request, schema: EditVendorSchema,
                 current_user: Admin = Depends(get_current_user)) -> list[VendorsModel]:
    return await VendorAPI.update(request, schema, current_user)


@vendor_router.put("/delete_vendor",
                   response_model=list[ReadVendorsSchema])
async def delete(request: Request, schema: DeleteVendorSchema,
                 current_user: Admin = Depends(get_current_user)) -> list[VendorsModel]:
    return await VendorAPI.delete_vendor(request, schema, current_user)
