


from typing import Any
from typing import List, Any, Dict

from fastapi import APIRouter, Depends, Request, UploadFile, File, Form

from app.dependencies.auth import get_current_user, get_current_admin_user
from app.models import InvoicesModel, Admin  ##, NotificationModel
from app.api import InvoicesAPI

from app.schemas import InvoiceReadSchema, CreateInvoiceSchema, EditInvoiceSchema,DeleteInvoiceSchema

invoices_router = APIRouter()


@invoices_router.get("/get_invoices/{user_id}",
                  response_model=list[InvoiceReadSchema]
                  )
async def get_invoices(request: Request,user_id:int, current_user: Admin = Depends(get_current_user)):
    return await InvoicesAPI.get_invoices(request,user_id, current_user)


# @invoices_router.get("/get_invoices_by_id/{blog_id}",
#                   response_model=BlogByIdSchema
#                   )
# async def blog_by_id(request: Request, blog_id: int, current_user: Admin = Depends(get_current_user)):
#     return await InvoicesAPI.blog_by_id(request, blog_id, current_user)


@invoices_router.post("/create_invoice",
                   response_model=list[InvoiceReadSchema]
                   )
async def insert_invoice(request: Request, schema: CreateInvoiceSchema, current_user: Admin = Depends(get_current_admin_user)):
    return await InvoicesAPI.insert_invoice(request, schema, current_user)


@invoices_router.post('/edit_invoice',
                   response_model=list[InvoiceReadSchema]
                   )
async def edit_invoice(request:Request,schema:EditInvoiceSchema,current_user: Admin = Depends(get_current_admin_user)):
    return await InvoicesAPI.edit_invoice(request,schema,current_user)

@invoices_router.post("/total_budget_left/{user_id}",
                   # response_model=Any
                   )
async def get_total_budget(request: Request, user_id: int, current_user: Admin = Depends(get_current_user)):
    return await InvoicesAPI.get_total_budget_left(request, user_id, current_user)


@invoices_router.post('/delete_invoice',
                   response_model=list[InvoiceReadSchema]
                   )
async def delete_invoice(request:Request,schema:DeleteInvoiceSchema,current_user: Admin = Depends(get_current_admin_user)):
    return await InvoicesAPI.delete_invoice(request,schema,current_user)

@invoices_router.post("/add_invoice/upload_invoice_file")
async def upload_invoice_file(request: Request, file: UploadFile, vendor: str = Form(...),
                              current_user: Admin = Depends(get_current_admin_user)):
    return await InvoicesAPI.upload_invoice_file(request, file, vendor, current_user)