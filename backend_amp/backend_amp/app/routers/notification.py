from typing import Any

from fastapi import APIRouter, Depends, Request

from app.dependencies.auth import get_current_user, get_current_admin_user
from app.models import OrdersModel, Admin  ##, NotificationModel
from app.api import NotificationAPI

from app.schemas import ReadNotificationSchema, EditNotificationSchema, UserReadNotificationSchema,EditMarkNotificationSchema,EditSingleUserNotificationSchema

notification_router = APIRouter()


@notification_router.get("/",
                         # response_model=list[Any]
                         )
async def all_notifications_list(request: Request, current_user: Admin = Depends(get_current_user)):
    return await NotificationAPI.all_notifications_list(request, current_user)


@notification_router.get("/get_notification",
                         response_model=list[UserReadNotificationSchema]
                         )
async def get(request: Request, current_user: Admin = Depends(get_current_user)):
    return await NotificationAPI.get_notification(request, current_user)


@notification_router.post("/read_notification",
                          response_model=list[UserReadNotificationSchema] or list[UserReadNotificationSchema]
                          )
async def read_notification(request: Request, schema: EditNotificationSchema,
                            current_user: Admin = Depends(get_current_user)) -> list[
    Any]:
    return await NotificationAPI.read_notification(request, schema, current_user)

@notification_router.post("/read_single_user_notification",
                          response_model=list[UserReadNotificationSchema] or list[UserReadNotificationSchema]
                          )
async def read_single_user_notification(request: Request, schema: EditSingleUserNotificationSchema,
                            current_user: Admin = Depends(get_current_user)) -> list[
    Any]:
    return await NotificationAPI.read_single_user_notification(request, schema, current_user)

@notification_router.post("/read_mark_notification",
                          )
async def read_mark_notification(request:Request,schema:EditMarkNotificationSchema,current_user: Admin = Depends(get_current_user)) -> list[
    Any]:
    return await NotificationAPI.read_mark_notification(request, schema, current_user)
@notification_router.post("/read_all_notification",
                          )
async def read_all_notification(request:Request,current_user: Admin = Depends(get_current_user)) -> list[
    Any]:
    return await NotificationAPI.read_all_notification(request, current_user)
