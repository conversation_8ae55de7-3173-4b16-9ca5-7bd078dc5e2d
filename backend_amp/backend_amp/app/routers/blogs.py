from typing import Any

from fastapi import APIRouter, Depends, Request, UploadFile, File, Form

from app.dependencies.auth import get_current_user, get_current_admin_user
from app.models import BlogModel, Admin  ##, NotificationModel
from app.api import BlogsAPI

from app.schemas import BlogsReadSchema, BlogByIdSchema, CreateBlogSchema, DeleteBlogSchema, EditBlogSchema, \
    EditBlogStatusSchema

blogs_router = APIRouter()


@blogs_router.get("/get_blogs",
                  response_model=list[BlogsReadSchema]
                  )
async def get_blogs(request: Request, current_user: Admin = Depends(get_current_user)):
    return await BlogsAPI.get_blogs(request, current_user)


@blogs_router.get("/get_blog_by_id/{blog_id}",
                  response_model=BlogByIdSchema
                  )
async def blog_by_id(request: Request, blog_id: int, current_user: Admin = Depends(get_current_user)):
    return await BlogsAP<PERSON>.blog_by_id(request, blog_id, current_user)


@blogs_router.post("/create_blog",
                   response_model=list[BlogsReadSchema]
                   )
async def insert_blog(request: Request, schema: CreateBlogSchema, current_user: Admin = Depends(get_current_admin_user)):
    return await BlogsAPI.insert_blog(request, schema, current_user)

@blogs_router.post("/edit_blog_status")
async def edit_blog_status(request: Request,schema:EditBlogStatusSchema, current_user: Admin = Depends(get_current_admin_user)):
    return await BlogsAPI.edit_blog_status(request,schema, current_user)


@blogs_router.post("/add_blog/upload_blog_picture")
async def upload_blog_picture(request: Request, file: UploadFile, post_title: str = Form(...),
                              current_user: Admin = Depends(get_current_admin_user)):
    return await BlogsAPI.upload_blog_picture(request, file, post_title, current_user)

@blogs_router.post('/edit_blog',
                   response_model=list[BlogsReadSchema]
                   )
async def edit_blog(request:Request,schema:EditBlogSchema,current_user: Admin = Depends(get_current_admin_user)):
    return await BlogsAPI.edit_blog(request,schema,current_user)
@blogs_router.post('/delete_blog',
                   response_model=list[BlogsReadSchema]
                   )
async def delete_blog(request:Request,schema:DeleteBlogSchema,current_user: Admin = Depends(get_current_admin_user)):
    return await BlogsAPI.delete_blog(request,schema,current_user)

@blogs_router.post("/add_blog/upload_blog_file")
async def upload_blog_file(request: Request, file: UploadFile, post_title: str = Form(...),
                              current_user: Admin = Depends(get_current_admin_user)):
    return await BlogsAPI.upload_blog_file(request, file, post_title, current_user)