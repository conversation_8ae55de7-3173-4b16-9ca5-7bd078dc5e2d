from typing import Any

from app.api import AuthAPI
from app.schemas import Token
from app.dependencies.auth import get_current_user
from app.models import Admin

from fastapi import APIRouter, Depends, Request
from fastapi.security import OAuth2PasswordRequestForm

auth_router = APIRouter()


@auth_router.post("/login", response_model=Token)
async def login(
    request: Request, form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    print('auth/login')

    return await AuthAPI.login(request, form_data)


@auth_router.get("/validate-token")
async def validate_token(request: Request, current_user: Admin = Depends(get_current_user)) -> Any:
    """Endpoint to validate if a token is still valid"""
    return await AuthAPI.validate_token(request, current_user)
