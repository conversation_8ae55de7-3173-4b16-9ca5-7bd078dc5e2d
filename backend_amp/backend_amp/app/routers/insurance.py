from app.dependencies.auth import get_current_user
from app.models import Admin, InsuranceVerificationModel
from fastapi import APIRouter, Depends, Request
from app.api.insurance import InsuranceAPI

from app.schemas import ReadInsuranceVerification, CreateInsuranceVerificationSchema, EditInsuranceVerificationSchema, \
    DeleteInsuranceVerificationSchema

insurance_router = APIRouter()


@insurance_router.post("/list_user_insurance/{user_id}",
                       response_model=list[ReadInsuranceVerification]
                       )
async def get(request: Request, user_id: int, current_user: Admin = Depends(get_current_user)) -> list[InsuranceVerificationModel]:
    return await InsuranceAPI.get_insurance(request, user_id, current_user)


@insurance_router.post(
    "/create_insurance",
    response_model=list[ReadInsuranceVerification],
)
async def create(request: Request, schema: CreateInsuranceVerificationSchema,
                 current_user: Admin = Depends(get_current_user)) -> list[InsuranceVerificationModel]:
    return await InsuranceAPI.create_insurance(request, schema, current_user)


@insurance_router.put("/update_insurance",
                      response_model=list[ReadInsuranceVerification])
async def update(request: Request, schema: EditInsuranceVerificationSchema,
                 current_user: Admin = Depends(get_current_user)) -> list[InsuranceVerificationModel]:
    return await InsuranceAPI.update_insurance(request, schema, current_user)


@insurance_router.put("/delete_insurance",
                      response_model=list[ReadInsuranceVerification])
async def delete(request: Request, schema: DeleteInsuranceVerificationSchema,
                 current_user: Admin = Depends(get_current_user)) -> list[InsuranceVerificationModel]:
    return await InsuranceAPI.delete_insurance(request, schema, current_user)

