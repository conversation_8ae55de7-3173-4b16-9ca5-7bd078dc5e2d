from typing import Any

from fastapi import APIRouter, Depends, Request

from app.dependencies.auth import get_current_user
from app.models import Admin, InventoryModel
from app.api.inventory import InventoryAPI

from app.schemas import ReadInventorySchema, CreateInventorySchema, DeleteInventorySchema, EditInventorySchema

inventory_router = APIRouter()


@inventory_router.get("/get_by_id/{inventory_id}",
                      response_model=ReadInventorySchema
                      )
async def get_by_id(request: Request, inventory_id: int,
                    current_user: Admin = Depends(get_current_user)) -> InventoryModel:
    return await InventoryAPI.get_by_id(request, inventory_id, current_user)


@inventory_router.post("/list_user_inventory/{user_id}",
                       response_model=list[ReadInventorySchema]
                       )
async def get(request: Request, user_id: int, current_user: Admin = Depends(get_current_user)) -> list[InventoryModel]:
    print('inventory/userid')
    return await InventoryAPI.get(request, user_id, current_user)


@inventory_router.post("/create_inventory", response_model=list[ReadInventorySchema])
async def create(request: Request, schema: CreateInventorySchema,
                 current_user: Admin = Depends(get_current_user)) -> list[InventoryModel]:
    return await InventoryAPI.create(request, schema, current_user)


@inventory_router.post("/delete_inventory", response_model=list[ReadInventorySchema])
async def delete(request: Request, schema: DeleteInventorySchema,
                 current_user: Admin = Depends(get_current_user)) -> list[InventoryModel]:
    return await InventoryAPI.delete_inventory(request, schema, current_user)


@inventory_router.post("/update_inventory",
                       response_model=list[ReadInventorySchema])
async def update(request: Request, schema: EditInventorySchema,
                 current_user: Admin = Depends(get_current_user)) -> list[InventoryModel]:
    return await InventoryAPI.update_inventory(request, schema, current_user)
#
# @inventory_router.put("/update_order",
#                   response_model=list[ReadOrderSchema])
# async def update(request: Request, schema: EditOrderSchema,
#                  current_user: Admin = Depends(get_current_user)) -> list[OrdersModel]:
#     return await OrderAPI.update(request, schema, current_user)
#
#
# @inventory_router.post("/total_budget_left/{user_id}",
#                    response_model=dict[str, int | str | Any]
#                    )
# async def get_total_budget(request: Request, user_id: int, current_user: Admin = Depends(get_current_user)) -> dict[
#         str, int | str | Any]:
#     return await OrderAPI.get_total_budget_left(request, user_id, current_user)
#
