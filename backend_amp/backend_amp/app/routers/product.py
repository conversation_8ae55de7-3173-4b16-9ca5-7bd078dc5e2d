from typing import Any

from app.api import ProductAPI
from app.dependencies.auth import get_current_user
from app.models import Admin,ProductsModel
from app.schemas import ReadProductSchema, CreateProductSchema
from fastapi import APIRouter, Depends, Request


product_router = APIRouter()


# @user_router.get("/", response_model=ReadUserSchema)
# async def get(request: Request, current_user: Admin = Depends(get_current_user)) -> Admin:
#     return UserAPI.get(request, current_user)


@product_router.post(
    "/",
    response_model=ReadProductSchema,
)
async def create(request: Request, schema: CreateProductSchema, current_user: Admin = Depends(get_current_user)) -> ProductsModel:
    return await ProductAPI.create(request, schema,current_user)
