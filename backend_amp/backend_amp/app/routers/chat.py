from fastapi import APIRout<PERSON>, Depends, HTTPException, WebSocket, WebSocketDisconnect, status
from typing import List, Optional
import json
import logging
from app.models.chat import Conversation, ConversationParticipant, Message
from app.schemas.chat import (
    ConversationCreate, 
    ConversationResponse, 
    MessageCreate, 
    MessageResponse
)
from ..dependencies.auth import get_current_user
from ..models.user import Admin
from django.db.models import Q, Count, Max, F, Value, BooleanField
from django.db.models.functions import Coalesce
from django.utils import timezone

# Set up logging
logger = logging.getLogger(__name__)

chat_router = APIRouter()

# WebSocket connections store
active_connections = {}

@chat_router.get("/conversations", response_model=List[ConversationResponse])
async def get_conversations(current_user: Admin = Depends(get_current_user)):
    """Get all conversations for the current user"""
    try:
        # Get all conversations where the user is a participant
        user_conversation_participants = ConversationParticipant.objects.filter(
            user=current_user
        )
        user_conversations = []
        async for participant in user_conversation_participants:
            user_conversations.append(participant.conversation_id)
        
        # Get the conversations with additional info
        conversations_queryset = Conversation.objects.filter(
            id__in=user_conversations
        ).annotate(
            unread_count=Count(
                'messages',
                filter=Q(messages__is_read=False) & ~Q(messages__sender=current_user)
            )
        ).order_by('-updated_at')
        
        # Format the response
        result = []
        async for conv in conversations_queryset:
            # Get participants
            participants_queryset = ConversationParticipant.objects.filter(
                conversation=conv
            ).select_related('user')
            
            participants = []
            async for p in participants_queryset:
                participants.append(p)
            
            # Get last message
            last_message = await Message.objects.filter(
                conversation=conv
            ).order_by('-created_at').afirst()

            # Format the conversation
            conv_data = {
                "id": conv.id,
                "created_at": conv.created_at,
                "updated_at": conv.updated_at,
                "unread_count": conv.unread_count,
                "participants": [
                    {
                        "user_id": p.user.id,
                        "username": p.user.username,
                        "email": p.user.email,
                        "is_admin": p.user.is_staff
                    } for p in participants
                ],
                "last_message": {
                    "id": last_message.id,
                    "content": last_message.content,
                    "sender_id": last_message.sender.id,
                    "sender_name": last_message.sender.username,
                    "created_at": last_message.created_at,
                    "is_read": last_message.is_read
                } if last_message else None
            }
            result.append(conv_data)
        
        return result
    except Exception as e:
        logger.error(f"Error getting conversations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting conversations: {str(e)}"
        )

@chat_router.post("/conversations", response_model=ConversationResponse)
async def create_conversation(
    data: ConversationCreate,
    current_user: Admin = Depends(get_current_user)
):
    """Create a new conversation or get existing one with a user"""
    try:
        # Check if the other user exists
        try:
            other_user = await Admin.objects.aget(id=data.user_id)
        except Admin.DoesNotExist:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"User with ID {data.user_id} not found"
            )
        
        # Check if a conversation already exists between these users
        user_conversations = await ConversationParticipant.objects.filter(
            user=current_user
        ).values_list('conversation_id', flat=True)
        
        other_user_conversations = await ConversationParticipant.objects.filter(
            user=other_user
        ).values_list('conversation_id', flat=True)
        
        # Find common conversations
        common_conversations = set(user_conversations).intersection(set(other_user_conversations))
        
        if common_conversations:
            # Return the existing conversation
            conversation = await Conversation.objects.aget(id=list(common_conversations)[0])
        else:
            # Create a new conversation
            conversation = await Conversation.objects.acreate()
            
            # Add participants
            await ConversationParticipant.objects.acreate(
                conversation=conversation,
                user=current_user
            )
            await ConversationParticipant.objects.acreate(
                conversation=conversation,
                user=other_user
            )
            
            # Create initial message
            await Message.objects.acreate(
                conversation=conversation,
                sender=current_user,
                content="Started a conversation",
                is_read=False
            )
        
        # Get participants
        participants = await ConversationParticipant.objects.filter(
            conversation=conversation
        ).select_related('user')
        
        # Get last message
        last_message = await Message.objects.filter(
            conversation=conversation
        ).order_by('-created_at').afirst()
        
        # Format the response
        return {
            "id": conversation.id,
            "created_at": conversation.created_at,
            "updated_at": conversation.updated_at,
            "unread_count": await Message.objects.filter(
                conversation=conversation,
                is_read=False
            ).exclude(sender=current_user).acount(),
            "participants": [
                {
                    "user_id": p.user.id,
                    "username": p.user.username,
                    "email": p.user.email,
                    "is_admin": p.user.is_staff
                } for p in participants
            ],
            "last_message": {
                "id": last_message.id,
                "content": last_message.content,
                "sender_id": last_message.sender.id,
                "sender_name": last_message.sender.username,
                "created_at": last_message.created_at,
                "is_read": last_message.is_read
            } if last_message else None
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating conversation: {str(e)}"
        )

@chat_router.get("/conversations/{conversation_id}/messages", response_model=List[MessageResponse])
async def get_messages(
    conversation_id: int,
    current_user: Admin = Depends(get_current_user)
):
    """Get all messages for a conversation"""
    try:
        # Check if the user is a participant in this conversation
        try:
            await ConversationParticipant.objects.aget(
                conversation_id=conversation_id,
                user=current_user
            )
        except ConversationParticipant.DoesNotExist:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not a participant in this conversation"
            )
        
        # Get all messages
        messages = await Message.objects.filter(
            conversation_id=conversation_id
        ).order_by('created_at')
        
        # Mark messages as read
        await Message.objects.filter(
            conversation_id=conversation_id,
            is_read=False
        ).exclude(sender=current_user).aupdate(is_read=True)
        
        # Format the response
        result = []
        for msg in messages:
            result.append({
                "id": msg.id,
                "conversation_id": msg.conversation.id,
                "sender_id": msg.sender.id,
                "sender_name": msg.sender.username,
                "content": msg.content,
                "created_at": msg.created_at,
                "is_read": msg.is_read
            })
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting messages: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting messages: {str(e)}"
        )

@chat_router.post("/conversations/{conversation_id}/messages", response_model=MessageResponse)
async def create_message(
    conversation_id: int,
    data: MessageCreate,
    current_user: Admin = Depends(get_current_user)
):
    """Create a new message in a conversation"""
    try:
        # Check if the user is a participant in this conversation
        try:
            await ConversationParticipant.objects.aget(
                conversation_id=conversation_id,
                user=current_user
            )
        except ConversationParticipant.DoesNotExist:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not a participant in this conversation"
            )
        
        # Get the conversation
        conversation = await Conversation.objects.aget(id=conversation_id)
        
        # Create the message
        message = await Message.objects.acreate(
            conversation=conversation,
            sender=current_user,
            content=data.content,
            is_read=False
        )
        
        # Update conversation timestamp
        conversation.updated_at = timezone.now()
        await conversation.asave()
        
        # Format the response
        response = {
            "id": message.id,
            "conversation_id": message.conversation.id,
            "sender_id": message.sender.id,
            "sender_name": message.sender.username,
            "content": message.content,
            "created_at": message.created_at,
            "is_read": message.is_read
        }
        
        # Send notification to other participants via WebSocket
        # This will be handled by the ASGI application
        
        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating message: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating message: {str(e)}"
        )

@chat_router.get("/users/admins", response_model=List[dict])
async def get_admin_users(current_user: Admin = Depends(get_current_user)):
    """Get all admin users for starting a conversation"""
    try:
        # Get all admin users except the current user

        
        # Format the response
        result = []
        async for user in Admin.objects.filter(
            is_staff=True,user_group=2
        ).exclude(id=current_user.id):
            result.append({
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "is_admin": user.is_staff
            })
        
        return result
    except Exception as e:
        logger.error(f"Error getting admin users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting admin users: {str(e)}"
        )
