from django.core.mail import EmailMessage
from django.template.loader import render_to_string
from asgiref.sync import sync_to_async
import logging

logger = logging.getLogger(__name__)

@sync_to_async
def send_email_async(email):
    try:
        email.send()
        logger.info('Email sent successfully')
    except Exception as e:
        logger.error(f'Failed to send email: {str(e)}')
        # Don't raise the exception, just log it
        pass

async def sender_notification_to_admin(sender='', order_content='', admin=None):
    if admin is None:
        admin = {'name': '', 'email': ''}
        return
        
    try:
        # Load the HTML template
        html_content = render_to_string('../templates/emails/email_notification_order.html',
                                    {'sender': sender, 'order_content': order_content, 'admin': admin['name']})
        
        # Create EmailMessage object
        email = EmailMessage(
            'User  ' + sender + ' Submit New Order With Title  ' + order_content,  # Subject
            html_content,  # HTML content
            '<EMAIL>',  # From email address
            [admin['email']]  # To email addresses
        )
        # Set content type to HTML
        email.content_subtype = "html"
        
        # Send email asynchronously
        await send_email_async(email)
        
    except Exception as e:
        logger.error(f'Error in sender_notification_to_admin: {str(e)}')
        # Don't raise the exception, just log it
        pass

