from __future__ import absolute_import, unicode_literals
import os
import subprocess

from celery import Celery
from django.conf import settings
from celery.utils.log import get_task_logger
from django.conf import settings

# from core.models import ModifyProcess
# getattr(settings, 'DJANGO_NOTIFICATIONS_CONFIG', {})
logger = get_task_logger(__name__)
# set the default Django settings module for the 'celery' program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
app = Celery('backend_amp')

# Using a string here means the worker will not have to
# pickle the object when using Windows.
app.config_from_object('django.conf:settings')
app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)


# @app.task(name="exec_tcbot_thread_task")
# def exec_tcbot_thread_task(chanel):
#     """sends an email when feedback form is filled successfully"""
#     logger.info("Sent feedback email")
#     pid = bg_task()
#     logger.info(pid)
#     return pid
#
#
# def bg_task():
#     os.chdir("TC-Bot/src")
#     cmd = 'python3 run.py'
#     process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE)
#     pid = process.pid
#     print(pid)
#     process.wait()


@app.task(bind=True)
def debug_task(self):
    print('Request: {0!r}'.format(self.request))
