"""ASGI config for config project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.0/howto/deployment/asgi/
"""

import os
import sys
import time

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

from django.core.asgi import get_asgi_application

"""
Settings
"""
env_state = os.getenv("ENV_STATE", "production")
if env_state == "production":
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.production")
elif env_state == "staging":
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.staging")
else:
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")

"""
Django settings
"""
django_app = get_asgi_application()

"""
FastAPI settings
"""
from app.routers import auth_router, health_router, user_router, notification_router, order_router, product_router, vendor_router, \
    inventory_router, home_router, patient_experience_router, insurance_router, notes_router, blogs_router, invoices_router, chat_router

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import json
from app.models.chat import Message, Conversation, ConversationParticipant
import logging

# Set up logging
logger = logging.getLogger(__name__)

origins = [
    '*',
    "https://amp.amedicalpartner.com",
    "http://**************",
    "http://localhost",
    "http://localhost:8000",
    "http://localhost:3000",
    "http://localhost:4000",
    "http://localhost:4001",
]

fastapi_app = FastAPI()
fastapi_app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# routers
fastapi_app.include_router(home_router, tags=["home"], prefix="")
fastapi_app.include_router(user_router, tags=["users"], prefix="/user")
fastapi_app.include_router(auth_router, tags=["auth"], prefix="/auth")
fastapi_app.include_router(health_router, tags=["health"], prefix="/health")
fastapi_app.include_router(order_router, tags=["order"], prefix="/order")
fastapi_app.include_router(product_router, tags=["product"], prefix="/product")
fastapi_app.include_router(vendor_router, tags=["vendor"], prefix="/vendor")
fastapi_app.include_router(inventory_router, tags=["inventory"], prefix="/inventory")
fastapi_app.include_router(insurance_router, tags=["insurance"], prefix="/insurance")
fastapi_app.include_router(patient_experience_router, tags=["patient_experience"], prefix="/patient_experience")
fastapi_app.include_router(notification_router, tags=["notification"], prefix="/notification")
fastapi_app.include_router(notes_router, tags=["notes"], prefix="/notes")
fastapi_app.include_router(blogs_router, tags=["blogs"], prefix="/blogs")
fastapi_app.include_router(invoices_router, tags=["invoices"], prefix="/invoices")
fastapi_app.include_router(chat_router, tags=["chat"], prefix="/chat")

fastapi_app.mount("/static", StaticFiles(directory="static"), name="static")
fastapi_app.mount("/media", StaticFiles(directory="media"), name="media")

# WebSocket connections store
active_connections = {}

@fastapi_app.websocket("/ws/chat/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: int):
    try:
        logger.info(f"WebSocket connection attempt for user {user_id}")
        await websocket.accept()
        logger.info(f"WebSocket connection accepted for user {user_id}")
        
        # Store the connection
        if user_id not in active_connections:
            active_connections[user_id] = []
        active_connections[user_id].append(websocket)
        logger.info(f"Active connections: {len(active_connections)}")
        
        # Send a confirmation message to the client
        await websocket.send_text(json.dumps({
            'type': 'connection_established',
            'message': 'Connected to chat server'
        }))
        
        try:
            while True:
                data = await websocket.receive_text()
                logger.info(f"Received message from user {user_id}: {data}")
                message_data = json.loads(data)
                
                # Handle different message types
                if message_data.get('type') == 'read_messages':
                    conversation_id = message_data.get('conversation_id')
                    # Mark messages as read in database
                    try:
                        from django.db import transaction
                        with transaction.atomic():
                            Message.objects.filter(
                                conversation_id=conversation_id,
                                is_read=False
                            ).exclude(sender_id=user_id).update(is_read=True)
                        logger.info(f"Marked messages as read in conversation {conversation_id}")
                    except Exception as e:
                        logger.error(f"Error marking messages as read: {e}")
                
                # Echo the message back to confirm receipt
                await websocket.send_text(json.dumps({
                    'type': 'message_received',
                    'message': 'Message received'
                }))
                
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected for user {user_id}")
            # Remove connection when disconnected
            if user_id in active_connections:
                active_connections[user_id].remove(websocket)
                if not active_connections[user_id]:
                    del active_connections[user_id]
        except Exception as e:
            logger.error(f"Error in websocket communication: {e}")
            
    except Exception as e:
        logger.error(f"Error establishing WebSocket connection: {e}")
        if websocket.client_state.CONNECTED:
            await websocket.close(code=1011, reason=f"Internal server error: {str(e)}")

# Add a function to send messages to connected clients
async def send_message_notification(message):
    try:
        # Get all participants in the conversation
        conversation = message.conversation
        participants = ConversationParticipant.objects.filter(conversation=conversation)
        
        # Prepare the message data
        message_data = {
            'id': message.id,
            'content': message.content,
            'sender_id': message.sender.id,
            'sender_name': message.sender.username,
            'conversation_id': conversation.id,
            'created_at': message.created_at.isoformat(),
            'is_read': message.is_read
        }
        
        logger.info(f"Sending message notification to participants: {[p.user.id for p in participants]}")
        
        # Send to all participants except the sender
        for participant in participants:
            if participant.user.id != message.sender.id and participant.user.id in active_connections:
                logger.info(f"Sending to user {participant.user.id}")
                for connection in active_connections[participant.user.id]:
                    try:
                        await connection.send_text(json.dumps({
                            'type': 'chat_message',
                            'message': message_data
                        }))
                        logger.info(f"Message sent to user {participant.user.id}")
                    except Exception as e:
                        logger.error(f"Error sending message to user {participant.user.id}: {e}")
    except Exception as e:
        logger.error(f"Error in send_message_notification: {e}")

# For debugging purposes
logger.info("ASGI module loaded successfully")
logger.info(f"Current directory: {os.getcwd()}")
logger.info(f"Python path: {sys.path}")
