#!/bin/bash
echo "Starting Celery worker and beat..."
#poetry run celery -A config.celery worker --loglevel=info &
#poetry run celery -A config.celery beat --loglevel=info &

echo "Starting FastAPI server with WebSocket support..."
poetry run uvicorn config.asgi:fastapi_app --reload --host 0.0.0.0 --port 4000 &

echo "Starting Django admin server..."
poetry run uvicorn config.asgi:django_app --reload --host 0.0.0.0 --port 4001

# Wait for all background processes to finish
wait

