import os

import pandas as pd
# from django.conf import settings as django_settings
# from swapper import load_model

# OrderStatus = (
#     ('back_order',1),
#     ('incoming',2),
#     ('in_progress',3),
#     ('confirmed',4),
#     ('partially_delivered',5),
#     ('delivered',6),
#     ('in_storage',7),
#     ('returned',8),
#     ('partially_returned',9),
# )
# OrderStatus = (
#     (1, 'back_order', 'BackOrder'),
#     (2, 'incoming', 'Incoming'),
#     (3, 'in_progress', 'In-Progress'),
#     (4, 'confirmed', 'Confirmed'),
#     (5, 'partially_delivered', 'Partially Delivered'),
#     (6, 'delivered', 'Delivered'),
#     (7, 'in_storage', 'In Storage'),
#     (8, 'returned', 'Returned'),
#     (9, 'partially_returned', 'Partially Returned'),
# )

# read by default 1st sheet of an excel file
# row_count = 59
# sheet_name = {
#     0: "Order Sheet",
#     1: "Received",
#     2: "Budget"
# }
# class
# os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.production")
# os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")
# import django

# django.setup()
# ProductsModel = load_model('app', 'ProductsModel')
# VendorsModel = load_model('app', 'VendorsModel')
# OrdersModel = load_model('app', 'OrdersModel')
# InventoryModel = load_model('app', 'InventoryModel')
# Admin = load_model('app', 'Admin')
# from app.models import OrdersModel, Admin
# from backend_amp.app.models import Admin

# order import data
# def order_import():
#     df = pd.read_excel('media/management/upload/test-data-mock_5srfYym.xlsx', sheet_name=0, skiprows=2, )
#     headers = df.columns.tolist()
#     df.dropna(subset=['DATE'], inplace=True)
#     r = 0
#     order_auth = Admin.objects.get(id=3)
#     for index, row in df.iterrows():
#         if row['DATE'] is not None:
#             r = r + 1
#             check_nan_value = row.isna()
#             if check_nan_value.iloc[1]:
#                 continue
#             row_status = 0
#             for index, status in enumerate(OrderStatus):
#                 if status[2] == row.iloc[7]:
#                     row_status = status[0]
#             product_object = ProductsModel.objects.get_or_create(**{"name": row.iloc[1]})
#             if not check_nan_value.iloc[4]:
#                 vendor_object = VendorsModel.objects.get_or_create(**{'admin': order_auth, 'name': row.iloc[4]})
#             else:
#                 vendor_object = None
#             object_create = {
#                 'admin': order_auth,
#                 'product': product_object[0],
#                 'vendor': vendor_object[0] if vendor_object else None,
#                 'qty': row.iloc[2] if not check_nan_value.iloc[2] else 1,
#                 'specific': row.iloc[3] if not check_nan_value.iloc[3] else False,
#                 'urgent': row.iloc[5] if not check_nan_value.iloc[5] else False,
#                 'timeline': row.iloc[6] if not check_nan_value.iloc[6] else '',
#                 'status': row_status if not check_nan_value.iloc[7] else 2,
#                 'received': row.iloc[8] if not check_nan_value.iloc[8] else False
#             }
#             if not object_create['timeline']:
#                 object_create.__delitem__('timeline')
#             if object_create['vendor'] is None:
#                 object_create.__delitem__('vendor')
#             print(object_create)
#             OrdersModel.objects.update_or_create(**object_create)
#             if not check_nan_value.iloc[8] and row.iloc[8]:
#                 InventoryModel.objects.create(**{
#                     "admin": order_auth,
#                     "product": product_object[0],
#                     "qty": row.iloc[2] if not check_nan_value.iloc[2] else 1,
#                     "notes": "From excel"
#                 })
#     print(r)
# #Patient Experience import data
class Patient(object):
    def __init__(self,row:pd,check:pd,index):
        self.patiant_name=row.iloc[0]if not check.iloc[0] else ''
        if index>5247:
            self.preferred_pronouns=''
            self.phone_number=row.iloc[1]if not check.iloc[1] else ''
            self.email=row.iloc[2]if not check.iloc[2] else ''
            if not check.iloc[3]:
                self.last_visited=row.iloc[3]if not check.iloc[3] else ''
            self.review=row.iloc[6]if not check.iloc[6] else ''
        else:
            self.preferred_pronouns=row.iloc[1]if not check.iloc[1] else ''
            self.phone_number=row.iloc[2]if not check.iloc[2] else ''
            self.email=row.iloc[3]if not check.iloc[3] else ''
            if not check.iloc[4]:
                self.last_visited=row.iloc[4]if not check.iloc[4] else ''
            self.review=row.iloc[7]if not check.iloc[7] else ''
    def get_object(self):
        print(self.__dict__)
def patient_experience():
    df = pd.read_excel('test-data-mock.xlsx', sheet_name=7, skiprows=[1,2], )
    for index,row in df.iterrows():
        check_nan_value = row.isna()
        if not check_nan_value.iloc[0]:
            if row.count()>1:
                object=Patient(row,check_nan_value,index)
                object.get_object()
        else:
            continue
patient_experience()
print("--------------------------------")
print("--------------------------------")
# print(df)
