import XCTest
@testable import AMP

class SecurityTests: XCTestCase {
    var keychainService: KeychainService!
    var encryptionService: EncryptionService!
    
    override func setUp() {
        super.setUp()
        keychainService = KeychainService.shared
        encryptionService = EncryptionService.shared
        
        // Clean up any existing test data
        keychainService.deleteData(for: "test_key")
        keychainService.deleteData(for: "test_string")
    }
    
    override func tearDown() {
        // Clean up test data
        keychainService.deleteData(for: "test_key")
        keychainService.deleteData(for: "test_string")
        
        keychainService = nil
        encryptionService = nil
        super.tearDown()
    }
    
    // MARK: - Keychain Tests
    
    func testKeychainSetAndGetData() {
        // Given
        let testData = "Hello, World!".data(using: .utf8)!
        let key = "test_key"
        
        // When
        let setResult = keychainService.setData(testData, for: key)
        let retrievedData = keychainService.getData(for: key)
        
        // Then
        XCTAssertTrue(setResult)
        XCTAssertNotNil(retrievedData)
        XCTAssertEqual(testData, retrievedData)
    }
    
    func testKeychainSetAndGetString() {
        // Given
        let testString = "Hello, World!"
        let key = "test_string"
        
        // When
        let setResult = keychainService.setString(testString, for: key)
        let retrievedString = keychainService.getString(for: key)
        
        // Then
        XCTAssertTrue(setResult)
        XCTAssertNotNil(retrievedString)
        XCTAssertEqual(testString, retrievedString)
    }
    
    func testKeychainDataExists() {
        // Given
        let testData = "Test".data(using: .utf8)!
        let key = "test_key"
        
        // When
        XCTAssertFalse(keychainService.dataExists(for: key))
        
        keychainService.setData(testData, for: key)
        
        // Then
        XCTAssertTrue(keychainService.dataExists(for: key))
    }
    
    func testKeychainDeleteData() {
        // Given
        let testData = "Test".data(using: .utf8)!
        let key = "test_key"
        
        keychainService.setData(testData, for: key)
        XCTAssertTrue(keychainService.dataExists(for: key))
        
        // When
        let deleteResult = keychainService.deleteData(for: key)
        
        // Then
        XCTAssertTrue(deleteResult)
        XCTAssertFalse(keychainService.dataExists(for: key))
    }
    
    func testKeychainGetNonExistentData() {
        // Given
        let key = "non_existent_key"
        
        // When
        let retrievedData = keychainService.getData(for: key)
        let retrievedString = keychainService.getString(for: key)
        
        // Then
        XCTAssertNil(retrievedData)
        XCTAssertNil(retrievedString)
    }
    
    // MARK: - Encryption Tests
    
    func testEncryptAndDecryptData() {
        // Given
        let originalData = "Sensitive information".data(using: .utf8)!
        
        // When
        do {
            let encryptedData = try encryptionService.encrypt(originalData)
            let decryptedData = try encryptionService.decrypt(encryptedData)
            
            // Then
            XCTAssertNotEqual(originalData, encryptedData)
            XCTAssertEqual(originalData, decryptedData)
        } catch {
            XCTFail("Encryption/Decryption failed: \(error)")
        }
    }
    
    func testEncryptAndDecryptString() {
        // Given
        let originalString = "Sensitive information"
        
        // When
        do {
            let encryptedData = try encryptionService.encrypt(originalString)
            let decryptedString = try encryptionService.decryptToString(encryptedData)
            
            // Then
            XCTAssertEqual(originalString, decryptedString)
        } catch {
            XCTFail("String encryption/decryption failed: \(error)")
        }
    }
    
    func testSHA256Hash() {
        // Given
        let testString = "Hello, World!"
        let expectedHash = "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f"
        
        // When
        let hash = encryptionService.sha256Hash(testString)
        
        // Then
        XCTAssertEqual(hash, expectedHash)
    }
    
    func testSHA256HashData() {
        // Given
        let testData = "Hello, World!".data(using: .utf8)!
        let expectedHash = "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f"
        
        // When
        let hash = encryptionService.sha256Hash(testData)
        
        // Then
        XCTAssertEqual(hash, expectedHash)
    }
    
    func testKeyDerivation() {
        // Given
        let password = "mySecurePassword"
        
        // When
        do {
            let (key1, salt1) = try encryptionService.deriveKey(from: password)
            let (key2, salt2) = try encryptionService.deriveKey(from: password, salt: salt1)
            
            // Then
            XCTAssertEqual(key1.withUnsafeBytes { Data($0) }, key2.withUnsafeBytes { Data($0) })
            XCTAssertEqual(salt1, salt2)
        } catch {
            XCTFail("Key derivation failed: \(error)")
        }
    }
    
    func testGenerateRandomData() {
        // Given
        let length = 32
        
        // When
        let randomData1 = encryptionService.generateRandomData(length: length)
        let randomData2 = encryptionService.generateRandomData(length: length)
        
        // Then
        XCTAssertEqual(randomData1.count, length)
        XCTAssertEqual(randomData2.count, length)
        XCTAssertNotEqual(randomData1, randomData2) // Should be different
    }
    
    func testGenerateRandomString() {
        // Given
        let length = 16
        
        // When
        let randomString1 = encryptionService.generateRandomString(length: length)
        let randomString2 = encryptionService.generateRandomString(length: length)
        
        // Then
        XCTAssertEqual(randomString1.count, length)
        XCTAssertEqual(randomString2.count, length)
        XCTAssertNotEqual(randomString1, randomString2) // Should be different
    }
    
    func testEncryptionConsistency() {
        // Given
        let originalString = "Test data for consistency"
        
        // When
        do {
            let encryptedData1 = try encryptionService.encrypt(originalString)
            let encryptedData2 = try encryptionService.encrypt(originalString)
            
            let decryptedString1 = try encryptionService.decryptToString(encryptedData1)
            let decryptedString2 = try encryptionService.decryptToString(encryptedData2)
            
            // Then
            XCTAssertNotEqual(encryptedData1, encryptedData2) // Should be different due to random IV
            XCTAssertEqual(decryptedString1, originalString)
            XCTAssertEqual(decryptedString2, originalString)
            XCTAssertEqual(decryptedString1, decryptedString2)
        } catch {
            XCTFail("Encryption consistency test failed: \(error)")
        }
    }
    
    func testDecryptInvalidData() {
        // Given
        let invalidData = "Invalid encrypted data".data(using: .utf8)!
        
        // When/Then
        XCTAssertThrowsError(try encryptionService.decrypt(invalidData)) { error in
            // Should throw an error when trying to decrypt invalid data
            XCTAssertTrue(error is EncryptionError || error is CryptoKitError)
        }
    }
    
    func testEncryptEmptyData() {
        // Given
        let emptyData = Data()
        
        // When
        do {
            let encryptedData = try encryptionService.encrypt(emptyData)
            let decryptedData = try encryptionService.decrypt(encryptedData)
            
            // Then
            XCTAssertEqual(emptyData, decryptedData)
        } catch {
            XCTFail("Empty data encryption/decryption failed: \(error)")
        }
    }
}
