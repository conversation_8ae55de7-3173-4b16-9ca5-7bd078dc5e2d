import XCTest
@testable import AMP

class OrderServiceTests: XCTestCase {
    var orderService: OrderService!
    
    override func setUp() {
        super.setUp()
        orderService = OrderService()
    }
    
    override func tearDown() {
        orderService = nil
        super.tearDown()
    }
    
    func testFilterOrders() {
        // Given
        let orders = [
            Order(
                id: 1,
                productId: 1,
                userId: 1,
                vendorId: 1,
                quantity: 10,
                price: 100.0,
                status: 1,
                createdAt: "2023-01-01T00:00:00.000Z",
                updatedAt: "2023-01-01T00:00:00.000Z",
                product: Product(
                    id: 1,
                    name: "Test Product",
                    description: "Test Description",
                    price: 10.0,
                    vendorId: 1,
                    categoryId: 1,
                    imageUrl: nil,
                    isActive: true,
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z"
                ),
                vendor: Vendor(
                    id: 1,
                    name: "Test Vendor",
                    email: "<EMAIL>",
                    phone: "************",
                    address: "123 Test St",
                    isActive: true,
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z"
                )
            ),
            Order(
                id: 2,
                productId: 2,
                userId: 1,
                vendorId: 2,
                quantity: 5,
                price: 50.0,
                status: 2,
                createdAt: "2023-01-02T00:00:00.000Z",
                updatedAt: "2023-01-02T00:00:00.000Z",
                product: Product(
                    id: 2,
                    name: "Another Product",
                    description: "Another Description",
                    price: 10.0,
                    vendorId: 2,
                    categoryId: 1,
                    imageUrl: nil,
                    isActive: true,
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z"
                ),
                vendor: Vendor(
                    id: 2,
                    name: "Another Vendor",
                    email: "<EMAIL>",
                    phone: "************",
                    address: "456 Test Ave",
                    isActive: true,
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z"
                )
            )
        ]
        
        orderService.orders = orders
        
        // When & Then
        
        // Test empty search
        XCTAssertEqual(orderService.filteredOrders(searchText: "", statusFilter: nil).count, 2)
        
        // Test search by product name
        XCTAssertEqual(orderService.filteredOrders(searchText: "Test Product", statusFilter: nil).count, 1)
        XCTAssertEqual(orderService.filteredOrders(searchText: "Product", statusFilter: nil).count, 2)
        
        // Test search by vendor name
        XCTAssertEqual(orderService.filteredOrders(searchText: "Test Vendor", statusFilter: nil).count, 1)
        XCTAssertEqual(orderService.filteredOrders(searchText: "Vendor", statusFilter: nil).count, 2)
        
        // Test status filter
        XCTAssertEqual(orderService.filteredOrders(searchText: "", statusFilter: 1).count, 1)
        XCTAssertEqual(orderService.filteredOrders(searchText: "", statusFilter: 2).count, 1)
        XCTAssertEqual(orderService.filteredOrders(searchText: "", statusFilter: 3).count, 0)
        
        // Test combined filters
        XCTAssertEqual(orderService.filteredOrders(searchText: "Test", statusFilter: 1).count, 1)
        XCTAssertEqual(orderService.filteredOrders(searchText: "Test", statusFilter: 2).count, 0)
    }
    
    func testGetOrderById() {
        // Given
        let orders = [
            Order(
                id: 1,
                productId: 1,
                userId: 1,
                vendorId: 1,
                quantity: 10,
                price: 100.0,
                status: 1,
                createdAt: "2023-01-01T00:00:00.000Z",
                updatedAt: "2023-01-01T00:00:00.000Z",
                product: Product(
                    id: 1,
                    name: "Test Product",
                    description: "Test Description",
                    price: 10.0,
                    vendorId: 1,
                    categoryId: 1,
                    imageUrl: nil,
                    isActive: true,
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z"
                ),
                vendor: Vendor(
                    id: 1,
                    name: "Test Vendor",
                    email: "<EMAIL>",
                    phone: "************",
                    address: "123 Test St",
                    isActive: true,
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z"
                )
            )
        ]
        
        orderService.orders = orders
        
        // When & Then
        XCTAssertNotNil(orderService.getOrder(by: 1))
        XCTAssertEqual(orderService.getOrder(by: 1)?.product.name, "Test Product")
        XCTAssertNil(orderService.getOrder(by: 2))
    }
    
    func testPendingOrders() {
        // Given
        let orders = [
            Order(
                id: 1,
                productId: 1,
                userId: 1,
                vendorId: 1,
                quantity: 10,
                price: 100.0,
                status: 1, // Pending
                createdAt: "2023-01-01T00:00:00.000Z",
                updatedAt: "2023-01-01T00:00:00.000Z",
                product: Product(
                    id: 1,
                    name: "Test Product",
                    description: "Test Description",
                    price: 10.0,
                    vendorId: 1,
                    categoryId: 1,
                    imageUrl: nil,
                    isActive: true,
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z"
                ),
                vendor: Vendor(
                    id: 1,
                    name: "Test Vendor",
                    email: "<EMAIL>",
                    phone: "************",
                    address: "123 Test St",
                    isActive: true,
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z"
                )
            ),
            Order(
                id: 2,
                productId: 2,
                userId: 1,
                vendorId: 2,
                quantity: 5,
                price: 50.0,
                status: 4, // Delivered
                createdAt: "2023-01-02T00:00:00.000Z",
                updatedAt: "2023-01-02T00:00:00.000Z",
                product: Product(
                    id: 2,
                    name: "Another Product",
                    description: "Another Description",
                    price: 10.0,
                    vendorId: 2,
                    categoryId: 1,
                    imageUrl: nil,
                    isActive: true,
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z"
                ),
                vendor: Vendor(
                    id: 2,
                    name: "Another Vendor",
                    email: "<EMAIL>",
                    phone: "************",
                    address: "456 Test Ave",
                    isActive: true,
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z"
                )
            )
        ]
        
        orderService.orders = orders
        
        // When & Then
        XCTAssertEqual(orderService.pendingOrders.count, 1)
        XCTAssertEqual(orderService.pendingOrders[0].id, 1)
    }
    
    func testTotalSpent() {
        // Given
        let orders = [
            Order(
                id: 1,
                productId: 1,
                userId: 1,
                vendorId: 1,
                quantity: 10,
                price: 100.0,
                status: 1,
                createdAt: "2023-01-01T00:00:00.000Z",
                updatedAt: "2023-01-01T00:00:00.000Z",
                product: Product(
                    id: 1,
                    name: "Test Product",
                    description: "Test Description",
                    price: 10.0,
                    vendorId: 1,
                    categoryId: 1,
                    imageUrl: nil,
                    isActive: true,
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z"
                ),
                vendor: Vendor(
                    id: 1,
                    name: "Test Vendor",
                    email: "<EMAIL>",
                    phone: "************",
                    address: "123 Test St",
                    isActive: true,
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z"
                )
            ),
            Order(
                id: 2,
                productId: 2,
                userId: 1,
                vendorId: 2,
                quantity: 5,
                price: 50.0,
                status: 2,
                createdAt: "2023-01-02T00:00:00.000Z",
                updatedAt: "2023-01-02T00:00:00.000Z",
                product: Product(
                    id: 2,
                    name: "Another Product",
                    description: "Another Description",
                    price: 10.0,
                    vendorId: 2,
                    categoryId: 1,
                    imageUrl: nil,
                    isActive: true,
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z"
                ),
                vendor: Vendor(
                    id: 2,
                    name: "Another Vendor",
                    email: "<EMAIL>",
                    phone: "************",
                    address: "456 Test Ave",
                    isActive: true,
                    createdAt: "2023-01-01T00:00:00.000Z",
                    updatedAt: "2023-01-01T00:00:00.000Z"
                )
            )
        ]
        
        orderService.orders = orders
        
        // When & Then
        XCTAssertEqual(orderService.totalSpent, 150.0)
    }
}
