import XCTest
@testable import AMP

class LocalizationServiceTests: XCTestCase {
    var localizationService: LocalizationService!
    
    override func setUp() {
        super.setUp()
        localizationService = LocalizationService.shared
    }
    
    override func tearDown() {
        localizationService = nil
        super.tearDown()
    }
    
    func testLanguageSelection() {
        // Given
        let originalLanguage = localizationService.currentLanguage
        let newLanguage = Language.spanish
        
        // When
        localizationService.setLanguage(newLanguage)
        
        // Then
        XCTAssertEqual(localizationService.currentLanguage, newLanguage)
        XCTAssertEqual(localizationService.isRTL, newLanguage.isRTL)
        
        // Cleanup
        localizationService.setLanguage(originalLanguage)
    }
    
    func testRTLLanguages() {
        // Given
        let rtlLanguages: [Language] = [.arabic, .hebrew]
        let ltrLanguages: [Language] = [.english, .spanish, .french]
        
        // When & Then
        for language in rtlLanguages {
            XCTAssertTrue(language.isRTL, "\(language.rawValue) should be RTL")
        }
        
        for language in ltrLanguages {
            XCTAssertFalse(language.isRTL, "\(language.rawValue) should be LTR")
        }
    }
    
    func testLanguageCodes() {
        // Given & When & Then
        XCTAssertEqual(Language.english.code, "en")
        XCTAssertEqual(Language.spanish.code, "es")
        XCTAssertEqual(Language.french.code, "fr")
        XCTAssertEqual(Language.german.code, "de")
        XCTAssertEqual(Language.arabic.code, "ar")
        XCTAssertEqual(Language.hebrew.code, "he")
    }
    
    func testLocaleCreation() {
        // Given
        let language = Language.french
        
        // When
        let locale = language.locale
        
        // Then
        XCTAssertEqual(locale.identifier, "fr")
    }
    
    func testDisplayNameGeneration() {
        // Given
        localizationService.setLanguage(.english)
        
        // When
        let spanishDisplayName = localizationService.displayName(for: .spanish)
        let frenchDisplayName = localizationService.displayName(for: .french)
        
        // Then
        XCTAssertFalse(spanishDisplayName.isEmpty)
        XCTAssertFalse(frenchDisplayName.isEmpty)
    }
    
    func testNumberFormatter() {
        // Given
        localizationService.setLanguage(.english)
        
        // When
        let formatter = localizationService.numberFormatter
        
        // Then
        XCTAssertEqual(formatter.locale.identifier, "en")
    }
    
    func testCurrencyFormatter() {
        // Given
        localizationService.setLanguage(.english)
        
        // When
        let formatter = localizationService.currencyFormatter
        
        // Then
        XCTAssertEqual(formatter.locale.identifier, "en")
        XCTAssertEqual(formatter.numberStyle, .currency)
    }
    
    func testDateFormatter() {
        // Given
        localizationService.setLanguage(.english)
        
        // When
        let formatter = localizationService.dateFormatter
        
        // Then
        XCTAssertEqual(formatter.locale.identifier, "en")
    }
    
    func testTextAlignment() {
        // Given & When & Then
        localizationService.setLanguage(.english)
        XCTAssertEqual(localizationService.textAlignment, .left)
        
        localizationService.setLanguage(.arabic)
        XCTAssertEqual(localizationService.textAlignment, .right)
    }
    
    func testStringLocalizationExtension() {
        // Given
        let testKey = "test_key"
        
        // When
        let localizedString = testKey.localized
        
        // Then
        XCTAssertNotNil(localizedString)
        // Note: In a real test environment, you would have actual localization files
        // For now, we're just testing that the method doesn't crash
    }
}
