import XCTest
@testable import AMP

class OfflineManagerTests: XCTestCase {
    var offlineManager: OfflineManager!
    
    override func setUp() {
        super.setUp()
        offlineManager = OfflineManager()
        
        // Clear any existing pending actions
        offlineManager.pendingActions.removeAll()
    }
    
    override func tearDown() {
        offlineManager = nil
        super.tearDown()
    }
    
    func testAddAndRemovePendingAction() {
        // Given
        let action = PendingAction(
            type: .create,
            endpoint: "/test/endpoint",
            data: ["key": "value"]
        )
        
        // When
        offlineManager.addPendingAction(action)
        
        // Then
        XCTAssertEqual(offlineManager.pendingActions.count, 1)
        XCTAssertEqual(offlineManager.pendingActions[0].type, .create)
        XCTAssertEqual(offlineManager.pendingActions[0].endpoint, "/test/endpoint")
        XCTAssertEqual(offlineManager.pendingActions[0].data["key"], "value")
        
        // When
        offlineManager.removePendingAction(id: offlineManager.pendingActions[0].id)
        
        // Then
        XCTAssertEqual(offlineManager.pendingActions.count, 0)
    }
    
    func testCacheData() {
        // Given
        let testData = ["test": "data"]
        let key = "testKey"
        
        // When
        offlineManager.cacheData(testData, forKey: key)
        
        // Then
        XCTAssertTrue(offlineManager.isCached(forKey: key))
        
        // When
        let retrievedData: [String: String]? = offlineManager.getCachedData(forKey: key)
        
        // Then
        XCTAssertNotNil(retrievedData)
        XCTAssertEqual(retrievedData?["test"], "data")
        
        // When
        let cacheDate = offlineManager.getCacheDate(forKey: key)
        
        // Then
        XCTAssertNotNil(cacheDate)
        XCTAssertLessThanOrEqual(cacheDate!.timeIntervalSinceNow, 0)
        XCTAssertGreaterThan(cacheDate!.timeIntervalSinceNow, -60) // Within the last minute
        
        // When
        offlineManager.clearCache()
        
        // Then
        XCTAssertFalse(offlineManager.isCached(forKey: key))
        XCTAssertNil(offlineManager.getCachedData(forKey: key) as [String: String]?)
    }
    
    func testSyncPendingActions() {
        // Given
        let action1 = PendingAction(
            type: .create,
            endpoint: "/test/endpoint1",
            data: ["key1": "value1"]
        )
        
        let action2 = PendingAction(
            type: .update,
            endpoint: "/test/endpoint2",
            data: ["key2": "value2"]
        )
        
        offlineManager.addPendingAction(action1)
        offlineManager.addPendingAction(action2)
        
        XCTAssertEqual(offlineManager.pendingActions.count, 2)
        
        // When
        offlineManager.syncPendingActions()
        
        // Then
        XCTAssertEqual(offlineManager.pendingActions.count, 0)
        XCTAssertNotNil(offlineManager.lastSyncDate)
        XCTAssertLessThanOrEqual(offlineManager.lastSyncDate!.timeIntervalSinceNow, 0)
        XCTAssertGreaterThan(offlineManager.lastSyncDate!.timeIntervalSinceNow, -60) // Within the last minute
    }
    
    func testOfflineStatus() {
        // Given
        offlineManager.isOffline = true
        
        // Then
        XCTAssertTrue(offlineManager.isAppOffline)
        
        // Given
        offlineManager.isOffline = false
        
        // Then
        XCTAssertFalse(offlineManager.isAppOffline)
    }
    
    func testPendingActionStatus() {
        // Given
        offlineManager.pendingActions.removeAll()
        
        // Then
        XCTAssertFalse(offlineManager.hasPendingActions)
        XCTAssertEqual(offlineManager.pendingActionCount, 0)
        
        // Given
        let action = PendingAction(
            type: .create,
            endpoint: "/test/endpoint",
            data: ["key": "value"]
        )
        offlineManager.addPendingAction(action)
        
        // Then
        XCTAssertTrue(offlineManager.hasPendingActions)
        XCTAssertEqual(offlineManager.pendingActionCount, 1)
    }
}
