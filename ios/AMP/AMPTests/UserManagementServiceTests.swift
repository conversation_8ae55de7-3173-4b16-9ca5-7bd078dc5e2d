import XCTest
@testable import AMP

class UserManagementServiceTests: XCTestCase {
    var userManagementService: UserManagementService!
    
    override func setUp() {
        super.setUp()
        userManagementService = UserManagementService()
    }
    
    override func tearDown() {
        userManagementService = nil
        super.tearDown()
    }
    
    func testFilterUsers() {
        // Given
        let users = [
            UserManagement(
                id: 1,
                email: "<EMAIL>",
                firstName: "Admin",
                lastName: "User",
                role: "admin",
                isActive: true,
                createdAt: "2023-01-01T00:00:00.000Z",
                updatedAt: "2023-01-01T00:00:00.000Z",
                lastLogin: "2023-01-01T00:00:00.000Z"
            ),
            UserManagement(
                id: 2,
                email: "<EMAIL>",
                firstName: "Manager",
                lastName: "User",
                role: "manager",
                isActive: true,
                createdAt: "2023-01-01T00:00:00.000Z",
                updatedAt: "2023-01-01T00:00:00.000Z",
                lastLogin: "2023-01-01T00:00:00.000Z"
            ),
            UserManagement(
                id: 3,
                email: "<EMAIL>",
                firstName: "Regular",
                lastName: "User",
                role: "user",
                isActive: false,
                createdAt: "2023-01-01T00:00:00.000Z",
                updatedAt: "2023-01-01T00:00:00.000Z",
                lastLogin: "2023-01-01T00:00:00.000Z"
            )
        ]
        
        userManagementService.users = users
        
        // When & Then
        
        // Test empty search
        XCTAssertEqual(userManagementService.filteredUsers(searchText: "").count, 3)
        
        // Test search by name
        XCTAssertEqual(userManagementService.filteredUsers(searchText: "Admin").count, 1)
        XCTAssertEqual(userManagementService.filteredUsers(searchText: "admin").count, 1)
        XCTAssertEqual(userManagementService.filteredUsers(searchText: "User").count, 3)
        
        // Test search by email
        XCTAssertEqual(userManagementService.filteredUsers(searchText: "admin@").count, 1)
        XCTAssertEqual(userManagementService.filteredUsers(searchText: "example.com").count, 3)
        
        // Test role filter
        XCTAssertEqual(userManagementService.filteredUsers(searchText: "", roleFilter: .admin).count, 1)
        XCTAssertEqual(userManagementService.filteredUsers(searchText: "", roleFilter: .manager).count, 1)
        XCTAssertEqual(userManagementService.filteredUsers(searchText: "", roleFilter: .user).count, 1)
        
        // Test active only filter
        XCTAssertEqual(userManagementService.filteredUsers(searchText: "", activeOnly: true).count, 2)
        
        // Test combined filters
        XCTAssertEqual(userManagementService.filteredUsers(searchText: "User", roleFilter: .admin, activeOnly: true).count, 1)
        XCTAssertEqual(userManagementService.filteredUsers(searchText: "User", roleFilter: .user, activeOnly: true).count, 0)
    }
    
    func testGetUserById() {
        // Given
        let users = [
            UserManagement(
                id: 1,
                email: "<EMAIL>",
                firstName: "Admin",
                lastName: "User",
                role: "admin",
                isActive: true,
                createdAt: "2023-01-01T00:00:00.000Z",
                updatedAt: "2023-01-01T00:00:00.000Z",
                lastLogin: "2023-01-01T00:00:00.000Z"
            ),
            UserManagement(
                id: 2,
                email: "<EMAIL>",
                firstName: "Manager",
                lastName: "User",
                role: "manager",
                isActive: true,
                createdAt: "2023-01-01T00:00:00.000Z",
                updatedAt: "2023-01-01T00:00:00.000Z",
                lastLogin: "2023-01-01T00:00:00.000Z"
            )
        ]
        
        userManagementService.users = users
        
        // When & Then
        XCTAssertNotNil(userManagementService.getUser(by: 1))
        XCTAssertEqual(userManagementService.getUser(by: 1)?.email, "<EMAIL>")
        
        XCTAssertNotNil(userManagementService.getUser(by: 2))
        XCTAssertEqual(userManagementService.getUser(by: 2)?.email, "<EMAIL>")
        
        XCTAssertNil(userManagementService.getUser(by: 3))
    }
}
