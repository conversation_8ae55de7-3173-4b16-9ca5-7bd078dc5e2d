import XCTest
@testable import AMP

class SearchServiceTests: XCTestCase {
    var searchService: SearchService!
    var mockOrderService: OrderService!
    var mockInventoryService: InventoryService!
    var mockVendorService: VendorService!
    var mockNoteService: NoteService!
    var mockUserManagementService: UserManagementService!
    
    override func setUp() {
        super.setUp()
        
        mockOrderService = OrderService()
        mockInventoryService = InventoryService()
        mockVendorService = VendorService()
        mockNoteService = NoteService()
        mockUserManagementService = UserManagementService()
        
        searchService = SearchService(
            orderService: mockOrderService,
            inventoryService: mockInventoryService,
            vendorService: mockVendorService,
            noteService: mockNoteService,
            userManagementService: mockUserManagementService
        )
        
        // Clear search history
        searchService.clearSearchHistory()
    }
    
    override func tearDown() {
        searchService = nil
        mockOrderService = nil
        mockInventoryService = nil
        mockVendorService = nil
        mockNoteService = nil
        mockUserManagementService = nil
        super.tearDown()
    }
    
    func testEmptySearchQuery() {
        // Given
        let emptyQuery = ""
        
        // When
        searchService.search(query: emptyQuery)
        
        // Then
        XCTAssertTrue(searchService.searchResults.isEmpty)
    }
    
    func testWhitespaceOnlySearchQuery() {
        // Given
        let whitespaceQuery = "   \n\t   "
        
        // When
        searchService.search(query: whitespaceQuery)
        
        // Then
        XCTAssertTrue(searchService.searchResults.isEmpty)
    }
    
    func testSearchHistoryManagement() {
        // Given
        let query1 = "test query 1"
        let query2 = "test query 2"
        let query3 = "test query 3"
        
        // When
        searchService.search(query: query1)
        searchService.search(query: query2)
        searchService.search(query: query3)
        
        // Then
        XCTAssertTrue(searchService.searchHistory.contains(query1))
        XCTAssertTrue(searchService.searchHistory.contains(query2))
        XCTAssertTrue(searchService.searchHistory.contains(query3))
        
        // Most recent should be first
        XCTAssertEqual(searchService.searchHistory.first, query3)
    }
    
    func testSearchHistoryDuplicateRemoval() {
        // Given
        let query = "duplicate query"
        
        // When
        searchService.search(query: query)
        searchService.search(query: query)
        searchService.search(query: query)
        
        // Then
        let occurrences = searchService.searchHistory.filter { $0 == query }
        XCTAssertEqual(occurrences.count, 1)
    }
    
    func testRecentSearchesLimit() {
        // Given
        let queries = (1...10).map { "query \($0)" }
        
        // When
        for query in queries {
            searchService.search(query: query)
        }
        
        // Then
        XCTAssertLessThanOrEqual(searchService.recentSearches.count, 5)
    }
    
    func testSearchHistoryLimit() {
        // Given
        let queries = (1...25).map { "query \($0)" }
        
        // When
        for query in queries {
            searchService.search(query: query)
        }
        
        // Then
        XCTAssertLessThanOrEqual(searchService.searchHistory.count, 20)
    }
    
    func testClearSearchHistory() {
        // Given
        searchService.search(query: "test query")
        XCTAssertFalse(searchService.searchHistory.isEmpty)
        
        // When
        searchService.clearSearchHistory()
        
        // Then
        XCTAssertTrue(searchService.searchHistory.isEmpty)
        XCTAssertTrue(searchService.recentSearches.isEmpty)
    }
    
    func testRemoveFromHistory() {
        // Given
        let query1 = "query 1"
        let query2 = "query 2"
        searchService.search(query: query1)
        searchService.search(query: query2)
        
        // When
        searchService.removeFromHistory(query1)
        
        // Then
        XCTAssertFalse(searchService.searchHistory.contains(query1))
        XCTAssertTrue(searchService.searchHistory.contains(query2))
        XCTAssertFalse(searchService.recentSearches.contains(query1))
    }
    
    func testRelevanceCalculation() {
        // This is a private method, so we'll test it indirectly through search results
        // We can't directly test the private calculateRelevance method, but we can verify
        // that search results are ordered by relevance
        
        // Given - we would need mock data to properly test this
        // For now, we'll just verify the method doesn't crash
        let query = "test"
        
        // When
        searchService.search(query: query)
        
        // Then
        // The search should complete without crashing
        XCTAssertNotNil(searchService.searchResults)
    }
    
    func testSearchResultTypes() {
        // Given
        let allTypes = SearchResultType.allCases
        
        // When & Then
        for type in allTypes {
            XCTAssertFalse(type.rawValue.isEmpty)
            XCTAssertFalse(type.icon.isEmpty)
            XCTAssertNotNil(type.color)
        }
    }
    
    func testSearchResultCreation() {
        // Given
        let result = SearchResult(
            id: "test_id",
            title: "Test Title",
            subtitle: "Test Subtitle",
            type: .order,
            relevanceScore: 0.8,
            data: "test data"
        )
        
        // When & Then
        XCTAssertEqual(result.id, "test_id")
        XCTAssertEqual(result.title, "Test Title")
        XCTAssertEqual(result.subtitle, "Test Subtitle")
        XCTAssertEqual(result.type, .order)
        XCTAssertEqual(result.relevanceScore, 0.8)
    }
    
    func testLevenshteinDistance() {
        // This tests the fuzzy matching capability indirectly
        // We can't test the private method directly, but we can test that
        // similar strings get reasonable relevance scores
        
        // Given
        let query = "test"
        
        // When
        searchService.search(query: query)
        
        // Then
        // The search should complete and handle fuzzy matching
        XCTAssertNotNil(searchService.searchResults)
    }
}
