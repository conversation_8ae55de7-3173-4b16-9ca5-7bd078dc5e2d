import XCTest
@testable import AMP

class AnalyticsServiceTests: XCTestCase {
    var analyticsService: AnalyticsService!
    
    override func setUp() {
        super.setUp()
        analyticsService = AnalyticsService.shared
        analyticsService.clearEvents()
    }
    
    override func tearDown() {
        analyticsService.clearEvents()
        analyticsService = nil
        super.tearDown()
    }
    
    func testTrackEvent() {
        // Given
        let eventName = "test_event"
        let category = EventCategory.interaction
        let properties = ["key": "value"]
        
        // When
        analyticsService.trackEvent(name: eventName, category: category, properties: properties)
        
        // Then
        XCTAssertEqual(analyticsService.events.count, 1)
        
        let event = analyticsService.events[0]
        XCTAssertEqual(event.name, eventName)
        XCTAssertEqual(event.category, category)
        XCTAssertEqual(event.properties["key"], "value")
    }
    
    func testTrackScreenView() {
        // Given
        let screenName = "TestScreen"
        
        // When
        analyticsService.trackScreenView(screenName)
        
        // Then
        XCTAssertEqual(analyticsService.events.count, 1)
        
        let event = analyticsService.events[0]
        XCTAssertEqual(event.name, "screen_view")
        XCTAssertEqual(event.category, .navigation)
        XCTAssertEqual(event.properties["screen_name"], screenName)
    }
    
    func testTrackUserAction() {
        // Given
        let action = "button_tap"
        let target = "login_button"
        
        // When
        analyticsService.trackUserAction(action, target: target)
        
        // Then
        XCTAssertEqual(analyticsService.events.count, 1)
        
        let event = analyticsService.events[0]
        XCTAssertEqual(event.name, "user_action")
        XCTAssertEqual(event.category, .interaction)
        XCTAssertEqual(event.properties["action"], action)
        XCTAssertEqual(event.properties["target"], target)
    }
    
    func testTrackError() {
        // Given
        let error = "Network error"
        let context = "Login attempt"
        
        // When
        analyticsService.trackError(error, context: context)
        
        // Then
        XCTAssertEqual(analyticsService.events.count, 1)
        
        let event = analyticsService.events[0]
        XCTAssertEqual(event.name, "error")
        XCTAssertEqual(event.category, .error)
        XCTAssertEqual(event.properties["error"], error)
        XCTAssertEqual(event.properties["context"], context)
    }
    
    func testTrackPerformance() {
        // Given
        let metric = "api_response_time"
        let value = 250.5
        let unit = "ms"
        
        // When
        analyticsService.trackPerformance(metric: metric, value: value, unit: unit)
        
        // Then
        XCTAssertEqual(analyticsService.events.count, 1)
        
        let event = analyticsService.events[0]
        XCTAssertEqual(event.name, "performance")
        XCTAssertEqual(event.category, .performance)
        XCTAssertEqual(event.properties["metric"], metric)
        XCTAssertEqual(event.properties["value"], String(value))
        XCTAssertEqual(event.properties["unit"], unit)
    }
    
    func testGetEventsByCategory() {
        // Given
        analyticsService.trackEvent(name: "event1", category: .interaction, properties: [:])
        analyticsService.trackEvent(name: "event2", category: .navigation, properties: [:])
        analyticsService.trackEvent(name: "event3", category: .interaction, properties: [:])
        
        // When
        let interactionEvents = analyticsService.getEvents(by: .interaction)
        let navigationEvents = analyticsService.getEvents(by: .navigation)
        let errorEvents = analyticsService.getEvents(by: .error)
        
        // Then
        XCTAssertEqual(interactionEvents.count, 2)
        XCTAssertEqual(navigationEvents.count, 1)
        XCTAssertEqual(errorEvents.count, 0)
    }
    
    func testGetEventsByName() {
        // Given
        analyticsService.trackEvent(name: "test_event", category: .interaction, properties: [:])
        analyticsService.trackEvent(name: "another_event", category: .navigation, properties: [:])
        analyticsService.trackEvent(name: "test_event", category: .error, properties: [:])
        
        // When
        let testEvents = analyticsService.getEvents(by: "test_event")
        let anotherEvents = analyticsService.getEvents(by: "another_event")
        let nonExistentEvents = analyticsService.getEvents(by: "non_existent")
        
        // Then
        XCTAssertEqual(testEvents.count, 2)
        XCTAssertEqual(anotherEvents.count, 1)
        XCTAssertEqual(nonExistentEvents.count, 0)
    }
    
    func testGetEventsInDateRange() {
        // Given
        let now = Date()
        let oneHourAgo = now.addingTimeInterval(-3600)
        let twoHoursAgo = now.addingTimeInterval(-7200)
        
        // Create events with different timestamps
        let event1 = AnalyticsEvent(name: "event1", category: .interaction, properties: [:], timestamp: twoHoursAgo)
        let event2 = AnalyticsEvent(name: "event2", category: .navigation, properties: [:], timestamp: oneHourAgo)
        let event3 = AnalyticsEvent(name: "event3", category: .error, properties: [:], timestamp: now)
        
        analyticsService.events = [event1, event2, event3]
        
        // When
        let recentEvents = analyticsService.getEvents(from: oneHourAgo, to: now)
        let allEvents = analyticsService.getEvents(from: twoHoursAgo, to: now)
        
        // Then
        XCTAssertEqual(recentEvents.count, 2)
        XCTAssertEqual(allEvents.count, 3)
    }
    
    func testGetEventCount() {
        // Given
        analyticsService.trackEvent(name: "event1", category: .interaction, properties: [:])
        analyticsService.trackEvent(name: "event2", category: .interaction, properties: [:])
        analyticsService.trackEvent(name: "event3", category: .navigation, properties: [:])
        
        // When
        let interactionCount = analyticsService.getEventCount(by: .interaction)
        let navigationCount = analyticsService.getEventCount(by: .navigation)
        let errorCount = analyticsService.getEventCount(by: .error)
        
        // Then
        XCTAssertEqual(interactionCount, 2)
        XCTAssertEqual(navigationCount, 1)
        XCTAssertEqual(errorCount, 0)
    }
    
    func testGetMostFrequentEvents() {
        // Given
        analyticsService.trackEvent(name: "event_a", category: .interaction, properties: [:])
        analyticsService.trackEvent(name: "event_b", category: .navigation, properties: [:])
        analyticsService.trackEvent(name: "event_a", category: .interaction, properties: [:])
        analyticsService.trackEvent(name: "event_c", category: .error, properties: [:])
        analyticsService.trackEvent(name: "event_a", category: .interaction, properties: [:])
        analyticsService.trackEvent(name: "event_b", category: .navigation, properties: [:])
        
        // When
        let mostFrequent = analyticsService.getMostFrequentEvents(limit: 3)
        
        // Then
        XCTAssertEqual(mostFrequent.count, 3)
        XCTAssertEqual(mostFrequent[0].0, "event_a")
        XCTAssertEqual(mostFrequent[0].1, 3)
        XCTAssertEqual(mostFrequent[1].0, "event_b")
        XCTAssertEqual(mostFrequent[1].1, 2)
        XCTAssertEqual(mostFrequent[2].0, "event_c")
        XCTAssertEqual(mostFrequent[2].1, 1)
    }
    
    func testClearEvents() {
        // Given
        analyticsService.trackEvent(name: "event1", category: .interaction, properties: [:])
        analyticsService.trackEvent(name: "event2", category: .navigation, properties: [:])
        
        XCTAssertEqual(analyticsService.events.count, 2)
        
        // When
        analyticsService.clearEvents()
        
        // Then
        XCTAssertEqual(analyticsService.events.count, 0)
    }
}
