import XCTest
@testable import AMP

class DataPersistenceServiceTests: XCTestCase {
    var dataPersistenceService: DataPersistenceService!
    let testFileName = "test_data.json"
    
    override func setUp() {
        super.setUp()
        dataPersistenceService = DataPersistenceService.shared
        
        // Clean up any existing test file
        _ = dataPersistenceService.delete(fileName: testFileName)
    }
    
    override func tearDown() {
        // Clean up test file
        _ = dataPersistenceService.delete(fileName: testFileName)
        dataPersistenceService = nil
        super.tearDown()
    }
    
    func testSaveAndLoadData() {
        // Given
        let testData = TestData(name: "Test", value: 42, isActive: true)
        
        // When
        let saveResult = dataPersistenceService.save(testData, fileName: testFileName)
        
        // Then
        XCTAssertTrue(saveResult)
        XCTAssertTrue(dataPersistenceService.exists(fileName: testFileName))
        
        // When
        let loadedData: TestData? = dataPersistenceService.load(fileName: testFileName)
        
        // Then
        XCTAssertNotNil(loadedData)
        XCTAssertEqual(loadedData?.name, "Test")
        XCTAssertEqual(loadedData?.value, 42)
        XCTAssertEqual(loadedData?.isActive, true)
    }
    
    func testSaveAndLoadArray() {
        // Given
        let testArray = [
            TestData(name: "Test1", value: 1, isActive: true),
            TestData(name: "Test2", value: 2, isActive: false),
            TestData(name: "Test3", value: 3, isActive: true)
        ]
        
        // When
        let saveResult = dataPersistenceService.save(testArray, fileName: testFileName)
        
        // Then
        XCTAssertTrue(saveResult)
        
        // When
        let loadedArray: [TestData]? = dataPersistenceService.load(fileName: testFileName)
        
        // Then
        XCTAssertNotNil(loadedArray)
        XCTAssertEqual(loadedArray?.count, 3)
        XCTAssertEqual(loadedArray?[0].name, "Test1")
        XCTAssertEqual(loadedArray?[1].value, 2)
        XCTAssertEqual(loadedArray?[2].isActive, true)
    }
    
    func testDeleteData() {
        // Given
        let testData = TestData(name: "Test", value: 42, isActive: true)
        _ = dataPersistenceService.save(testData, fileName: testFileName)
        
        XCTAssertTrue(dataPersistenceService.exists(fileName: testFileName))
        
        // When
        let deleteResult = dataPersistenceService.delete(fileName: testFileName)
        
        // Then
        XCTAssertTrue(deleteResult)
        XCTAssertFalse(dataPersistenceService.exists(fileName: testFileName))
    }
    
    func testDeleteNonExistentFile() {
        // Given
        let nonExistentFileName = "non_existent_file.json"
        
        // When
        let deleteResult = dataPersistenceService.delete(fileName: nonExistentFileName)
        
        // Then
        XCTAssertTrue(deleteResult) // Should return true even if file doesn't exist
    }
    
    func testGetModificationDate() {
        // Given
        let testData = TestData(name: "Test", value: 42, isActive: true)
        let beforeSave = Date()
        
        // When
        _ = dataPersistenceService.save(testData, fileName: testFileName)
        let afterSave = Date()
        
        // Then
        let modificationDate = dataPersistenceService.getModificationDate(fileName: testFileName)
        XCTAssertNotNil(modificationDate)
        XCTAssertGreaterThanOrEqual(modificationDate!, beforeSave)
        XCTAssertLessThanOrEqual(modificationDate!, afterSave)
    }
    
    func testGetModificationDateForNonExistentFile() {
        // Given
        let nonExistentFileName = "non_existent_file.json"
        
        // When
        let modificationDate = dataPersistenceService.getModificationDate(fileName: nonExistentFileName)
        
        // Then
        XCTAssertNil(modificationDate)
    }
    
    func testLoadNonExistentFile() {
        // Given
        let nonExistentFileName = "non_existent_file.json"
        
        // When
        let loadedData: TestData? = dataPersistenceService.load(fileName: nonExistentFileName)
        
        // Then
        XCTAssertNil(loadedData)
    }
    
    func testClearCache() {
        // Given
        let testData1 = TestData(name: "Test1", value: 1, isActive: true)
        let testData2 = TestData(name: "Test2", value: 2, isActive: false)
        
        _ = dataPersistenceService.save(testData1, fileName: "test1.json")
        _ = dataPersistenceService.save(testData2, fileName: "test2.json")
        
        XCTAssertTrue(dataPersistenceService.exists(fileName: "test1.json"))
        XCTAssertTrue(dataPersistenceService.exists(fileName: "test2.json"))
        
        // When
        let clearResult = dataPersistenceService.clearCache()
        
        // Then
        XCTAssertTrue(clearResult)
        XCTAssertFalse(dataPersistenceService.exists(fileName: "test1.json"))
        XCTAssertFalse(dataPersistenceService.exists(fileName: "test2.json"))
    }
    
    func testSaveInvalidData() {
        // Given
        let invalidData = InvalidTestData()
        
        // When
        let saveResult = dataPersistenceService.save(invalidData, fileName: testFileName)
        
        // Then
        XCTAssertFalse(saveResult)
        XCTAssertFalse(dataPersistenceService.exists(fileName: testFileName))
    }
}

// MARK: - Test Data Structures

struct TestData: Codable {
    let name: String
    let value: Int
    let isActive: Bool
}

// This struct intentionally doesn't conform to Codable to test error handling
struct InvalidTestData {
    let invalidProperty = { print("This closure cannot be encoded") }
}
