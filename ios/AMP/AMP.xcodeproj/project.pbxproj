// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		7D7CFA502DE074D60006F1F5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7D7CFA342DE074D40006F1F5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7D7CFA3B2DE074D40006F1F5;
			remoteInfo = AMP;
		};
		7D7CFA5A2DE074D60006F1F5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7D7CFA342DE074D40006F1F5 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7D7CFA3B2DE074D40006F1F5;
			remoteInfo = AMP;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		7D7CFA3C2DE074D40006F1F5 /* AMP.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = AMP.app; sourceTree = BUILT_PRODUCTS_DIR; };
		7D7CFA4F2DE074D60006F1F5 /* AMPTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = AMPTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		7D7CFA592DE074D60006F1F5 /* AMPUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = AMPUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		7D7CFB562DE086F40006F1F5 /* Exceptions for "AMP" folder in "AMP" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 7D7CFA3B2DE074D40006F1F5 /* AMP */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		7D7CFA3E2DE074D40006F1F5 /* AMP */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				7D7CFB562DE086F40006F1F5 /* Exceptions for "AMP" folder in "AMP" target */,
			);
			path = AMP;
			sourceTree = "<group>";
		};
		7D7CFA522DE074D60006F1F5 /* AMPTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = AMPTests;
			sourceTree = "<group>";
		};
		7D7CFA5C2DE074D60006F1F5 /* AMPUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = AMPUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		7D7CFA392DE074D40006F1F5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D7CFA4C2DE074D60006F1F5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D7CFA562DE074D60006F1F5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7D7CFA332DE074D40006F1F5 = {
			isa = PBXGroup;
			children = (
				7D7CFA3E2DE074D40006F1F5 /* AMP */,
				7D7CFA522DE074D60006F1F5 /* AMPTests */,
				7D7CFA5C2DE074D60006F1F5 /* AMPUITests */,
				7D7CFA3D2DE074D40006F1F5 /* Products */,
			);
			sourceTree = "<group>";
		};
		7D7CFA3D2DE074D40006F1F5 /* Products */ = {
			isa = PBXGroup;
			children = (
				7D7CFA3C2DE074D40006F1F5 /* AMP.app */,
				7D7CFA4F2DE074D60006F1F5 /* AMPTests.xctest */,
				7D7CFA592DE074D60006F1F5 /* AMPUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7D7CFA3B2DE074D40006F1F5 /* AMP */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7D7CFA632DE074D60006F1F5 /* Build configuration list for PBXNativeTarget "AMP" */;
			buildPhases = (
				7D7CFA382DE074D40006F1F5 /* Sources */,
				7D7CFA392DE074D40006F1F5 /* Frameworks */,
				7D7CFA3A2DE074D40006F1F5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				7D7CFA3E2DE074D40006F1F5 /* AMP */,
			);
			name = AMP;
			packageProductDependencies = (
			);
			productName = AMP;
			productReference = 7D7CFA3C2DE074D40006F1F5 /* AMP.app */;
			productType = "com.apple.product-type.application";
		};
		7D7CFA4E2DE074D60006F1F5 /* AMPTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7D7CFA662DE074D60006F1F5 /* Build configuration list for PBXNativeTarget "AMPTests" */;
			buildPhases = (
				7D7CFA4B2DE074D60006F1F5 /* Sources */,
				7D7CFA4C2DE074D60006F1F5 /* Frameworks */,
				7D7CFA4D2DE074D60006F1F5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7D7CFA512DE074D60006F1F5 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7D7CFA522DE074D60006F1F5 /* AMPTests */,
			);
			name = AMPTests;
			packageProductDependencies = (
			);
			productName = AMPTests;
			productReference = 7D7CFA4F2DE074D60006F1F5 /* AMPTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		7D7CFA582DE074D60006F1F5 /* AMPUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7D7CFA692DE074D60006F1F5 /* Build configuration list for PBXNativeTarget "AMPUITests" */;
			buildPhases = (
				7D7CFA552DE074D60006F1F5 /* Sources */,
				7D7CFA562DE074D60006F1F5 /* Frameworks */,
				7D7CFA572DE074D60006F1F5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7D7CFA5B2DE074D60006F1F5 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7D7CFA5C2DE074D60006F1F5 /* AMPUITests */,
			);
			name = AMPUITests;
			packageProductDependencies = (
			);
			productName = AMPUITests;
			productReference = 7D7CFA592DE074D60006F1F5 /* AMPUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7D7CFA342DE074D40006F1F5 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					7D7CFA3B2DE074D40006F1F5 = {
						CreatedOnToolsVersion = 16.2;
					};
					7D7CFA4E2DE074D60006F1F5 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 7D7CFA3B2DE074D40006F1F5;
					};
					7D7CFA582DE074D60006F1F5 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 7D7CFA3B2DE074D40006F1F5;
					};
				};
			};
			buildConfigurationList = 7D7CFA372DE074D40006F1F5 /* Build configuration list for PBXProject "AMP" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 7D7CFA332DE074D40006F1F5;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				7D7CFB592DE08C090006F1F5 /* XCRemoteSwiftPackageReference "ChartView" */,
				7D7CFB5A2DE08C5F0006F1F5 /* XCRemoteSwiftPackageReference "Alamofire" */,
				7D7CFB5B2DE08CC00006F1F5 /* XCRemoteSwiftPackageReference "Starscream" */,
				7D7CFB5E2DE08D440006F1F5 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */,
				7D7CFB5F2DE08D740006F1F5 /* XCRemoteSwiftPackageReference "keychain-swift" */,
				7D7CFB602DE08E800006F1F5 /* XCRemoteSwiftPackageReference "SwiftyJSON" */,
				7D7CFB612DE08EC60006F1F5 /* XCRemoteSwiftPackageReference "lottie-ios" */,
				7D7CFB622DE08EE50006F1F5 /* XCRemoteSwiftPackageReference "CodeScanner" */,
				7D7CFB632DE08F140006F1F5 /* XCRemoteSwiftPackageReference "BiometricAuthentication" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 7D7CFA3D2DE074D40006F1F5 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7D7CFA3B2DE074D40006F1F5 /* AMP */,
				7D7CFA4E2DE074D60006F1F5 /* AMPTests */,
				7D7CFA582DE074D60006F1F5 /* AMPUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7D7CFA3A2DE074D40006F1F5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D7CFA4D2DE074D60006F1F5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D7CFA572DE074D60006F1F5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7D7CFA382DE074D40006F1F5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D7CFA4B2DE074D60006F1F5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D7CFA552DE074D60006F1F5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		7D7CFA512DE074D60006F1F5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7D7CFA3B2DE074D40006F1F5 /* AMP */;
			targetProxy = 7D7CFA502DE074D60006F1F5 /* PBXContainerItemProxy */;
		};
		7D7CFA5B2DE074D60006F1F5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7D7CFA3B2DE074D40006F1F5 /* AMP */;
			targetProxy = 7D7CFA5A2DE074D60006F1F5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		7D7CFA612DE074D60006F1F5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7D7CFA622DE074D60006F1F5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		7D7CFA642DE074D60006F1F5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = AMP/AMP.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"AMP/Preview Content\"";
				DEVELOPMENT_TEAM = 6867MT9L8R;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AMP/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.business";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = amedicalpartner.com.AMP;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		7D7CFA652DE074D60006F1F5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = AMP/AMP.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"AMP/Preview Content\"";
				DEVELOPMENT_TEAM = 6867MT9L8R;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AMP/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.business";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = amedicalpartner.com.AMP;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		7D7CFA672DE074D60006F1F5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6867MT9L8R;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = amedicalpartner.com.AMPTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/AMP.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/AMP";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		7D7CFA682DE074D60006F1F5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6867MT9L8R;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = amedicalpartner.com.AMPTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/AMP.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/AMP";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		7D7CFA6A2DE074D60006F1F5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6867MT9L8R;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = amedicalpartner.com.AMPUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = AMP;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		7D7CFA6B2DE074D60006F1F5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6867MT9L8R;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = amedicalpartner.com.AMPUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = AMP;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7D7CFA372DE074D40006F1F5 /* Build configuration list for PBXProject "AMP" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7D7CFA612DE074D60006F1F5 /* Debug */,
				7D7CFA622DE074D60006F1F5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7D7CFA632DE074D60006F1F5 /* Build configuration list for PBXNativeTarget "AMP" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7D7CFA642DE074D60006F1F5 /* Debug */,
				7D7CFA652DE074D60006F1F5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7D7CFA662DE074D60006F1F5 /* Build configuration list for PBXNativeTarget "AMPTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7D7CFA672DE074D60006F1F5 /* Debug */,
				7D7CFA682DE074D60006F1F5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7D7CFA692DE074D60006F1F5 /* Build configuration list for PBXNativeTarget "AMPUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7D7CFA6A2DE074D60006F1F5 /* Debug */,
				7D7CFA6B2DE074D60006F1F5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		7D7CFB592DE08C090006F1F5 /* XCRemoteSwiftPackageReference "ChartView" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/AppPear/ChartView";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.5.5;
			};
		};
		7D7CFB5A2DE08C5F0006F1F5 /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.0.0;
			};
		};
		7D7CFB5B2DE08CC00006F1F5 /* XCRemoteSwiftPackageReference "Starscream" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/daltoniam/Starscream";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.0.0;
			};
		};
		7D7CFB5E2DE08D440006F1F5 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SDWebImage/SDWebImageSwiftUI";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.2.0;
			};
		};
		7D7CFB5F2DE08D740006F1F5 /* XCRemoteSwiftPackageReference "keychain-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/evgenyneu/keychain-swift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 20.0.0;
			};
		};
		7D7CFB602DE08E800006F1F5 /* XCRemoteSwiftPackageReference "SwiftyJSON" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SwiftyJSON/SwiftyJSON";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
		7D7CFB612DE08EC60006F1F5 /* XCRemoteSwiftPackageReference "lottie-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/airbnb/lottie-ios";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.3.0;
			};
		};
		7D7CFB622DE08EE50006F1F5 /* XCRemoteSwiftPackageReference "CodeScanner" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/twostraws/CodeScanner";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.3.0;
			};
		};
		7D7CFB632DE08F140006F1F5 /* XCRemoteSwiftPackageReference "BiometricAuthentication" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/rushisangani/BiometricAuthentication";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 3.1.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */
	};
	rootObject = 7D7CFA342DE074D40006F1F5 /* Project object */;
}
