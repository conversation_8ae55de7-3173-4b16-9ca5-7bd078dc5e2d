import SwiftUI
import Combine

struct SecuritySettingsView: View {
    @EnvironmentObject var authService: AuthService
    @Environment(\.presentationMode) var presentationMode
    @ObservedObject var biometricAuthService = BiometricAuthService()
    
    @State private var autoLockEnabled = true
    @State private var autoLockTimeout = AutoLockTimeout.fiveMinutes
    @State private var requireAuthOnLaunch = false
    @State private var showSensitiveData = false
    @State private var encryptLocalData = true
    @State private var showingBiometricTest = false
    @State private var showingClearDataAlert = false
    @State private var biometricTestResult: String?
    
    @State private var cancellables = Set<AnyCancellable>()
    
    enum AutoLockTimeout: Int, CaseIterable {
        case immediately = 0
        case oneMinute = 60
        case fiveMinutes = 300
        case fifteenMinutes = 900
        case thirtyMinutes = 1800
        case oneHour = 3600
        case never = -1
        
        var displayName: String {
            switch self {
            case .immediately:
                return "Immediately"
            case .oneMinute:
                return "1 Minute"
            case .fiveMinutes:
                return "5 Minutes"
            case .fifteenMinutes:
                return "15 Minutes"
            case .thirtyMinutes:
                return "30 Minutes"
            case .oneHour:
                return "1 Hour"
            case .never:
                return "Never"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            List {
                // Biometric Authentication Section
                if biometricAuthService.isAvailable {
                    Section(header: Text("Biometric Authentication"), footer: Text("Use \(biometricAuthService.biometricDisplayName) to unlock the app and authenticate sensitive actions.")) {
                        Toggle(biometricAuthService.biometricDisplayName, isOn: $biometricAuthService.isEnabled)
                            .onChange(of: biometricAuthService.isEnabled) { enabled in
                                biometricAuthService.setBiometricEnabled(enabled)
                            }
                        
                        if biometricAuthService.isEnabled {
                            Button(action: {
                                testBiometricAuth()
                            }) {
                                HStack {
                                    Label(
                                        title: { Text("Test \(biometricAuthService.biometricDisplayName)") },
                                        icon: { Image(systemName: biometricAuthService.biometricIconName) }
                                    )
                                    .foregroundColor(Color("PrimaryColor"))
                                    
                                    Spacer()
                                }
                            }
                        }
                    }
                }
                
                // App Lock Section
                Section(header: Text("App Lock"), footer: Text("Automatically lock the app when it's not in use.")) {
                    Toggle("Auto Lock", isOn: $autoLockEnabled)
                        .onChange(of: autoLockEnabled) { _ in saveSettings() }
                    
                    if autoLockEnabled {
                        Picker("Auto Lock Timeout", selection: $autoLockTimeout) {
                            ForEach(AutoLockTimeout.allCases, id: \.self) { timeout in
                                Text(timeout.displayName).tag(timeout)
                            }
                        }
                        .onChange(of: autoLockTimeout) { _ in saveSettings() }
                    }
                    
                    Toggle("Require Authentication on Launch", isOn: $requireAuthOnLaunch)
                        .onChange(of: requireAuthOnLaunch) { _ in saveSettings() }
                }
                
                // Privacy Section
                Section(header: Text("Privacy"), footer: Text("Control how sensitive information is displayed and stored.")) {
                    Toggle("Show Sensitive Data", isOn: $showSensitiveData)
                        .onChange(of: showSensitiveData) { _ in saveSettings() }
                    
                    Toggle("Encrypt Local Data", isOn: $encryptLocalData)
                        .onChange(of: encryptLocalData) { _ in saveSettings() }
                }
                
                // Data Management Section
                Section(header: Text("Data Management"), footer: Text("Manage your locally stored data and cache.")) {
                    Button(action: {
                        clearCache()
                    }) {
                        HStack {
                            Label(
                                title: { Text("Clear Cache") },
                                icon: { Image(systemName: "trash") }
                            )
                            .foregroundColor(Color("WarningColor"))
                            
                            Spacer()
                        }
                    }
                    
                    Button(action: {
                        showingClearDataAlert = true
                    }) {
                        HStack {
                            Label(
                                title: { Text("Clear All Local Data") },
                                icon: { Image(systemName: "exclamationmark.triangle") }
                            )
                            .foregroundColor(.red)
                            
                            Spacer()
                        }
                    }
                }
                
                // Security Information Section
                Section(header: Text("Security Information")) {
                    HStack {
                        Text("Device Passcode")
                        Spacer()
                        Text(devicePasscodeStatus)
                            .foregroundColor(devicePasscodeStatus == "Set" ? .green : .red)
                    }
                    
                    HStack {
                        Text("Biometric Support")
                        Spacer()
                        Text(biometricAuthService.supportsBiometrics ? "Available" : "Not Available")
                            .foregroundColor(biometricAuthService.supportsBiometrics ? .green : .red)
                    }
                    
                    if biometricAuthService.supportsBiometrics {
                        HStack {
                            Text("Biometric Type")
                            Spacer()
                            Text(biometricAuthService.biometricDisplayName)
                                .foregroundColor(Color("SecondaryColor"))
                        }
                    }
                }
                
                // Test Results Section
                if let result = biometricTestResult {
                    Section(header: Text("Test Results")) {
                        Text(result)
                            .foregroundColor(result.contains("Success") ? .green : .red)
                    }
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("Security")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .alert(isPresented: $showingClearDataAlert) {
                Alert(
                    title: Text("Clear All Local Data"),
                    message: Text("This will remove all locally stored data including cache, offline data, and settings. This action cannot be undone."),
                    primaryButton: .destructive(Text("Clear All")) {
                        clearAllLocalData()
                    },
                    secondaryButton: .cancel()
                )
            }
            .onAppear {
                loadSettings()
                biometricAuthService.checkBiometricAvailability()
            }
        }
    }
    
    private var devicePasscodeStatus: String {
        // This is a simplified check - in a real app you might want more sophisticated detection
        return biometricAuthService.isAvailable || biometricAuthService.supportsBiometrics ? "Set" : "Not Set"
    }
    
    private func testBiometricAuth() {
        biometricAuthService.authenticate(reason: "Test biometric authentication")
            .sink { success in
                biometricTestResult = success ? "Biometric authentication test: Success" : "Biometric authentication test: Failed"
            }
            .store(in: &cancellables)
    }
    
    private func loadSettings() {
        autoLockEnabled = UserDefaults.standard.bool(forKey: "autoLockEnabled")
        let timeoutValue = UserDefaults.standard.integer(forKey: "autoLockTimeout")
        autoLockTimeout = AutoLockTimeout(rawValue: timeoutValue) ?? .fiveMinutes
        requireAuthOnLaunch = UserDefaults.standard.bool(forKey: "requireAuthOnLaunch")
        showSensitiveData = UserDefaults.standard.bool(forKey: "showSensitiveData")
        encryptLocalData = UserDefaults.standard.bool(forKey: "encryptLocalData")
    }
    
    private func saveSettings() {
        UserDefaults.standard.set(autoLockEnabled, forKey: "autoLockEnabled")
        UserDefaults.standard.set(autoLockTimeout.rawValue, forKey: "autoLockTimeout")
        UserDefaults.standard.set(requireAuthOnLaunch, forKey: "requireAuthOnLaunch")
        UserDefaults.standard.set(showSensitiveData, forKey: "showSensitiveData")
        UserDefaults.standard.set(encryptLocalData, forKey: "encryptLocalData")
    }
    
    private func clearCache() {
        let offlineManager = OfflineManager()
        offlineManager.clearCache()
        
        // Show success message
        biometricTestResult = "Cache cleared successfully"
    }
    
    private func clearAllLocalData() {
        // Clear cache
        let offlineManager = OfflineManager()
        offlineManager.clearCache()
        
        // Clear keychain data
        let keychain = KeychainService.shared
        keychain.deleteAllData()
        
        // Clear UserDefaults
        let domain = Bundle.main.bundleIdentifier!
        UserDefaults.standard.removePersistentDomain(forName: domain)
        UserDefaults.standard.synchronize()
        
        // Reset settings to defaults
        loadSettings()
        
        // Show success message
        biometricTestResult = "All local data cleared successfully"
    }
}

struct SecuritySettingsView_Previews: PreviewProvider {
    static var previews: some View {
        SecuritySettingsView()
            .environmentObject(AuthService())
    }
}
