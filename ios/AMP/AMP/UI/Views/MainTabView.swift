import SwiftUI
import Combine

// Import feature modules
import Foundation

struct MainTabView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var orderService: OrderService
    @EnvironmentObject var dashboardService: DashboardService
    @EnvironmentObject var inventoryService: InventoryService
    @EnvironmentObject var vendorService: VendorService
    @EnvironmentObject var noteService: NoteService
    @State private var selectedTab = 0

    var body: some View {
        TabView(selection: $selectedTab) {
            // Dashboard Tab
            DashboardView()
                .tabItem {
                    Label("Dashboard", systemImage: "chart.bar")
                }
                .tag(0)

            // Orders Tab
            OrderListView()
                .tabItem {
                    Label("Orders", systemImage: "shippingbox")
                }
                .tag(1)

            // Inventory Tab
            InventoryListView()
                .tabItem {
                    Label("Inventory", systemImage: "cube.box")
                }
                .tag(2)

            // Vendors Tab
            VendorListView()
                .tabItem {
                    Label("Vendors", systemImage: "building.2")
                }
                .tag(3)

            // Notes Tab
            NoteListView()
                .tabItem {
                    Label("Notes", systemImage: "note.text")
                }
                .tag(4)

            // Profile Tab
            ProfileView()
                .tabItem {
                    Label("Profile", systemImage: "person.circle")
                }
                .tag(5)
        }
        .accentColor(Color("PrimaryColor"))
    }
}

struct ProfileView: View {
    @EnvironmentObject var authService: AuthService
    @State private var showingLogoutAlert = false

    var body: some View {
        NavigationView {
            List {
                // User Info Section
                Section {
                    HStack(spacing: 16) {
                        // User Avatar
                        if let avatarURL = authService.user?.avatarURL {
                            AsyncImage(url: avatarURL) { image in
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                            } placeholder: {
                                ProgressView()
                            }
                            .frame(width: 60, height: 60)
                            .clipShape(Circle())
                        } else {
                            Circle()
                                .fill(Color("PrimaryColor"))
                                .frame(width: 60, height: 60)
                                .overlay(
                                    Text(authService.user?.initials ?? "")
                                        .font(.title3)
                                        .fontWeight(.bold)
                                        .foregroundColor(.white)
                                )
                        }

                        // User Details
                        VStack(alignment: .leading, spacing: 4) {
                            Text(authService.user?.fullName ?? "")
                                .font(.headline)
                                .foregroundColor(Color("TextColor"))

                            Text(authService.user?.email ?? "")
                                .font(.subheadline)
                                .foregroundColor(Color("SecondaryColor"))

                            Text(authService.user?.userRole.rawValue.capitalized ?? "")
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(Color("PrimaryColor").opacity(0.2))
                                .foregroundColor(Color("PrimaryColor"))
                                .cornerRadius(4)
                        }
                    }
                    .padding(.vertical, 8)
                }

                // Account Section
                Section(header: Text("Account")) {
                    NavigationLink(destination: Text("Edit Profile - Coming Soon")) {
                        Label("Edit Profile", systemImage: "person")
                    }

                    NavigationLink(destination: Text("Change Password - Coming Soon")) {
                        Label("Change Password", systemImage: "lock")
                    }

                    NavigationLink(destination: NotificationSettingsView()) {
                        Label("Notifications", systemImage: "bell")
                    }

                    if authService.user?.userRole == .admin {
                        NavigationLink(destination: UserListView()) {
                            Label("User Management", systemImage: "person.3")
                        }

                        NavigationLink(destination: AnalyticsDashboardView()) {
                            Label("Analytics", systemImage: "chart.bar.xaxis")
                        }

                        NavigationLink(destination: DataExportView()) {
                            Label("Data Export", systemImage: "square.and.arrow.up")
                        }
                    }
                }

                // Preferences Section
                Section(header: Text("Preferences")) {
                    NavigationLink(destination: ThemeSettingsView()) {
                        Label("Appearance", systemImage: "paintbrush")
                    }

                    NavigationLink(destination: LanguageSelectionView()) {
                        Label("Language", systemImage: "globe")
                    }

                    NavigationLink(destination: OfflineSettingsView()) {
                        Label("Offline Mode", systemImage: "wifi.slash")
                    }

                    NavigationLink(destination: SecuritySettingsView()) {
                        Label("Security", systemImage: "lock.shield")
                    }
                }

                // Support Section
                Section(header: Text("Support")) {
                    NavigationLink(destination: Text("Help Center - Coming Soon")) {
                        Label("Help Center", systemImage: "questionmark.circle")
                    }

                    NavigationLink(destination: Text("Contact Support - Coming Soon")) {
                        Label("Contact Support", systemImage: "envelope")
                    }

                    NavigationLink(destination: Text("About - Coming Soon")) {
                        Label("About", systemImage: "info.circle")
                    }
                }

                // Logout Section
                Section {
                    Button(action: {
                        showingLogoutAlert = true
                    }) {
                        HStack {
                            Label("Logout", systemImage: "arrow.right.square")
                                .foregroundColor(.red)
                            Spacer()
                        }
                    }
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("Profile")
            .alert(isPresented: $showingLogoutAlert) {
                Alert(
                    title: Text("Logout"),
                    message: Text("Are you sure you want to logout?"),
                    primaryButton: .destructive(Text("Logout")) {
                        authService.signOut()
                    },
                    secondaryButton: .cancel()
                )
            }
        }
    }
}

struct MainTabView_Previews: PreviewProvider {
    static var previews: some View {
        MainTabView()
            .environmentObject(AuthService())
            .environmentObject(OrderService())
            .environmentObject(DashboardService())
            .environmentObject(InventoryService())
            .environmentObject(VendorService())
            .environmentObject(NoteService())
    }
}
