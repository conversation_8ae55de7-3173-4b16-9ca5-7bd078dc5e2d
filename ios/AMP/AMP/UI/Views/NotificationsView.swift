import SwiftUI

struct NotificationsView: View {
    @EnvironmentObject var webSocketService: WebSocketService
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ZStack {
                Color("BackgroundColor")
                    .ignoresSafeArea()
                
                if webSocketService.notifications.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "bell.slash")
                            .font(.system(size: 60))
                            .foregroundColor(Color("SecondaryColor").opacity(0.5))
                        
                        Text("No Notifications")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(Color("TextColor"))
                        
                        Text("You don't have any notifications yet")
                            .font(.subheadline)
                            .foregroundColor(Color("TextColor").opacity(0.7))
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 32)
                    }
                } else {
                    List {
                        ForEach(webSocketService.notifications) { notification in
                            NotificationRow(notification: notification)
                        }
                    }
                    .listStyle(PlainListStyle())
                }
            }
            .navigationTitle("Notifications")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        webSocketService.notifications.removeAll()
                    }) {
                        Text("Clear")
                    }
                    .disabled(webSocketService.notifications.isEmpty)
                }
            }
        }
    }
}

struct NotificationRow: View {
    let notification: WebSocketNotification
    
    var body: some View {
        HStack(alignment: .top, spacing: 16) {
            // Icon
            Image(systemName: notification.icon)
                .font(.title2)
                .foregroundColor(notification.color)
                .frame(width: 30, height: 30)
            
            VStack(alignment: .leading, spacing: 8) {
                // Title
                Text(notification.title)
                    .font(.headline)
                    .foregroundColor(Color("TextColor"))
                
                // Message
                Text(notification.message)
                    .font(.subheadline)
                    .foregroundColor(Color("SecondaryColor"))
                    .lineLimit(3)
                
                // Timestamp
                Text(notification.formattedTimestamp)
                    .font(.caption)
                    .foregroundColor(Color("SecondaryColor"))
            }
        }
        .padding(.vertical, 8)
    }
}

struct NotificationsView_Previews: PreviewProvider {
    static var previews: some View {
        let webSocketService = WebSocketService()
        webSocketService.notifications = [
            WebSocketNotification(
                id: "1",
                type: "order",
                title: "New Order",
                message: "A new order has been created",
                timestamp: "2023-01-01T12:00:00.000Z",
                data: nil
            ),
            WebSocketNotification(
                id: "2",
                type: "inventory",
                title: "Low Stock Alert",
                message: "Product X is running low on stock",
                timestamp: "2023-01-01T11:00:00.000Z",
                data: nil
            ),
            WebSocketNotification(
                id: "3",
                type: "note",
                title: "New Note",
                message: "A new note has been added",
                timestamp: "2023-01-01T10:00:00.000Z",
                data: nil
            )
        ]
        
        return NotificationsView()
            .environmentObject(webSocketService)
    }
}
