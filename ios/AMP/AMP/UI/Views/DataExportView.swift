import SwiftUI

struct DataExportView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var orderService: OrderService
    @EnvironmentObject var inventoryService: InventoryService
    @EnvironmentObject var vendorService: VendorService
    @EnvironmentObject var localizationService: LocalizationService
    @Environment(\.presentationMode) var presentationMode
    
    @StateObject private var exportService = DataExportService()
    @State private var selectedExportType: ExportType = .orders
    @State private var selectedFormat: ExportFormat = .csv
    @State private var showingExportAlert = false
    @State private var exportAlertMessage = ""
    @State private var exportedFiles: [URL] = []
    
    enum ExportType: String, CaseIterable {
        case orders = "Orders"
        case inventory = "Inventory"
        case vendors = "Vendors"
        case analytics = "Analytics"
        
        var icon: String {
            switch self {
            case .orders: return "shippingbox"
            case .inventory: return "cube.box"
            case .vendors: return "building.2"
            case .analytics: return "chart.bar"
            }
        }
    }
    
    enum ExportFormat: String, CaseIterable {
        case csv = "CSV"
        case json = "JSON"
        
        var description: String {
            switch self {
            case .csv: return "Comma-separated values"
            case .json: return "JavaScript Object Notation"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            List {
                // Export Configuration Section
                Section(header: Text("export_configuration".localized)) {
                    // Data Type Selection
                    Picker("data_type".localized, selection: $selectedExportType) {
                        ForEach(ExportType.allCases, id: \.self) { type in
                            HStack {
                                Image(systemName: type.icon)
                                Text(type.rawValue.localized)
                            }
                            .tag(type)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    
                    // Format Selection
                    Picker("export_format".localized, selection: $selectedFormat) {
                        ForEach(ExportFormat.allCases, id: \.self) { format in
                            VStack(alignment: .leading) {
                                Text(format.rawValue)
                                Text(format.description)
                                    .font(.caption)
                                    .foregroundColor(Color("SecondaryColor"))
                            }
                            .tag(format)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                // Export Action Section
                Section(header: Text("export_action".localized)) {
                    Button(action: performExport) {
                        HStack {
                            if exportService.isExporting {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "square.and.arrow.up")
                            }
                            
                            Text("export_data".localized)
                                .fontWeight(.medium)
                            
                            Spacer()
                        }
                        .foregroundColor(exportService.isExporting ? Color("SecondaryColor") : Color("PrimaryColor"))
                    }
                    .disabled(exportService.isExporting)
                    
                    if exportService.isExporting {
                        ProgressView(value: exportService.exportProgress)
                            .progressViewStyle(LinearProgressViewStyle())
                    }
                }
                
                // Export History Section
                if !exportedFiles.isEmpty {
                    Section(header: Text("export_history".localized)) {
                        ForEach(exportedFiles, id: \.self) { fileURL in
                            ExportedFileRow(
                                fileURL: fileURL,
                                onShare: {
                                    exportService.shareFile(fileURL)
                                },
                                onDelete: {
                                    exportService.deleteExportedFile(fileURL)
                                    loadExportedFiles()
                                }
                            )
                        }
                        
                        Button(action: {
                            exportService.clearAllExports()
                            loadExportedFiles()
                        }) {
                            HStack {
                                Image(systemName: "trash")
                                Text("clear_all_exports".localized)
                                Spacer()
                            }
                            .foregroundColor(.red)
                        }
                    }
                }
                
                // Export Information Section
                Section(header: Text("export_information".localized)) {
                    InfoRow(
                        title: "data_count".localized,
                        value: "\(getDataCount())"
                    )
                    
                    InfoRow(
                        title: "estimated_size".localized,
                        value: getEstimatedSize()
                    )
                    
                    InfoRow(
                        title: "export_location".localized,
                        value: "documents_folder".localized
                    )
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("data_export".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("close".localized) {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .alert(isPresented: $showingExportAlert) {
                Alert(
                    title: Text("export_complete".localized),
                    message: Text(exportAlertMessage),
                    primaryButton: .default(Text("share".localized)) {
                        if let url = exportService.lastExportURL {
                            exportService.shareFile(url)
                        }
                    },
                    secondaryButton: .cancel(Text("ok".localized))
                )
            }
            .onAppear {
                loadExportedFiles()
            }
        }
        .environment(\.layoutDirection, localizationService.isRTL ? .rightToLeft : .leftToRight)
    }
    
    private func performExport() {
        guard !exportService.isExporting else { return }
        
        switch selectedExportType {
        case .orders:
            exportOrders()
        case .inventory:
            exportInventory()
        case .vendors:
            exportVendors()
        case .analytics:
            exportAnalytics()
        }
    }
    
    private func exportOrders() {
        let orders = orderService.orders
        guard !orders.isEmpty else {
            showExportError("no_orders_to_export".localized)
            return
        }
        
        switch selectedFormat {
        case .csv:
            exportService.exportOrdersToCSV(orders) { result in
                handleExportResult(result)
            }
        case .json:
            exportService.exportToJSON(orders, fileName: "orders_export") { result in
                handleExportResult(result)
            }
        }
    }
    
    private func exportInventory() {
        let products = inventoryService.products
        guard !products.isEmpty else {
            showExportError("no_products_to_export".localized)
            return
        }
        
        switch selectedFormat {
        case .csv:
            exportService.exportInventoryToCSV(products) { result in
                handleExportResult(result)
            }
        case .json:
            exportService.exportToJSON(products, fileName: "inventory_export") { result in
                handleExportResult(result)
            }
        }
    }
    
    private func exportVendors() {
        let vendors = vendorService.vendors
        guard !vendors.isEmpty else {
            showExportError("no_vendors_to_export".localized)
            return
        }
        
        switch selectedFormat {
        case .csv:
            exportService.exportVendorsToCSV(vendors) { result in
                handleExportResult(result)
            }
        case .json:
            exportService.exportToJSON(vendors, fileName: "vendors_export") { result in
                handleExportResult(result)
            }
        }
    }
    
    private func exportAnalytics() {
        let events = AnalyticsService.shared.events
        guard !events.isEmpty else {
            showExportError("no_analytics_to_export".localized)
            return
        }
        
        switch selectedFormat {
        case .csv:
            exportService.exportAnalyticsToCSV(events) { result in
                handleExportResult(result)
            }
        case .json:
            exportService.exportToJSON(events, fileName: "analytics_export") { result in
                handleExportResult(result)
            }
        }
    }
    
    private func handleExportResult(_ result: Result<URL, Error>) {
        switch result {
        case .success(let url):
            exportAlertMessage = "export_success_message".localized(with: url.lastPathComponent)
            showingExportAlert = true
            loadExportedFiles()
            
            // Track analytics
            AnalyticsService.shared.trackUserAction("data_export", target: "\(selectedExportType.rawValue)_\(selectedFormat.rawValue)")
            
        case .failure(let error):
            showExportError(error.localizedDescription)
        }
    }
    
    private func showExportError(_ message: String) {
        exportAlertMessage = message
        showingExportAlert = true
    }
    
    private func loadExportedFiles() {
        exportedFiles = exportService.getExportedFiles()
    }
    
    private func getDataCount() -> Int {
        switch selectedExportType {
        case .orders:
            return orderService.orders.count
        case .inventory:
            return inventoryService.products.count
        case .vendors:
            return vendorService.vendors.count
        case .analytics:
            return AnalyticsService.shared.events.count
        }
    }
    
    private func getEstimatedSize() -> String {
        let count = getDataCount()
        let estimatedBytesPerItem: Int
        
        switch selectedExportType {
        case .orders:
            estimatedBytesPerItem = selectedFormat == .csv ? 150 : 300
        case .inventory:
            estimatedBytesPerItem = selectedFormat == .csv ? 200 : 400
        case .vendors:
            estimatedBytesPerItem = selectedFormat == .csv ? 100 : 200
        case .analytics:
            estimatedBytesPerItem = selectedFormat == .csv ? 250 : 500
        }
        
        let estimatedBytes = count * estimatedBytesPerItem
        
        if estimatedBytes < 1024 {
            return "\(estimatedBytes) B"
        } else if estimatedBytes < 1024 * 1024 {
            return String(format: "%.1f KB", Double(estimatedBytes) / 1024.0)
        } else {
            return String(format: "%.1f MB", Double(estimatedBytes) / (1024.0 * 1024.0))
        }
    }
}

struct ExportedFileRow: View {
    let fileURL: URL
    let onShare: () -> Void
    let onDelete: () -> Void
    
    private var fileName: String {
        fileURL.lastPathComponent
    }
    
    private var fileSize: String {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: fileURL.path)
            if let size = attributes[.size] as? Int64 {
                if size < 1024 {
                    return "\(size) B"
                } else if size < 1024 * 1024 {
                    return String(format: "%.1f KB", Double(size) / 1024.0)
                } else {
                    return String(format: "%.1f MB", Double(size) / (1024.0 * 1024.0))
                }
            }
        } catch {
            print("Error getting file size: \(error)")
        }
        return "Unknown"
    }
    
    private var creationDate: String {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: fileURL.path)
            if let date = attributes[.creationDate] as? Date {
                let formatter = DateFormatter()
                formatter.dateStyle = .short
                formatter.timeStyle = .short
                return formatter.string(from: date)
            }
        } catch {
            print("Error getting creation date: \(error)")
        }
        return "Unknown"
    }
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(fileName)
                    .font(.headline)
                    .foregroundColor(Color("TextColor"))
                    .lineLimit(1)
                
                HStack {
                    Text(fileSize)
                        .font(.caption)
                        .foregroundColor(Color("SecondaryColor"))
                    
                    Text("•")
                        .font(.caption)
                        .foregroundColor(Color("SecondaryColor"))
                    
                    Text(creationDate)
                        .font(.caption)
                        .foregroundColor(Color("SecondaryColor"))
                }
            }
            
            Spacer()
            
            HStack(spacing: 16) {
                Button(action: onShare) {
                    Image(systemName: "square.and.arrow.up")
                        .foregroundColor(Color("PrimaryColor"))
                }
                
                Button(action: onDelete) {
                    Image(systemName: "trash")
                        .foregroundColor(.red)
                }
            }
        }
        .padding(.vertical, 4)
    }
}

struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(Color("TextColor"))
            
            Spacer()
            
            Text(value)
                .foregroundColor(Color("SecondaryColor"))
        }
    }
}

struct DataExportView_Previews: PreviewProvider {
    static var previews: some View {
        DataExportView()
            .environmentObject(AuthService())
            .environmentObject(OrderService())
            .environmentObject(InventoryService())
            .environmentObject(VendorService())
            .environmentObject(LocalizationService.shared)
    }
}
