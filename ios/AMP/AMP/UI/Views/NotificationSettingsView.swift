import SwiftUI
import UserNotifications

struct NotificationSettingsView: View {
    @Environment(\.presentationMode) var presentationMode
    @ObservedObject var pushNotificationService = PushNotificationService()
    
    @State private var notificationsEnabled = false
    @State private var orderNotifications = true
    @State private var inventoryNotifications = true
    @State private var vendorNotifications = false
    @State private var noteNotifications = true
    @State private var systemNotifications = true
    @State private var soundEnabled = true
    @State private var badgeEnabled = true
    @State private var alertsEnabled = true
    
    @State private var pendingNotifications: [UNNotificationRequest] = []
    @State private var deliveredNotifications: [UNNotification] = []
    
    var body: some View {
        NavigationView {
            List {
                // Permission Section
                Section(header: Text("Permissions"), footer: Text("You can change notification permissions in Settings.")) {
                    HStack {
                        Label(
                            title: { Text("Notifications") },
                            icon: { Image(systemName: "bell") }
                        )
                        
                        Spacer()
                        
                        Text(pushNotificationService.isAuthorized ? "Enabled" : "Disabled")
                            .foregroundColor(pushNotificationService.isAuthorized ? .green : .red)
                        
                        if !pushNotificationService.isAuthorized {
                            Button("Enable") {
                                pushNotificationService.requestPermission()
                            }
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color("PrimaryColor"))
                            .foregroundColor(.white)
                            .cornerRadius(4)
                        }
                    }
                }
                
                // Notification Types Section
                if pushNotificationService.isAuthorized {
                    Section(header: Text("Notification Types")) {
                        Toggle("Order Updates", isOn: $orderNotifications)
                        Toggle("Inventory Alerts", isOn: $inventoryNotifications)
                        Toggle("Vendor Notifications", isOn: $vendorNotifications)
                        Toggle("Note Updates", isOn: $noteNotifications)
                        Toggle("System Notifications", isOn: $systemNotifications)
                    }
                    
                    // Notification Style Section
                    Section(header: Text("Notification Style")) {
                        Toggle("Sound", isOn: $soundEnabled)
                        Toggle("Badge", isOn: $badgeEnabled)
                        Toggle("Alerts", isOn: $alertsEnabled)
                    }
                    
                    // Test Notifications Section
                    Section(header: Text("Test Notifications"), footer: Text("Send test notifications to verify your settings.")) {
                        Button(action: {
                            sendTestNotification()
                        }) {
                            HStack {
                                Label(
                                    title: { Text("Send Test Notification") },
                                    icon: { Image(systemName: "paperplane") }
                                )
                                .foregroundColor(Color("PrimaryColor"))
                                
                                Spacer()
                            }
                        }
                        
                        Button(action: {
                            scheduleTestNotification()
                        }) {
                            HStack {
                                Label(
                                    title: { Text("Schedule Test Notification") },
                                    icon: { Image(systemName: "clock") }
                                )
                                .foregroundColor(Color("PrimaryColor"))
                                
                                Spacer()
                            }
                        }
                    }
                    
                    // Pending Notifications Section
                    if !pendingNotifications.isEmpty {
                        Section(header: Text("Pending Notifications (\(pendingNotifications.count))")) {
                            ForEach(pendingNotifications, id: \.identifier) { notification in
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(notification.content.title)
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(Color("TextColor"))
                                    
                                    Text(notification.content.body)
                                        .font(.caption)
                                        .foregroundColor(Color("SecondaryColor"))
                                    
                                    if let trigger = notification.trigger as? UNTimeIntervalNotificationTrigger {
                                        Text("Scheduled in \(Int(trigger.timeInterval)) seconds")
                                            .font(.caption)
                                            .foregroundColor(Color("SecondaryColor"))
                                    } else if let trigger = notification.trigger as? UNCalendarNotificationTrigger {
                                        Text("Scheduled for \(formatDateComponents(trigger.dateComponents))")
                                            .font(.caption)
                                            .foregroundColor(Color("SecondaryColor"))
                                    }
                                }
                                .padding(.vertical, 4)
                            }
                            .onDelete(perform: deletePendingNotifications)
                            
                            Button(action: {
                                pushNotificationService.removeAllPendingNotifications()
                                loadPendingNotifications()
                            }) {
                                HStack {
                                    Label(
                                        title: { Text("Clear All Pending") },
                                        icon: { Image(systemName: "trash") }
                                    )
                                    .foregroundColor(.red)
                                    
                                    Spacer()
                                }
                            }
                        }
                    }
                    
                    // Delivered Notifications Section
                    if !deliveredNotifications.isEmpty {
                        Section(header: Text("Delivered Notifications (\(deliveredNotifications.count))")) {
                            ForEach(deliveredNotifications, id: \.request.identifier) { notification in
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(notification.request.content.title)
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(Color("TextColor"))
                                    
                                    Text(notification.request.content.body)
                                        .font(.caption)
                                        .foregroundColor(Color("SecondaryColor"))
                                    
                                    Text("Delivered: \(formatDate(notification.date))")
                                        .font(.caption)
                                        .foregroundColor(Color("SecondaryColor"))
                                }
                                .padding(.vertical, 4)
                            }
                            
                            Button(action: {
                                pushNotificationService.removeAllDeliveredNotifications()
                                loadDeliveredNotifications()
                            }) {
                                HStack {
                                    Label(
                                        title: { Text("Clear All Delivered") },
                                        icon: { Image(systemName: "trash") }
                                    )
                                    .foregroundColor(.red)
                                    
                                    Spacer()
                                }
                            }
                        }
                    }
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("Notifications")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Refresh") {
                        loadNotifications()
                    }
                }
            }
            .onAppear {
                loadNotifications()
                loadSettings()
            }
            .onChange(of: orderNotifications) { _ in saveSettings() }
            .onChange(of: inventoryNotifications) { _ in saveSettings() }
            .onChange(of: vendorNotifications) { _ in saveSettings() }
            .onChange(of: noteNotifications) { _ in saveSettings() }
            .onChange(of: systemNotifications) { _ in saveSettings() }
            .onChange(of: soundEnabled) { _ in saveSettings() }
            .onChange(of: badgeEnabled) { _ in saveSettings() }
            .onChange(of: alertsEnabled) { _ in saveSettings() }
        }
    }
    
    private func loadNotifications() {
        loadPendingNotifications()
        loadDeliveredNotifications()
    }
    
    private func loadPendingNotifications() {
        pushNotificationService.getPendingNotifications { notifications in
            self.pendingNotifications = notifications
        }
    }
    
    private func loadDeliveredNotifications() {
        pushNotificationService.getDeliveredNotifications { notifications in
            self.deliveredNotifications = notifications
        }
    }
    
    private func deletePendingNotifications(at offsets: IndexSet) {
        let identifiers = offsets.map { pendingNotifications[$0].identifier }
        pushNotificationService.removePendingNotifications(withIdentifiers: identifiers)
        loadPendingNotifications()
    }
    
    private func sendTestNotification() {
        pushNotificationService.scheduleLocalNotification(
            title: "Test Notification",
            body: "This is a test notification from AMP.",
            timeInterval: 1,
            userInfo: ["type": "test"]
        )
        
        // Refresh pending notifications after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            loadPendingNotifications()
        }
    }
    
    private func scheduleTestNotification() {
        let futureDate = Date().addingTimeInterval(10) // 10 seconds from now
        
        pushNotificationService.scheduleNotification(
            title: "Scheduled Test",
            body: "This is a scheduled test notification.",
            date: futureDate,
            userInfo: ["type": "scheduled_test"]
        )
        
        // Refresh pending notifications after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            loadPendingNotifications()
        }
    }
    
    private func loadSettings() {
        orderNotifications = UserDefaults.standard.bool(forKey: "orderNotifications")
        inventoryNotifications = UserDefaults.standard.bool(forKey: "inventoryNotifications")
        vendorNotifications = UserDefaults.standard.bool(forKey: "vendorNotifications")
        noteNotifications = UserDefaults.standard.bool(forKey: "noteNotifications")
        systemNotifications = UserDefaults.standard.bool(forKey: "systemNotifications")
        soundEnabled = UserDefaults.standard.bool(forKey: "soundEnabled")
        badgeEnabled = UserDefaults.standard.bool(forKey: "badgeEnabled")
        alertsEnabled = UserDefaults.standard.bool(forKey: "alertsEnabled")
    }
    
    private func saveSettings() {
        UserDefaults.standard.set(orderNotifications, forKey: "orderNotifications")
        UserDefaults.standard.set(inventoryNotifications, forKey: "inventoryNotifications")
        UserDefaults.standard.set(vendorNotifications, forKey: "vendorNotifications")
        UserDefaults.standard.set(noteNotifications, forKey: "noteNotifications")
        UserDefaults.standard.set(systemNotifications, forKey: "systemNotifications")
        UserDefaults.standard.set(soundEnabled, forKey: "soundEnabled")
        UserDefaults.standard.set(badgeEnabled, forKey: "badgeEnabled")
        UserDefaults.standard.set(alertsEnabled, forKey: "alertsEnabled")
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
    
    private func formatDateComponents(_ components: DateComponents) -> String {
        let calendar = Calendar.current
        if let date = calendar.date(from: components) {
            return formatDate(date)
        }
        return "Unknown"
    }
}

struct NotificationSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        NotificationSettingsView()
    }
}
