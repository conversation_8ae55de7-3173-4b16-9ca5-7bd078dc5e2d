import SwiftUI

struct ContentView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var themeService: ThemeService
    @EnvironmentObject var webSocketService: WebSocketService
    @EnvironmentObject var offlineManager: OfflineManager

    @State private var showingNotifications = false
    @State private var showingGlobalSearch = false

    var body: some View {
        ZStack {
            // Main content
            if authService.isAuthenticated {
                ZStack {
                    MainTabView()

                    // Offline Indicator
                    VStack {
                        OfflineIndicatorView()

                        Spacer()
                    }

                    // Action Buttons
                    VStack {
                        HStack {
                            // Search Button
                            Button(action: {
                                showingGlobalSearch = true
                            }) {
                                Circle()
                                    .fill(Color("SecondaryColor"))
                                    .frame(width: 44, height: 44)
                                    .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)
                                    .overlay(
                                        Image(systemName: "magnifyingglass")
                                            .foregroundColor(.white)
                                    )
                            }
                            .padding(.leading, 16)
                            .padding(.top, 16)

                            Spacer()

                            // Notification But<PERSON>(action: {
                                showingNotifications = true
                            }) {
                                ZStack {
                                    Circle()
                                        .fill(Color("PrimaryColor"))
                                        .frame(width: 44, height: 44)
                                        .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)

                                    Image(systemName: "bell.fill")
                                        .foregroundColor(.white)

                                    // Badge
                                    if !webSocketService.notifications.isEmpty {
                                        Text("\(webSocketService.notifications.count)")
                                            .font(.caption2)
                                            .fontWeight(.bold)
                                            .foregroundColor(.white)
                                            .padding(4)
                                            .background(Color.red)
                                            .clipShape(Circle())
                                            .offset(x: 12, y: -12)
                                    }
                                }
                            }
                            .padding(.trailing, 16)
                            .padding(.top, 16)
                        }

                        Spacer()
                    }
                }
                .sheet(isPresented: $showingNotifications) {
                    NotificationsView()
                }
                .sheet(isPresented: $showingGlobalSearch) {
                    GlobalSearchView()
                }
                .onAppear {
                    // Connect to WebSocket when authenticated
                    if let user = authService.user, let token = authService.token {
                        webSocketService.connect(userId: user.id, token: token)
                    }
                }
                .onDisappear {
                    // Disconnect from WebSocket when not authenticated
                    webSocketService.disconnect()
                }
            } else {
                LoginView()
            }

            // Loading overlay
            if authService.isLoading {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()

                VStack {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.5)

                    Text("Loading...")
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding(.top, 16)
                }
                .padding(32)
                .background(Color("CardBackgroundColor").opacity(0.8))
                .cornerRadius(16)
            }
        }
        .preferredColorScheme(themeService.currentTheme.colorScheme)
    }
}

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
            .environmentObject(AuthService())
            .environmentObject(ThemeService())
            .environmentObject(WebSocketService())
            .environmentObject(OfflineManager())
    }
}
