import SwiftUI

struct AnalyticsDashboardView: View {
    @ObservedObject var analyticsService = AnalyticsService.shared
    @Environment(\.presentationMode) var presentationMode
    
    @State private var selectedTimeRange = TimeRange.day
    @State private var selectedCategory: EventCategory? = nil
    
    enum TimeRange: String, CaseIterable {
        case hour = "Last Hour"
        case day = "Last Day"
        case week = "Last Week"
        case month = "Last Month"
        case all = "All Time"
        
        var timeInterval: TimeInterval {
            switch self {
            case .hour:
                return 3600
            case .day:
                return 86400
            case .week:
                return 604800
            case .month:
                return 2592000
            case .all:
                return TimeInterval.greatestFiniteMagnitude
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Time Range Picker
                    Picker("Time Range", selection: $selectedTimeRange) {
                        ForEach(TimeRange.allCases, id: \.self) { range in
                            Text(range.rawValue).tag(range)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .padding(.horizontal)
                    
                    // Session Info
                    SessionInfoCard()
                    
                    // Event Categories
                    EventCategoriesCard(
                        events: filteredEvents,
                        selectedCategory: $selectedCategory
                    )
                    
                    // Most Frequent Events
                    MostFrequentEventsCard(events: filteredEvents)
                    
                    // Event Timeline
                    EventTimelineCard(events: filteredEvents)
                }
                .padding(.vertical)
            }
            .navigationTitle("Analytics")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Clear") {
                        analyticsService.clearEvents()
                    }
                }
            }
        }
        .onAppear {
            analyticsService.trackScreenView("Analytics Dashboard")
        }
    }
    
    private var filteredEvents: [AnalyticsEvent] {
        let endDate = Date()
        let startDate = selectedTimeRange == .all ? 
            Date.distantPast : 
            endDate.addingTimeInterval(-selectedTimeRange.timeInterval)
        
        var events = analyticsService.getEvents(from: startDate, to: endDate)
        
        if let category = selectedCategory {
            events = events.filter { $0.category == category }
        }
        
        return events
    }
}

struct SessionInfoCard: View {
    @ObservedObject var analyticsService = AnalyticsService.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Current Session")
                .font(.headline)
                .foregroundColor(Color("TextColor"))
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Duration")
                        .font(.caption)
                        .foregroundColor(Color("SecondaryColor"))
                    
                    Text(formatDuration(analyticsService.sessionDuration))
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(Color("TextColor"))
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Events")
                        .font(.caption)
                        .foregroundColor(Color("SecondaryColor"))
                    
                    Text("\(analyticsService.events.count)")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(Color("TextColor"))
                }
            }
        }
        .padding()
        .background(Color("CardBackgroundColor"))
        .cornerRadius(12)
        .padding(.horizontal)
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        let seconds = Int(duration) % 60
        
        if hours > 0 {
            return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
}

struct EventCategoriesCard: View {
    let events: [AnalyticsEvent]
    @Binding var selectedCategory: EventCategory?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Event Categories")
                .font(.headline)
                .foregroundColor(Color("TextColor"))
            
            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 12) {
                ForEach(EventCategory.allCases, id: \.self) { category in
                    let count = events.filter { $0.category == category }.count
                    
                    Button(action: {
                        selectedCategory = selectedCategory == category ? nil : category
                    }) {
                        VStack(spacing: 8) {
                            Text(category.rawValue.capitalized)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(selectedCategory == category ? .white : Color("TextColor"))
                            
                            Text("\(count)")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(selectedCategory == category ? .white : Color("PrimaryColor"))
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(selectedCategory == category ? Color("PrimaryColor") : Color("FieldBackgroundColor"))
                        .cornerRadius(8)
                    }
                }
            }
        }
        .padding()
        .background(Color("CardBackgroundColor"))
        .cornerRadius(12)
        .padding(.horizontal)
    }
}

struct MostFrequentEventsCard: View {
    let events: [AnalyticsEvent]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Most Frequent Events")
                .font(.headline)
                .foregroundColor(Color("TextColor"))
            
            let eventCounts = Dictionary(grouping: events, by: { $0.name })
                .mapValues { $0.count }
                .sorted { $0.value > $1.value }
                .prefix(5)
            
            if eventCounts.isEmpty {
                Text("No events to display")
                    .font(.subheadline)
                    .foregroundColor(Color("SecondaryColor"))
                    .padding()
            } else {
                ForEach(Array(eventCounts.enumerated()), id: \.offset) { index, item in
                    HStack {
                        Text("\(index + 1).")
                            .font(.subheadline)
                            .foregroundColor(Color("SecondaryColor"))
                            .frame(width: 20, alignment: .leading)
                        
                        Text(item.key)
                            .font(.subheadline)
                            .foregroundColor(Color("TextColor"))
                        
                        Spacer()
                        
                        Text("\(item.value)")
                            .font(.subheadline)
                            .fontWeight(.bold)
                            .foregroundColor(Color("PrimaryColor"))
                    }
                    .padding(.vertical, 4)
                }
            }
        }
        .padding()
        .background(Color("CardBackgroundColor"))
        .cornerRadius(12)
        .padding(.horizontal)
    }
}

struct EventTimelineCard: View {
    let events: [AnalyticsEvent]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Events")
                .font(.headline)
                .foregroundColor(Color("TextColor"))
            
            if events.isEmpty {
                Text("No events to display")
                    .font(.subheadline)
                    .foregroundColor(Color("SecondaryColor"))
                    .padding()
            } else {
                ForEach(events.suffix(10).reversed(), id: \.id) { event in
                    HStack(alignment: .top, spacing: 12) {
                        Circle()
                            .fill(categoryColor(event.category))
                            .frame(width: 8, height: 8)
                            .padding(.top, 6)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(event.name)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(Color("TextColor"))
                            
                            Text(event.category.rawValue.capitalized)
                                .font(.caption)
                                .foregroundColor(Color("SecondaryColor"))
                            
                            Text(formatTimestamp(event.timestamp))
                                .font(.caption)
                                .foregroundColor(Color("SecondaryColor"))
                        }
                        
                        Spacer()
                    }
                    .padding(.vertical, 4)
                }
            }
        }
        .padding()
        .background(Color("CardBackgroundColor"))
        .cornerRadius(12)
        .padding(.horizontal)
    }
    
    private func categoryColor(_ category: EventCategory) -> Color {
        switch category {
        case .system:
            return Color("PrimaryColor")
        case .navigation:
            return Color("InfoColor")
        case .interaction:
            return Color("SuccessColor")
        case .error:
            return Color("ErrorColor")
        case .performance:
            return Color("WarningColor")
        case .business:
            return Color("SecondaryColor")
        }
    }
    
    private func formatTimestamp(_ timestamp: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .medium
        return formatter.string(from: timestamp)
    }
}

struct AnalyticsDashboardView_Previews: PreviewProvider {
    static var previews: some View {
        AnalyticsDashboardView()
    }
}
