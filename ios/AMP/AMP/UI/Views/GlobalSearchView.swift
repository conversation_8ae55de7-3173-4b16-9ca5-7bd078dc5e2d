import SwiftUI

struct GlobalSearchView: View {
    @EnvironmentObject var searchService: SearchService
    @EnvironmentObject var localizationService: LocalizationService
    @Environment(\.presentationMode) var presentationMode
    
    @State private var searchText = ""
    @State private var selectedFilter: SearchResultType? = nil
    @State private var showingFilters = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search Bar
                SearchBar(
                    text: $searchText,
                    placeholder: "search_placeholder".localized,
                    onSearchButtonClicked: {
                        performSearch()
                    },
                    onTextChanged: { text in
                        if text.isEmpty {
                            searchService.searchResults = []
                        } else {
                            searchService.searchWithDebounce(query: text)
                        }
                    }
                )
                .padding()
                
                // Filter Bar
                if !searchService.searchResults.isEmpty {
                    FilterBar(
                        selectedFilter: $selectedFilter,
                        results: searchService.searchResults
                    )
                    .padding(.horizontal)
                }
                
                // Content
                if searchText.isEmpty {
                    SearchSuggestionsView()
                } else if searchService.isSearching {
                    SearchLoadingView()
                } else if searchService.searchResults.isEmpty && !searchText.isEmpty {
                    SearchEmptyView(query: searchText)
                } else {
                    SearchResultsView(
                        results: filteredResults,
                        onResultTap: { result in
                            handleResultTap(result)
                        }
                    )
                }
                
                Spacer()
            }
            .navigationTitle("search_title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingFilters = true
                    }) {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                    }
                }
            }
            .sheet(isPresented: $showingFilters) {
                SearchFiltersView(selectedFilter: $selectedFilter)
            }
        }
        .environment(\.layoutDirection, localizationService.isRTL ? .rightToLeft : .leftToRight)
    }
    
    private var filteredResults: [SearchResult] {
        if let filter = selectedFilter {
            return searchService.searchResults.filter { $0.type == filter }
        }
        return searchService.searchResults
    }
    
    private func performSearch() {
        guard !searchText.isEmpty else { return }
        searchService.search(query: searchText)
        
        // Track search analytics
        AnalyticsService.shared.trackUserAction("search", target: "global_search")
    }
    
    private func handleResultTap(_ result: SearchResult) {
        // Track analytics
        AnalyticsService.shared.trackUserAction("search_result_tap", target: result.type.rawValue)
        
        // Navigate to appropriate view based on result type
        switch result.type {
        case .order:
            // Navigate to order detail
            break
        case .product:
            // Navigate to product detail
            break
        case .vendor:
            // Navigate to vendor detail
            break
        case .note:
            // Navigate to note detail
            break
        case .user:
            // Navigate to user detail
            break
        }
        
        presentationMode.wrappedValue.dismiss()
    }
}

struct SearchBar: View {
    @Binding var text: String
    let placeholder: String
    let onSearchButtonClicked: () -> Void
    let onTextChanged: (String) -> Void
    
    @State private var isEditing = false
    
    var body: some View {
        HStack {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(Color("SecondaryColor"))
                
                TextField(placeholder, text: $text, onEditingChanged: { editing in
                    isEditing = editing
                }, onCommit: {
                    onSearchButtonClicked()
                })
                .onChange(of: text) { newValue in
                    onTextChanged(newValue)
                }
                .foregroundColor(Color("TextColor"))
                
                if !text.isEmpty {
                    Button(action: {
                        text = ""
                        onTextChanged("")
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(Color("SecondaryColor"))
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color("FieldBackgroundColor"))
            .cornerRadius(10)
            
            if isEditing {
                Button("cancel".localized) {
                    text = ""
                    isEditing = false
                    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                }
                .foregroundColor(Color("PrimaryColor"))
                .transition(.move(edge: .trailing))
            }
        }
        .animation(.easeInOut(duration: 0.2), value: isEditing)
    }
}

struct FilterBar: View {
    @Binding var selectedFilter: SearchResultType?
    let results: [SearchResult]
    
    private var availableFilters: [SearchResultType] {
        let types = Set(results.map { $0.type })
        return SearchResultType.allCases.filter { types.contains($0) }
    }
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                // All filter
                FilterChip(
                    title: "all".localized,
                    count: results.count,
                    isSelected: selectedFilter == nil,
                    onTap: {
                        selectedFilter = nil
                    }
                )
                
                // Type filters
                ForEach(availableFilters, id: \.self) { type in
                    let count = results.filter { $0.type == type }.count
                    
                    FilterChip(
                        title: type.rawValue.localized,
                        count: count,
                        isSelected: selectedFilter == type,
                        onTap: {
                            selectedFilter = selectedFilter == type ? nil : type
                        }
                    )
                }
            }
            .padding(.horizontal)
        }
    }
}

struct FilterChip: View {
    let title: String
    let count: Int
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("(\(count))")
                    .font(.caption)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(isSelected ? Color("PrimaryColor") : Color("FieldBackgroundColor"))
            .foregroundColor(isSelected ? .white : Color("TextColor"))
            .cornerRadius(16)
        }
    }
}

struct SearchSuggestionsView: View {
    @EnvironmentObject var searchService: SearchService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            if !searchService.recentSearches.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("recent_searches".localized)
                            .font(.headline)
                            .foregroundColor(Color("TextColor"))
                        
                        Spacer()
                        
                        Button("clear_all".localized) {
                            searchService.clearSearchHistory()
                        }
                        .font(.caption)
                        .foregroundColor(Color("PrimaryColor"))
                    }
                    
                    ForEach(searchService.recentSearches, id: \.self) { search in
                        SearchSuggestionRow(
                            text: search,
                            icon: "clock",
                            onTap: {
                                // Implement search suggestion tap
                            },
                            onRemove: {
                                searchService.removeFromHistory(search)
                            }
                        )
                    }
                }
                .padding()
            }
            
            // Popular searches or suggestions could go here
            
            Spacer()
        }
    }
}

struct SearchSuggestionRow: View {
    let text: String
    let icon: String
    let onTap: () -> Void
    let onRemove: () -> Void
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(Color("SecondaryColor"))
                .frame(width: 20)
            
            Text(text)
                .foregroundColor(Color("TextColor"))
            
            Spacer()
            
            Button(action: onRemove) {
                Image(systemName: "xmark")
                    .foregroundColor(Color("SecondaryColor"))
                    .font(.caption)
            }
        }
        .padding(.vertical, 8)
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
    }
}

struct SearchLoadingView: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle())
                .scaleEffect(1.5)
            
            Text("searching".localized)
                .font(.subheadline)
                .foregroundColor(Color("SecondaryColor"))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct SearchEmptyView: View {
    let query: String
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(Color("SecondaryColor").opacity(0.5))
            
            Text("no_results_found".localized)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(Color("TextColor"))
            
            Text("no_results_message".localized(with: query))
                .font(.subheadline)
                .foregroundColor(Color("SecondaryColor"))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 32)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct SearchResultsView: View {
    let results: [SearchResult]
    let onResultTap: (SearchResult) -> Void
    
    var body: some View {
        List {
            ForEach(results, id: \.id) { result in
                SearchResultRow(result: result) {
                    onResultTap(result)
                }
                .listRowBackground(Color("CardBackgroundColor"))
            }
        }
        .listStyle(PlainListStyle())
    }
}

struct SearchResultRow: View {
    let result: SearchResult
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Type icon
                Image(systemName: result.type.icon)
                    .foregroundColor(result.type.color)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 4) {
                    // Title
                    Text(result.title)
                        .font(.headline)
                        .foregroundColor(Color("TextColor"))
                        .lineLimit(1)
                    
                    // Subtitle
                    Text(result.subtitle)
                        .font(.subheadline)
                        .foregroundColor(Color("SecondaryColor"))
                        .lineLimit(2)
                    
                    // Type badge
                    Text(result.type.rawValue.localized)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(result.type.color.opacity(0.2))
                        .foregroundColor(result.type.color)
                        .cornerRadius(4)
                }
                
                Spacer()
                
                // Relevance indicator
                Circle()
                    .fill(Color("PrimaryColor"))
                    .frame(width: 8, height: 8)
                    .opacity(result.relevanceScore)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct SearchFiltersView: View {
    @Binding var selectedFilter: SearchResultType?
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("filter_by_type".localized)) {
                    ForEach(SearchResultType.allCases, id: \.self) { type in
                        HStack {
                            Image(systemName: type.icon)
                                .foregroundColor(type.color)
                                .frame(width: 24)
                            
                            Text(type.rawValue.localized)
                                .foregroundColor(Color("TextColor"))
                            
                            Spacer()
                            
                            if selectedFilter == type {
                                Image(systemName: "checkmark")
                                    .foregroundColor(Color("PrimaryColor"))
                            }
                        }
                        .contentShape(Rectangle())
                        .onTapGesture {
                            selectedFilter = selectedFilter == type ? nil : type
                        }
                    }
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("search_filters".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("done".localized) {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

struct GlobalSearchView_Previews: PreviewProvider {
    static var previews: some View {
        GlobalSearchView()
            .environmentObject(SearchService(
                orderService: OrderService(),
                inventoryService: InventoryService(),
                vendorService: VendorService(),
                noteService: NoteService(),
                userManagementService: UserManagementService()
            ))
            .environmentObject(LocalizationService.shared)
    }
}
