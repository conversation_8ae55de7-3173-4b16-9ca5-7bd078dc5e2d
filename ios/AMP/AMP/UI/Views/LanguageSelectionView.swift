import SwiftUI

struct LanguageSelectionView: View {
    @EnvironmentObject var localizationService: LocalizationService
    @Environment(\.presentationMode) var presentationMode
    
    @State private var showingRestartAlert = false
    @State private var selectedLanguage: Language
    
    init() {
        _selectedLanguage = State(initialValue: LocalizationService.shared.currentLanguage)
    }
    
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("language_selection_header".localized)) {
                    ForEach(Language.allCases) { language in
                        LanguageRow(
                            language: language,
                            isSelected: selectedLanguage == language,
                            onTap: {
                                selectLanguage(language)
                            }
                        )
                    }
                }
                
                Section(footer: Text("language_selection_footer".localized)) {
                    EmptyView()
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("language_selection_title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("done".localized) {
                        applyLanguageChange()
                    }
                    .disabled(selectedLanguage == localizationService.currentLanguage)
                }
            }
            .alert(isPresented: $showingRestartAlert) {
                Alert(
                    title: Text("restart_required_title".localized),
                    message: Text("restart_required_message".localized),
                    primaryButton: .default(Text("restart_now".localized)) {
                        restartApp()
                    },
                    secondaryButton: .cancel(Text("restart_later".localized)) {
                        presentationMode.wrappedValue.dismiss()
                    }
                )
            }
        }
        .environment(\.layoutDirection, localizationService.isRTL ? .rightToLeft : .leftToRight)
    }
    
    private func selectLanguage(_ language: Language) {
        selectedLanguage = language
    }
    
    private func applyLanguageChange() {
        guard selectedLanguage != localizationService.currentLanguage else {
            presentationMode.wrappedValue.dismiss()
            return
        }
        
        localizationService.setLanguage(selectedLanguage)
        showingRestartAlert = true
    }
    
    private func restartApp() {
        // In a real app, you might want to handle this more gracefully
        // For now, we'll just dismiss and let the user manually restart
        presentationMode.wrappedValue.dismiss()
        
        // Post notification to update UI
        NotificationCenter.default.post(name: .languageDidChange, object: selectedLanguage)
    }
}

struct LanguageRow: View {
    let language: Language
    let isSelected: Bool
    let onTap: () -> Void
    
    @EnvironmentObject var localizationService: LocalizationService
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                // Flag
                Text(language.flag)
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 4) {
                    // Native name
                    Text(language.nativeName)
                        .font(.headline)
                        .foregroundColor(Color("TextColor"))
                    
                    // Localized name
                    if language != localizationService.currentLanguage {
                        Text(localizationService.displayName(for: language))
                            .font(.subheadline)
                            .foregroundColor(Color("SecondaryColor"))
                    }
                }
                
                Spacer()
                
                // Selection indicator
                if isSelected {
                    Image(systemName: "checkmark")
                        .foregroundColor(Color("PrimaryColor"))
                        .font(.headline)
                }
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct LanguageSelectionView_Previews: PreviewProvider {
    static var previews: some View {
        LanguageSelectionView()
            .environmentObject(LocalizationService.shared)
    }
}
