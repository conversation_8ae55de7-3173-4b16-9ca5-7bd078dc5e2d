import SwiftUI

struct OfflineSettingsView: View {
    @EnvironmentObject var offlineManager: OfflineManager
    @Environment(\.presentationMode) var presentationMode
    
    @State private var showingClearCacheAlert = false
    @State private var showingPendingActionsAlert = false
    
    var body: some View {
        NavigationView {
            List {
                // Status Section
                Section(header: Text("Status")) {
                    HStack {
                        Label(
                            title: { Text("Network Status") },
                            icon: { Image(systemName: offlineManager.isOffline ? "wifi.slash" : "wifi") }
                        )
                        
                        Spacer()
                        
                        Text(offlineManager.isOffline ? "Offline" : "Online")
                            .foregroundColor(offlineManager.isOffline ? .red : .green)
                    }
                    
                    HStack {
                        Label(
                            title: { Text("Pending Actions") },
                            icon: { Image(systemName: "arrow.triangle.2.circlepath") }
                        )
                        
                        Spacer()
                        
                        Text("\(offlineManager.pendingActionCount)")
                            .foregroundColor(offlineManager.hasPendingActions ? .orange : Color("TextColor"))
                    }
                    
                    if let lastSyncDate = offlineManager.lastSyncDate {
                        HStack {
                            Label(
                                title: { Text("Last Sync") },
                                icon: { Image(systemName: "clock") }
                            )
                            
                            Spacer()
                            
                            Text(formatDate(lastSyncDate))
                                .foregroundColor(Color("SecondaryColor"))
                        }
                    }
                }
                
                // Cache Section
                Section(header: Text("Cache"), footer: Text("Clearing the cache will remove all locally stored data. This will not affect your account or data stored on the server.")) {
                    Button(action: {
                        showingClearCacheAlert = true
                    }) {
                        HStack {
                            Label(
                                title: { Text("Clear Cache") },
                                icon: { Image(systemName: "trash") }
                            )
                            .foregroundColor(.red)
                            
                            Spacer()
                        }
                    }
                }
                
                // Pending Actions Section
                if offlineManager.hasPendingActions {
                    Section(header: Text("Pending Actions"), footer: Text("You can sync pending actions when you're back online.")) {
                        Button(action: {
                            offlineManager.syncPendingActions()
                        }) {
                            HStack {
                                Label(
                                    title: { Text("Sync Now") },
                                    icon: { Image(systemName: "arrow.up.arrow.down") }
                                )
                                .foregroundColor(offlineManager.isOffline ? Color("SecondaryColor") : Color("PrimaryColor"))
                                
                                Spacer()
                            }
                        }
                        .disabled(offlineManager.isOffline)
                        
                        Button(action: {
                            showingPendingActionsAlert = true
                        }) {
                            HStack {
                                Label(
                                    title: { Text("Clear Pending Actions") },
                                    icon: { Image(systemName: "xmark.circle") }
                                )
                                .foregroundColor(.red)
                                
                                Spacer()
                            }
                        }
                    }
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("Offline Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .alert(isPresented: $showingClearCacheAlert) {
                Alert(
                    title: Text("Clear Cache"),
                    message: Text("Are you sure you want to clear the cache? This will remove all locally stored data."),
                    primaryButton: .destructive(Text("Clear")) {
                        offlineManager.clearCache()
                    },
                    secondaryButton: .cancel()
                )
            }
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

struct OfflineSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        let offlineManager = OfflineManager()
        offlineManager.isOffline = true
        
        return OfflineSettingsView()
            .environmentObject(offlineManager)
    }
}
