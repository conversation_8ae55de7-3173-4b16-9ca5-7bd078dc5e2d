import SwiftUI

/// A custom animated tab bar with enhanced visual effects
struct CustomTabBar: View {
    @Binding var selectedTab: Int
    let tabs: [TabItem]
    
    @ObservedObject private var accessibilityService = AccessibilityService.shared
    @State private var tabFrames: [CGRect] = []
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(0..<tabs.count, id: \.self) { index in
                TabBarButton(
                    tab: tabs[index],
                    isSelected: selectedTab == index,
                    onTap: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            selectedTab = index
                        }
                        
                        // Haptic feedback
                        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                        impactFeedback.impactOccurred()
                        
                        // Accessibility announcement
                        accessibilityService.announce("Selected \(tabs[index].title)")
                        
                        // Analytics
                        AnalyticsService.shared.trackUserAction("tab_selection", target: tabs[index].title)
                    }
                )
                .frame(maxWidth: .infinity)
                .background(
                    GeometryReader { geometry in
                        Color.clear
                            .onAppear {
                                if tabFrames.count <= index {
                                    tabFrames.append(geometry.frame(in: .global))
                                } else {
                                    tabFrames[index] = geometry.frame(in: .global)
                                }
                            }
                    }
                )
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            ZStack {
                // Background blur effect
                if !accessibilityService.shouldReduceTransparency {
                    BlurView(style: .systemMaterial)
                } else {
                    Color("CardBackgroundColor")
                }
                
                // Selection indicator
                if !tabFrames.isEmpty && selectedTab < tabFrames.count {
                    SelectionIndicator()
                        .offset(x: indicatorOffset)
                        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: selectedTab)
                }
            }
        )
        .cornerRadius(accessibilityService.cornerRadius(25))
        .shadow(
            color: Color.black.opacity(accessibilityService.shouldReduceTransparency ? 0.1 : 0.15),
            radius: accessibilityService.shouldReduceTransparency ? 2 : 8,
            x: 0,
            y: 4
        )
        .padding(.horizontal, 20)
        .padding(.bottom, 34) // Safe area padding
    }
    
    private var indicatorOffset: CGFloat {
        guard !tabFrames.isEmpty, selectedTab < tabFrames.count else { return 0 }
        
        let tabWidth = UIScreen.main.bounds.width / CGFloat(tabs.count)
        let baseOffset = CGFloat(selectedTab) * tabWidth - (UIScreen.main.bounds.width / 2) + (tabWidth / 2)
        
        return baseOffset
    }
}

struct TabBarButton: View {
    let tab: TabItem
    let isSelected: Bool
    let onTap: () -> Void
    
    @ObservedObject private var accessibilityService = AccessibilityService.shared
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 4) {
                ZStack {
                    // Background circle for selected state
                    if isSelected {
                        Circle()
                            .fill(Color("PrimaryColor"))
                            .frame(width: 32, height: 32)
                            .scaleEffect(accessibilityService.isVoiceOverEnabled ? 1.2 : 1.0)
                    }
                    
                    // Icon
                    Image(systemName: isSelected ? tab.selectedIcon : tab.icon)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(isSelected ? .white : Color("SecondaryColor"))
                        .scaleEffect(isSelected ? 1.1 : 1.0)
                }
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
                
                // Title
                Text(tab.title)
                    .font(.caption2)
                    .fontWeight(isSelected ? .semibold : .medium)
                    .foregroundColor(isSelected ? Color("PrimaryColor") : Color("SecondaryColor"))
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel(tab.title)
        .accessibilityHint("Tab \(tab.title)")
        .accessibilityAddTraits(isSelected ? .isSelected : [])
    }
}

struct SelectionIndicator: View {
    @ObservedObject private var accessibilityService = AccessibilityService.shared
    
    var body: some View {
        RoundedRectangle(cornerRadius: 2)
            .fill(Color("PrimaryColor"))
            .frame(width: 30, height: 3)
            .offset(y: -25)
            .opacity(accessibilityService.shouldReduceMotion ? 0 : 1)
    }
}

/// Represents a tab item
struct TabItem {
    let title: String
    let icon: String
    let selectedIcon: String
    let badgeCount: Int?
    
    init(title: String, icon: String, selectedIcon: String? = nil, badgeCount: Int? = nil) {
        self.title = title
        self.icon = icon
        self.selectedIcon = selectedIcon ?? "\(icon).fill"
        self.badgeCount = badgeCount
    }
}

/// A blur view for iOS
struct BlurView: UIViewRepresentable {
    let style: UIBlurEffect.Style
    
    func makeUIView(context: Context) -> UIVisualEffectView {
        let view = UIVisualEffectView(effect: UIBlurEffect(style: style))
        return view
    }
    
    func updateUIView(_ uiView: UIVisualEffectView, context: Context) {
        uiView.effect = UIBlurEffect(style: style)
    }
}

/// A floating action button component
struct FloatingActionButton: View {
    let icon: String
    let action: () -> Void
    let backgroundColor: Color
    let foregroundColor: Color
    
    @ObservedObject private var accessibilityService = AccessibilityService.shared
    @State private var isPressed = false
    
    init(
        icon: String,
        backgroundColor: Color = Color("PrimaryColor"),
        foregroundColor: Color = .white,
        action: @escaping () -> Void
    ) {
        self.icon = icon
        self.backgroundColor = backgroundColor
        self.foregroundColor = foregroundColor
        self.action = action
    }
    
    var body: some View {
        Button(action: {
            // Haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
            
            action()
        }) {
            Image(systemName: icon)
                .font(.system(size: 24, weight: .semibold))
                .foregroundColor(foregroundColor)
                .frame(width: 56, height: 56)
                .background(backgroundColor)
                .clipShape(Circle())
                .shadow(
                    color: Color.black.opacity(accessibilityService.shouldReduceTransparency ? 0.2 : 0.3),
                    radius: accessibilityService.shouldReduceTransparency ? 4 : 8,
                    x: 0,
                    y: 4
                )
                .scaleEffect(isPressed ? 0.95 : 1.0)
                .animation(.spring(response: 0.2, dampingFraction: 0.6), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .accessibilityLabel("Floating action button")
        .accessibilityHint("Performs the primary action")
    }
}

/// A custom navigation bar with enhanced styling
struct CustomNavigationBar: View {
    let title: String
    let leftButton: (() -> AnyView)?
    let rightButton: (() -> AnyView)?
    
    @ObservedObject private var accessibilityService = AccessibilityService.shared
    
    init(
        title: String,
        leftButton: (() -> AnyView)? = nil,
        rightButton: (() -> AnyView)? = nil
    ) {
        self.title = title
        self.leftButton = leftButton
        self.rightButton = rightButton
    }
    
    var body: some View {
        HStack {
            // Left button
            if let leftButton = leftButton {
                leftButton()
            } else {
                Spacer()
                    .frame(width: 44, height: 44)
            }
            
            Spacer()
            
            // Title
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(Color("TextColor"))
                .lineLimit(1)
                .minimumScaleFactor(0.8)
            
            Spacer()
            
            // Right button
            if let rightButton = rightButton {
                rightButton()
            } else {
                Spacer()
                    .frame(width: 44, height: 44)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            ZStack {
                if !accessibilityService.shouldReduceTransparency {
                    BlurView(style: .systemMaterial)
                } else {
                    Color("CardBackgroundColor")
                }
            }
        )
        .overlay(
            Rectangle()
                .fill(Color("SecondaryColor").opacity(0.2))
                .frame(height: 0.5),
            alignment: .bottom
        )
    }
}

/// A custom segmented control with enhanced styling
struct CustomSegmentedControl: View {
    @Binding var selectedIndex: Int
    let segments: [String]
    
    @ObservedObject private var accessibilityService = AccessibilityService.shared
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(0..<segments.count, id: \.self) { index in
                Button(action: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        selectedIndex = index
                    }
                    
                    // Haptic feedback
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                }) {
                    Text(segments[index])
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(selectedIndex == index ? .white : Color("TextColor"))
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: accessibilityService.cornerRadius(8))
                                .fill(selectedIndex == index ? Color("PrimaryColor") : Color.clear)
                        )
                }
                .buttonStyle(PlainButtonStyle())
                .accessibilityLabel(segments[index])
                .accessibilityAddTraits(selectedIndex == index ? .isSelected : [])
            }
        }
        .padding(4)
        .background(
            RoundedRectangle(cornerRadius: accessibilityService.cornerRadius(12))
                .fill(Color("FieldBackgroundColor"))
        )
    }
}

struct CustomTabBar_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            Spacer()
            
            CustomTabBar(
                selectedTab: .constant(0),
                tabs: [
                    TabItem(title: "Dashboard", icon: "chart.bar"),
                    TabItem(title: "Orders", icon: "shippingbox"),
                    TabItem(title: "Inventory", icon: "cube.box"),
                    TabItem(title: "Vendors", icon: "building.2"),
                    TabItem(title: "Profile", icon: "person.circle")
                ]
            )
        }
        .background(Color("BackgroundColor"))
        .previewLayout(.sizeThatFits)
    }
}
