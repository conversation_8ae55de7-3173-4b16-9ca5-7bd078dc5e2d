import SwiftUI

struct PieChartView: View {
    let data: [PieChartData]
    let showLegend: Bool
    let showPercentages: Bool
    let valueFormatter: (Double) -> String
    
    init(
        data: [PieChartData],
        showLegend: Bool = true,
        showPercentages: Bool = true,
        valueFormatter: @escaping (Double) -> String = { value in
            return String(format: "%.1f", value)
        }
    ) {
        self.data = data
        self.showLegend = showLegend
        self.showPercentages = showPercentages
        self.valueFormatter = valueFormatter
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Chart
            PieChart(data: data)
                .frame(height: 200)
            
            // Legend
            if showLegend {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(data) { item in
                        HStack(spacing: 8) {
                            // Color indicator
                            Circle()
                                .fill(item.color)
                                .frame(width: 12, height: 12)
                            
                            // Label
                            Text(item.label)
                                .font(.caption)
                                .foregroundColor(Color("TextColor"))
                            
                            Spacer()
                            
                            // Value
                            if showPercentages {
                                Text("\(Int(item.percentage * 100))%")
                                    .font(.caption)
                                    .foregroundColor(Color("SecondaryColor"))
                            }
                            
                            Text(valueFormatter(item.value))
                                .font(.caption)
                                .foregroundColor(Color("SecondaryColor"))
                        }
                    }
                }
            }
        }
    }
}

struct PieChart: View {
    let data: [PieChartData]
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                ForEach(0..<data.count, id: \.self) { index in
                    PieSlice(
                        startAngle: startAngle(for: index),
                        endAngle: endAngle(for: index),
                        color: data[index].color
                    )
                }
                
                // Center circle (optional)
                Circle()
                    .fill(Color("CardBackgroundColor"))
                    .frame(width: geometry.size.width * 0.5, height: geometry.size.width * 0.5)
                
                // Total value
                VStack {
                    Text("Total")
                        .font(.caption)
                        .foregroundColor(Color("SecondaryColor"))
                    
                    Text(String(format: "%.2f", totalValue))
                        .font(.headline)
                        .foregroundColor(Color("TextColor"))
                }
            }
        }
        .aspectRatio(1, contentMode: .fit)
    }
    
    private var totalValue: Double {
        data.reduce(0) { $0 + $1.value }
    }
    
    private func startAngle(for index: Int) -> Double {
        let precedingValues = data.prefix(index).reduce(0) { $0 + $1.value }
        let precedingPercentage = precedingValues / totalValue
        return precedingPercentage * 360
    }
    
    private func endAngle(for index: Int) -> Double {
        let precedingAndCurrentValues = data.prefix(index + 1).reduce(0) { $0 + $1.value }
        let precedingAndCurrentPercentage = precedingAndCurrentValues / totalValue
        return precedingAndCurrentPercentage * 360
    }
}

struct PieSlice: View {
    let startAngle: Double
    let endAngle: Double
    let color: Color
    
    var body: some View {
        GeometryReader { geometry in
            Path { path in
                let center = CGPoint(x: geometry.size.width / 2, y: geometry.size.height / 2)
                let radius = min(geometry.size.width, geometry.size.height) / 2
                
                path.move(to: center)
                
                path.addArc(
                    center: center,
                    radius: radius,
                    startAngle: Angle(degrees: startAngle - 90),
                    endAngle: Angle(degrees: endAngle - 90),
                    clockwise: false
                )
                
                path.closeSubpath()
            }
            .fill(color)
        }
    }
}

struct PieChartData: Identifiable {
    let id = UUID()
    let label: String
    let value: Double
    let color: Color
    let percentage: Double
    
    init(label: String, value: Double, color: Color, total: Double? = nil) {
        self.label = label
        self.value = value
        self.color = color
        
        if let total = total, total > 0 {
            self.percentage = value / total
        } else {
            self.percentage = 0
        }
    }
}

struct PieChartView_Previews: PreviewProvider {
    static var previews: some View {
        let total = 350.0
        
        PieChartView(
            data: [
                PieChartData(label: "Category 1", value: 100, color: .blue, total: total),
                PieChartData(label: "Category 2", value: 150, color: .green, total: total),
                PieChartData(label: "Category 3", value: 80, color: .orange, total: total),
                PieChartData(label: "Category 4", value: 20, color: .red, total: total)
            ]
        )
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
