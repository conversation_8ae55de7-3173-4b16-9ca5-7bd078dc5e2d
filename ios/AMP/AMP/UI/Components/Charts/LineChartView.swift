import SwiftUI

struct LineChartView: View {
    let data: [LineChartData]
    let maxY: Double
    let minY: Double
    let lineColor: Color
    let showLabels: Bool
    let showAxis: Bool
    let showPoints: Bool
    let showArea: Bool
    let valueFormatter: (Double) -> String
    
    init(
        data: [LineChartData],
        maxY: Double? = nil,
        minY: Double? = nil,
        lineColor: Color = Color("PrimaryColor"),
        showLabels: Bool = true,
        showAxis: Bool = true,
        showPoints: Bool = true,
        showArea: Bool = true,
        valueFormatter: @escaping (Double) -> String = { value in
            return String(format: "%.1f", value)
        }
    ) {
        self.data = data
        self.maxY = maxY ?? (data.map { $0.value }.max() ?? 1.0)
        self.minY = minY ?? (data.map { $0.value }.min() ?? 0.0)
        self.lineColor = lineColor
        self.showLabels = showLabels
        self.showAxis = showAxis
        self.showPoints = showPoints
        self.showArea = showArea
        self.valueFormatter = valueFormatter
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Chart
            HStack(alignment: .bottom, spacing: 0) {
                if showAxis {
                    // Y-axis
                    VStack(alignment: .trailing, spacing: 0) {
                        Text(valueFormatter(maxY))
                            .font(.caption)
                            .foregroundColor(Color("SecondaryColor"))
                        
                        Spacer()
                        
                        if minY != maxY {
                            Text(valueFormatter(minY))
                                .font(.caption)
                                .foregroundColor(Color("SecondaryColor"))
                        }
                    }
                    .frame(width: 40)
                }
                
                // Line chart
                GeometryReader { geometry in
                    ZStack {
                        // Area
                        if showArea {
                            Path { path in
                                // Start at the bottom left
                                path.move(to: CGPoint(x: 0, y: geometry.size.height))
                                
                                // Draw line to the first point
                                path.addLine(to: CGPoint(
                                    x: 0,
                                    y: yPosition(for: data.first?.value ?? 0, in: geometry)
                                ))
                                
                                // Draw lines through all points
                                for i in 0..<data.count {
                                    let point = CGPoint(
                                        x: xPosition(for: i, in: geometry),
                                        y: yPosition(for: data[i].value, in: geometry)
                                    )
                                    path.addLine(to: point)
                                }
                                
                                // Draw line to the bottom right
                                path.addLine(to: CGPoint(x: geometry.size.width, y: geometry.size.height))
                                
                                // Close the path
                                path.closeSubpath()
                            }
                            .fill(lineColor.opacity(0.2))
                        }
                        
                        // Line
                        Path { path in
                            // Start at the first point
                            path.move(to: CGPoint(
                                x: 0,
                                y: yPosition(for: data.first?.value ?? 0, in: geometry)
                            ))
                            
                            // Draw lines through all points
                            for i in 0..<data.count {
                                let point = CGPoint(
                                    x: xPosition(for: i, in: geometry),
                                    y: yPosition(for: data[i].value, in: geometry)
                                )
                                path.addLine(to: point)
                            }
                        }
                        .stroke(lineColor, lineWidth: 2)
                        
                        // Points
                        if showPoints {
                            ForEach(0..<data.count, id: \.self) { i in
                                Circle()
                                    .fill(lineColor)
                                    .frame(width: 6, height: 6)
                                    .position(
                                        x: xPosition(for: i, in: geometry),
                                        y: yPosition(for: data[i].value, in: geometry)
                                    )
                            }
                        }
                    }
                }
            }
            .frame(height: 200)
            
            if showLabels {
                // X-axis labels
                HStack {
                    if showAxis {
                        // Spacer for Y-axis
                        Text("")
                            .frame(width: 40)
                    }
                    
                    // Labels
                    HStack(spacing: 0) {
                        ForEach(0..<data.count, id: \.self) { i in
                            Text(data[i].label)
                                .font(.caption)
                                .foregroundColor(Color("SecondaryColor"))
                                .frame(maxWidth: .infinity)
                                .lineLimit(1)
                        }
                    }
                }
            }
            
            if showAxis {
                // X-axis
                Rectangle()
                    .fill(Color("SecondaryColor").opacity(0.3))
                    .frame(height: 1)
            }
        }
    }
    
    private func xPosition(for index: Int, in geometry: GeometryProxy) -> CGFloat {
        let width = geometry.size.width
        let count = CGFloat(data.count - 1)
        
        if count == 0 {
            return width / 2
        }
        
        return width * (CGFloat(index) / count)
    }
    
    private func yPosition(for value: Double, in geometry: GeometryProxy) -> CGFloat {
        let height = geometry.size.height
        let range = maxY - minY
        
        if range == 0 {
            return height / 2
        }
        
        let normalizedValue = (value - minY) / range
        return height - (height * CGFloat(normalizedValue))
    }
}

struct LineChartData: Identifiable {
    let id = UUID()
    let label: String
    let value: Double
}

struct LineChartView_Previews: PreviewProvider {
    static var previews: some View {
        LineChartView(
            data: [
                LineChartData(label: "Jan", value: 100),
                LineChartData(label: "Feb", value: 150),
                LineChartData(label: "Mar", value: 80),
                LineChartData(label: "Apr", value: 200),
                LineChartData(label: "May", value: 120),
                LineChartData(label: "Jun", value: 180)
            ]
        )
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
