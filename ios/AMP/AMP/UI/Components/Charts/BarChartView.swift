import SwiftUI

struct BarChartView: View {
    let data: [BarChartData]
    let maxValue: Double
    let barColor: Color
    let showLabels: Bool
    let showValues: Bool
    let showAxis: Bool
    let barCornerRadius: CGFloat
    let spacing: CGFloat
    let valueFormatter: (Double) -> String
    
    init(
        data: [BarChartData],
        maxValue: Double? = nil,
        barColor: Color = Color("PrimaryColor"),
        showLabels: Bool = true,
        showValues: Bool = true,
        showAxis: Bool = true,
        barCornerRadius: CGFloat = 4,
        spacing: CGFloat = 8,
        valueFormatter: @escaping (Double) -> String = { value in
            return String(format: "%.1f", value)
        }
    ) {
        self.data = data
        self.maxValue = maxValue ?? (data.map { $0.value }.max() ?? 1.0)
        self.barColor = barColor
        self.showLabels = showLabels
        self.showValues = showValues
        self.showAxis = showAxis
        self.barCornerRadius = barCornerRadius
        self.spacing = spacing
        self.valueFormatter = valueFormatter
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Chart
            HStack(alignment: .bottom, spacing: spacing) {
                if showAxis {
                    // Y-axis
                    VStack(alignment: .trailing, spacing: 0) {
                        Text(valueFormatter(maxValue))
                            .font(.caption)
                            .foregroundColor(Color("SecondaryColor"))
                        
                        Spacer()
                        
                        Text("0")
                            .font(.caption)
                            .foregroundColor(Color("SecondaryColor"))
                    }
                    .frame(width: 40)
                }
                
                // Bars
                HStack(alignment: .bottom, spacing: spacing) {
                    ForEach(data) { item in
                        VStack(spacing: 4) {
                            // Value
                            if showValues {
                                Text(valueFormatter(item.value))
                                    .font(.caption)
                                    .foregroundColor(Color("SecondaryColor"))
                            }
                            
                            // Bar
                            RoundedRectangle(cornerRadius: barCornerRadius)
                                .fill(barColor)
                                .frame(height: calculateBarHeight(for: item.value))
                            
                            // Label
                            if showLabels {
                                Text(item.label)
                                    .font(.caption)
                                    .foregroundColor(Color("SecondaryColor"))
                                    .lineLimit(1)
                                    .fixedSize(horizontal: false, vertical: true)
                            }
                        }
                        .frame(maxWidth: .infinity)
                    }
                }
            }
            .frame(height: 200)
            
            if showAxis {
                // X-axis
                Rectangle()
                    .fill(Color("SecondaryColor").opacity(0.3))
                    .frame(height: 1)
            }
        }
    }
    
    private func calculateBarHeight(for value: Double) -> CGFloat {
        let percentage = value / maxValue
        return max(0, min(180, 180 * CGFloat(percentage)))
    }
}

struct BarChartData: Identifiable {
    let id = UUID()
    let label: String
    let value: Double
}

struct BarChartView_Previews: PreviewProvider {
    static var previews: some View {
        BarChartView(
            data: [
                BarChartData(label: "Jan", value: 100),
                BarChartData(label: "Feb", value: 150),
                BarChartData(label: "Mar", value: 80),
                BarChartData(label: "Apr", value: 200),
                BarChartData(label: "May", value: 120)
            ]
        )
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
