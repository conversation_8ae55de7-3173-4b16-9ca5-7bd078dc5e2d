import SwiftUI

/// A lazy loading list component that loads items as needed for better performance
struct LazyLoadingList<Item: Identifiable, Content: View>: View {
    let items: [Item]
    let loadMoreItems: (() -> Void)?
    let isLoading: Bool
    let hasMoreItems: Bool
    let content: (Item) -> Content
    
    @State private var visibleItems: Set<Item.ID> = []
    @ObservedObject private var performanceMonitor = PerformanceMonitor.shared
    
    private let loadThreshold = 5 // Load more when 5 items from the end
    
    init(
        items: [Item],
        isLoading: Bool = false,
        hasMoreItems: Bool = false,
        loadMoreItems: (() -> Void)? = nil,
        @ViewBuilder content: @escaping (Item) -> Content
    ) {
        self.items = items
        self.isLoading = isLoading
        self.hasMoreItems = hasMoreItems
        self.loadMoreItems = loadMoreItems
        self.content = content
    }
    
    var body: some View {
        LazyVStack(spacing: 0) {
            ForEach(Array(items.enumerated()), id: \.element.id) { index, item in
                LazyItemView(
                    item: item,
                    content: content,
                    onAppear: {
                        handleItemAppear(item: item, index: index)
                    },
                    onDisappear: {
                        handleItemDisappear(item: item)
                    }
                )
            }
            
            // Loading indicator
            if isLoading {
                LoadingIndicatorView()
                    .padding()
            }
            
            // Load more trigger
            if hasMoreItems && !isLoading {
                LoadMoreTriggerView {
                    loadMoreItems?()
                }
            }
        }
    }
    
    private func handleItemAppear(item: Item, index: Int) {
        visibleItems.insert(item.id)
        
        // Trigger load more when approaching the end
        if let loadMoreItems = loadMoreItems,
           hasMoreItems && !isLoading,
           index >= items.count - loadThreshold {
            
            performanceMonitor.trackPerformance(name: "LazyList_LoadMore") {
                loadMoreItems()
            }
        }
    }
    
    private func handleItemDisappear(item: Item) {
        visibleItems.remove(item.id)
    }
}

/// A wrapper view for lazy loading items
private struct LazyItemView<Item: Identifiable, Content: View>: View {
    let item: Item
    let content: (Item) -> Content
    let onAppear: () -> Void
    let onDisappear: () -> Void
    
    @State private var hasAppeared = false
    
    var body: some View {
        Group {
            if hasAppeared {
                content(item)
            } else {
                PlaceholderView()
                    .onAppear {
                        hasAppeared = true
                        onAppear()
                    }
            }
        }
        .onDisappear {
            onDisappear()
        }
    }
}

/// A placeholder view shown while items are loading
private struct PlaceholderView: View {
    var body: some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(Color("FieldBackgroundColor"))
            .frame(height: 80)
            .padding(.horizontal)
            .padding(.vertical, 4)
            .redacted(reason: .placeholder)
    }
}

/// A loading indicator view
private struct LoadingIndicatorView: View {
    var body: some View {
        HStack {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle())
            
            Text("Loading...")
                .font(.subheadline)
                .foregroundColor(Color("SecondaryColor"))
        }
    }
}

/// A trigger view for loading more items
private struct LoadMoreTriggerView: View {
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Spacer()
                
                Text("Load More")
                    .font(.subheadline)
                    .foregroundColor(Color("PrimaryColor"))
                
                Spacer()
            }
            .padding()
            .background(Color("CardBackgroundColor"))
            .cornerRadius(8)
            .padding(.horizontal)
        }
        .onAppear {
            // Automatically trigger load more when this view appears
            action()
        }
    }
}

/// A virtualized grid component for better performance with large datasets
struct VirtualizedGrid<Item: Identifiable, Content: View>: View {
    let items: [Item]
    let columns: Int
    let itemHeight: CGFloat
    let content: (Item) -> Content
    
    @State private var scrollOffset: CGFloat = 0
    @State private var visibleRange: Range<Int> = 0..<0
    
    private let bufferSize = 10 // Number of items to render outside visible area
    
    init(
        items: [Item],
        columns: Int = 2,
        itemHeight: CGFloat = 200,
        @ViewBuilder content: @escaping (Item) -> Content
    ) {
        self.items = items
        self.columns = columns
        self.itemHeight = itemHeight
        self.content = content
    }
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: columns), spacing: 8) {
                    ForEach(visibleItems, id: \.id) { item in
                        content(item)
                            .frame(height: itemHeight)
                    }
                }
                .padding()
                .background(
                    GeometryReader { scrollGeometry in
                        Color.clear
                            .preference(key: ScrollOffsetPreferenceKey.self, value: scrollGeometry.frame(in: .named("scroll")).minY)
                    }
                )
            }
            .coordinateSpace(name: "scroll")
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                scrollOffset = value
                updateVisibleRange(geometry: geometry)
            }
        }
        .onAppear {
            updateVisibleRange(geometry: nil)
        }
    }
    
    private var visibleItems: [Item] {
        let startIndex = max(0, visibleRange.lowerBound - bufferSize)
        let endIndex = min(items.count, visibleRange.upperBound + bufferSize)
        
        return Array(items[startIndex..<endIndex])
    }
    
    private func updateVisibleRange(geometry: GeometryProxy?) {
        guard let geometry = geometry else {
            visibleRange = 0..<min(items.count, 20) // Initial load
            return
        }
        
        let viewHeight = geometry.size.height
        let rowHeight = itemHeight + 8 // Including spacing
        let rowsPerScreen = Int(ceil(viewHeight / rowHeight))
        let itemsPerScreen = rowsPerScreen * columns
        
        let scrollPosition = -scrollOffset
        let firstVisibleRow = max(0, Int(scrollPosition / rowHeight))
        let firstVisibleItem = firstVisibleRow * columns
        
        let startIndex = max(0, firstVisibleItem)
        let endIndex = min(items.count, startIndex + itemsPerScreen * 2) // Load 2 screens worth
        
        visibleRange = startIndex..<endIndex
    }
}

/// Preference key for tracking scroll offset
private struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

/// A cached async image component for better performance
struct CachedAsyncImage<Content: View, Placeholder: View>: View {
    let url: URL?
    let content: (Image) -> Content
    let placeholder: () -> Placeholder
    
    @State private var image: UIImage?
    @State private var isLoading = false
    @ObservedObject private var imageCache = ImageCacheService.shared
    
    init(
        url: URL?,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> Placeholder
    ) {
        self.url = url
        self.content = content
        self.placeholder = placeholder
    }
    
    var body: some View {
        Group {
            if let image = image {
                content(Image(uiImage: image))
            } else {
                placeholder()
                    .onAppear {
                        loadImage()
                    }
            }
        }
    }
    
    private func loadImage() {
        guard let url = url, !isLoading else { return }
        
        isLoading = true
        
        imageCache.loadImage(from: url)
            .sink(
                receiveCompletion: { _ in
                    isLoading = false
                },
                receiveValue: { uiImage in
                    image = uiImage
                    isLoading = false
                }
            )
            .store(in: &imageCache.cancellables)
    }
}

// Extension to add cancellables to ImageCacheService
extension ImageCacheService {
    var cancellables: Set<AnyCancellable> {
        get {
            objc_getAssociatedObject(self, &cancellablesKey) as? Set<AnyCancellable> ?? Set<AnyCancellable>()
        }
        set {
            objc_setAssociatedObject(self, &cancellablesKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
}

private var cancellablesKey: UInt8 = 0

struct LazyLoadingList_Previews: PreviewProvider {
    static var previews: some View {
        LazyLoadingList(
            items: Array(0..<100).map { TestItem(id: $0, title: "Item \($0)") },
            isLoading: false,
            hasMoreItems: true,
            loadMoreItems: {
                print("Loading more items...")
            }
        ) { item in
            HStack {
                Text(item.title)
                    .font(.headline)
                
                Spacer()
            }
            .padding()
            .background(Color("CardBackgroundColor"))
            .cornerRadius(8)
            .padding(.horizontal)
        }
    }
}

private struct TestItem: Identifiable {
    let id: Int
    let title: String
}
