import SwiftUI

/// An accessible button component with enhanced accessibility features
struct AccessibleButton: View {
    let title: String
    let action: () -> Void
    let style: ButtonStyle
    let isEnabled: Bool
    let accessibilityLabel: String?
    let accessibilityHint: String?
    let accessibilityValue: String?
    
    @ObservedObject private var accessibilityService = AccessibilityService.shared
    
    enum ButtonStyle {
        case primary
        case secondary
        case destructive
        case ghost
        
        var backgroundColor: Color {
            switch self {
            case .primary:
                return Color("PrimaryColor")
            case .secondary:
                return Color("SecondaryColor")
            case .destructive:
                return Color.red
            case .ghost:
                return Color.clear
            }
        }
        
        var foregroundColor: Color {
            switch self {
            case .primary, .secondary, .destructive:
                return Color.white
            case .ghost:
                return Color("PrimaryColor")
            }
        }
        
        var borderColor: Color? {
            switch self {
            case .ghost:
                return Color("PrimaryColor")
            default:
                return nil
            }
        }
    }
    
    init(
        title: String,
        style: ButtonStyle = .primary,
        isEnabled: Bool = true,
        accessibilityLabel: String? = nil,
        accessibilityHint: String? = nil,
        accessibilityValue: String? = nil,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.style = style
        self.isEnabled = isEnabled
        self.accessibilityLabel = accessibilityLabel
        self.accessibilityHint = accessibilityHint
        self.accessibilityValue = accessibilityValue
        self.action = action
    }
    
    var body: some View {
        Button(action: {
            // Provide haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
            
            action()
        }) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(isEnabled ? style.foregroundColor : Color("SecondaryColor"))
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: accessibilityService.cornerRadius(8))
                        .fill(isEnabled ? style.backgroundColor : Color("FieldBackgroundColor"))
                        .opacity(accessibilityService.opacity(isEnabled ? 1.0 : 0.6))
                )
                .overlay(
                    RoundedRectangle(cornerRadius: accessibilityService.cornerRadius(8))
                        .stroke(style.borderColor ?? Color.clear, lineWidth: accessibilityService.isButtonShapesEnabled ? 2 : 1)
                )
                .scaleEffect(accessibilityService.isVoiceOverEnabled ? 1.0 : 1.0)
        }
        .disabled(!isEnabled)
        .accessibilityLabel(accessibilityLabel ?? title)
        .accessibilityHint(accessibilityHint ?? "")
        .accessibilityValue(accessibilityValue ?? "")
        .accessibilityAddTraits(isEnabled ? .none : .notEnabled)
        .accessibilityRemoveTraits(.isImage)
        .animation(.easeInOut(duration: accessibilityService.animationDuration(0.2)), value: isEnabled)
    }
}

/// An accessible toggle component
struct AccessibleToggle: View {
    @Binding var isOn: Bool
    let title: String
    let accessibilityLabel: String?
    let accessibilityHint: String?
    
    @ObservedObject private var accessibilityService = AccessibilityService.shared
    
    init(
        title: String,
        isOn: Binding<Bool>,
        accessibilityLabel: String? = nil,
        accessibilityHint: String? = nil
    ) {
        self.title = title
        self._isOn = isOn
        self.accessibilityLabel = accessibilityLabel
        self.accessibilityHint = accessibilityHint
    }
    
    var body: some View {
        HStack {
            Text(title)
                .font(.body)
                .foregroundColor(Color("TextColor"))
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .toggleStyle(SwitchToggleStyle(tint: Color("PrimaryColor")))
                .accessibilityLabel(accessibilityLabel ?? title)
                .accessibilityHint(accessibilityHint ?? "")
                .accessibilityValue(isOn ? "On" : "Off")
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            // Allow tapping the entire row to toggle
            isOn.toggle()
            
            // Provide haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
            
            // Announce the change for VoiceOver users
            accessibilityService.announce("\(title) \(isOn ? "enabled" : "disabled")")
        }
    }
}

/// An accessible text field component
struct AccessibleTextField: View {
    @Binding var text: String
    let placeholder: String
    let isSecure: Bool
    let keyboardType: UIKeyboardType
    let accessibilityLabel: String?
    let accessibilityHint: String?
    
    @ObservedObject private var accessibilityService = AccessibilityService.shared
    @State private var isSecureTextVisible = false
    
    init(
        text: Binding<String>,
        placeholder: String,
        isSecure: Bool = false,
        keyboardType: UIKeyboardType = .default,
        accessibilityLabel: String? = nil,
        accessibilityHint: String? = nil
    ) {
        self._text = text
        self.placeholder = placeholder
        self.isSecure = isSecure
        self.keyboardType = keyboardType
        self.accessibilityLabel = accessibilityLabel
        self.accessibilityHint = accessibilityHint
    }
    
    var body: some View {
        HStack {
            Group {
                if isSecure && !isSecureTextVisible {
                    SecureField(placeholder, text: $text)
                } else {
                    TextField(placeholder, text: $text)
                }
            }
            .keyboardType(keyboardType)
            .textFieldStyle(PlainTextFieldStyle())
            .accessibilityLabel(accessibilityLabel ?? placeholder)
            .accessibilityHint(accessibilityHint ?? "")
            
            if isSecure {
                Button(action: {
                    isSecureTextVisible.toggle()
                    
                    // Announce the change for VoiceOver users
                    accessibilityService.announce(isSecureTextVisible ? "Password visible" : "Password hidden")
                }) {
                    Image(systemName: isSecureTextVisible ? "eye.slash" : "eye")
                        .foregroundColor(Color("SecondaryColor"))
                }
                .accessibilityLabel(isSecureTextVisible ? "Hide password" : "Show password")
                .accessibilityHint("Toggles password visibility")
            }
        }
        .padding()
        .background(Color("FieldBackgroundColor"))
        .cornerRadius(accessibilityService.cornerRadius(8))
        .overlay(
            RoundedRectangle(cornerRadius: accessibilityService.cornerRadius(8))
                .stroke(Color("SecondaryColor").opacity(0.3), lineWidth: 1)
        )
    }
}

/// An accessible card component
struct AccessibleCard<Content: View>: View {
    let content: Content
    let accessibilityLabel: String?
    let accessibilityHint: String?
    let onTap: (() -> Void)?
    
    @ObservedObject private var accessibilityService = AccessibilityService.shared
    
    init(
        accessibilityLabel: String? = nil,
        accessibilityHint: String? = nil,
        onTap: (() -> Void)? = nil,
        @ViewBuilder content: () -> Content
    ) {
        self.content = content()
        self.accessibilityLabel = accessibilityLabel
        self.accessibilityHint = accessibilityHint
        self.onTap = onTap
    }
    
    var body: some View {
        VStack {
            content
        }
        .padding()
        .background(Color("CardBackgroundColor"))
        .cornerRadius(accessibilityService.cornerRadius(12))
        .shadow(
            color: Color.black.opacity(accessibilityService.shouldReduceTransparency ? 0.1 : 0.05),
            radius: accessibilityService.shouldReduceTransparency ? 2 : 4,
            x: 0,
            y: 2
        )
        .contentShape(Rectangle())
        .onTapGesture {
            if let onTap = onTap {
                // Provide haptic feedback
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
                
                onTap()
            }
        }
        .accessibilityElement(children: onTap != nil ? .ignore : .contain)
        .accessibilityLabel(accessibilityLabel ?? "")
        .accessibilityHint(accessibilityHint ?? "")
        .accessibilityAddTraits(onTap != nil ? .isButton : [])
    }
}

struct AccessibleButton_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            AccessibleButton(
                title: "Primary Button",
                style: .primary,
                accessibilityHint: "Performs the primary action"
            ) {
                print("Primary button tapped")
            }
            
            AccessibleButton(
                title: "Secondary Button",
                style: .secondary,
                accessibilityHint: "Performs a secondary action"
            ) {
                print("Secondary button tapped")
            }
            
            AccessibleButton(
                title: "Destructive Button",
                style: .destructive,
                accessibilityHint: "Performs a destructive action"
            ) {
                print("Destructive button tapped")
            }
            
            AccessibleButton(
                title: "Ghost Button",
                style: .ghost,
                accessibilityHint: "Performs an alternative action"
            ) {
                print("Ghost button tapped")
            }
            
            AccessibleToggle(
                title: "Enable Notifications",
                isOn: .constant(true),
                accessibilityHint: "Toggles notification settings"
            )
            
            AccessibleTextField(
                text: .constant(""),
                placeholder: "Enter your email",
                keyboardType: .emailAddress,
                accessibilityHint: "Enter your email address"
            )
            
            AccessibleTextField(
                text: .constant(""),
                placeholder: "Enter your password",
                isSecure: true,
                accessibilityHint: "Enter your password"
            )
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
