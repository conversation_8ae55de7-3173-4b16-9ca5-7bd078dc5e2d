import SwiftUI

/// A reusable component for displaying a detail row with a title and value
struct DetailRow: View {
    let title: String
    let value: String
    let titleWidth: CGFloat
    
    init(title: String, value: String, titleWidth: CGFloat = 120) {
        self.title = title
        self.value = value
        self.titleWidth = titleWidth
    }
    
    var body: some View {
        HStack(alignment: .top) {
            Text(title)
                .font(.subheadline)
                .foregroundColor(Color("SecondaryColor"))
                .frame(width: titleWidth, alignment: .leading)
            
            Text(value)
                .font(.subheadline)
                .foregroundColor(Color("TextColor"))
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.vertical, 4)
    }
}

struct DetailRow_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            DetailRow(title: "Name", value: "John Doe")
            DetailRow(title: "Email", value: "<EMAIL>")
            DetailRow(title: "Phone", value: "+****************")
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
