import SwiftUI

struct OfflineIndicatorView: View {
    @EnvironmentObject var offlineManager: OfflineManager
    @State private var showingPendingActions = false
    
    var body: some View {
        VStack(spacing: 0) {
            if offlineManager.isOffline {
                // Offline Banner
                HStack {
                    Image(systemName: "wifi.slash")
                        .foregroundColor(.white)
                    
                    Text("You are offline")
                        .font(.subheadline)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    <PERSON><PERSON>(action: {
                        // Try to reconnect
                    }) {
                        Text("Retry")
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.white.opacity(0.2))
                            .cornerRadius(4)
                            .foregroundColor(.white)
                    }
                }
                .padding(.horizontal)
                .padding(.vertical, 8)
                .background(Color.red)
                .transition(.move(edge: .top))
            }
            
            if !offlineManager.isOffline && offlineManager.hasPendingActions {
                // Pending Actions Banner
                HStack {
                    Image(systemName: "arrow.triangle.2.circlepath")
                        .foregroundColor(.white)
                    
                    Text("\(offlineManager.pendingActionCount) pending actions")
                        .font(.subheadline)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    <PERSON><PERSON>(action: {
                        showingPendingActions = true
                    }) {
                        Text("View")
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.white.opacity(0.2))
                            .cornerRadius(4)
                            .foregroundColor(.white)
                    }
                }
                .padding(.horizontal)
                .padding(.vertical, 8)
                .background(Color.orange)
                .transition(.move(edge: .top))
            }
            
            Spacer()
        }
        .animation(.easeInOut, value: offlineManager.isOffline)
        .animation(.easeInOut, value: offlineManager.hasPendingActions)
        .sheet(isPresented: $showingPendingActions) {
            PendingActionsView()
        }
    }
}

struct PendingActionsView: View {
    @EnvironmentObject var offlineManager: OfflineManager
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            List {
                ForEach(offlineManager.pendingActions) { action in
                    VStack(alignment: .leading, spacing: 8) {
                        // Action Type
                        HStack {
                            Image(systemName: actionIcon(action.type))
                                .foregroundColor(actionColor(action.type))
                            
                            Text(action.type.rawValue.capitalized)
                                .font(.headline)
                                .foregroundColor(Color("TextColor"))
                        }
                        
                        // Endpoint
                        Text(action.endpoint)
                            .font(.subheadline)
                            .foregroundColor(Color("SecondaryColor"))
                        
                        // Created At
                        Text(formatDate(action.createdAt))
                            .font(.caption)
                            .foregroundColor(Color("SecondaryColor"))
                    }
                    .padding(.vertical, 8)
                }
                .onDelete(perform: deletePendingActions)
            }
            .listStyle(PlainListStyle())
            .navigationTitle("Pending Actions")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        offlineManager.syncPendingActions()
                    }) {
                        Text("Sync All")
                    }
                    .disabled(offlineManager.isOffline || offlineManager.pendingActions.isEmpty)
                }
            }
        }
    }
    
    private func deletePendingActions(at offsets: IndexSet) {
        for index in offsets {
            let action = offlineManager.pendingActions[index]
            offlineManager.removePendingAction(id: action.id)
        }
    }
    
    private func actionIcon(_ type: PendingAction.ActionType) -> String {
        switch type {
        case .create:
            return "plus.circle"
        case .update:
            return "pencil.circle"
        case .delete:
            return "minus.circle"
        }
    }
    
    private func actionColor(_ type: PendingAction.ActionType) -> Color {
        switch type {
        case .create:
            return Color("SuccessColor")
        case .update:
            return Color("WarningColor")
        case .delete:
            return Color("ErrorColor")
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

struct OfflineIndicatorView_Previews: PreviewProvider {
    static var previews: some View {
        let offlineManager = OfflineManager()
        offlineManager.isOffline = true
        
        return OfflineIndicatorView()
            .environmentObject(offlineManager)
    }
}
