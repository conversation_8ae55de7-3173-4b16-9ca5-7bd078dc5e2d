import Foundation
import SwiftUI

/// Service for managing app theme
class ThemeService: ObservableObject {
    @Published var currentTheme: AppTheme
    
    init() {
        // Load theme from UserDefaults
        if let themeName = UserDefaults.standard.string(forKey: Config.UserDefaultsKeys.appTheme),
           let theme = AppTheme(rawValue: themeName) {
            self.currentTheme = theme
        } else {
            // Default to system theme
            self.currentTheme = .system
        }
    }
    
    /// Sets the app theme
    func setTheme(_ theme: AppTheme) {
        self.currentTheme = theme
        
        // Save theme to UserDefaults
        UserDefaults.standard.set(theme.rawValue, forKey: Config.UserDefaultsKeys.appTheme)
    }
}

/// App theme options
enum AppTheme: String, CaseIterable, Identifiable {
    case light = "Light"
    case dark = "Dark"
    case system = "System"
    
    var id: String { self.rawValue }
    
    /// Returns the color scheme for the theme
    var colorScheme: ColorScheme? {
        switch self {
        case .light:
            return .light
        case .dark:
            return .dark
        case .system:
            return nil
        }
    }
    
    /// Returns the icon for the theme
    var icon: String {
        switch self {
        case .light:
            return "sun.max.fill"
        case .dark:
            return "moon.fill"
        case .system:
            return "gear"
        }
    }
}
