import SwiftUI

struct ThemeSettingsView: View {
    @EnvironmentObject var themeService: ThemeService
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("Appearance")) {
                    ForEach(AppTheme.allCases) { theme in
                        ThemeRow(theme: theme, isSelected: themeService.currentTheme == theme)
                            .onTapGesture {
                                themeService.setTheme(theme)
                            }
                    }
                }
                
                Section(header: Text("Preview"), footer: Text("Changes are applied immediately")) {
                    VStack(spacing: 16) {
                        // Light Mode Preview
                        ThemePreviewCard(title: "Light Mode", colorScheme: .light)
                        
                        // Dark Mode Preview
                        ThemePreviewCard(title: "Dark Mode", colorScheme: .dark)
                    }
                    .padding(.vertical, 8)
                }
            }
            .navigationTitle("Theme Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

struct ThemeRow: View {
    let theme: AppTheme
    let isSelected: Bool
    
    var body: some View {
        HStack {
            Image(systemName: theme.icon)
                .foregroundColor(Color("PrimaryColor"))
                .font(.title3)
                .frame(width: 30)
            
            Text(theme.rawValue)
                .font(.body)
                .foregroundColor(Color("TextColor"))
            
            Spacer()
            
            if isSelected {
                Image(systemName: "checkmark")
                    .foregroundColor(Color("PrimaryColor"))
            }
        }
        .padding(.vertical, 8)
    }
}

struct ThemePreviewCard: View {
    let title: String
    let colorScheme: ColorScheme
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.headline)
                .foregroundColor(Color("TextColor"))
            
            HStack(spacing: 12) {
                // Primary Color
                ColorCircle(color: Color("PrimaryColor"), label: "Primary")
                
                // Secondary Color
                ColorCircle(color: Color("SecondaryColor"), label: "Secondary")
                
                // Success Color
                ColorCircle(color: Color("SuccessColor"), label: "Success")
                
                // Error Color
                ColorCircle(color: Color("ErrorColor"), label: "Error")
            }
            
            // Sample UI Elements
            HStack(spacing: 16) {
                // Button
                Button(action: {}) {
                    Text("Button")
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color("PrimaryColor"))
                        .foregroundColor(.white)
                        .cornerRadius(8)
                }
                
                // Toggle
                Toggle("Toggle", isOn: .constant(true))
                    .toggleStyle(SwitchToggleStyle(tint: Color("PrimaryColor")))
                    .labelsHidden()
            }
            .padding(.top, 8)
        }
        .padding()
        .background(Color("CardBackgroundColor"))
        .cornerRadius(12)
        .preferredColorScheme(colorScheme)
    }
}

struct ColorCircle: View {
    let color: Color
    let label: String
    
    var body: some View {
        VStack {
            Circle()
                .fill(color)
                .frame(width: 30, height: 30)
            
            Text(label)
                .font(.caption)
                .foregroundColor(Color("SecondaryColor"))
        }
    }
}

struct ThemeSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        ThemeSettingsView()
            .environmentObject(ThemeService())
    }
}
