import Foundation
import UIKit
import Combine

/// Service for managing accessibility features
class AccessibilityService: ObservableObject {
    static let shared = AccessibilityService()
    
    @Published var isVoiceOverEnabled = false
    @Published var isSwitchControlEnabled = false
    @Published var isReduceMotionEnabled = false
    @Published var isReduceTransparencyEnabled = false
    @Published var isInvertColorsEnabled = false
    @Published var isDarkerSystemColorsEnabled = false
    @Published var preferredContentSizeCategory: UIContentSizeCategory = .medium
    @Published var isBoldTextEnabled = false
    @Published var isButtonShapesEnabled = false
    @Published var isOnOffLabelsEnabled = false
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupAccessibilityObservers()
        updateAccessibilityStatus()
    }
    
    // MARK: - Setup
    
    private func setupAccessibilityObservers() {
        // VoiceOver
        NotificationCenter.default.publisher(for: UIAccessibility.voiceOverStatusDidChangeNotification)
            .sink { [weak self] _ in
                self?.updateVoiceOverStatus()
            }
            .store(in: &cancellables)
        
        // Switch Control
        NotificationCenter.default.publisher(for: UIAccessibility.switchControlStatusDidChangeNotification)
            .sink { [weak self] _ in
                self?.updateSwitchControlStatus()
            }
            .store(in: &cancellables)
        
        // Reduce Motion
        NotificationCenter.default.publisher(for: UIAccessibility.reduceMotionStatusDidChangeNotification)
            .sink { [weak self] _ in
                self?.updateReduceMotionStatus()
            }
            .store(in: &cancellables)
        
        // Reduce Transparency
        NotificationCenter.default.publisher(for: UIAccessibility.reduceTransparencyStatusDidChangeNotification)
            .sink { [weak self] _ in
                self?.updateReduceTransparencyStatus()
            }
            .store(in: &cancellables)
        
        // Invert Colors
        NotificationCenter.default.publisher(for: UIAccessibility.invertColorsStatusDidChangeNotification)
            .sink { [weak self] _ in
                self?.updateInvertColorsStatus()
            }
            .store(in: &cancellables)
        
        // Darker System Colors
        NotificationCenter.default.publisher(for: UIAccessibility.darkerSystemColorsStatusDidChangeNotification)
            .sink { [weak self] _ in
                self?.updateDarkerSystemColorsStatus()
            }
            .store(in: &cancellables)
        
        // Content Size Category
        NotificationCenter.default.publisher(for: UIContentSizeCategory.didChangeNotification)
            .sink { [weak self] _ in
                self?.updateContentSizeCategory()
            }
            .store(in: &cancellables)
        
        // Bold Text
        NotificationCenter.default.publisher(for: UIAccessibility.boldTextStatusDidChangeNotification)
            .sink { [weak self] _ in
                self?.updateBoldTextStatus()
            }
            .store(in: &cancellables)
        
        // Button Shapes
        NotificationCenter.default.publisher(for: UIAccessibility.buttonShapesEnabledStatusDidChangeNotification)
            .sink { [weak self] _ in
                self?.updateButtonShapesStatus()
            }
            .store(in: &cancellables)
        
        // On/Off Labels
        NotificationCenter.default.publisher(for: UIAccessibility.onOffSwitchLabelsDidChangeNotification)
            .sink { [weak self] _ in
                self?.updateOnOffLabelsStatus()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Status Updates
    
    private func updateAccessibilityStatus() {
        updateVoiceOverStatus()
        updateSwitchControlStatus()
        updateReduceMotionStatus()
        updateReduceTransparencyStatus()
        updateInvertColorsStatus()
        updateDarkerSystemColorsStatus()
        updateContentSizeCategory()
        updateBoldTextStatus()
        updateButtonShapesStatus()
        updateOnOffLabelsStatus()
    }
    
    private func updateVoiceOverStatus() {
        DispatchQueue.main.async {
            self.isVoiceOverEnabled = UIAccessibility.isVoiceOverRunning
        }
    }
    
    private func updateSwitchControlStatus() {
        DispatchQueue.main.async {
            self.isSwitchControlEnabled = UIAccessibility.isSwitchControlRunning
        }
    }
    
    private func updateReduceMotionStatus() {
        DispatchQueue.main.async {
            self.isReduceMotionEnabled = UIAccessibility.isReduceMotionEnabled
        }
    }
    
    private func updateReduceTransparencyStatus() {
        DispatchQueue.main.async {
            self.isReduceTransparencyEnabled = UIAccessibility.isReduceTransparencyEnabled
        }
    }
    
    private func updateInvertColorsStatus() {
        DispatchQueue.main.async {
            self.isInvertColorsEnabled = UIAccessibility.isInvertColorsEnabled
        }
    }
    
    private func updateDarkerSystemColorsStatus() {
        DispatchQueue.main.async {
            self.isDarkerSystemColorsEnabled = UIAccessibility.isDarkerSystemColorsEnabled
        }
    }
    
    private func updateContentSizeCategory() {
        DispatchQueue.main.async {
            self.preferredContentSizeCategory = UIApplication.shared.preferredContentSizeCategory
        }
    }
    
    private func updateBoldTextStatus() {
        DispatchQueue.main.async {
            self.isBoldTextEnabled = UIAccessibility.isBoldTextEnabled
        }
    }
    
    private func updateButtonShapesStatus() {
        DispatchQueue.main.async {
            self.isButtonShapesEnabled = UIAccessibility.isButtonShapesEnabled
        }
    }
    
    private func updateOnOffLabelsStatus() {
        DispatchQueue.main.async {
            self.isOnOffLabelsEnabled = UIAccessibility.isOnOffSwitchLabelsEnabled
        }
    }
    
    // MARK: - Accessibility Helpers
    
    /// Returns whether any assistive technology is running
    var isAssistiveTechnologyRunning: Bool {
        return isVoiceOverEnabled || isSwitchControlEnabled
    }
    
    /// Returns whether the user prefers larger text
    var prefersLargerText: Bool {
        return preferredContentSizeCategory.isAccessibilityCategory
    }
    
    /// Returns whether animations should be reduced
    var shouldReduceAnimations: Bool {
        return isReduceMotionEnabled
    }
    
    /// Returns whether transparency should be reduced
    var shouldReduceTransparency: Bool {
        return isReduceTransparencyEnabled
    }
    
    /// Returns the appropriate animation duration based on accessibility settings
    func animationDuration(_ defaultDuration: TimeInterval) -> TimeInterval {
        return shouldReduceAnimations ? 0.0 : defaultDuration
    }
    
    /// Returns the appropriate corner radius based on accessibility settings
    func cornerRadius(_ defaultRadius: CGFloat) -> CGFloat {
        return isButtonShapesEnabled ? max(defaultRadius, 8.0) : defaultRadius
    }
    
    /// Returns the appropriate opacity based on accessibility settings
    func opacity(_ defaultOpacity: Double) -> Double {
        return shouldReduceTransparency ? 1.0 : defaultOpacity
    }
    
    // MARK: - Accessibility Announcements
    
    /// Posts an accessibility announcement
    /// - Parameters:
    ///   - message: The message to announce
    ///   - priority: The priority of the announcement
    func announce(_ message: String, priority: UIAccessibility.AnnouncementPriority = .medium) {
        guard isVoiceOverEnabled else { return }
        
        DispatchQueue.main.async {
            UIAccessibility.post(notification: .announcement, argument: message)
        }
    }
    
    /// Posts a layout change notification
    /// - Parameter element: The element that should receive focus (optional)
    func announceLayoutChange(focusElement element: Any? = nil) {
        guard isVoiceOverEnabled else { return }
        
        DispatchQueue.main.async {
            UIAccessibility.post(notification: .layoutChanged, argument: element)
        }
    }
    
    /// Posts a screen change notification
    /// - Parameter element: The element that should receive focus (optional)
    func announceScreenChange(focusElement element: Any? = nil) {
        guard isVoiceOverEnabled else { return }
        
        DispatchQueue.main.async {
            UIAccessibility.post(notification: .screenChanged, argument: element)
        }
    }
    
    // MARK: - Dynamic Type Support
    
    /// Returns a scaled font for the given text style
    /// - Parameters:
    ///   - textStyle: The text style to scale
    ///   - maximumSize: The maximum size for the font (optional)
    /// - Returns: A scaled font
    func scaledFont(for textStyle: UIFont.TextStyle, maximumSize: CGFloat? = nil) -> UIFont {
        let font = UIFont.preferredFont(forTextStyle: textStyle)
        
        if let maximumSize = maximumSize {
            let fontMetrics = UIFontMetrics(forTextStyle: textStyle)
            return fontMetrics.scaledFont(for: font, maximumPointSize: maximumSize)
        }
        
        return font
    }
    
    /// Returns whether the current content size category is an accessibility size
    var isAccessibilitySize: Bool {
        return preferredContentSizeCategory.isAccessibilityCategory
    }
}
