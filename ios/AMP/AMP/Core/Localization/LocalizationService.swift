import Foundation
import Combine
import SwiftUI


/// Service for managing app localization
class LocalizationService: ObservableObject {
    static let shared = LocalizationService()
    
    @Published var currentLanguage: Language = .english
    @Published var isRTL: Bool = false
    
    private let userDefaults = UserDefaults.standard
    private let languageKey = "selectedLanguage"
    
    private init() {
        loadSavedLanguage()
        updateRTLStatus()
    }
    
    // MARK: - Language Management
    
    /// Sets the current language
    /// - Parameter language: The language to set
    func setLanguage(_ language: Language) {
        currentLanguage = language
        isRTL = language.isRTL
        
        // Save to UserDefaults
        userDefaults.set(language.code, forKey: languageKey)
        
        // Update app language
        UserDefaults.standard.set([language.code], forKey: "AppleLanguages")
        UserDefaults.standard.synchronize()
        
        // Post notification for UI updates
        NotificationCenter.default.post(name: .languageDidChange, object: language)
    }
    
    /// Loads the saved language from UserDefaults
    private func loadSavedLanguage() {
        if let savedLanguageCode = userDefaults.string(forKey: languageKey),
           let language = Language.allCases.first(where: { $0.code == savedLanguageCode }) {
            currentLanguage = language
        } else {
            // Use system language if available, otherwise default to English
            
            let systemLanguageCode = Locale.current.languageCode ?? "en"
            currentLanguage = Language.allCases.first(where: { $0.code == systemLanguageCode }) ?? .english
        }
    }
    
    /// Updates the RTL status based on current language
    private func updateRTLStatus() {
        isRTL = currentLanguage.isRTL
    }
    
    // MARK: - Localized Strings
    
    /// Returns a localized string for the given key
    /// - Parameters:
    ///   - key: The localization key
    ///   - comment: Optional comment for translators
    /// - Returns: The localized string
    func localizedString(for key: String, comment: String = "") -> String {
        guard let path = Bundle.main.path(forResource: currentLanguage.code, ofType: "lproj"),
              let bundle = Bundle(path: path) else {
            // Fallback to main bundle if language bundle not found
            return NSLocalizedString(key, comment: comment)
        }
        
        return NSLocalizedString(key, bundle: bundle, comment: comment)
    }
    
    /// Returns a localized string with format arguments
    /// - Parameters:
    ///   - key: The localization key
    ///   - arguments: Format arguments
    /// - Returns: The formatted localized string
    func localizedString(for key: String, arguments: CVarArg...) -> String {
        let format = localizedString(for: key)
        return String(format: format, arguments: arguments)
    }
    
    // MARK: - Number and Date Formatting
    
    /// Returns a number formatter for the current locale
    var numberFormatter: NumberFormatter {
        let formatter = NumberFormatter()
        formatter.locale = currentLanguage.locale
        return formatter
    }
    
    /// Returns a currency formatter for the current locale
    var currencyFormatter: NumberFormatter {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.locale = currentLanguage.locale
        return formatter
    }
    
    /// Returns a date formatter for the current locale
    var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.locale = currentLanguage.locale
        return formatter
    }
    
    /// Returns a relative date formatter for the current locale
    var relativeDateFormatter: RelativeDateTimeFormatter {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = currentLanguage.locale
        return formatter
    }
    
    // MARK: - Utility Methods
    
    /// Returns the display name of a language in the current language
    /// - Parameter language: The language to get the display name for
    /// - Returns: The localized display name
    func displayName(for language: Language) -> String {
        return currentLanguage.locale.localizedString(forLanguageCode: language.code) ?? language.nativeName
    }
    
    /// Returns whether the current language uses RTL layout
    var isRightToLeft: Bool {
        return isRTL
    }
    
    /// Returns the text alignment for the current language
    var textAlignment: TextAlignment {
        #if canImport(UIKit)
        return isRTL ? .trailing : .leading
        #elseif canImport(AppKit)
        return isRTL ? .trailing : .leading
        #else
        return isRTL ? .trailing : .leading
        #endif
    }
    
    // For backward compatibility with UIKit/AppKit code
    #if canImport(UIKit)
    var uiTextAlignment: NSTextAlignment {
        return isRTL ? .right : .left
    }
    #elseif canImport(AppKit)
    var nsTextAlignment: NSTextAlignment {
        return isRTL ? .right : .left
    }
    #endif
}

/// Supported languages
enum Language: String, CaseIterable, Identifiable {
    case english = "English"
    case spanish = "Español"
    case french = "Français"
    case german = "Deutsch"
    case italian = "Italiano"
    case portuguese = "Português"
    case russian = "Русский"
    case chinese = "中文"
    case japanese = "日本語"
    case korean = "한국어"
    case arabic = "العربية"
    case hebrew = "עברית"
    
    var id: String { rawValue }
    
    /// Language code (ISO 639-1)
    var code: String {
        switch self {
        case .english: return "en"
        case .spanish: return "es"
        case .french: return "fr"
        case .german: return "de"
        case .italian: return "it"
        case .portuguese: return "pt"
        case .russian: return "ru"
        case .chinese: return "zh"
        case .japanese: return "ja"
        case .korean: return "ko"
        case .arabic: return "ar"
        case .hebrew: return "he"
        }
    }
    
    /// Native name of the language
    var nativeName: String {
        return rawValue
    }
    
    /// Whether the language uses right-to-left layout
    var isRTL: Bool {
        switch self {
        case .arabic, .hebrew:
            return true
        default:
            return false
        }
    }
    
    /// Locale for the language
    var locale: Locale {
        return Locale(identifier: code)
    }
    
    /// Flag emoji for the language
    var flag: String {
        switch self {
        case .english: return "🇺🇸"
        case .spanish: return "🇪🇸"
        case .french: return "🇫🇷"
        case .german: return "🇩🇪"
        case .italian: return "🇮🇹"
        case .portuguese: return "🇵🇹"
        case .russian: return "🇷🇺"
        case .chinese: return "🇨🇳"
        case .japanese: return "🇯🇵"
        case .korean: return "🇰🇷"
        case .arabic: return "🇸🇦"
        case .hebrew: return "🇮🇱"
        }
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let languageDidChange = Notification.Name("languageDidChange")
}

// MARK: - String Extension for Localization

extension String {
    /// Returns the localized version of the string
    var localized: String {
        return LocalizationService.shared.localizedString(for: self)
    }
    
    /// Returns the localized version of the string with format arguments
    /// - Parameter arguments: Format arguments
    /// - Returns: The formatted localized string
    func localized(with arguments: CVarArg...) -> String {
        let format = LocalizationService.shared.localizedString(for: self)
        return String(format: format, arguments: arguments)
    }
}
