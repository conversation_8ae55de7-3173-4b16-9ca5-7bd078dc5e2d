import Foundation
import UIKit
import Combine

/// Service for monitoring app performance
class PerformanceMonitor: ObservableObject {
    static let shared = PerformanceMonitor()
    
    @Published var cpuUsage: Double = 0.0
    @Published var memoryUsage: Double = 0.0
    @Published var diskUsage: Double = 0.0
    @Published var networkLatency: TimeInterval = 0.0
    @Published var frameRate: Double = 60.0
    
    private var timer: Timer?
    private var displayLink: CADisplayLink?
    private var frameCount = 0
    private var lastTimestamp: CFTimeInterval = 0
    private var performanceMetrics: [PerformanceMetric] = []
    
    private init() {
        startMonitoring()
    }
    
    deinit {
        stopMonitoring()
    }
    
    // MARK: - Monitoring Control
    
    /// Starts performance monitoring
    func startMonitoring() {
        // Monitor system metrics every second
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateSystemMetrics()
        }
        
        // Monitor frame rate
        displayLink = CADisplayLink(target: self, selector: #selector(displayLinkCallback))
        displayLink?.add(to: .main, forMode: .common)
    }
    
    /// Stops performance monitoring
    func stopMonitoring() {
        timer?.invalidate()
        timer = nil
        
        displayLink?.invalidate()
        displayLink = nil
    }
    
    // MARK: - Metric Collection
    
    private func updateSystemMetrics() {
        DispatchQueue.global(qos: .utility).async { [weak self] in
            let cpu = self?.getCPUUsage() ?? 0.0
            let memory = self?.getMemoryUsage() ?? 0.0
            let disk = self?.getDiskUsage() ?? 0.0
            
            DispatchQueue.main.async {
                self?.cpuUsage = cpu
                self?.memoryUsage = memory
                self?.diskUsage = disk
            }
        }
    }
    
    @objc private func displayLinkCallback() {
        let currentTimestamp = displayLink?.timestamp ?? 0
        
        if lastTimestamp > 0 {
            frameCount += 1
            
            let deltaTime = currentTimestamp - lastTimestamp
            if deltaTime >= 1.0 {
                let fps = Double(frameCount) / deltaTime
                frameRate = fps
                frameCount = 0
                lastTimestamp = currentTimestamp
            }
        } else {
            lastTimestamp = currentTimestamp
        }
    }
    
    // MARK: - System Metrics
    
    private func getCPUUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / Double(ProcessInfo.processInfo.physicalMemory) * 100.0
        }
        
        return 0.0
    }
    
    private func getMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let usedMemory = Double(info.resident_size)
            let totalMemory = Double(ProcessInfo.processInfo.physicalMemory)
            return (usedMemory / totalMemory) * 100.0
        }
        
        return 0.0
    }
    
    private func getDiskUsage() -> Double {
        guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return 0.0
        }
        
        do {
            let resourceValues = try documentsPath.resourceValues(forKeys: [.volumeAvailableCapacityKey, .volumeTotalCapacityKey])
            
            if let availableCapacity = resourceValues.volumeAvailableCapacity,
               let totalCapacity = resourceValues.volumeTotalCapacity {
                let usedCapacity = totalCapacity - availableCapacity
                return (Double(usedCapacity) / Double(totalCapacity)) * 100.0
            }
        } catch {
            print("Error getting disk usage: \(error)")
        }
        
        return 0.0
    }
    
    // MARK: - Performance Tracking
    
    /// Tracks the execution time of a block of code
    /// - Parameters:
    ///   - name: The name of the operation being tracked
    ///   - operation: The operation to track
    /// - Returns: The result of the operation
    func trackPerformance<T>(name: String, operation: () throws -> T) rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try operation()
        let endTime = CFAbsoluteTimeGetCurrent()
        let duration = endTime - startTime
        
        let metric = PerformanceMetric(
            name: name,
            duration: duration,
            timestamp: Date(),
            memoryUsage: memoryUsage,
            cpuUsage: cpuUsage
        )
        
        performanceMetrics.append(metric)
        
        // Keep only the last 100 metrics
        if performanceMetrics.count > 100 {
            performanceMetrics.removeFirst(performanceMetrics.count - 100)
        }
        
        // Log slow operations
        if duration > 0.1 { // 100ms threshold
            print("⚠️ Slow operation detected: \(name) took \(String(format: "%.3f", duration))s")
        }
        
        // Track with analytics
        AnalyticsService.shared.trackPerformance(metric: name, value: duration * 1000, unit: "ms")
        
        return result
    }
    
    /// Tracks the execution time of an async operation
    /// - Parameters:
    ///   - name: The name of the operation being tracked
    ///   - operation: The async operation to track
    /// - Returns: The result of the operation
    func trackPerformance<T>(name: String, operation: () async throws -> T) async rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try await operation()
        let endTime = CFAbsoluteTimeGetCurrent()
        let duration = endTime - startTime
        
        let metric = PerformanceMetric(
            name: name,
            duration: duration,
            timestamp: Date(),
            memoryUsage: memoryUsage,
            cpuUsage: cpuUsage
        )
        
        performanceMetrics.append(metric)
        
        // Keep only the last 100 metrics
        if performanceMetrics.count > 100 {
            performanceMetrics.removeFirst(performanceMetrics.count - 100)
        }
        
        // Log slow operations
        if duration > 0.1 { // 100ms threshold
            print("⚠️ Slow async operation detected: \(name) took \(String(format: "%.3f", duration))s")
        }
        
        // Track with analytics
        AnalyticsService.shared.trackPerformance(metric: name, value: duration * 1000, unit: "ms")
        
        return result
    }
    
    // MARK: - Network Performance
    
    /// Measures network latency to a given URL
    /// - Parameter url: The URL to test latency against
    func measureNetworkLatency(to url: URL) {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        var request = URLRequest(url: url)
        request.httpMethod = "HEAD"
        request.timeoutInterval = 10.0
        
        URLSession.shared.dataTask(with: request) { [weak self] _, _, _ in
            let endTime = CFAbsoluteTimeGetCurrent()
            let latency = endTime - startTime
            
            DispatchQueue.main.async {
                self?.networkLatency = latency
            }
        }.resume()
    }
    
    // MARK: - Performance Reports
    
    /// Gets performance metrics for a specific operation
    /// - Parameter name: The name of the operation
    /// - Returns: Array of performance metrics
    func getMetrics(for name: String) -> [PerformanceMetric] {
        return performanceMetrics.filter { $0.name == name }
    }
    
    /// Gets average performance for a specific operation
    /// - Parameter name: The name of the operation
    /// - Returns: Average duration in seconds
    func getAveragePerformance(for name: String) -> TimeInterval {
        let metrics = getMetrics(for: name)
        guard !metrics.isEmpty else { return 0.0 }
        
        let totalDuration = metrics.reduce(0.0) { $0 + $1.duration }
        return totalDuration / Double(metrics.count)
    }
    
    /// Gets the slowest operations
    /// - Parameter limit: Maximum number of operations to return
    /// - Returns: Array of the slowest operations
    func getSlowestOperations(limit: Int = 10) -> [PerformanceMetric] {
        return performanceMetrics
            .sorted { $0.duration > $1.duration }
            .prefix(limit)
            .map { $0 }
    }
    
    /// Clears all performance metrics
    func clearMetrics() {
        performanceMetrics.removeAll()
    }
    
    // MARK: - Memory Management
    
    /// Forces garbage collection and memory cleanup
    func performMemoryCleanup() {
        // Clear image cache if memory usage is high
        if memoryUsage > 80.0 {
            ImageCacheService.shared.clearMemoryCache()
        }
        
        // Clear analytics events if memory usage is very high
        if memoryUsage > 90.0 {
            AnalyticsService.shared.clearEvents()
        }
        
        // Force garbage collection
        autoreleasepool {
            // This will help release any autoreleased objects
        }
    }
}

/// Represents a performance metric
struct PerformanceMetric {
    let name: String
    let duration: TimeInterval
    let timestamp: Date
    let memoryUsage: Double
    let cpuUsage: Double
    
    var formattedDuration: String {
        if duration < 0.001 {
            return String(format: "%.2f μs", duration * 1_000_000)
        } else if duration < 1.0 {
            return String(format: "%.2f ms", duration * 1000)
        } else {
            return String(format: "%.2f s", duration)
        }
    }
}
