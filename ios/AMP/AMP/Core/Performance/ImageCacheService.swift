import Foundation
import UIKit
import Combine

/// Service for caching images to improve performance
class ImageCacheService: ObservableObject {
    static let shared = ImageCacheService()
    
    private let cache = NSCache<NSString, UIImage>()
    private let fileManager = FileManager.default
    private let cacheDirectory: URL
    private let maxCacheSize: Int = 100 * 1024 * 1024 // 100 MB
    private let maxCacheAge: TimeInterval = 7 * 24 * 60 * 60 // 7 days
    
    private var downloadTasks: [String: URLSessionDataTask] = [:]
    private let downloadQueue = DispatchQueue(label: "com.amp.imagecache", qos: .utility)
    
    private init() {
        // Set up cache directory
        let cacheDir = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first!
        cacheDirectory = cacheDir.appendingPathComponent("ImageCache", isDirectory: true)
        
        // Create cache directory if it doesn't exist
        try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true, attributes: nil)
        
        // Configure NSCache
        cache.countLimit = 100 // Maximum number of images in memory
        cache.totalCostLimit = 50 * 1024 * 1024 // 50 MB memory limit
        
        // Clean up old cache files on initialization
        cleanupOldCacheFiles()
        
        // Set up memory warning observer
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(clearMemoryCache),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Public Methods
    
    /// Loads an image from cache or downloads it if not cached
    /// - Parameter url: The URL of the image
    /// - Returns: A publisher that emits the image or an error
    func loadImage(from url: URL) -> AnyPublisher<UIImage, Error> {
        let cacheKey = cacheKey(for: url)
        
        // Check memory cache first
        if let cachedImage = cache.object(forKey: cacheKey as NSString) {
            return Just(cachedImage)
                .setFailureType(to: Error.self)
                .eraseToAnyPublisher()
        }
        
        // Check disk cache
        if let diskImage = loadImageFromDisk(cacheKey: cacheKey) {
            // Store in memory cache for faster access
            cache.setObject(diskImage, forKey: cacheKey as NSString)
            
            return Just(diskImage)
                .setFailureType(to: Error.self)
                .eraseToAnyPublisher()
        }
        
        // Download image
        return downloadImage(from: url, cacheKey: cacheKey)
    }
    
    /// Preloads images for better performance
    /// - Parameter urls: Array of image URLs to preload
    func preloadImages(_ urls: [URL]) {
        for url in urls {
            let cacheKey = cacheKey(for: url)
            
            // Skip if already cached
            if cache.object(forKey: cacheKey as NSString) != nil ||
               loadImageFromDisk(cacheKey: cacheKey) != nil {
                continue
            }
            
            // Download in background
            downloadQueue.async { [weak self] in
                _ = self?.downloadImage(from: url, cacheKey: cacheKey)
                    .sink(
                        receiveCompletion: { _ in },
                        receiveValue: { _ in }
                    )
            }
        }
    }
    
    /// Clears all cached images
    func clearCache() {
        clearMemoryCache()
        clearDiskCache()
    }
    
    /// Clears only the memory cache
    @objc func clearMemoryCache() {
        cache.removeAllObjects()
    }
    
    /// Clears only the disk cache
    func clearDiskCache() {
        try? fileManager.removeItem(at: cacheDirectory)
        try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true, attributes: nil)
    }
    
    /// Returns the current cache size in bytes
    func getCacheSize() -> Int {
        guard let enumerator = fileManager.enumerator(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey]) else {
            return 0
        }
        
        var totalSize = 0
        for case let fileURL as URL in enumerator {
            if let resourceValues = try? fileURL.resourceValues(forKeys: [.fileSizeKey]),
               let fileSize = resourceValues.fileSize {
                totalSize += fileSize
            }
        }
        
        return totalSize
    }
    
    // MARK: - Private Methods
    
    private func cacheKey(for url: URL) -> String {
        return url.absoluteString.replacingOccurrences(of: "/", with: "_")
            .replacingOccurrences(of: ":", with: "_")
    }
    
    private func cacheFileURL(for cacheKey: String) -> URL {
        return cacheDirectory.appendingPathComponent(cacheKey)
    }
    
    private func loadImageFromDisk(cacheKey: String) -> UIImage? {
        let fileURL = cacheFileURL(for: cacheKey)
        
        guard fileManager.fileExists(atPath: fileURL.path) else {
            return nil
        }
        
        // Check if file is too old
        if let attributes = try? fileManager.attributesOfItem(atPath: fileURL.path),
           let modificationDate = attributes[.modificationDate] as? Date,
           Date().timeIntervalSince(modificationDate) > maxCacheAge {
            // Remove old file
            try? fileManager.removeItem(at: fileURL)
            return nil
        }
        
        guard let data = try? Data(contentsOf: fileURL),
              let image = UIImage(data: data) else {
            return nil
        }
        
        return image
    }
    
    private func saveImageToDisk(_ image: UIImage, cacheKey: String) {
        guard let data = image.jpegData(compressionQuality: 0.8) else {
            return
        }
        
        let fileURL = cacheFileURL(for: cacheKey)
        
        do {
            try data.write(to: fileURL)
            
            // Clean up cache if it's getting too large
            if getCacheSize() > maxCacheSize {
                cleanupOldCacheFiles()
            }
        } catch {
            print("Failed to save image to disk: \(error)")
        }
    }
    
    private func downloadImage(from url: URL, cacheKey: String) -> AnyPublisher<UIImage, Error> {
        // Cancel existing download for this URL if any
        downloadTasks[cacheKey]?.cancel()
        
        return URLSession.shared.dataTaskPublisher(for: url)
            .map(\.data)
            .tryMap { data -> UIImage in
                guard let image = UIImage(data: data) else {
                    throw ImageCacheError.invalidImageData
                }
                return image
            }
            .handleEvents(
                receiveOutput: { [weak self] image in
                    // Cache the image
                    self?.cache.setObject(image, forKey: cacheKey as NSString)
                    self?.saveImageToDisk(image, cacheKey: cacheKey)
                },
                receiveCompletion: { [weak self] _ in
                    // Remove from download tasks
                    self?.downloadTasks.removeValue(forKey: cacheKey)
                }
            )
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    private func cleanupOldCacheFiles() {
        guard let enumerator = fileManager.enumerator(
            at: cacheDirectory,
            includingPropertiesForKeys: [.contentModificationDateKey, .fileSizeKey]
        ) else {
            return
        }
        
        var filesToDelete: [(URL, Date)] = []
        let now = Date()
        
        for case let fileURL as URL in enumerator {
            guard let resourceValues = try? fileURL.resourceValues(forKeys: [.contentModificationDateKey]),
                  let modificationDate = resourceValues.contentModificationDate else {
                continue
            }
            
            // Mark files older than maxCacheAge for deletion
            if now.timeIntervalSince(modificationDate) > maxCacheAge {
                filesToDelete.append((fileURL, modificationDate))
            }
        }
        
        // Sort by modification date (oldest first) and delete
        filesToDelete.sort { $0.1 < $1.1 }
        
        for (fileURL, _) in filesToDelete {
            try? fileManager.removeItem(at: fileURL)
        }
        
        // If cache is still too large, delete more files
        if getCacheSize() > maxCacheSize {
            cleanupLargestFiles()
        }
    }
    
    private func cleanupLargestFiles() {
        guard let enumerator = fileManager.enumerator(
            at: cacheDirectory,
            includingPropertiesForKeys: [.fileSizeKey, .contentModificationDateKey]
        ) else {
            return
        }
        
        var files: [(URL, Int, Date)] = []
        
        for case let fileURL as URL in enumerator {
            guard let resourceValues = try? fileURL.resourceValues(forKeys: [.fileSizeKey, .contentModificationDateKey]),
                  let fileSize = resourceValues.fileSize,
                  let modificationDate = resourceValues.contentModificationDate else {
                continue
            }
            
            files.append((fileURL, fileSize, modificationDate))
        }
        
        // Sort by size (largest first)
        files.sort { $0.1 > $1.1 }
        
        var currentSize = getCacheSize()
        let targetSize = maxCacheSize / 2 // Reduce to half of max size
        
        for (fileURL, fileSize, _) in files {
            if currentSize <= targetSize {
                break
            }
            
            try? fileManager.removeItem(at: fileURL)
            currentSize -= fileSize
        }
    }
}

/// Errors that can occur during image caching
enum ImageCacheError: Error, LocalizedError {
    case invalidImageData
    case downloadFailed
    case cacheWriteFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidImageData:
            return "Invalid image data"
        case .downloadFailed:
            return "Failed to download image"
        case .cacheWriteFailed:
            return "Failed to write image to cache"
        }
    }
}
