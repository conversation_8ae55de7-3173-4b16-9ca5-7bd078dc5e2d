import Foundation
import LocalAuthentication
import Combine

/// Service for handling biometric authentication
class BiometricAuthService: ObservableObject {
    @Published var isAvailable = false
    @Published var biometricType: LABiometryType = .none
    @Published var isEnabled = false
    
    private let context = LAContext()
    
    init() {
        checkBiometricAvailability()
        loadSettings()
    }
    
    // MARK: - Availability Check
    
    /// Checks if biometric authentication is available
    func checkBiometricAvailability() {
        var error: NSError?
        
        if context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) {
            isAvailable = true
            biometricType = context.biometryType
        } else {
            isAvailable = false
            biometricType = .none
            
            if let error = error {
                print("Biometric authentication not available: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - Authentication
    
    /// Authenticates the user using biometrics
    /// - Parameter reason: The reason for authentication
    /// - Returns: A publisher that emits the authentication result
    func authenticate(reason: String = "Authenticate to access AMP") -> AnyPublisher<Bool, Never> {
        return Future<Bool, Never> { [weak self] promise in
            guard let self = self else {
                promise(.success(false))
                return
            }
            
            guard self.isAvailable else {
                promise(.success(false))
                return
            }
            
            let context = LAContext()
            context.localizedFallbackTitle = "Use Passcode"
            
            context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: reason) { success, error in
                DispatchQueue.main.async {
                    if success {
                        promise(.success(true))
                    } else {
                        if let error = error {
                            print("Biometric authentication failed: \(error.localizedDescription)")
                        }
                        promise(.success(false))
                    }
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// Authenticates the user using device passcode or biometrics
    /// - Parameter reason: The reason for authentication
    /// - Returns: A publisher that emits the authentication result
    func authenticateWithPasscode(reason: String = "Authenticate to access AMP") -> AnyPublisher<Bool, Never> {
        return Future<Bool, Never> { promise in
            let context = LAContext()
            context.localizedFallbackTitle = "Cancel"
            
            context.evaluatePolicy(.deviceOwnerAuthentication, localizedReason: reason) { success, error in
                DispatchQueue.main.async {
                    if success {
                        promise(.success(true))
                    } else {
                        if let error = error {
                            print("Device authentication failed: \(error.localizedDescription)")
                        }
                        promise(.success(false))
                    }
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Settings
    
    /// Enables or disables biometric authentication
    /// - Parameter enabled: Whether to enable biometric authentication
    func setBiometricEnabled(_ enabled: Bool) {
        isEnabled = enabled
        UserDefaults.standard.set(enabled, forKey: "biometricAuthEnabled")
    }
    
    /// Loads biometric settings from UserDefaults
    private func loadSettings() {
        isEnabled = UserDefaults.standard.bool(forKey: "biometricAuthEnabled")
    }
    
    // MARK: - Utility
    
    /// Returns the display name for the current biometric type
    var biometricDisplayName: String {
        switch biometricType {
        case .faceID:
            return "Face ID"
        case .touchID:
            return "Touch ID"
        case .opticID:
            return "Optic ID"
        case .none:
            return "Biometric Authentication"
        @unknown default:
            return "Biometric Authentication"
        }
    }
    
    /// Returns the system icon name for the current biometric type
    var biometricIconName: String {
        switch biometricType {
        case .faceID:
            return "faceid"
        case .touchID:
            return "touchid"
        case .opticID:
            return "opticid"
        case .none:
            return "lock"
        @unknown default:
            return "lock"
        }
    }
    
    /// Returns whether the device supports any form of biometric authentication
    var supportsBiometrics: Bool {
        return biometricType != .none
    }
}

/// Represents the result of a biometric authentication attempt
enum BiometricAuthResult {
    case success
    case failure(BiometricAuthError)
    case userCancel
    case userFallback
    case systemCancel
    case passcodeNotSet
    case biometryNotAvailable
    case biometryNotEnrolled
    case biometryLockout
}

/// Errors that can occur during biometric authentication
enum BiometricAuthError: Error, LocalizedError {
    case notAvailable
    case notEnrolled
    case lockout
    case systemCancel
    case userCancel
    case userFallback
    case passcodeNotSet
    case unknown(Error)
    
    var errorDescription: String? {
        switch self {
        case .notAvailable:
            return "Biometric authentication is not available on this device."
        case .notEnrolled:
            return "No biometric data is enrolled on this device."
        case .lockout:
            return "Biometric authentication is locked out. Please try again later."
        case .systemCancel:
            return "Authentication was cancelled by the system."
        case .userCancel:
            return "Authentication was cancelled by the user."
        case .userFallback:
            return "User chose to use fallback authentication."
        case .passcodeNotSet:
            return "Device passcode is not set."
        case .unknown(let error):
            return "An unknown error occurred: \(error.localizedDescription)"
        }
    }
}
