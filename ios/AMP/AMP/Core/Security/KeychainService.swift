import Foundation
import Security

/// Service for securely storing data in the iOS Keychain
class KeychainService {
    static let shared = KeychainService()
    
    private let serviceName = "com.amp.keychain"
    
    private init() {}
    
    // MARK: - Data Storage
    
    /// Stores data in the keychain
    /// - Parameters:
    ///   - data: The data to store
    ///   - key: The key to associate with the data
    /// - Returns: True if successful, false otherwise
    @discardableResult
    func setData(_ data: Data, for key: String) -> <PERSON><PERSON> {
        // Delete any existing item first
        deleteData(for: key)
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        let status = SecItemAdd(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    /// Stores a string in the keychain
    /// - Parameters:
    ///   - string: The string to store
    ///   - key: The key to associate with the string
    /// - Returns: True if successful, false otherwise
    @discardableResult
    func setString(_ string: String, for key: String) -> Bool {
        guard let data = string.data(using: .utf8) else {
            return false
        }
        
        return setData(data, for: key)
    }
    
    // MARK: - Data Retrieval
    
    /// Retrieves data from the keychain
    /// - Parameter key: The key associated with the data
    /// - Returns: The data if found, nil otherwise
    func getData(for key: String) -> Data? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess {
            return result as? Data
        }
        
        return nil
    }
    
    /// Retrieves a string from the keychain
    /// - Parameter key: The key associated with the string
    /// - Returns: The string if found, nil otherwise
    func getString(for key: String) -> String? {
        guard let data = getData(for: key) else {
            return nil
        }
        
        return String(data: data, encoding: .utf8)
    }
    
    // MARK: - Data Deletion
    
    /// Deletes data from the keychain
    /// - Parameter key: The key associated with the data to delete
    /// - Returns: True if successful or item doesn't exist, false otherwise
    @discardableResult
    func deleteData(for key: String) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        return status == errSecSuccess || status == errSecItemNotFound
    }
    
    /// Deletes all data for this service from the keychain
    /// - Returns: True if successful, false otherwise
    @discardableResult
    func deleteAllData() -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        return status == errSecSuccess || status == errSecItemNotFound
    }
    
    // MARK: - Data Existence Check
    
    /// Checks if data exists in the keychain for the given key
    /// - Parameter key: The key to check
    /// - Returns: True if data exists, false otherwise
    func dataExists(for key: String) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key,
            kSecReturnData as String: false,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        let status = SecItemCopyMatching(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    // MARK: - Biometric Protection
    
    /// Stores data in the keychain with biometric protection
    /// - Parameters:
    ///   - data: The data to store
    ///   - key: The key to associate with the data
    /// - Returns: True if successful, false otherwise
    @discardableResult
    func setBiometricProtectedData(_ data: Data, for key: String) -> Bool {
        // Delete any existing item first
        deleteData(for: key)
        
        let access = SecAccessControlCreateWithFlags(
            nil,
            kSecAttrAccessibleWhenUnlockedThisDeviceOnly,
            .biometryAny,
            nil
        )
        
        guard let accessControl = access else {
            return false
        }
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessControl as String: accessControl
        ]
        
        let status = SecItemAdd(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    /// Stores a string in the keychain with biometric protection
    /// - Parameters:
    ///   - string: The string to store
    ///   - key: The key to associate with the string
    /// - Returns: True if successful, false otherwise
    @discardableResult
    func setBiometricProtectedString(_ string: String, for key: String) -> Bool {
        guard let data = string.data(using: .utf8) else {
            return false
        }
        
        return setBiometricProtectedData(data, for: key)
    }
    
    // MARK: - Error Handling
    
    /// Converts a Security framework error code to a human-readable string
    /// - Parameter status: The OSStatus error code
    /// - Returns: A human-readable error description
    func errorDescription(for status: OSStatus) -> String {
        switch status {
        case errSecSuccess:
            return "Success"
        case errSecItemNotFound:
            return "Item not found"
        case errSecDuplicateItem:
            return "Duplicate item"
        case errSecAuthFailed:
            return "Authentication failed"
        case errSecUserCancel:
            return "User cancelled"
        case errSecNotAvailable:
            return "Not available"
        case errSecParam:
            return "Invalid parameter"
        case errSecAllocate:
            return "Memory allocation failed"
        case errSecUnimplemented:
            return "Function not implemented"
        case errSecDiskFull:
            return "Disk full"
        case errSecIO:
            return "I/O error"
        default:
            return "Unknown error (\(status))"
        }
    }
}
