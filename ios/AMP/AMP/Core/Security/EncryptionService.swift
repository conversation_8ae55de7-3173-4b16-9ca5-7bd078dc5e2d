import Foundation
import CryptoKit
import Security

/// Service for encrypting and decrypting sensitive data
class EncryptionService {
    static let shared = EncryptionService()
    
    private let keychain = KeychainService.shared
    private let encryptionKeyTag = "com.amp.encryption.key"
    
    private init() {}
    
    // MARK: - Key Management
    
    /// Generates or retrieves the encryption key
    private func getEncryptionKey() throws -> SymmetricKey {
        // Try to retrieve existing key from keychain
        if let keyData = keychain.getData(for: encryptionKeyTag) {
            return SymmetricKey(data: keyData)
        }
        
        // Generate new key if none exists
        let key = SymmetricKey(size: .bits256)
        let keyData = key.withUnsafeBytes { Data($0) }
        
        // Store key in keychain
        _ = keychain.setData(keyData, for: encryptionKeyTag)
        
        return key
    }
    
    // MARK: - Encryption
    
    /// Encrypts data using AES-GCM
    /// - Parameter data: The data to encrypt
    /// - Returns: The encrypted data
    /// - Throws: EncryptionError if encryption fails
    func encrypt(_ data: Data) throws -> Data {
        let key = try getEncryptionKey()
        let sealedBox = try AES.GCM.seal(data, using: key)
        
        guard let encryptedData = sealedBox.combined else {
            throw EncryptionError.encryptionFailed
        }
        
        return encryptedData
    }
    
    /// Encrypts a string using AES-GCM
    /// - Parameter string: The string to encrypt
    /// - Returns: The encrypted data
    /// - Throws: EncryptionError if encryption fails
    func encrypt(_ string: String) throws -> Data {
        guard let data = string.data(using: .utf8) else {
            throw EncryptionError.invalidData
        }
        
        return try encrypt(data)
    }
    
    // MARK: - Decryption
    
    /// Decrypts data using AES-GCM
    /// - Parameter encryptedData: The encrypted data
    /// - Returns: The decrypted data
    /// - Throws: EncryptionError if decryption fails
    func decrypt(_ encryptedData: Data) throws -> Data {
        let key = try getEncryptionKey()
        let sealedBox = try AES.GCM.SealedBox(combined: encryptedData)
        let decryptedData = try AES.GCM.open(sealedBox, using: key)
        
        return decryptedData
    }
    
    /// Decrypts data to a string using AES-GCM
    /// - Parameter encryptedData: The encrypted data
    /// - Returns: The decrypted string
    /// - Throws: EncryptionError if decryption fails
    func decryptToString(_ encryptedData: Data) throws -> String {
        let decryptedData = try decrypt(encryptedData)
        
        guard let string = String(data: decryptedData, encoding: .utf8) else {
            throw EncryptionError.invalidData
        }
        
        return string
    }
    
    // MARK: - Hashing
    
    /// Creates a SHA256 hash of the input data
    /// - Parameter data: The data to hash
    /// - Returns: The hash as a hex string
    func sha256Hash(_ data: Data) -> String {
        let hash = SHA256.hash(data: data)
        return hash.compactMap { String(format: "%02x", $0) }.joined()
    }
    
    /// Creates a SHA256 hash of the input string
    /// - Parameter string: The string to hash
    /// - Returns: The hash as a hex string
    func sha256Hash(_ string: String) -> String {
        guard let data = string.data(using: .utf8) else {
            return ""
        }
        
        return sha256Hash(data)
    }
    
    // MARK: - Key Derivation
    
    /// Derives a key from a password using PBKDF2
    /// - Parameters:
    ///   - password: The password to derive from
    ///   - salt: The salt to use (optional, will generate if not provided)
    ///   - iterations: The number of iterations (default: 100,000)
    /// - Returns: A tuple containing the derived key and salt
    func deriveKey(from password: String, salt: Data? = nil, iterations: Int = 100_000) throws -> (key: SymmetricKey, salt: Data) {
        let saltData = salt ?? Data((0..<16).map { _ in UInt8.random(in: 0...255) })
        
        guard let passwordData = password.data(using: .utf8) else {
            throw EncryptionError.invalidData
        }
        
        let derivedKey = try HKDF<SHA256>.deriveKey(
            inputKeyMaterial: SymmetricKey(data: passwordData),
            salt: saltData,
            outputByteCount: 32
        )
        
        return (key: derivedKey, salt: saltData)
    }
    
    // MARK: - Secure Random
    
    /// Generates secure random data
    /// - Parameter length: The length of random data to generate
    /// - Returns: The random data
    func generateRandomData(length: Int) -> Data {
        var data = Data(count: length)
        let result = data.withUnsafeMutableBytes { bytes in
            SecRandomCopyBytes(kSecRandomDefault, length, bytes.bindMemory(to: UInt8.self).baseAddress!)
        }
        
        if result == errSecSuccess {
            return data
        } else {
            // Fallback to CryptoKit if SecRandomCopyBytes fails
            return Data((0..<length).map { _ in UInt8.random(in: 0...255) })
        }
    }
    
    /// Generates a secure random string
    /// - Parameter length: The length of the string to generate
    /// - Returns: The random string
    func generateRandomString(length: Int) -> String {
        let characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return String((0..<length).compactMap { _ in characters.randomElement() })
    }
}

/// Errors that can occur during encryption/decryption
enum EncryptionError: Error, LocalizedError {
    case keyGenerationFailed
    case encryptionFailed
    case decryptionFailed
    case invalidData
    case keychainError
    
    var errorDescription: String? {
        switch self {
        case .keyGenerationFailed:
            return "Failed to generate encryption key"
        case .encryptionFailed:
            return "Failed to encrypt data"
        case .decryptionFailed:
            return "Failed to decrypt data"
        case .invalidData:
            return "Invalid data provided"
        case .keychainError:
            return "Keychain operation failed"
        }
    }
}
