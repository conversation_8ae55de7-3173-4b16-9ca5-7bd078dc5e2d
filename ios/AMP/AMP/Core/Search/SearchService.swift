import Foundation
import Combine
import SwiftUI
/// Service for handling search functionality across the app
class SearchService: ObservableObject {
    @Published var searchResults: [SearchResult] = []
    @Published var isSearching = false
    @Published var searchHistory: [String] = []
    @Published var recentSearches: [String] = []
    
    private var cancellables = Set<AnyCancellable>()
    private let maxHistoryItems = 20
    private let maxRecentItems = 5
    
    // Services for searching different data types
    private let orderService: OrderService
    private let inventoryService: InventoryService
    private let vendorService: VendorService
    private let noteService: NoteService
    private let userManagementService: UserManagementService
    
    init(
        orderService: OrderService,
        inventoryService: InventoryService,
        vendorService: VendorService,
        noteService: NoteService,
        userManagementService: UserManagementService
    ) {
        self.orderService = orderService
        self.inventoryService = inventoryService
        self.vendorService = vendorService
        self.noteService = noteService
        self.userManagementService = userManagementService
        
        loadSearchHistory()
    }
    
    // MARK: - Search Methods
    
    /// Performs a global search across all data types
    /// - Parameter query: The search query
    func search(query: String) {
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            searchResults = []
            return
        }
        
        isSearching = true
        
        // Add to search history
        addToSearchHistory(query)
        
        // Perform search across all data types
        let results = performGlobalSearch(query: query)
        
        DispatchQueue.main.async { [weak self] in
            self?.searchResults = results
            self?.isSearching = false
        }
    }
    
    /// Performs a search with debouncing for real-time search
    /// - Parameter query: The search query
    func searchWithDebounce(query: String) {
        // Cancel previous search
        cancellables.removeAll()
        
        // Debounce search by 300ms
        Just(query)
            .delay(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .sink { [weak self] debouncedQuery in
                self?.search(query: debouncedQuery)
            }
            .store(in: &cancellables)
    }
    
    /// Performs search across all data types
    /// - Parameter query: The search query
    /// - Returns: Array of search results
    private func performGlobalSearch(query: String) -> [SearchResult] {
        var results: [SearchResult] = []
        
        // Search orders
        let orderResults = searchOrders(query: query)
        results.append(contentsOf: orderResults)
        
        // Search inventory
        let inventoryResults = searchInventory(query: query)
        results.append(contentsOf: inventoryResults)
        
        // Search vendors
        let vendorResults = searchVendors(query: query)
        results.append(contentsOf: vendorResults)
        
        // Search notes
        let noteResults = searchNotes(query: query)
        results.append(contentsOf: noteResults)
        
        // Search users (if available)
        let userResults = searchUsers(query: query)
        results.append(contentsOf: userResults)
        
        // Sort results by relevance
        return results.sorted { $0.relevanceScore > $1.relevanceScore }
    }
    
    // MARK: - Specific Search Methods
    
    private func searchOrders(query: String) -> [SearchResult] {
        let filteredOrders = orderService.filteredOrders(searchText: query, statusFilter: nil)
        
        return filteredOrders.map { order in
            SearchResult(
                id: "order_\(order.id)",
                title: order.product.name,
                subtitle: "Order #\(order.id) • \(order.vendor.name)",
                type: .order,
                relevanceScore: calculateRelevance(query: query, text: order.product.name),
                data: order
            )
        }
    }
    
    private func searchInventory(query: String) -> [SearchResult] {
        let filteredProducts = inventoryService.filteredProducts(searchText: query, categoryFilter: nil, statusFilter: nil)
        
        return filteredProducts.map { product in
            SearchResult(
                id: "product_\(product.id)",
                title: product.name,
                subtitle: product.description,
                type: .product,
                relevanceScore: calculateRelevance(query: query, text: "\(product.name) \(product.description)"),
                data: product
            )
        }
    }
    
    private func searchVendors(query: String) -> [SearchResult] {
        let filteredVendors = vendorService.filteredVendors(searchText: query, activeOnly: false)
        
        return filteredVendors.map { vendor in
            SearchResult(
                id: "vendor_\(vendor.id)",
                title: vendor.name,
                subtitle: vendor.email,
                type: .vendor,
                relevanceScore: calculateRelevance(query: query, text: "\(vendor.name) \(vendor.email)"),
                data: vendor
            )
        }
    }
    
    private func searchNotes(query: String) -> [SearchResult] {
        let filteredNotes = noteService.filteredNotes(searchText: query)
        
        return filteredNotes.map { note in
            SearchResult(
                id: "note_\(note.id)",
                title: note.title,
                subtitle: note.contentPreview,
                type: .note,
                relevanceScore: calculateRelevance(query: query, text: "\(note.title) \(note.content)"),
                data: note
            )
        }
    }
    
    private func searchUsers(query: String) -> [SearchResult] {
        let filteredUsers = userManagementService.filteredUsers(searchText: query)
        
        return filteredUsers.map { user in
            SearchResult(
                id: "user_\(user.id)",
                title: user.fullName,
                subtitle: user.email,
                type: .user,
                relevanceScore: calculateRelevance(query: query, text: "\(user.fullName) \(user.email)"),
                data: user
            )
        }
    }
    
    // MARK: - Relevance Calculation
    
    /// Calculates relevance score for search results
    /// - Parameters:
    ///   - query: The search query
    ///   - text: The text to search in
    /// - Returns: Relevance score (0.0 to 1.0)
    private func calculateRelevance(query: String, text: String) -> Double {
        let queryLower = query.lowercased()
        let textLower = text.lowercased()
        
        // Exact match gets highest score
        if textLower == queryLower {
            return 1.0
        }
        
        // Starts with query gets high score
        if textLower.hasPrefix(queryLower) {
            return 0.9
        }
        
        // Contains query gets medium score
        if textLower.contains(queryLower) {
            return 0.7
        }
        
        // Word boundary matches get good score
        let words = textLower.components(separatedBy: .whitespacesAndPunctuation)
        for word in words {
            if word.hasPrefix(queryLower) {
                return 0.6
            }
        }
        
        // Fuzzy matching for partial matches
        let distance = levenshteinDistance(queryLower, textLower)
        let maxLength = max(queryLower.count, textLower.count)
        let similarity = 1.0 - (Double(distance) / Double(maxLength))
        
        return max(0.0, similarity - 0.3) // Threshold for fuzzy matches
    }
    
    /// Calculates Levenshtein distance between two strings
    private func levenshteinDistance(_ s1: String, _ s2: String) -> Int {
        let a = Array(s1)
        let b = Array(s2)
        
        var matrix = Array(repeating: Array(repeating: 0, count: b.count + 1), count: a.count + 1)
        
        for i in 0...a.count {
            matrix[i][0] = i
        }
        
        for j in 0...b.count {
            matrix[0][j] = j
        }
        
        for i in 1...a.count {
            for j in 1...b.count {
                if a[i-1] == b[j-1] {
                    matrix[i][j] = matrix[i-1][j-1]
                } else {
                    matrix[i][j] = min(
                        matrix[i-1][j] + 1,
                        matrix[i][j-1] + 1,
                        matrix[i-1][j-1] + 1
                    )
                }
            }
        }
        
        return matrix[a.count][b.count]
    }
    
    // MARK: - Search History
    
    /// Adds a query to search history
    /// - Parameter query: The search query
    private func addToSearchHistory(_ query: String) {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedQuery.isEmpty else { return }
        
        // Remove if already exists
        searchHistory.removeAll { $0 == trimmedQuery }
        recentSearches.removeAll { $0 == trimmedQuery }
        
        // Add to beginning
        searchHistory.insert(trimmedQuery, at: 0)
        recentSearches.insert(trimmedQuery, at: 0)
        
        // Limit size
        if searchHistory.count > maxHistoryItems {
            searchHistory = Array(searchHistory.prefix(maxHistoryItems))
        }
        
        if recentSearches.count > maxRecentItems {
            recentSearches = Array(recentSearches.prefix(maxRecentItems))
        }
        
        // Save to UserDefaults
        saveSearchHistory()
    }
    
    /// Clears search history
    func clearSearchHistory() {
        searchHistory.removeAll()
        recentSearches.removeAll()
        saveSearchHistory()
    }
    
    /// Removes a specific item from search history
    /// - Parameter query: The query to remove
    func removeFromHistory(_ query: String) {
        searchHistory.removeAll { $0 == query }
        recentSearches.removeAll { $0 == query }
        saveSearchHistory()
    }
    
    /// Saves search history to UserDefaults
    private func saveSearchHistory() {
        UserDefaults.standard.set(searchHistory, forKey: "searchHistory")
        UserDefaults.standard.set(recentSearches, forKey: "recentSearches")
    }
    
    /// Loads search history from UserDefaults
    private func loadSearchHistory() {
        searchHistory = UserDefaults.standard.stringArray(forKey: "searchHistory") ?? []
        recentSearches = UserDefaults.standard.stringArray(forKey: "recentSearches") ?? []
    }
}

/// Represents a search result
struct SearchResult: Identifiable {
    let id: String
    let title: String
    let subtitle: String
    let type: SearchResultType
    let relevanceScore: Double
    let data: Any
}

/// Types of search results
enum SearchResultType: String, CaseIterable {
    case order = "Order"
    case product = "Product"
    case vendor = "Vendor"
    case note = "Note"
    case user = "User"
    
    var icon: String {
        switch self {
        case .order:
            return "shippingbox"
        case .product:
            return "cube.box"
        case .vendor:
            return "building.2"
        case .note:
            return "note.text"
        case .user:
            return "person"
        }
    }
    
    var color: Color {
        switch self {
        case .order:
            return Color("PrimaryColor")
        case .product:
            return Color("SuccessColor")
        case .vendor:
            return Color("InfoColor")
        case .note:
            return Color("WarningColor")
        case .user:
            return Color("SecondaryColor")
        }
    }
}
