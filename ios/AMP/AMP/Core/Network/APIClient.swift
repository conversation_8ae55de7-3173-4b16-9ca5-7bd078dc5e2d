import Foundation
import Combine

/// API Client for making network requests
class APIClient {
    static let shared = APIClient()
    
    private let session: URLSession
    private let decoder: JSONDecoder
    
    private init() {
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30.0
        configuration.timeoutIntervalForResource = 60.0
        
        self.session = URLSession(configuration: configuration)
        
        self.decoder = JSONDecoder()
        self.decoder.keyDecodingStrategy = .convertFromSnakeCase
        self.decoder.dateDecodingStrategy = .iso8601
    }
    
    /// Makes a GET request to the specified endpoint
    /// - Parameters:
    ///   - endpoint: The endpoint to request
    ///   - token: The authentication token (optional)
    /// - Returns: A publisher with the decoded response
    func get<T: Decodable>(endpoint: String, token: String? = nil) -> AnyPublisher<T, APIError> {
        return request(method: "GET", endpoint: endpoint, token: token)
    }
    
    /// Makes a POST request to the specified endpoint
    /// - Parameters:
    ///   - endpoint: The endpoint to request
    ///   - body: The request body (optional)
    ///   - token: The authentication token (optional)
    /// - Returns: A publisher with the decoded response
    func post<T: Decodable, U: Encodable>(endpoint: String, body: U? = nil, token: String? = nil) -> AnyPublisher<T, APIError> {
        return request(method: "POST", endpoint: endpoint, body: body, token: token)
    }
    
    /// Makes a PUT request to the specified endpoint
    /// - Parameters:
    ///   - endpoint: The endpoint to request
    ///   - body: The request body (optional)
    ///   - token: The authentication token (optional)
    /// - Returns: A publisher with the decoded response
    func put<T: Decodable, U: Encodable>(endpoint: String, body: U? = nil, token: String? = nil) -> AnyPublisher<T, APIError> {
        return request(method: "PUT", endpoint: endpoint, body: body, token: token)
    }
    
    /// Makes a DELETE request to the specified endpoint
    /// - Parameters:
    ///   - endpoint: The endpoint to request
    ///   - token: The authentication token (optional)
    /// - Returns: A publisher with the decoded response
    func delete<T: Decodable>(endpoint: String, token: String? = nil) -> AnyPublisher<T, APIError> {
        return request(method: "DELETE", endpoint: endpoint, token: token)
    }
    
    /// Makes a request to the specified endpoint
    /// - Parameters:
    ///   - method: The HTTP method
    ///   - endpoint: The endpoint to request
    ///   - body: The request body (optional)
    ///   - token: The authentication token (optional)
    /// - Returns: A publisher with the decoded response
    private func request<T: Decodable, U: Encodable>(method: String, endpoint: String, body: U? = nil, token: String? = nil) -> AnyPublisher<T, APIError> {
        // Create URL
        guard let url = URL(string: Config.apiURL + endpoint) else {
            return Fail(error: APIError.invalidURL).eraseToAnyPublisher()
        }
        
        // Create request
        var request = URLRequest(url: url)
        request.httpMethod = method
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("application/json", forHTTPHeaderField: "Accept")
        
        // Add authentication token if provided
        if let token = token {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        // Add body if provided
        if let body = body {
            do {
                let encoder = JSONEncoder()
                encoder.keyEncodingStrategy = .convertToSnakeCase
                request.httpBody = try encoder.encode(body)
            } catch {
                return Fail(error: APIError.encodingError(error)).eraseToAnyPublisher()
            }
        }
        
        // Make request
        return session.dataTaskPublisher(for: request)
            .tryMap { data, response -> Data in
                // Check response
                guard let httpResponse = response as? HTTPURLResponse else {
                    throw APIError.invalidResponse
                }
                
                // Check status code
                switch httpResponse.statusCode {
                case 200...299:
                    return data
                case 401:
                    throw APIError.unauthorized
                case 403:
                    throw APIError.forbidden
                case 404:
                    throw APIError.notFound
                default:
                    // Try to decode error message
                    if let errorResponse = try? JSONDecoder().decode(ErrorResponse.self, from: data) {
                        throw APIError.serverError(httpResponse.statusCode, errorResponse.detail)
                    } else {
                        throw APIError.serverError(httpResponse.statusCode, "Unknown server error")
                    }
                }
            }
            .decode(type: T.self, decoder: decoder)
            .mapError { error -> APIError in
                if let apiError = error as? APIError {
                    return apiError
                } else if error is DecodingError {
                    return APIError.decodingError(error)
                } else {
                    return APIError.networkError(error)
                }
            }
            .eraseToAnyPublisher()
    }
    
    /// Makes a multipart form data request to the specified endpoint
    /// - Parameters:
    ///   - endpoint: The endpoint to request
    ///   - parameters: The form parameters
    ///   - fileData: The file data
    ///   - fileName: The file name
    ///   - fileKey: The file key
    ///   - mimeType: The file MIME type
    ///   - token: The authentication token (optional)
    /// - Returns: A publisher with the decoded response
    func uploadMultipartFormData<T: Decodable>(
        endpoint: String,
        parameters: [String: String],
        fileData: Data,
        fileName: String,
        fileKey: String,
        mimeType: String,
        token: String? = nil
    ) -> AnyPublisher<T, APIError> {
        // Create URL
        guard let url = URL(string: Config.apiURL + endpoint) else {
            return Fail(error: APIError.invalidURL).eraseToAnyPublisher()
        }
        
        // Create request
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        
        // Add authentication token if provided
        if let token = token {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        // Generate boundary string
        let boundary = UUID().uuidString
        request.addValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
        // Create body
        var body = Data()
        
        // Add parameters
        for (key, value) in parameters {
            body.append("--\(boundary)\r\n".data(using: .utf8)!)
            body.append("Content-Disposition: form-data; name=\"\(key)\"\r\n\r\n".data(using: .utf8)!)
            body.append("\(value)\r\n".data(using: .utf8)!)
        }
        
        // Add file
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"\(fileKey)\"; filename=\"\(fileName)\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: \(mimeType)\r\n\r\n".data(using: .utf8)!)
        body.append(fileData)
        body.append("\r\n".data(using: .utf8)!)
        
        // Add closing boundary
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)
        
        // Set body
        request.httpBody = body
        
        // Make request
        return session.dataTaskPublisher(for: request)
            .tryMap { data, response -> Data in
                // Check response
                guard let httpResponse = response as? HTTPURLResponse else {
                    throw APIError.invalidResponse
                }
                
                // Check status code
                switch httpResponse.statusCode {
                case 200...299:
                    return data
                case 401:
                    throw APIError.unauthorized
                case 403:
                    throw APIError.forbidden
                case 404:
                    throw APIError.notFound
                default:
                    // Try to decode error message
                    if let errorResponse = try? JSONDecoder().decode(ErrorResponse.self, from: data) {
                        throw APIError.serverError(httpResponse.statusCode, errorResponse.detail)
                    } else {
                        throw APIError.serverError(httpResponse.statusCode, "Unknown server error")
                    }
                }
            }
            .decode(type: T.self, decoder: decoder)
            .mapError { error -> APIError in
                if let apiError = error as? APIError {
                    return apiError
                } else if error is DecodingError {
                    return APIError.decodingError(error)
                } else {
                    return APIError.networkError(error)
                }
            }
            .eraseToAnyPublisher()
    }
}
