import Foundation
import Combine
import SwiftUI
/// Service for WebSocket connections
class WebSocketService: ObservableObject {
    enum ConnectionState {
        case disconnected
        case connecting
        case connected
    }
    
    @Published var connectionState: ConnectionState = .disconnected
    @Published var lastMessage: String?
    @Published var notifications: [WebSocketNotification] = []
    
    private var webSocketTask: URLSessionWebSocketTask?
    private var pingTimer: Timer?
    private var reconnectTimer: Timer?
    private var reconnectAttempts = 0
    private let maxReconnectAttempts = 5
    private let reconnectDelay: TimeInterval = 3.0
    
    private var userId: Int?
    private var token: String?
    
    // MARK: - Connection Management
    
    /// Connects to the WebSocket server
    func connect(userId: Int, token: String) {
        guard connectionState == .disconnected else { return }
        
        self.userId = userId
        self.token = token
        
        connectionState = .connecting
        
        // Create WebSocket URL with authentication
        var urlComponents = URLComponents(string: Config.webSocketURL)
        urlComponents?.queryItems = [
            URLQueryItem(name: "user_id", value: String(userId)),
            URLQueryItem(name: "token", value: token)
        ]
        
        guard let url = urlComponents?.url else {
            print("Invalid WebSocket URL")
            connectionState = .disconnected
            return
        }
        
        // Create WebSocket task
        let session = URLSession(configuration: .default)
        webSocketTask = session.webSocketTask(with: url)
        
        // Start receiving messages
        receiveMessage()
        
        // Connect
        webSocketTask?.resume()
        connectionState = .connected
        
        // Start ping timer
        startPingTimer()
        
        // Reset reconnect attempts
        reconnectAttempts = 0
    }
    
    /// Disconnects from the WebSocket server
    func disconnect() {
        stopPingTimer()
        stopReconnectTimer()
        
        webSocketTask?.cancel(with: .normalClosure, reason: nil)
        webSocketTask = nil
        
        connectionState = .disconnected
    }
    
    /// Reconnects to the WebSocket server
    private func reconnect() {
        guard let userId = userId, let token = token else { return }
        
        reconnectAttempts += 1
        
        if reconnectAttempts <= maxReconnectAttempts {
            print("Attempting to reconnect (\(reconnectAttempts)/\(maxReconnectAttempts))...")
            connect(userId: userId, token: token)
        } else {
            print("Max reconnect attempts reached")
            stopReconnectTimer()
        }
    }
    
    // MARK: - Message Handling
    
    /// Receives messages from the WebSocket server
    private func receiveMessage() {
        webSocketTask?.receive { [weak self] result in
            switch result {
            case .success(let message):
                switch message {
                case .string(let text):
                    self?.handleMessage(text)
                case .data(let data):
                    if let text = String(data: data, encoding: .utf8) {
                        self?.handleMessage(text)
                    }
                @unknown default:
                    break
                }
                
                // Continue receiving messages
                self?.receiveMessage()
                
            case .failure(let error):
                print("WebSocket receive error: \(error)")
                
                // Handle disconnection
                self?.connectionState = .disconnected
                self?.startReconnectTimer()
            }
        }
    }
    
    /// Handles a message from the WebSocket server
    private func handleMessage(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            self?.lastMessage = message
            
            // Try to decode as notification
            if let data = message.data(using: .utf8),
               let notification = try? JSONDecoder().decode(WebSocketNotification.self, from: data) {
                self?.notifications.insert(notification, at: 0)
                
                // Limit the number of notifications
                if let count = self?.notifications.count, count > 20 {
                    self?.notifications = Array(self?.notifications.prefix(20) ?? [])
                }
            }
        }
    }
    
    /// Sends a message to the WebSocket server
    func send(message: String, completion: @escaping (Error?) -> Void) {
        webSocketTask?.send(.string(message)) { error in
            if let error = error {
                print("WebSocket send error: \(error)")
                completion(error)
            } else {
                completion(nil)
            }
        }
    }
    
    // MARK: - Ping/Pong
    
    /// Starts the ping timer
    private func startPingTimer() {
        pingTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            self?.ping()
        }
    }
    
    /// Stops the ping timer
    private func stopPingTimer() {
        pingTimer?.invalidate()
        pingTimer = nil
    }
    
    /// Sends a ping to the WebSocket server
    private func ping() {
        webSocketTask?.sendPing { [weak self] error in
            if let error = error {
                print("WebSocket ping error: \(error)")
                self?.connectionState = .disconnected
                self?.startReconnectTimer()
            }
        }
    }
    
    // MARK: - Reconnect Timer
    
    /// Starts the reconnect timer
    private func startReconnectTimer() {
        stopReconnectTimer()
        
        reconnectTimer = Timer.scheduledTimer(withTimeInterval: reconnectDelay, repeats: false) { [weak self] _ in
            self?.reconnect()
        }
    }
    
    /// Stops the reconnect timer
    private func stopReconnectTimer() {
        reconnectTimer?.invalidate()
        reconnectTimer = nil
    }
    
    // MARK: - Cleanup
    
    /// Cleans up resources
    deinit {
        disconnect()
    }
}

/// Represents a notification from the WebSocket server
struct WebSocketNotification: Codable, Identifiable {
    let id: String
    let type: String
    let title: String
    let message: String
    let timestamp: String
    let data: [String: String]?
    
    /// Returns the formatted timestamp
    var formattedTimestamp: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        
        if let date = dateFormatter.date(from: timestamp) {
            dateFormatter.dateFormat = "MMM d, yyyy h:mm a"
            return dateFormatter.string(from: date)
        }
        
        return timestamp
    }
    
    /// Returns the icon for the notification type
    var icon: String {
        switch type {
        case "order":
            return "shippingbox"
        case "inventory":
            return "cube.box"
        case "vendor":
            return "building.2"
        case "note":
            return "note.text"
        case "user":
            return "person"
        default:
            return "bell"
        }
    }
    
    /// Returns the color for the notification type
    var color: Color {
        switch type {
        case "order":
            return Color("PrimaryColor")
        case "inventory":
            return Color("WarningColor")
        case "vendor":
            return Color("InfoColor")
        case "note":
            return Color("SuccessColor")
        case "user":
            return Color("SecondaryColor")
        default:
            return Color("PrimaryColor")
        }
    }
}
