import Foundation
import Combine
import SwiftUI

/// Manager for handling offline functionality
class OfflineManager: ObservableObject {
    @Published var isOffline = false
    @Published var pendingActions: [PendingAction] = []
    @Published var lastSyncDate: Date?
    
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        // Load pending actions from disk
        loadPendingActions()
        
        // Subscribe to network status changes
        NetworkMonitor.shared.objectWillChange
            .sink { [weak self] _ in
                DispatchQueue.main.async {
                    self?.isOffline = !NetworkMonitor.shared.isConnected
                    
                    // If we're back online, try to sync pending actions
                    if NetworkMonitor.shared.isConnected {
                        self?.syncPendingActions()
                    }
                }
            }
            .store(in: &cancellables)
        
        // Set initial offline status
        isOffline = !NetworkMonitor.shared.isConnected
    }
    
    // MARK: - Pending Actions
    
    /// Adds a pending action
    /// - Parameter action: The action to add
    func addPendingAction(_ action: PendingAction) {
        pendingActions.append(action)
        savePendingActions()
    }
    
    /// Removes a pending action
    /// - Parameter id: The ID of the action to remove
    func removePendingAction(id: UUID) {
        pendingActions.removeAll { $0.id == id }
        savePendingActions()
    }
    
    /// Saves pending actions to disk
    private func savePendingActions() {
        _ = DataPersistenceService.shared.save(pendingActions, fileName: "pendingActions.json")
    }
    
    /// Loads pending actions from disk
    private func loadPendingActions() {
        if let actions: [PendingAction] = DataPersistenceService.shared.load(fileName: "pendingActions.json") {
            pendingActions = actions
        }
    }
    
    /// Syncs pending actions with the server
    func syncPendingActions() {
        guard !pendingActions.isEmpty else { return }
        
        // In a real app, we would process each action and send it to the server
        // For now, we'll just simulate success
        
        // Remove all pending actions
        pendingActions.removeAll()
        savePendingActions()
        
        // Update last sync date
        lastSyncDate = Date()
    }
    
    // MARK: - Cache Management
    
    /// Caches data
    /// - Parameters:
    ///   - data: The data to cache
    ///   - key: The key to use for caching
    func cacheData<T: Encodable>(_ data: T, forKey key: String) {
        _ = DataPersistenceService.shared.save(data, fileName: "\(key).json")
    }
    
    /// Retrieves cached data
    /// - Parameter key: The key to use for retrieval
    /// - Returns: The cached data, or nil if no data is cached
    func getCachedData<T: Decodable>(forKey key: String) -> T? {
        return DataPersistenceService.shared.load(fileName: "\(key).json")
    }
    
    /// Checks if data is cached
    /// - Parameter key: The key to check
    /// - Returns: A boolean indicating whether data is cached
    func isCached(forKey key: String) -> Bool {
        return DataPersistenceService.shared.exists(fileName: "\(key).json")
    }
    
    /// Gets the modification date of cached data
    /// - Parameter key: The key to check
    /// - Returns: The modification date, or nil if no data is cached
    func getCacheDate(forKey key: String) -> Date? {
        return DataPersistenceService.shared.getModificationDate(fileName: "\(key).json")
    }
    
    /// Clears all cached data
    func clearCache() {
        _ = DataPersistenceService.shared.clearCache()
    }
    
    // MARK: - Offline Status
    
    /// Returns a boolean indicating whether the app is offline
    var isAppOffline: Bool {
        return isOffline
    }
    
    /// Returns a boolean indicating whether there are pending actions
    var hasPendingActions: Bool {
        return !pendingActions.isEmpty
    }
    
    /// Returns the number of pending actions
    var pendingActionCount: Int {
        return pendingActions.count
    }
}

/// Represents a pending action
struct PendingAction: Codable, Identifiable {
    let id: UUID
    let type: ActionType
    let endpoint: String
    let data: [String: String]
    let createdAt: Date
    
    enum ActionType: String, Codable {
        case create
        case update
        case delete
    }
    
    init(type: ActionType, endpoint: String, data: [String: String]) {
        self.id = UUID()
        self.type = type
        self.endpoint = endpoint
        self.data = data
        self.createdAt = Date()
    }
}
