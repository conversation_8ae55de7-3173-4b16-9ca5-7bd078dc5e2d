import Foundation

/// App configuration
struct Config {
    /// Environment
    enum Environment: String {
        case development
        case staging
        case production
    }
    
    /// Current environment
    static var environment: Environment {
        #if DEBUG
        return .development
        #else
        return .production
        #endif
    }
    
    /// Base URL
    static var baseURL: String {
        switch environment {
        case .development:
            #if targetEnvironment(simulator)
            return "http://localhost:8000"  // For simulator testing
            #else
            return "http://192.168.1.XXX:8000"  // Replace with your actual IP for device testing
            #endif
        case .staging:
            return "https://staging-api.example.com"
        case .production:
            return "https://api.example.com"
        }
    }
    
    /// API URL
    static var apiURL: String {
        return baseURL + "/api"
    }
    
    /// WebSocket URL
    static var webSocketURL: String {
        switch environment {
        case .development:
            #if targetEnvironment(simulator)
            return "ws://localhost:8000/ws"  // For simulator testing
            #else
            return "ws://192.168.1.XXX:8000/ws"  // Replace with your actual IP for device testing
            #endif
        case .staging:
            return "wss://staging-api.example.com/ws"
        case .production:
            return "wss://api.example.com/ws"
        }
    }
    
    /// App version
    static var appVersion: String {
        if let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            return version
        }
        return "1.0.0"
    }
    
    /// Build number
    static var buildNumber: String {
        if let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String {
            return build
        }
        return "1"
    }
    
    /// Full version string
    static var versionString: String {
        return "v\(appVersion) (\(buildNumber))"
    }
    
    /// UserDefaults keys
    struct UserDefaultsKeys {
        static let authToken = "com.amp.auth.token"
        static let user = "com.amp.auth.user"
        static let refreshToken = "com.amp.auth.refreshToken"
        static let appTheme = "com.amp.settings.theme"
    }
    
    /// Keychain keys
    struct KeychainKeys {
        static let authToken = "com.amp.auth.token"
        static let refreshToken = "com.amp.auth.refreshToken"
    }
}
