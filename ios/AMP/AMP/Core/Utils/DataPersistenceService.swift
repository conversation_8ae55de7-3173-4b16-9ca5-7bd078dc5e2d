import Foundation
import Combine

/// Service for persisting data locally
class DataPersistenceService {
    static let shared = DataPersistenceService()
    
    private let fileManager = FileManager.default
    private let decoder = JSONDecoder()
    private let encoder = JSONEncoder()
    
    private init() {
        // Set up decoder and encoder
        decoder.keyDecodingStrategy = .convertFromSnakeCase
        decoder.dateDecodingStrategy = .iso8601
        
        encoder.keyEncodingStrategy = .convertToSnakeCase
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = .prettyPrinted
        
        // Create cache directory if it doesn't exist
        createCacheDirectoryIfNeeded()
    }
    
    // MARK: - Directory Management
    
    /// Returns the URL for the cache directory
    private func getCacheDirectory() -> URL {
        let paths = fileManager.urls(for: .cachesDirectory, in: .userDomainMask)
        let cacheDirectory = paths[0].appendingPathComponent("com.amp.offlineCache", isDirectory: true)
        return cacheDirectory
    }
    
    /// Creates the cache directory if it doesn't exist
    private func createCacheDirectoryIfNeeded() {
        let cacheDirectory = getCacheDirectory()
        
        if !fileManager.fileExists(atPath: cacheDirectory.path) {
            do {
                try fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true, attributes: nil)
            } catch {
                print("Error creating cache directory: \(error)")
            }
        }
    }
    
    // MARK: - Save Data
    
    /// Saves data to a file
    /// - Parameters:
    ///   - data: The data to save
    ///   - fileName: The name of the file
    /// - Returns: A boolean indicating whether the save was successful
    func save<T: Encodable>(_ data: T, fileName: String) -> Bool {
        let fileURL = getCacheDirectory().appendingPathComponent(fileName)
        
        do {
            let data = try encoder.encode(data)
            try data.write(to: fileURL, options: .atomic)
            return true
        } catch {
            print("Error saving data: \(error)")
            return false
        }
    }
    
    // MARK: - Load Data
    
    /// Loads data from a file
    /// - Parameter fileName: The name of the file
    /// - Returns: The loaded data, or nil if the file doesn't exist or couldn't be loaded
    func load<T: Decodable>(fileName: String) -> T? {
        let fileURL = getCacheDirectory().appendingPathComponent(fileName)
        
        guard fileManager.fileExists(atPath: fileURL.path) else {
            return nil
        }
        
        do {
            let data = try Data(contentsOf: fileURL)
            let decodedData = try decoder.decode(T.self, from: data)
            return decodedData
        } catch {
            print("Error loading data: \(error)")
            return nil
        }
    }
    
    // MARK: - Delete Data
    
    /// Deletes a file
    /// - Parameter fileName: The name of the file
    /// - Returns: A boolean indicating whether the delete was successful
    func delete(fileName: String) -> Bool {
        let fileURL = getCacheDirectory().appendingPathComponent(fileName)
        
        guard fileManager.fileExists(atPath: fileURL.path) else {
            return true
        }
        
        do {
            try fileManager.removeItem(at: fileURL)
            return true
        } catch {
            print("Error deleting file: \(error)")
            return false
        }
    }
    
    // MARK: - Check Data
    
    /// Checks if a file exists
    /// - Parameter fileName: The name of the file
    /// - Returns: A boolean indicating whether the file exists
    func exists(fileName: String) -> Bool {
        let fileURL = getCacheDirectory().appendingPathComponent(fileName)
        return fileManager.fileExists(atPath: fileURL.path)
    }
    
    // MARK: - Get File Date
    
    /// Gets the modification date of a file
    /// - Parameter fileName: The name of the file
    /// - Returns: The modification date, or nil if the file doesn't exist
    func getModificationDate(fileName: String) -> Date? {
        let fileURL = getCacheDirectory().appendingPathComponent(fileName)
        
        guard fileManager.fileExists(atPath: fileURL.path) else {
            return nil
        }
        
        do {
            let attributes = try fileManager.attributesOfItem(atPath: fileURL.path)
            return attributes[.modificationDate] as? Date
        } catch {
            print("Error getting file attributes: \(error)")
            return nil
        }
    }
    
    // MARK: - Clear Cache
    
    /// Clears all cached data
    /// - Returns: A boolean indicating whether the clear was successful
    func clearCache() -> Bool {
        let cacheDirectory = getCacheDirectory()
        
        do {
            let fileURLs = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil, options: [])
            
            for fileURL in fileURLs {
                try fileManager.removeItem(at: fileURL)
            }
            
            return true
        } catch {
            print("Error clearing cache: \(error)")
            return false
        }
    }
}
