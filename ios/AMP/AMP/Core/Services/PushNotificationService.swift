import Foundation
import UserNotifications
import UIKit
import Combine

/// Service for managing push notifications
class PushNotificationService: NSObject, ObservableObject {
    @Published var isAuthorized = false
    @Published var deviceToken: String?
    @Published var lastNotification: UNNotificationRequest?
    
    private var cancellables = Set<AnyCancellable>()
    
    override init() {
        super.init()
        checkAuthorizationStatus()
    }
    
    // MARK: - Authorization
    
    /// Requests permission for push notifications
    func requestPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { [weak self] granted, error in
            DispatchQueue.main.async {
                self?.isAuthorized = granted
                
                if granted {
                    self?.registerForRemoteNotifications()
                }
                
                if let error = error {
                    print("Push notification permission error: \(error)")
                }
            }
        }
    }
    
    /// Checks the current authorization status
    private func checkAuthorizationStatus() {
        UNUserNotificationCenter.current().getNotificationSettings { [weak self] settings in
            DispatchQueue.main.async {
                self?.isAuthorized = settings.authorizationStatus == .authorized
            }
        }
    }
    
    /// Registers for remote notifications
    private func registerForRemoteNotifications() {
        DispatchQueue.main.async {
            UIApplication.shared.registerForRemoteNotifications()
        }
    }
    
    // MARK: - Device Token
    
    /// Sets the device token
    func setDeviceToken(_ token: Data) {
        let tokenString = token.map { String(format: "%02.2hhx", $0) }.joined()
        self.deviceToken = tokenString
        
        // Send token to server
        sendTokenToServer(tokenString)
    }
    
    /// Sends the device token to the server
    private func sendTokenToServer(_ token: String) {
        // In a real app, you would send this to your backend
        print("Device token: \(token)")
        
        // Store token locally for now
        UserDefaults.standard.set(token, forKey: "deviceToken")
    }
    
    // MARK: - Local Notifications
    
    /// Schedules a local notification
    func scheduleLocalNotification(
        title: String,
        body: String,
        timeInterval: TimeInterval = 1,
        userInfo: [String: Any] = [:]
    ) {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = .default
        content.userInfo = userInfo
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: timeInterval, repeats: false)
        let request = UNNotificationRequest(
            identifier: UUID().uuidString,
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error scheduling local notification: \(error)")
            }
        }
    }
    
    /// Schedules a notification for a specific date
    func scheduleNotification(
        title: String,
        body: String,
        date: Date,
        userInfo: [String: Any] = [:]
    ) {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = .default
        content.userInfo = userInfo
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: date)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)
        
        let request = UNNotificationRequest(
            identifier: UUID().uuidString,
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error scheduling notification: \(error)")
            }
        }
    }
    
    // MARK: - Badge Management
    
    /// Updates the app badge count
    func updateBadgeCount(_ count: Int) {
        DispatchQueue.main.async {
            UIApplication.shared.applicationIconBadgeNumber = count
        }
    }
    
    /// Clears the app badge
    func clearBadge() {
        updateBadgeCount(0)
    }
    
    // MARK: - Notification Management
    
    /// Gets pending notifications
    func getPendingNotifications(completion: @escaping ([UNNotificationRequest]) -> Void) {
        UNUserNotificationCenter.current().getPendingNotificationRequests { requests in
            DispatchQueue.main.async {
                completion(requests)
            }
        }
    }
    
    /// Removes pending notifications
    func removePendingNotifications(withIdentifiers identifiers: [String]) {
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: identifiers)
    }
    
    /// Removes all pending notifications
    func removeAllPendingNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
    }
    
    /// Gets delivered notifications
    func getDeliveredNotifications(completion: @escaping ([UNNotification]) -> Void) {
        UNUserNotificationCenter.current().getDeliveredNotifications { notifications in
            DispatchQueue.main.async {
                completion(notifications)
            }
        }
    }
    
    /// Removes delivered notifications
    func removeDeliveredNotifications(withIdentifiers identifiers: [String]) {
        UNUserNotificationCenter.current().removeDeliveredNotifications(withIdentifiers: identifiers)
    }
    
    /// Removes all delivered notifications
    func removeAllDeliveredNotifications() {
        UNUserNotificationCenter.current().removeAllDeliveredNotifications()
    }
}

// MARK: - UNUserNotificationCenterDelegate

extension PushNotificationService: UNUserNotificationCenterDelegate {
    /// Called when a notification is received while the app is in the foreground
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        lastNotification = notification.request
        
        // Show the notification even when the app is in the foreground
        completionHandler([.alert, .badge, .sound])
    }
    
    /// Called when the user taps on a notification
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        lastNotification = response.notification.request
        
        // Handle the notification tap
        handleNotificationTap(response.notification.request)
        
        completionHandler()
    }
    
    /// Handles notification tap
    private func handleNotificationTap(_ request: UNNotificationRequest) {
        let userInfo = request.content.userInfo
        
        // Handle different notification types
        if let type = userInfo["type"] as? String {
            switch type {
            case "order":
                // Navigate to orders
                NotificationCenter.default.post(name: .navigateToOrders, object: nil)
            case "inventory":
                // Navigate to inventory
                NotificationCenter.default.post(name: .navigateToInventory, object: nil)
            case "vendor":
                // Navigate to vendors
                NotificationCenter.default.post(name: .navigateToVendors, object: nil)
            case "note":
                // Navigate to notes
                NotificationCenter.default.post(name: .navigateToNotes, object: nil)
            default:
                // Navigate to dashboard
                NotificationCenter.default.post(name: .navigateToDashboard, object: nil)
            }
        }
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let navigateToDashboard = Notification.Name("navigateToDashboard")
    static let navigateToOrders = Notification.Name("navigateToOrders")
    static let navigateToInventory = Notification.Name("navigateToInventory")
    static let navigateToVendors = Notification.Name("navigateToVendors")
    static let navigateToNotes = Notification.Name("navigateToNotes")
}
