import Foundation
import Combine

/// Service for tracking analytics events
class AnalyticsService: ObservableObject {
    static let shared = AnalyticsService()
    
    @Published var events: [AnalyticsEvent] = []
    @Published var sessionStartTime: Date?
    @Published var sessionDuration: TimeInterval = 0
    
    private var sessionTimer: Timer?
    private let maxEventsToStore = 1000
    
    private init() {
        startSession()
        loadStoredEvents()
    }
    
    // MARK: - Session Management
    
    /// Starts a new analytics session
    func startSession() {
        sessionStartTime = Date()
        sessionDuration = 0
        
        // Start session timer
        sessionTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateSessionDuration()
        }
        
        // Track session start
        trackEvent(
            name: "session_start",
            category: .system,
            properties: [
                "timestamp": ISO8601DateFormatter().string(from: Date()),
                "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "unknown",
                "device_model": UIDevice.current.model,
                "os_version": UIDevice.current.systemVersion
            ]
        )
    }
    
    /// Ends the current analytics session
    func endSession() {
        sessionTimer?.invalidate()
        sessionTimer = nil
        
        // Track session end
        trackEvent(
            name: "session_end",
            category: .system,
            properties: [
                "duration": String(sessionDuration),
                "timestamp": ISO8601DateFormatter().string(from: Date())
            ]
        )
        
        sessionStartTime = nil
        sessionDuration = 0
    }
    
    /// Updates the session duration
    private func updateSessionDuration() {
        guard let startTime = sessionStartTime else { return }
        sessionDuration = Date().timeIntervalSince(startTime)
    }
    
    // MARK: - Event Tracking
    
    /// Tracks an analytics event
    func trackEvent(
        name: String,
        category: EventCategory,
        properties: [String: String] = [:]
    ) {
        let event = AnalyticsEvent(
            name: name,
            category: category,
            properties: properties,
            timestamp: Date()
        )
        
        events.append(event)
        
        // Limit the number of stored events
        if events.count > maxEventsToStore {
            events.removeFirst(events.count - maxEventsToStore)
        }
        
        // Store events locally
        storeEvents()
        
        // Send to analytics service (in a real app)
        sendEventToService(event)
    }
    
    /// Tracks a screen view
    func trackScreenView(_ screenName: String) {
        trackEvent(
            name: "screen_view",
            category: .navigation,
            properties: [
                "screen_name": screenName,
                "timestamp": ISO8601DateFormatter().string(from: Date())
            ]
        )
    }
    
    /// Tracks a user action
    func trackUserAction(_ action: String, target: String? = nil) {
        var properties = [
            "action": action,
            "timestamp": ISO8601DateFormatter().string(from: Date())
        ]
        
        if let target = target {
            properties["target"] = target
        }
        
        trackEvent(
            name: "user_action",
            category: .interaction,
            properties: properties
        )
    }
    
    /// Tracks an error
    func trackError(_ error: String, context: String? = nil) {
        var properties = [
            "error": error,
            "timestamp": ISO8601DateFormatter().string(from: Date())
        ]
        
        if let context = context {
            properties["context"] = context
        }
        
        trackEvent(
            name: "error",
            category: .error,
            properties: properties
        )
    }
    
    /// Tracks a performance metric
    func trackPerformance(metric: String, value: Double, unit: String = "ms") {
        trackEvent(
            name: "performance",
            category: .performance,
            properties: [
                "metric": metric,
                "value": String(value),
                "unit": unit,
                "timestamp": ISO8601DateFormatter().string(from: Date())
            ]
        )
    }
    
    // MARK: - Data Management
    
    /// Stores events locally
    private func storeEvents() {
        _ = DataPersistenceService.shared.save(events, fileName: "analytics_events.json")
    }
    
    /// Loads stored events
    private func loadStoredEvents() {
        if let storedEvents: [AnalyticsEvent] = DataPersistenceService.shared.load(fileName: "analytics_events.json") {
            events = storedEvents
        }
    }
    
    /// Clears all stored events
    func clearEvents() {
        events.removeAll()
        _ = DataPersistenceService.shared.delete(fileName: "analytics_events.json")
    }
    
    /// Sends event to analytics service
    private func sendEventToService(_ event: AnalyticsEvent) {
        // In a real app, you would send this to your analytics service
        // For now, we'll just print it
        print("Analytics Event: \(event.name) - \(event.category.rawValue)")
        print("Properties: \(event.properties)")
    }
    
    // MARK: - Analytics Queries
    
    /// Gets events by category
    func getEvents(by category: EventCategory) -> [AnalyticsEvent] {
        return events.filter { $0.category == category }
    }
    
    /// Gets events by name
    func getEvents(by name: String) -> [AnalyticsEvent] {
        return events.filter { $0.name == name }
    }
    
    /// Gets events in date range
    func getEvents(from startDate: Date, to endDate: Date) -> [AnalyticsEvent] {
        return events.filter { event in
            event.timestamp >= startDate && event.timestamp <= endDate
        }
    }
    
    /// Gets event count by category
    func getEventCount(by category: EventCategory) -> Int {
        return getEvents(by: category).count
    }
    
    /// Gets most frequent events
    func getMostFrequentEvents(limit: Int = 10) -> [(String, Int)] {
        let eventCounts = Dictionary(grouping: events, by: { $0.name })
            .mapValues { $0.count }
        
        return eventCounts.sorted { $0.value > $1.value }
            .prefix(limit)
            .map { ($0.key, $0.value) }
    }
    
    deinit {
        endSession()
    }
}

/// Represents an analytics event
struct AnalyticsEvent: Codable, Identifiable {
    let id: UUID
    let name: String
    let category: EventCategory
    let properties: [String: String]
    let timestamp: Date
    
    init(name: String, category: EventCategory, properties: [String: String], timestamp: Date) {
        self.id = UUID()
        self.name = name
        self.category = category
        self.properties = properties
        self.timestamp = timestamp
    }
}

/// Event categories for analytics
enum EventCategory: String, Codable, CaseIterable {
    case system = "system"
    case navigation = "navigation"
    case interaction = "interaction"
    case error = "error"
    case performance = "performance"
    case business = "business"
}
