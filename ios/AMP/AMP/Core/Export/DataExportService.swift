import Foundation
import UIKit
import Combine

/// Service for exporting data in various formats
class DataExportService: ObservableObject {
    @Published var isExporting = false
    @Published var exportProgress: Double = 0.0
    @Published var lastExportURL: URL?
    
    private let fileManager = FileManager.default
    private let documentsDirectory: URL
    
    init() {
        documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
    }
    
    // MARK: - Export Methods
    
    /// Exports orders to CSV format
    /// - Parameters:
    ///   - orders: Array of orders to export
    ///   - completion: Completion handler with export result
    func exportOrdersToCSV(_ orders: [Order], completion: @escaping (Result<URL, Error>) -> Void) {
        isExporting = true
        exportProgress = 0.0
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            do {
                let csvContent = self?.generateOrdersCSV(orders) ?? ""
                let fileName = "orders_export_\(self?.dateString() ?? "").csv"
                let fileURL = self?.documentsDirectory.appendingPathComponent(fileName)
                
                guard let url = fileURL else {
                    throw ExportError.fileCreationFailed
                }
                
                try csvContent.write(to: url, atomically: true, encoding: .utf8)
                
                DispatchQueue.main.async {
                    self?.isExporting = false
                    self?.exportProgress = 1.0
                    self?.lastExportURL = url
                    completion(.success(url))
                }
            } catch {
                DispatchQueue.main.async {
                    self?.isExporting = false
                    self?.exportProgress = 0.0
                    completion(.failure(error))
                }
            }
        }
    }
    
    /// Exports inventory to CSV format
    /// - Parameters:
    ///   - products: Array of products to export
    ///   - completion: Completion handler with export result
    func exportInventoryToCSV(_ products: [Product], completion: @escaping (Result<URL, Error>) -> Void) {
        isExporting = true
        exportProgress = 0.0
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            do {
                let csvContent = self?.generateInventoryCSV(products) ?? ""
                let fileName = "inventory_export_\(self?.dateString() ?? "").csv"
                let fileURL = self?.documentsDirectory.appendingPathComponent(fileName)
                
                guard let url = fileURL else {
                    throw ExportError.fileCreationFailed
                }
                
                try csvContent.write(to: url, atomically: true, encoding: .utf8)
                
                DispatchQueue.main.async {
                    self?.isExporting = false
                    self?.exportProgress = 1.0
                    self?.lastExportURL = url
                    completion(.success(url))
                }
            } catch {
                DispatchQueue.main.async {
                    self?.isExporting = false
                    self?.exportProgress = 0.0
                    completion(.failure(error))
                }
            }
        }
    }
    
    /// Exports vendors to CSV format
    /// - Parameters:
    ///   - vendors: Array of vendors to export
    ///   - completion: Completion handler with export result
    func exportVendorsToCSV(_ vendors: [Vendor], completion: @escaping (Result<URL, Error>) -> Void) {
        isExporting = true
        exportProgress = 0.0
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            do {
                let csvContent = self?.generateVendorsCSV(vendors) ?? ""
                let fileName = "vendors_export_\(self?.dateString() ?? "").csv"
                let fileURL = self?.documentsDirectory.appendingPathComponent(fileName)
                
                guard let url = fileURL else {
                    throw ExportError.fileCreationFailed
                }
                
                try csvContent.write(to: url, atomically: true, encoding: .utf8)
                
                DispatchQueue.main.async {
                    self?.isExporting = false
                    self?.exportProgress = 1.0
                    self?.lastExportURL = url
                    completion(.success(url))
                }
            } catch {
                DispatchQueue.main.async {
                    self?.isExporting = false
                    self?.exportProgress = 0.0
                    completion(.failure(error))
                }
            }
        }
    }
    
    /// Exports data to JSON format
    /// - Parameters:
    ///   - data: Codable data to export
    ///   - fileName: Name of the file
    ///   - completion: Completion handler with export result
    func exportToJSON<T: Codable>(_ data: T, fileName: String, completion: @escaping (Result<URL, Error>) -> Void) {
        isExporting = true
        exportProgress = 0.0
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            do {
                let encoder = JSONEncoder()
                encoder.outputFormatting = .prettyPrinted
                encoder.dateEncodingStrategy = .iso8601
                
                let jsonData = try encoder.encode(data)
                let fullFileName = "\(fileName)_\(self?.dateString() ?? "").json"
                let fileURL = self?.documentsDirectory.appendingPathComponent(fullFileName)
                
                guard let url = fileURL else {
                    throw ExportError.fileCreationFailed
                }
                
                try jsonData.write(to: url)
                
                DispatchQueue.main.async {
                    self?.isExporting = false
                    self?.exportProgress = 1.0
                    self?.lastExportURL = url
                    completion(.success(url))
                }
            } catch {
                DispatchQueue.main.async {
                    self?.isExporting = false
                    self?.exportProgress = 0.0
                    completion(.failure(error))
                }
            }
        }
    }
    
    /// Exports analytics data to CSV
    /// - Parameters:
    ///   - events: Array of analytics events
    ///   - completion: Completion handler with export result
    func exportAnalyticsToCSV(_ events: [AnalyticsEvent], completion: @escaping (Result<URL, Error>) -> Void) {
        isExporting = true
        exportProgress = 0.0
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            do {
                let csvContent = self?.generateAnalyticsCSV(events) ?? ""
                let fileName = "analytics_export_\(self?.dateString() ?? "").csv"
                let fileURL = self?.documentsDirectory.appendingPathComponent(fileName)
                
                guard let url = fileURL else {
                    throw ExportError.fileCreationFailed
                }
                
                try csvContent.write(to: url, atomically: true, encoding: .utf8)
                
                DispatchQueue.main.async {
                    self?.isExporting = false
                    self?.exportProgress = 1.0
                    self?.lastExportURL = url
                    completion(.success(url))
                }
            } catch {
                DispatchQueue.main.async {
                    self?.isExporting = false
                    self?.exportProgress = 0.0
                    completion(.failure(error))
                }
            }
        }
    }
    
    // MARK: - CSV Generation
    
    private func generateOrdersCSV(_ orders: [Order]) -> String {
        var csv = "Order ID,Product Name,Vendor Name,Quantity,Price,Status,Created Date,Updated Date\n"
        
        for order in orders {
            let row = [
                "\(order.id)",
                escapeCSVField(order.product.name),
                escapeCSVField(order.vendor.name),
                "\(order.quantity)",
                String(format: "%.2f", order.price),
                order.statusText,
                order.formattedCreatedDate,
                order.formattedUpdatedDate
            ].joined(separator: ",")
            
            csv += row + "\n"
        }
        
        return csv
    }
    
    private func generateInventoryCSV(_ products: [Product]) -> String {
        var csv = "Product ID,Name,Description,Price,Vendor ID,Category ID,Active,Created Date,Updated Date\n"
        
        for product in products {
            let row = [
                "\(product.id)",
                escapeCSVField(product.name),
                escapeCSVField(product.description),
                String(format: "%.2f", product.price),
                "\(product.vendorId)",
                "\(product.categoryId)",
                product.isActive ? "Yes" : "No",
                product.formattedCreatedDate,
                product.formattedUpdatedDate
            ].joined(separator: ",")
            
            csv += row + "\n"
        }
        
        return csv
    }
    
    private func generateVendorsCSV(_ vendors: [Vendor]) -> String {
        var csv = "Vendor ID,Name,Email,Phone,Address,Active,Created Date,Updated Date\n"
        
        for vendor in vendors {
            let row = [
                "\(vendor.id)",
                escapeCSVField(vendor.name),
                escapeCSVField(vendor.email),
                escapeCSVField(vendor.phone),
                escapeCSVField(vendor.address),
                vendor.isActive ? "Yes" : "No",
                vendor.formattedCreatedDate,
                vendor.formattedUpdatedDate
            ].joined(separator: ",")
            
            csv += row + "\n"
        }
        
        return csv
    }
    
    private func generateAnalyticsCSV(_ events: [AnalyticsEvent]) -> String {
        var csv = "Event ID,Name,Category,Timestamp,Properties\n"
        
        for event in events {
            let propertiesString = event.properties.map { "\($0.key):\($0.value)" }.joined(separator: ";")
            
            let row = [
                event.id.uuidString,
                escapeCSVField(event.name),
                event.category.rawValue,
                ISO8601DateFormatter().string(from: event.timestamp),
                escapeCSVField(propertiesString)
            ].joined(separator: ",")
            
            csv += row + "\n"
        }
        
        return csv
    }
    
    private func escapeCSVField(_ field: String) -> String {
        if field.contains(",") || field.contains("\"") || field.contains("\n") {
            return "\"" + field.replacingOccurrences(of: "\"", with: "\"\"") + "\""
        }
        return field
    }
    
    // MARK: - Utility Methods
    
    private func dateString() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        return formatter.string(from: Date())
    }
    
    /// Shares a file using the system share sheet
    /// - Parameters:
    ///   - url: URL of the file to share
    ///   - sourceView: Source view for iPad presentation
    func shareFile(_ url: URL, from sourceView: UIView? = nil) {
        let activityViewController = UIActivityViewController(activityItems: [url], applicationActivities: nil)
        
        // For iPad
        if let popover = activityViewController.popoverPresentationController {
            if let sourceView = sourceView {
                popover.sourceView = sourceView
                popover.sourceRect = sourceView.bounds
            } else {
                popover.sourceView = UIApplication.shared.windows.first?.rootViewController?.view
                popover.sourceRect = CGRect(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 2, width: 0, height: 0)
                popover.permittedArrowDirections = []
            }
        }
        
        UIApplication.shared.windows.first?.rootViewController?.present(activityViewController, animated: true)
    }
    
    /// Gets the list of exported files
    /// - Returns: Array of exported file URLs
    func getExportedFiles() -> [URL] {
        do {
            let files = try fileManager.contentsOfDirectory(at: documentsDirectory, includingPropertiesForKeys: [.creationDateKey], options: [])
            return files.filter { url in
                let pathExtension = url.pathExtension.lowercased()
                return pathExtension == "csv" || pathExtension == "json"
            }.sorted { url1, url2 in
                let date1 = (try? url1.resourceValues(forKeys: [.creationDateKey]))?.creationDate ?? Date.distantPast
                let date2 = (try? url2.resourceValues(forKeys: [.creationDateKey]))?.creationDate ?? Date.distantPast
                return date1 > date2
            }
        } catch {
            print("Error getting exported files: \(error)")
            return []
        }
    }
    
    /// Deletes an exported file
    /// - Parameter url: URL of the file to delete
    func deleteExportedFile(_ url: URL) {
        try? fileManager.removeItem(at: url)
    }
    
    /// Clears all exported files
    func clearAllExports() {
        let exportedFiles = getExportedFiles()
        for file in exportedFiles {
            try? fileManager.removeItem(at: file)
        }
    }
}

/// Errors that can occur during export
enum ExportError: Error, LocalizedError {
    case fileCreationFailed
    case encodingFailed
    case noDataToExport
    
    var errorDescription: String? {
        switch self {
        case .fileCreationFailed:
            return "Failed to create export file"
        case .encodingFailed:
            return "Failed to encode data for export"
        case .noDataToExport:
            return "No data available to export"
        }
    }
}
