//// swift-tools-version: 5.9
//// The swift-tools-version declares the minimum version of Swift required to build this package.
//
//import PackageDescription
//
//let package = Package(
//    name: "AMP",
//    platforms: [
//        .iOS(.v15),
//        .macOS(.v12)
//    ],
//    products: [
//        .library(
//            name: "AMP",
//            targets: ["AMP"]
//        ),
//    ],
//    dependencies: [
//        // SwiftUI Charts for data visualization
//        .package(url: "https://github.com/AppPear/ChartView", from: "2.0.0"),
//        
//        // Networking and WebSocket support
//        .package(url: "https://github.com/Alamofire/Alamofire", from: "5.8.0"),
//        .package(url: "https://github.com/daltoniam/Starscream", from: "4.0.0"),
//        
//        // Image loading and caching
//        .package(url: "https://github.com/SDWebImage/SDWebImageSwiftUI", from: "2.2.0"),
//        
//        // Keychain wrapper
//        .package(url: "https://github.com/evgenyneu/keychain-swift", from: "20.0.0"),
//        
//        // JSON parsing and utilities
//        .package(url: "https://github.com/SwiftyJSON/SwiftyJSON", from: "5.0.0"),
//        
//        // Lottie animations (optional)
//        .package(url: "https://github.com/airbnb/lottie-ios", from: "4.3.0"),
//        
//        // QR Code scanning
//        .package(url: "https://github.com/twostraws/CodeScanner", from: "2.3.0"),
//        
//        // Biometric authentication helper
//        .package(url: "https://github.com/rushisangani/BiometricAuthentication", from: "3.2.0"),
//    ],
//    targets: [
//        .target(
//            name: "AMP",
//            dependencies: [
//                "ChartView",
//                "Alamofire",
//                "Starscream",
//                .product(name: "SDWebImageSwiftUI", package: "SDWebImageSwiftUI"),
//                .product(name: "KeychainSwift", package: "keychain-swift"),
//                "SwiftyJSON",
//                .product(name: "Lottie", package: "lottie-ios"),
//                "CodeScanner",
//                "BiometricAuthentication"
//            ]
//        ),
//        .testTarget(
//            name: "AMPTests",
//            dependencies: ["AMP"]
//        ),
//    ]
//)
