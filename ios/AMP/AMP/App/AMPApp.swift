import SwiftUI

@main
struct AMPApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    // Services
    @StateObject private var authService = AuthService()
    @StateObject private var themeService = ThemeService()
    @StateObject private var orderService = OrderService()
    @StateObject private var dashboardService = DashboardService()
    @StateObject private var inventoryService = InventoryService()
    @StateObject private var vendorService = VendorService()
    @StateObject private var noteService = NoteService()
    @StateObject private var webSocketService = WebSocketService()
    @StateObject private var offlineManager = OfflineManager()
    @StateObject private var userManagementService = UserManagementService()
    @StateObject private var pushNotificationService = PushNotificationService()
    @StateObject private var biometricAuthService = BiometricAuthService()
    @StateObject private var accessibilityService = AccessibilityService.shared
    @StateObject private var performanceMonitor = PerformanceMonitor.shared
    @StateObject private var localizationService = LocalizationService.shared
    @StateObject private var searchService = SearchService(
        orderService: OrderService(),
        inventoryService: InventoryService(),
        vendorService: VendorService(),
        noteService: NoteService(),
        userManagementService: UserManagementService()
    )

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(authService)
                .environmentObject(themeService)
                .environmentObject(orderService)
                .environmentObject(dashboardService)
                .environmentObject(inventoryService)
                .environmentObject(vendorService)
                .environmentObject(noteService)
                .environmentObject(webSocketService)
                .environmentObject(offlineManager)
                .environmentObject(userManagementService)
                .environmentObject(pushNotificationService)
                .environmentObject(biometricAuthService)
                .environmentObject(accessibilityService)
                .environmentObject(performanceMonitor)
                .environmentObject(localizationService)
                .environmentObject(searchService)
        }
    }
}
