import UIKit
import SwiftUI
import UserNotifications

class AppDelegate: NSObject, UIApplicationDelegate {
    var pushNotificationService: PushNotificationService?

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        // Start network monitoring
        NetworkMonitor.shared.startMonitoring()

        // Set up push notifications
        setupPushNotifications()

        return true
    }

    func applicationWillTerminate(_ application: UIApplication) {
        // Stop network monitoring
        NetworkMonitor.shared.stopMonitoring()
    }

    // MARK: - Push Notifications Setup

    private func setupPushNotifications() {
        pushNotificationService = PushNotificationService()

        // Set the notification center delegate
        UNUserNotificationCenter.current().delegate = pushNotificationService

        // Request permission
        pushNotificationService?.requestPermission()
    }

    // MARK: - Remote Notifications

    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        pushNotificationService?.setDeviceToken(deviceToken)
    }

    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("Failed to register for remote notifications: \(error)")
    }
}
