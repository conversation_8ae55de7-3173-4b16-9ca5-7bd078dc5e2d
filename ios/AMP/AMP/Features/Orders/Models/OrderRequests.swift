import Foundation

/// Request to create a new order
struct CreateOrderRequest: Codable {
    let adminId: Int
    let product: String
    let sku: String
    let vendor: String?
    let qty: Int
    let price: Double
    let saved: Double
    let wouldPaid: Double
    let paid: Double
    let urgent: Bool
    let timeline: String?
    let notes: String?
    let specific: Bool
    let status: Int
    
    enum CodingKeys: String, CodingKey {
        case adminId = "admin_id"
        case product
        case sku = "s_k_u"
        case vendor
        case qty
        case price
        case saved
        case wouldPaid = "would_paid"
        case paid
        case urgent
        case timeline
        case notes
        case specific
        case status
    }
}

/// Request to update an existing order
struct UpdateOrderRequest: Codable {
    let id: Int
    let adminId: Int
    let product: String
    let sku: String
    let vendor: String?
    let qty: Int
    let qtyDelivered: Int
    let qtyPending: Int
    let price: Double
    let saved: Double
    let wouldPaid: Double
    let paid: Double
    let urgent: Bool
    let timeline: String?
    let received: Bool
    let status: Int
    let notes: String?
    let specific: Bool
    let myProduct: Bool
    
    enum CodingKeys: String, CodingKey {
        case id
        case adminId = "admin_id"
        case product
        case sku = "s_k_u"
        case vendor
        case qty
        case qtyDelivered = "qty_delivered"
        case qtyPending = "qty_pending"
        case price
        case saved
        case wouldPaid = "would_paid"
        case paid
        case urgent
        case timeline
        case received
        case status
        case notes
        case specific
        case myProduct = "my_product"
    }
}

/// Request to update the favorite status of an order
struct UpdateFavoriteRequest: Codable {
    let id: Int
    let adminId: Int
    let myProduct: Bool
    
    enum CodingKeys: String, CodingKey {
        case id
        case adminId = "admin_id"
        case myProduct = "my_product"
    }
}

/// Request to update the paid status of an order
struct UpdatePaidRequest: Codable {
    let id: Int
    let adminId: Int
    let wouldPaid: Double
    let paid: Double
    let saved: Double
    
    enum CodingKeys: String, CodingKey {
        case id
        case adminId = "admin_id"
        case wouldPaid = "would_paid"
        case paid
        case saved
    }
}

/// Request to delete an order
struct DeleteOrderRequest: Codable {
    let id: Int
    let adminId: Int
    
    enum CodingKeys: String, CodingKey {
        case id
        case adminId = "admin_id"
    }
}
