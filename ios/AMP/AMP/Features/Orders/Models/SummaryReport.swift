import Foundation

/// Represents a summary report
struct SummaryReport: Codable {
    let totalOrders: Int
    let pendingOrders: Int
    let completedOrders: Int
    let totalSpent: Double
    let totalSaved: Double
    let recentOrders: [OrderSummary]
    
    enum CodingKeys: String, CodingKey {
        case totalOrders = "total_orders"
        case pendingOrders = "pending_orders"
        case completedOrders = "completed_orders"
        case totalSpent = "total_spent"
        case totalSaved = "total_saved"
        case recentOrders = "recent_orders"
    }
}

/// Represents a simplified order for summary reports
struct OrderSummary: Codable, Identifiable {
    let id: Int
    let orderNumber: String
    let status: Int
    let productName: String
    let price: Double
    let createdAt: String
    
    enum CodingKeys: String, CodingKey {
        case id
        case orderNumber = "order_number"
        case status
        case productName = "product_name"
        case price
        case createdAt = "created_at"
    }
    
    var statusText: String {
        switch status {
        case 1: return "Pending"
        case 2: return "Processing"
        case 3: return "Shipped"
        case 4: return "Delivered"
        case 5: return "Cancelled"
        default: return "Unknown"
        }
    }
    
    var statusColor: String {
        switch status {
        case 1: return "PendingColor"
        case 2: return "ProcessingColor"
        case 3: return "ShippedColor"
        case 4: return "DeliveredColor"
        case 5: return "CancelledColor"
        default: return "DefaultColor"
        }
    }
    
    var formattedDate: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        
        if let date = dateFormatter.date(from: createdAt) {
            dateFormatter.dateFormat = "MMM d, yyyy"
            return dateFormatter.string(from: date)
        }
        
        return createdAt
    }
}

/// Represents the response from the summary report endpoint
struct SummaryReportResponse: Codable {
    let summary: SummaryReport
    let stats: SummaryStats
}

/// Represents summary statistics
struct SummaryStats: Codable {
    let monthlySpending: [MonthlySpending]
    let categoryBreakdown: [CategoryBreakdown]
    
    enum CodingKeys: String, CodingKey {
        case monthlySpending = "monthly_spending"
        case categoryBreakdown = "category_breakdown"
    }
}

/// Represents monthly spending
struct MonthlySpending: Codable {
    let month: String
    let amount: Double
}

/// Represents category breakdown
struct CategoryBreakdown: Codable {
    let category: String
    let amount: Double
    let percentage: Double
}

/// Represents a year of summary reports
struct YearSummaryReport: Identifiable {
    let id = UUID()
    let year: Int
    var months: [MonthSummaryReport]
    
    var totalPaid: Double {
        return months.reduce(0) { $0 + $1.paid }
    }
    
    var totalWouldPaid: Double {
        return months.reduce(0) { $0 + $1.wouldPaid }
    }
    
    var totalSaved: Double {
        return months.reduce(0) { $0 + $1.saved }
    }
    
    var totalQty: Int {
        return months.reduce(0) { $0 + $1.qty }
    }
    
    var totalCount: Int {
        return months.reduce(0) { $0 + $1.countReport }
    }
}

/// Represents a month of summary reports
struct MonthSummaryReport: Identifiable {
    let id = UUID()
    let monthId: Int
    let name: String
    let paid: Double
    let wouldPaid: Double
    let saved: Double
    let qty: Int
    let countReport: Int
    
    init(monthId: Int, name: String, paid: Double, wouldPaid: Double, saved: Double, qty: Int, countReport: Int) {
        self.monthId = monthId
        self.name = name
        self.paid = paid
        self.wouldPaid = wouldPaid
        self.saved = saved
        self.qty = qty
        self.countReport = countReport
    }
}
