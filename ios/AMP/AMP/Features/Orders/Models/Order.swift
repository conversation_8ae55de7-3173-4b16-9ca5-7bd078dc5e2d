import Foundation

/// Represents an order in the system
struct Order: Codable, Identifiable, Equatable {
    let id: Int
    let orderNumber: String
    let status: Int
    let product: Product
    let vendor: Vendor?
    let qty: Int
    let qtyDelivered: Int
    let qtyPending: Int
    let price: Double
    let saved: Double
    let wouldPaid: Double
    let paid: Double
    let urgent: Bool
    let timeline: String?
    let received: Bool
    let favorite: Bool
    let admin: Int
    let createdAt: String
    let updatedAt: String
    let notes: String?
    
    /// Returns the status as a string
    var statusText: String {
        switch status {
        case 1: return "Pending"
        case 2: return "Processing"
        case 3: return "Shipped"
        case 4: return "Delivered"
        case 5: return "Cancelled"
        default: return "Unknown"
        }
    }
    
    /// Returns the status color
    var statusColor: String {
        switch status {
        case 1: return "PendingColor"
        case 2: return "ProcessingColor"
        case 3: return "ShippedColor"
        case 4: return "DeliveredColor"
        case 5: return "CancelledColor"
        default: return "DefaultColor"
        }
    }
    
    /// Returns the formatted date
    var formattedDate: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        
        if let date = dateFormatter.date(from: createdAt) {
            dateFormatter.dateFormat = "MMM d, yyyy"
            return dateFormatter.string(from: date)
        }
        
        return createdAt
    }
    
    /// Returns the total price
    var totalPrice: Double {
        return price * Double(qty)
    }
    
    /// Returns the total saved
    var totalSaved: Double {
        return saved * Double(qty)
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case orderNumber = "order_number"
        case status
        case product
        case vendor
        case qty
        case qtyDelivered = "qty_delivered"
        case qtyPending = "qty_pending"
        case price
        case saved
        case wouldPaid = "would_paid"
        case paid
        case urgent
        case timeline
        case received
        case favorite = "my_product"
        case admin
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case notes
    }
    
    static func == (lhs: Order, rhs: Order) -> Bool {
        return lhs.id == rhs.id
    }
}

/// Represents a product in an order
struct Product: Codable, Identifiable, Equatable {
    let id: Int
    let name: String
    let sku: String
    
    enum CodingKeys: String, CodingKey {
        case id
        case name
        case sku = "s_k_u"
    }
    
    static func == (lhs: Product, rhs: Product) -> Bool {
        return lhs.id == rhs.id
    }
}

/// Represents a vendor in an order
struct Vendor: Codable, Identifiable, Equatable {
    let id: Int
    let name: String
    
    static func == (lhs: Vendor, rhs: Vendor) -> Bool {
        return lhs.id == rhs.id
    }
}
