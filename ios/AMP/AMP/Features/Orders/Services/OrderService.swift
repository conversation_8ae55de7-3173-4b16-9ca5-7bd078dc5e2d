import Foundation
import Combine
import SwiftUI

/// Service for managing orders
class OrderService: ObservableObject {
    @Published var orders: [Order] = []
    @Published var isLoading = false
    @Published var error: String?
    @Published var summaryReport: SummaryReportResponse?
    @Published var yearSummaryReports: [YearSummaryReport] = []
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Get User Orders
    func getUserOrders(userId: Int, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.post(endpoint: "/order/list_user_orders/\(userId)/", token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (orders: [Order]) in
                    self?.orders = orders
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Create Order
    func createOrder(order: CreateOrderRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.post(endpoint: "/order/create_order/", body: order, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (orders: [Order]) in
                    self?.orders = orders
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Update Order
    func updateOrder(updateRequest: UpdateOrderRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.put(endpoint: "/order/update_order/", body: updateRequest, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (orders: [Order]) in
                    self?.orders = orders
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Update Favorite
    func updateFavorite(updateRequest: UpdateFavoriteRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.put(endpoint: "/order/update_favorite/", body: updateRequest, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (orders: [Order]) in
                    self?.orders = orders
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Update Paid Status
    func updatePaidStatus(updateRequest: UpdatePaidRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.put(endpoint: "/order/update_paid/", body: updateRequest, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (orders: [Order]) in
                    self?.orders = orders
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Delete Order
    func deleteOrder(deleteRequest: DeleteOrderRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.post(endpoint: "/order/delete_order/", body: deleteRequest, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (orders: [Order]) in
                    self?.orders = orders
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Get Summary Reports
    func getSummaryReports(userId: Int, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.get(endpoint: "/order/summery_reports/\(userId)/", token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (response: [String: [String: [String: Any]]]) in
                    self?.processSummaryReports(response)
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Process Summary Reports
    private func processSummaryReports(_ response: [String: [String: [String: Any]]]) {
        var yearReports: [YearSummaryReport] = []
        
        for (yearString, months) in response {
            guard let year = Int(yearString) else { continue }
            
            var monthReports: [MonthSummaryReport] = []
            
            for (monthString, data) in months {
                guard let monthId = Int(monthString),
                      let paid = data["paid"] as? Double,
                      let wouldPaid = data["would_paid"] as? Double,
                      let saved = data["saved"] as? Double,
                      let qty = data["qty"] as? Int,
                      let countReport = data["count_report"] as? Int,
                      let name = data["name"] as? String else { continue }
                
                let monthReport = MonthSummaryReport(
                    monthId: monthId,
                    name: name,
                    paid: paid,
                    wouldPaid: wouldPaid,
                    saved: saved,
                    qty: qty,
                    countReport: countReport
                )
                
                monthReports.append(monthReport)
            }
            
            // Sort months by month ID
            monthReports.sort { $0.monthId < $1.monthId }
            
            let yearReport = YearSummaryReport(year: year, months: monthReports)
            yearReports.append(yearReport)
        }
        
        // Sort years in descending order
        yearReports.sort { $0.year > $1.year }
        
        self.yearSummaryReports = yearReports
    }
    
    // MARK: - Filter Orders
    func filteredOrders(searchText: String, statusFilter: Int?, favoriteOnly: Bool) -> [Order] {
        return orders.filter { order in
            let matchesSearch = searchText.isEmpty || 
                order.product.name.localizedCaseInsensitiveContains(searchText) ||
                order.orderNumber.localizedCaseInsensitiveContains(searchText) ||
                (order.vendor?.name.localizedCaseInsensitiveContains(searchText) ?? false)
            
            let matchesStatus = statusFilter == nil || order.status == statusFilter
            
            let matchesFavorite = !favoriteOnly || order.favorite
            
            return matchesSearch && matchesStatus && matchesFavorite
        }
    }
    
    // MARK: - Get Pending Orders
    var pendingOrders: [Order] {
        return orders.filter { $0.status == 1 || $0.status == 2 || $0.status == 3 }
    }
    
    // MARK: - Get Completed Orders
    var completedOrders: [Order] {
        return orders.filter { $0.status == 4 }
    }
    
    // MARK: - Get Cancelled Orders
    var cancelledOrders: [Order] {
        return orders.filter { $0.status == 5 }
    }
    
    // MARK: - Get Favorite Orders
    var favoriteOrders: [Order] {
        return orders.filter { $0.favorite }
    }
    
    // MARK: - Get Urgent Orders
    var urgentOrders: [Order] {
        return orders.filter { $0.urgent }
    }
    
    // MARK: - Get Total Spent
    var totalSpent: Double {
        return orders.reduce(0) { $0 + $1.paid }
    }
    
    // MARK: - Get Total Saved
    var totalSaved: Double {
        return orders.reduce(0) { $0 + $1.saved }
    }
}
