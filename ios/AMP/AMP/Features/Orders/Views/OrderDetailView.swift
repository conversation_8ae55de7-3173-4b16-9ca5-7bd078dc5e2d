import SwiftUI

struct OrderDetailView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var orderService: OrderService
    
    let order: Order
    
    @State private var isEditing = false
    @State private var showingDeleteAlert = false
    @State private var showingActionSheet = false
    @State private var isUpdatingFavorite = false
    @State private var isUpdatingPaid = false
    @State private var isDeleting = false
    
    // Editable fields
    @State private var editedPrice = ""
    @State private var editedWouldPaid = ""
    @State private var editedPaid = ""
    @State private var editedSaved = ""
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Order Header
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text(order.product.name)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(Color("TextColor"))
                        
                        Spacer()
                        
                        // Favorite Button
                        Button(action: toggleFavorite) {
                            Image(systemName: order.favorite ? "star.fill" : "star")
                                .foregroundColor(order.favorite ? .yellow : Color("SecondaryColor"))
                                .font(.title2)
                        }
                        .disabled(isUpdatingFavorite)
                    }
                    
                    Text("Order #\(order.orderNumber)")
                        .font(.subheadline)
                        .foregroundColor(Color("SecondaryColor"))
                    
                    HStack {
                        Text(order.statusText)
                            .font(.subheadline)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(Color(order.statusColor).opacity(0.2))
                            .foregroundColor(Color(order.statusColor))
                            .cornerRadius(8)
                        
                        Spacer()
                        
                        Text(order.formattedDate)
                            .font(.subheadline)
                            .foregroundColor(Color("SecondaryColor"))
                    }
                }
                .padding()
                .background(Color("CardBackgroundColor"))
                .cornerRadius(12)
                
                // Order Details
                VStack(alignment: .leading, spacing: 16) {
                    Text("Order Details")
                        .font(.headline)
                        .foregroundColor(Color("TextColor"))
                    
                    // Product Info
                    DetailRow(title: "Product", value: order.product.name)
                    DetailRow(title: "SKU", value: order.product.sku)
                    
                    if let vendor = order.vendor {
                        DetailRow(title: "Vendor", value: vendor.name)
                    }
                    
                    DetailRow(title: "Quantity", value: "\(order.qty)")
                    
                    if order.qtyDelivered > 0 {
                        DetailRow(title: "Delivered", value: "\(order.qtyDelivered)")
                    }
                    
                    if order.qtyPending > 0 {
                        DetailRow(title: "Pending", value: "\(order.qtyPending)")
                    }
                    
                    if order.urgent {
                        DetailRow(title: "Urgent", value: "Yes")
                        
                        if let timeline = order.timeline {
                            DetailRow(title: "Timeline", value: timeline)
                        }
                    }
                    
                    if order.received {
                        DetailRow(title: "Received", value: "Yes")
                    }
                    
                    if let notes = order.notes, !notes.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Notes")
                                .font(.subheadline)
                                .foregroundColor(Color("SecondaryColor"))
                            
                            Text(notes)
                                .font(.body)
                                .foregroundColor(Color("TextColor"))
                                .padding()
                                .background(Color("FieldBackgroundColor"))
                                .cornerRadius(8)
                        }
                    }
                }
                .padding()
                .background(Color("CardBackgroundColor"))
                .cornerRadius(12)
                
                // Price Information
                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        Text("Price Information")
                            .font(.headline)
                            .foregroundColor(Color("TextColor"))
                        
                        Spacer()
                        
                        if !isEditing {
                            Button(action: {
                                startEditing()
                            }) {
                                Text("Edit")
                                    .font(.subheadline)
                                    .foregroundColor(Color("PrimaryColor"))
                            }
                        }
                    }
                    
                    if isEditing {
                        // Editable Price Fields
                        VStack(spacing: 12) {
                            PriceEditField(title: "Price", value: $editedPrice, placeholder: String(format: "%.2f", order.price))
                            PriceEditField(title: "Would Pay", value: $editedWouldPaid, placeholder: String(format: "%.2f", order.wouldPaid))
                            PriceEditField(title: "Paid", value: $editedPaid, placeholder: String(format: "%.2f", order.paid))
                            PriceEditField(title: "Saved", value: $editedSaved, placeholder: String(format: "%.2f", order.saved))
                            
                            HStack {
                                Button(action: cancelEditing) {
                                    Text("Cancel")
                                        .font(.subheadline)
                                        .foregroundColor(Color("SecondaryColor"))
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(Color("FieldBackgroundColor"))
                                        .cornerRadius(8)
                                }
                                
                                Spacer()
                                
                                Button(action: saveChanges) {
                                    Text("Save")
                                        .font(.subheadline)
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(Color("PrimaryColor"))
                                        .cornerRadius(8)
                                }
                                .disabled(isUpdatingPaid)
                            }
                        }
                    } else {
                        // Display Price Information
                        DetailRow(title: "Price", value: "$\(order.price, specifier: "%.2f")")
                        DetailRow(title: "Quantity", value: "\(order.qty)")
                        DetailRow(title: "Total", value: "$\(order.totalPrice, specifier: "%.2f")")
                        DetailRow(title: "Would Pay", value: "$\(order.wouldPaid, specifier: "%.2f")")
                        DetailRow(title: "Paid", value: "$\(order.paid, specifier: "%.2f")")
                        DetailRow(title: "Saved", value: "$\(order.saved, specifier: "%.2f")")
                    }
                }
                .padding()
                .background(Color("CardBackgroundColor"))
                .cornerRadius(12)
                
                // Action Buttons
                HStack {
                    Button(action: {
                        showingActionSheet = true
                    }) {
                        Text("Update Status")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color("PrimaryColor"))
                            .cornerRadius(12)
                    }
                    
                    Button(action: {
                        showingDeleteAlert = true
                    }) {
                        Text("Delete")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.red)
                            .cornerRadius(12)
                    }
                    .disabled(isDeleting)
                }
                .padding()
            }
            .padding()
        }
        .navigationTitle("Order Details")
        .navigationBarTitleDisplayMode(.inline)
        .actionSheet(isPresented: $showingActionSheet) {
            ActionSheet(
                title: Text("Update Order Status"),
                buttons: [
                    .default(Text("Pending")) { updateOrderStatus(1) },
                    .default(Text("Processing")) { updateOrderStatus(2) },
                    .default(Text("Shipped")) { updateOrderStatus(3) },
                    .default(Text("Delivered")) { updateOrderStatus(4) },
                    .destructive(Text("Cancelled")) { updateOrderStatus(5) },
                    .cancel()
                ]
            )
        }
        .alert(isPresented: $showingDeleteAlert) {
            Alert(
                title: Text("Delete Order"),
                message: Text("Are you sure you want to delete this order? This action cannot be undone."),
                primaryButton: .destructive(Text("Delete")) {
                    deleteOrder()
                },
                secondaryButton: .cancel()
            )
        }
    }
    
    // Start editing price information
    private func startEditing() {
        editedPrice = String(format: "%.2f", order.price)
        editedWouldPaid = String(format: "%.2f", order.wouldPaid)
        editedPaid = String(format: "%.2f", order.paid)
        editedSaved = String(format: "%.2f", order.saved)
        isEditing = true
    }
    
    // Cancel editing
    private func cancelEditing() {
        isEditing = false
    }
    
    // Save price changes
    private func saveChanges() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        isUpdatingPaid = true
        
        let updateRequest = UpdatePaidRequest(
            id: order.id,
            adminId: authService.selectedUser?.id ?? user.id,
            wouldPaid: Double(editedWouldPaid) ?? order.wouldPaid,
            paid: Double(editedPaid) ?? order.paid,
            saved: Double(editedSaved) ?? order.saved
        )
        
        orderService.updatePaidStatus(updateRequest: updateRequest, token: token) { success in
            isUpdatingPaid = false
            
            if success {
                isEditing = false
            }
        }
    }
    
    // Toggle favorite status
    private func toggleFavorite() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        isUpdatingFavorite = true
        
        let updateRequest = UpdateFavoriteRequest(
            id: order.id,
            adminId: authService.selectedUser?.id ?? user.id,
            myProduct: !order.favorite
        )
        
        orderService.updateFavorite(updateRequest: updateRequest, token: token) { success in
            isUpdatingFavorite = false
        }
    }
    
    // Update order status
    private func updateOrderStatus(_ status: Int) {
        // This would be implemented in a real app
        // For now, we'll just show a placeholder
    }
    
    // Delete order
    private func deleteOrder() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        isDeleting = true
        
        let deleteRequest = DeleteOrderRequest(
            id: order.id,
            adminId: authService.selectedUser?.id ?? user.id
        )
        
        orderService.deleteOrder(deleteRequest: deleteRequest, token: token) { success in
            isDeleting = false
            
            if success {
                // Navigate back to order list
                // In a real app, we would use a navigation coordinator or similar
            }
        }
    }
}

// Detail Row Component
struct DetailRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(Color("SecondaryColor"))
                .frame(width: 100, alignment: .leading)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .foregroundColor(Color("TextColor"))
                .multilineTextAlignment(.trailing)
        }
    }
}

// Price Edit Field Component
struct PriceEditField: View {
    let title: String
    @Binding var value: String
    let placeholder: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(Color("SecondaryColor"))
                .frame(width: 100, alignment: .leading)
            
            Spacer()
            
            TextField(placeholder, text: $value)
                .font(.subheadline)
                .foregroundColor(Color("TextColor"))
                .multilineTextAlignment(.trailing)
                .keyboardType(.decimalPad)
                .padding(8)
                .background(Color("FieldBackgroundColor"))
                .cornerRadius(8)
                .frame(width: 120)
        }
    }
}
