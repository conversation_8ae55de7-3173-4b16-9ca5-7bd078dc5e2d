import SwiftUI

struct OrderListView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var orderService: OrderService
    
    @State private var searchText = ""
    @State private var statusFilter: Int? = nil
    @State private var favoriteOnly = false
    @State private var showingAddOrder = false
    @State private var isRefreshing = false
    
    var body: some View {
        NavigationView {
            ZStack {
                Color("BackgroundColor")
                    .ignoresSafeArea()
                
                VStack {
                    // Search and Filter Bar
                    HStack {
                        // Search Field
                        HStack {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(Color("SecondaryColor"))
                            
                            TextField("Search orders", text: $searchText)
                                .foregroundColor(Color("TextColor"))
                        }
                        .padding(10)
                        .background(Color("FieldBackgroundColor"))
                        .cornerRadius(10)
                        
                        // Filter Button
                        Menu {
                            Button(action: {
                                statusFilter = nil
                            }) {
                                Label("All", systemImage: "list.bullet")
                            }
                            
                            But<PERSON>(action: {
                                statusFilter = 1
                            }) {
                                Label("Pending", systemImage: "clock")
                            }
                            
                            Button(action: {
                                statusFilter = 2
                            }) {
                                Label("Processing", systemImage: "gear")
                            }
                            
                            Button(action: {
                                statusFilter = 3
                            }) {
                                Label("Shipped", systemImage: "shippingbox")
                            }
                            
                            Button(action: {
                                statusFilter = 4
                            }) {
                                Label("Delivered", systemImage: "checkmark.circle")
                            }
                            
                            Button(action: {
                                statusFilter = 5
                            }) {
                                Label("Cancelled", systemImage: "xmark.circle")
                            }
                            
                            Divider()
                            
                            Button(action: {
                                favoriteOnly.toggle()
                            }) {
                                Label(favoriteOnly ? "Show All" : "Favorites Only", systemImage: favoriteOnly ? "star.fill" : "star")
                            }
                        } label: {
                            Image(systemName: "line.3.horizontal.decrease.circle")
                                .font(.title2)
                                .foregroundColor(Color("PrimaryColor"))
                        }
                        .padding(.leading, 8)
                    }
                    .padding(.horizontal)
                    .padding(.top, 8)
                    
                    // Order List
                    if orderService.isLoading && orderService.orders.isEmpty {
                        Spacer()
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(1.5)
                        Spacer()
                    } else if orderService.orders.isEmpty {
                        Spacer()
                        VStack(spacing: 16) {
                            Image(systemName: "doc.text.magnifyingglass")
                                .font(.system(size: 60))
                                .foregroundColor(Color("SecondaryColor").opacity(0.5))
                            
                            Text("No Orders Found")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(Color("TextColor"))
                            
                            Text("Try adjusting your filters or add a new order")
                                .font(.subheadline)
                                .foregroundColor(Color("TextColor").opacity(0.7))
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 32)
                            
                            Button(action: {
                                showingAddOrder = true
                            }) {
                                HStack {
                                    Image(systemName: "plus.circle.fill")
                                    Text("Add New Order")
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(Color("PrimaryColor"))
                                .foregroundColor(.white)
                                .cornerRadius(8)
                            }
                            .padding(.top, 8)
                        }
                        Spacer()
                    } else {
                        List {
                            ForEach(filteredOrders) { order in
                                NavigationLink(destination: OrderDetailView(order: order)) {
                                    OrderRowView(order: order)
                                }
                                .listRowBackground(Color("CardBackgroundColor"))
                            }
                        }
                        .listStyle(PlainListStyle())
                        .refreshable {
                            await refreshOrders()
                        }
                    }
                }
                .navigationTitle("Orders")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(action: {
                            showingAddOrder = true
                        }) {
                            Image(systemName: "plus")
                        }
                    }
                }
                .sheet(isPresented: $showingAddOrder) {
                    AddOrderView()
                }
            }
        }
        .onAppear {
            loadOrders()
        }
    }
    
    private var filteredOrders: [Order] {
        return orderService.filteredOrders(searchText: searchText, statusFilter: statusFilter, favoriteOnly: favoriteOnly)
    }
    
    private func loadOrders() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        let userId = authService.selectedUser?.id ?? user.id
        
        orderService.getUserOrders(userId: userId, token: token) { _ in }
    }
    
    private func refreshOrders() async {
        isRefreshing = true
        
        guard let user = authService.user else {
            isRefreshing = false
            return
        }
        guard let token = authService.token else {
            isRefreshing = false
            return
        }
        
        let userId = authService.selectedUser?.id ?? user.id
        
        await withCheckedContinuation { continuation in
            orderService.getUserOrders(userId: userId, token: token) { _ in
                isRefreshing = false
                continuation.resume()
            }
        }
    }
}

struct OrderRowView: View {
    let order: Order
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // Status Indicator
            Circle()
                .fill(Color(order.statusColor))
                .frame(width: 12, height: 12)
                .padding(.top, 4)
            
            VStack(alignment: .leading, spacing: 4) {
                // Product Name and Favorite
                HStack {
                    Text(order.product.name)
                        .font(.headline)
                        .foregroundColor(Color("TextColor"))
                    
                    Spacer()
                    
                    if order.favorite {
                        Image(systemName: "star.fill")
                            .foregroundColor(Color.yellow)
                    }
                    
                    if order.urgent {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(Color.red)
                    }
                }
                
                // Order Number and Date
                HStack {
                    Text(order.orderNumber)
                        .font(.subheadline)
                        .foregroundColor(Color("SecondaryColor"))
                    
                    Spacer()
                    
                    Text(order.formattedDate)
                        .font(.caption)
                        .foregroundColor(Color("SecondaryColor"))
                }
                
                // Vendor and Status
                HStack {
                    if let vendor = order.vendor {
                        Text(vendor.name)
                            .font(.caption)
                            .foregroundColor(Color("TextColor").opacity(0.7))
                    }
                    
                    Spacer()
                    
                    Text(order.statusText)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color(order.statusColor).opacity(0.2))
                        .foregroundColor(Color(order.statusColor))
                        .cornerRadius(4)
                }
                
                // Price and Quantity
                HStack {
                    Text("$\(order.price, specifier: "%.2f") × \(order.qty)")
                        .font(.subheadline)
                        .foregroundColor(Color("TextColor"))
                    
                    Spacer()
                    
                    Text("$\(order.totalPrice, specifier: "%.2f")")
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(Color("PrimaryColor"))
                }
            }
        }
        .padding(.vertical, 8)
    }
}

struct OrderDetailView: View {
    let order: Order
    
    var body: some View {
        Text("Order Detail View - Coming Soon")
            .navigationTitle("Order Details")
    }
}

struct AddOrderView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        Text("Add Order View - Coming Soon")
            .navigationTitle("New Order")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
    }
}

struct OrderListView_Previews: PreviewProvider {
    static var previews: some View {
        OrderListView()
            .environmentObject(AuthService())
            .environmentObject(OrderService())
    }
}
