import SwiftUI

struct AddOrderView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var orderService: OrderService
    
    // Form fields
    @State private var productName = ""
    @State private var sku = ""
    @State private var vendorName = ""
    @State private var quantity = ""
    @State private var price = ""
    @State private var wouldPaid = ""
    @State private var paid = ""
    @State private var saved = ""
    @State private var notes = ""
    @State private var isUrgent = false
    @State private var isSpecific = false
    @State private var timeline = Date()
    
    // UI state
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isSubmitting = false
    
    var body: some View {
        NavigationView {
            Form {
                // Product Section
                Section(header: Text("Product Information")) {
                    TextField("Product Name", text: $productName)
                    TextField("SKU", text: $sku)
                    TextField("Vendor Name", text: $vendorName)
                }
                
                // Quantity and Price Section
                Section(header: Text("Quantity and Price")) {
                    TextField("Quantity", text: $quantity)
                        .keyboardType(.numberPad)
                    
                    TextField("Price", text: $price)
                        .keyboardType(.decimalPad)
                    
                    TextField("Would Pay", text: $wouldPaid)
                        .keyboardType(.decimalPad)
                    
                    TextField("Paid", text: $paid)
                        .keyboardType(.decimalPad)
                    
                    TextField("Saved", text: $saved)
                        .keyboardType(.decimalPad)
                }
                
                // Options Section
                Section(header: Text("Options")) {
                    Toggle("Urgent", isOn: $isUrgent)
                    
                    if isUrgent {
                        DatePicker("Timeline", selection: $timeline, displayedComponents: .date)
                    }
                    
                    Toggle("Specific", isOn: $isSpecific)
                }
                
                // Notes Section
                Section(header: Text("Notes")) {
                    TextEditor(text: $notes)
                        .frame(minHeight: 100)
                }
                
                // Submit Button
                Section {
                    Button(action: submitOrder) {
                        HStack {
                            Spacer()
                            
                            if isSubmitting {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                            } else {
                                Text("Create Order")
                                    .fontWeight(.semibold)
                            }
                            
                            Spacer()
                        }
                    }
                    .disabled(isSubmitting || !isFormValid)
                }
            }
            .navigationTitle("New Order")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Error"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }
    
    // Validate form
    private var isFormValid: Bool {
        !productName.isEmpty &&
        !sku.isEmpty &&
        !quantity.isEmpty &&
        !price.isEmpty &&
        !wouldPaid.isEmpty &&
        !paid.isEmpty &&
        !saved.isEmpty &&
        Int(quantity) ?? 0 > 0 &&
        Double(price) ?? 0 > 0
    }
    
    // Submit order
    private func submitOrder() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        // Validate form
        guard isFormValid else {
            alertMessage = "Please fill in all required fields with valid values."
            showingAlert = true
            return
        }
        
        isSubmitting = true
        
        // Create order request
        let orderRequest = CreateOrderRequest(
            adminId: authService.selectedUser?.id ?? user.id,
            product: productName,
            sku: sku,
            vendor: vendorName.isEmpty ? nil : vendorName,
            qty: Int(quantity) ?? 0,
            price: Double(price) ?? 0,
            saved: Double(saved) ?? 0,
            wouldPaid: Double(wouldPaid) ?? 0,
            paid: Double(paid) ?? 0,
            urgent: isUrgent,
            timeline: isUrgent ? formatDate(timeline) : nil,
            notes: notes.isEmpty ? nil : notes,
            specific: isSpecific,
            status: 1 // Default to pending
        )
        
        // Submit order
        orderService.createOrder(order: orderRequest, token: token) { success in
            isSubmitting = false
            
            if success {
                presentationMode.wrappedValue.dismiss()
            } else {
                alertMessage = orderService.error ?? "Failed to create order. Please try again."
                showingAlert = true
            }
        }
    }
    
    // Format date for API
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }
}

struct AddOrderView_Previews: PreviewProvider {
    static var previews: some View {
        AddOrderView()
            .environmentObject(AuthService())
            .environmentObject(OrderService())
    }
}
