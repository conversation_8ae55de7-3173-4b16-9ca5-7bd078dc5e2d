import Foundation

/// Represents a vendor in the system
struct VendorDetail: Codable, Identifiable, Equatable {
    let id: Int
    let name: String
    let email: String?
    let phone: String?
    let address: String?
    let website: String?
    let notes: String?
    let adminId: Int
    let createdAt: String
    let updatedAt: String
    let isActive: Bool
    
    /// Returns the formatted created date
    var formattedCreatedDate: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        
        if let date = dateFormatter.date(from: createdAt) {
            dateFormatter.dateFormat = "MMM d, yyyy"
            return dateFormatter.string(from: date)
        }
        
        return createdAt
    }
    
    /// Returns the formatted updated date
    var formattedUpdatedDate: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        
        if let date = dateFormatter.date(from: updatedAt) {
            dateFormatter.dateFormat = "MMM d, yyyy"
            return dateFormatter.string(from: date)
        }
        
        return updatedAt
    }
    
    /// Returns the first letter of the vendor name
    var initial: String {
        return String(name.prefix(1).uppercased())
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case name
        case email
        case phone
        case address
        case website
        case notes
        case adminId = "admin_id"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case isActive = "is_active"
    }
    
    static func == (lhs: VendorDetail, rhs: VendorDetail) -> Bool {
        return lhs.id == rhs.id
    }
}

/// Request to create a new vendor
struct CreateVendorRequest: Codable {
    let adminId: Int
    let name: String
    let email: String?
    let phone: String?
    let address: String?
    let website: String?
    let notes: String?
    
    enum CodingKeys: String, CodingKey {
        case adminId = "admin_id"
        case name
        case email
        case phone
        case address
        case website
        case notes
    }
}

/// Request to update an existing vendor
struct UpdateVendorRequest: Codable {
    let id: Int
    let adminId: Int
    let name: String
    let email: String?
    let phone: String?
    let address: String?
    let website: String?
    let notes: String?
    let isActive: Bool
    
    enum CodingKeys: String, CodingKey {
        case id
        case adminId = "admin_id"
        case name
        case email
        case phone
        case address
        case website
        case notes
        case isActive = "is_active"
    }
}

/// Request to delete a vendor
struct DeleteVendorRequest: Codable {
    let id: Int
    let adminId: Int
    
    enum CodingKeys: String, CodingKey {
        case id
        case adminId = "admin_id"
    }
}

/// Represents a vendor statistic
struct VendorStatistic: Identifiable {
    let id = UUID()
    let name: String
    let ordersCount: Int
    let totalSpent: Double
    let lastOrderDate: String?
    
    /// Returns the formatted last order date
    var formattedLastOrderDate: String? {
        guard let lastOrderDate = lastOrderDate else { return nil }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        
        if let date = dateFormatter.date(from: lastOrderDate) {
            dateFormatter.dateFormat = "MMM d, yyyy"
            return dateFormatter.string(from: date)
        }
        
        return lastOrderDate
    }
}
