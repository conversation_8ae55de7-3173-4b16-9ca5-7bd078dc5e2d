import Foundation
import Combine
import SwiftUI

/// Service for managing vendors
class VendorService: ObservableObject {
    @Published var vendors: [VendorDetail] = []
    @Published var isLoading = false
    @Published var error: String?
    @Published var vendorStatistics: [VendorStatistic] = []
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Get User Vendors
    func getUserVendors(userId: Int, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.post(endpoint: "/vendor/list_user_vendors/\(userId)/", token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (vendors: [VendorDetail]) in
                    self?.vendors = vendors
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Create Vendor
    func createVendor(vendor: CreateVendorRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.post(endpoint: "/vendor/create_vendor/", body: vendor, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (vendors: [VendorDetail]) in
                    self?.vendors = vendors
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Update Vendor
    func updateVendor(vendor: UpdateVendorRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.put(endpoint: "/vendor/update_vendor/", body: vendor, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (vendors: [VendorDetail]) in
                    self?.vendors = vendors
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Delete Vendor
    func deleteVendor(deleteRequest: DeleteVendorRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.put(endpoint: "/vendor/delete_vendor/", body: deleteRequest, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (vendors: [VendorDetail]) in
                    self?.vendors = vendors
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Calculate Vendor Statistics
    func calculateVendorStatistics(orders: [Order]) {
        var vendorStats: [String: VendorStatistic] = [:]
        
        for order in orders {
            guard let vendor = order.vendor else { continue }
            
            let vendorName = vendor.name
            let orderDate = order.createdAt
            let orderTotal = order.totalPrice
            
            if let existingStat = vendorStats[vendorName] {
                let newOrdersCount = existingStat.ordersCount + 1
                let newTotalSpent = existingStat.totalSpent + orderTotal
                
                // Compare dates to find the most recent order
                let lastOrderDate: String?
                if let existingDate = existingStat.lastOrderDate {
                    // Simple string comparison works for ISO format dates
                    lastOrderDate = existingDate > orderDate ? existingDate : orderDate
                } else {
                    lastOrderDate = orderDate
                }
                
                vendorStats[vendorName] = VendorStatistic(
                    name: vendorName,
                    ordersCount: newOrdersCount,
                    totalSpent: newTotalSpent,
                    lastOrderDate: lastOrderDate
                )
            } else {
                vendorStats[vendorName] = VendorStatistic(
                    name: vendorName,
                    ordersCount: 1,
                    totalSpent: orderTotal,
                    lastOrderDate: orderDate
                )
            }
        }
        
        // Convert dictionary to array and sort by total spent (descending)
        vendorStatistics = vendorStats.values.sorted { $0.totalSpent > $1.totalSpent }
    }
    
    // MARK: - Filter Vendors
    func filteredVendors(searchText: String, activeOnly: Bool = false) -> [VendorDetail] {
        return vendors.filter { vendor in
            let matchesSearch = searchText.isEmpty || 
                vendor.name.localizedCaseInsensitiveContains(searchText) ||
                (vendor.email?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                (vendor.phone?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                (vendor.notes?.localizedCaseInsensitiveContains(searchText) ?? false)
            
            let matchesActive = !activeOnly || vendor.isActive
            
            return matchesSearch && matchesActive
        }
    }
    
    // MARK: - Get Top Vendors
    func getTopVendors(limit: Int = 5) -> [VendorStatistic] {
        return Array(vendorStatistics.prefix(limit))
    }
}
