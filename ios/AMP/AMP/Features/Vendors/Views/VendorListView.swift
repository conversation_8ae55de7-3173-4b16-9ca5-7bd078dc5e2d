import SwiftUI

struct VendorListView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var vendorService: VendorService
    @EnvironmentObject var orderService: OrderService
    
    @State private var searchText = ""
    @State private var activeOnly = false
    @State private var showingAddVendor = false
    @State private var isRefreshing = false
    @State private var sortOption = SortOption.name
    
    enum SortOption: String, CaseIterable {
        case name = "Name"
        case recent = "Recently Updated"
        case oldest = "Oldest First"
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color("BackgroundColor")
                    .ignoresSafeArea()
                
                VStack {
                    // Search and Filter Bar
                    HStack {
                        // Search Field
                        HStack {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(Color("SecondaryColor"))
                            
                            TextField("Search vendors", text: $searchText)
                                .foregroundColor(Color("TextColor"))
                        }
                        .padding(10)
                        .background(Color("FieldBackgroundColor"))
                        .cornerRadius(10)
                        
                        // Filter Button
                        Menu {
                            // Active Only Filter
                            Button(action: {
                                activeOnly.toggle()
                            }) {
                                Label(activeOnly ? "Show All" : "Active Only", systemImage: activeOnly ? "checkmark.circle.fill" : "checkmark.circle")
                            }
                            
                            Divider()
                            
                            // Sort Options
                            Menu {
                                ForEach(SortOption.allCases, id: \.self) { option in
                                    Button(action: {
                                        sortOption = option
                                    }) {
                                        Label(option.rawValue, systemImage: sortIcon(for: option))
                                    }
                                }
                            } label: {
                                Label("Sort By", systemImage: "arrow.up.arrow.down")
                            }
                        } label: {
                            Image(systemName: "line.3.horizontal.decrease.circle")
                                .font(.title2)
                                .foregroundColor(Color("PrimaryColor"))
                        }
                        .padding(.leading, 8)
                    }
                    .padding(.horizontal)
                    .padding(.top, 8)
                    
                    // Vendor List
                    if vendorService.isLoading && vendorService.vendors.isEmpty {
                        Spacer()
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(1.5)
                        Spacer()
                    } else if vendorService.vendors.isEmpty {
                        Spacer()
                        VStack(spacing: 16) {
                            Image(systemName: "building.2")
                                .font(.system(size: 60))
                                .foregroundColor(Color("SecondaryColor").opacity(0.5))
                            
                            Text("No Vendors Found")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(Color("TextColor"))
                            
                            Text("Try adjusting your filters or add a new vendor")
                                .font(.subheadline)
                                .foregroundColor(Color("TextColor").opacity(0.7))
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 32)
                            
                            Button(action: {
                                showingAddVendor = true
                            }) {
                                HStack {
                                    Image(systemName: "plus.circle.fill")
                                    Text("Add New Vendor")
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(Color("PrimaryColor"))
                                .foregroundColor(.white)
                                .cornerRadius(8)
                            }
                            .padding(.top, 8)
                        }
                        Spacer()
                    } else {
                        List {
                            ForEach(sortedFilteredVendors) { vendor in
                                NavigationLink(destination: VendorDetailView(vendor: vendor)) {
                                    VendorRowView(vendor: vendor)
                                }
                                .listRowBackground(Color("CardBackgroundColor"))
                            }
                        }
                        .listStyle(PlainListStyle())
                        .refreshable {
                            await refreshVendors()
                        }
                    }
                }
                .navigationTitle("Vendors")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(action: {
                            showingAddVendor = true
                        }) {
                            Image(systemName: "plus")
                        }
                    }
                }
                .sheet(isPresented: $showingAddVendor) {
                    AddVendorView()
                }
            }
        }
        .onAppear {
            loadVendors()
            // Calculate vendor statistics from orders
            vendorService.calculateVendorStatistics(orders: orderService.orders)
        }
    }
    
    private var sortedFilteredVendors: [VendorDetail] {
        let filteredVendors = vendorService.filteredVendors(
            searchText: searchText,
            activeOnly: activeOnly
        )
        
        switch sortOption {
        case .name:
            // Sort by name (ascending)
            return filteredVendors.sorted { $0.name < $1.name }
            
        case .recent:
            // Sort by updated date (descending)
            return filteredVendors.sorted { $0.updatedAt > $1.updatedAt }
            
        case .oldest:
            // Sort by created date (ascending)
            return filteredVendors.sorted { $0.createdAt < $1.createdAt }
        }
    }
    
    private func sortIcon(for option: SortOption) -> String {
        switch option {
        case .name:
            return "textformat"
        case .recent:
            return "arrow.down"
        case .oldest:
            return "arrow.up"
        }
    }
    
    private func loadVendors() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        let userId = authService.selectedUser?.id ?? user.id
        
        vendorService.getUserVendors(userId: userId, token: token) { _ in }
    }
    
    private func refreshVendors() async {
        isRefreshing = true
        
        guard let user = authService.user else {
            isRefreshing = false
            return
        }
        guard let token = authService.token else {
            isRefreshing = false
            return
        }
        
        let userId = authService.selectedUser?.id ?? user.id
        
        await withCheckedContinuation { continuation in
            vendorService.getUserVendors(userId: userId, token: token) { _ in
                isRefreshing = false
                continuation.resume()
            }
        }
    }
}

struct VendorRowView: View {
    let vendor: VendorDetail
    
    var body: some View {
        HStack(spacing: 16) {
            // Vendor Avatar
            Circle()
                .fill(Color("PrimaryColor"))
                .frame(width: 40, height: 40)
                .overlay(
                    Text(vendor.initial)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                // Vendor Name
                Text(vendor.name)
                    .font(.headline)
                    .foregroundColor(Color("TextColor"))
                
                // Vendor Contact Info
                if let email = vendor.email, !email.isEmpty {
                    Text(email)
                        .font(.subheadline)
                        .foregroundColor(Color("SecondaryColor"))
                } else if let phone = vendor.phone, !phone.isEmpty {
                    Text(phone)
                        .font(.subheadline)
                        .foregroundColor(Color("SecondaryColor"))
                } else {
                    Text("No contact info")
                        .font(.caption)
                        .foregroundColor(Color("SecondaryColor").opacity(0.7))
                }
            }
            
            Spacer()
            
            // Status Indicator
            if !vendor.isActive {
                Text("Inactive")
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(Color("SecondaryColor").opacity(0.2))
                    .foregroundColor(Color("SecondaryColor"))
                    .cornerRadius(4)
            }
        }
        .padding(.vertical, 8)
    }
}

struct VendorDetailView: View {
    let vendor: VendorDetail
    
    var body: some View {
        Text("Vendor Detail View - Coming Soon")
            .navigationTitle(vendor.name)
    }
}

struct AddVendorView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        Text("Add Vendor View - Coming Soon")
            .navigationTitle("New Vendor")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
    }
}

struct VendorListView_Previews: PreviewProvider {
    static var previews: some View {
        VendorListView()
            .environmentObject(AuthService())
            .environmentObject(VendorService())
            .environmentObject(OrderService())
    }
}
