import SwiftUI

struct VendorDetailView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var vendorService: VendorService
    @EnvironmentObject var orderService: OrderService
    
    let vendor: VendorDetail
    
    @State private var isEditing = false
    @State private var showingDeleteAlert = false
    @State private var isUpdating = false
    @State private var isDeleting = false
    @State private var showingVendorOrders = false
    
    // Editable fields
    @State private var editedName = ""
    @State private var editedEmail = ""
    @State private var editedPhone = ""
    @State private var editedAddress = ""
    @State private var editedWebsite = ""
    @State private var editedNotes = ""
    @State private var editedIsActive = true
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Vendor Header
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        // Vendor Avatar
                        Circle()
                            .fill(Color("PrimaryColor"))
                            .frame(width: 60, height: 60)
                            .overlay(
                                Text(vendor.initial)
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                            )
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(vendor.name)
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(Color("TextColor"))
                            
                            if !vendor.isActive {
                                Text("Inactive")
                                    .font(.caption)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 2)
                                    .background(Color("SecondaryColor").opacity(0.2))
                                    .foregroundColor(Color("SecondaryColor"))
                                    .cornerRadius(4)
                            }
                        }
                        
                        Spacer()
                        
                        if !isEditing {
                            Button(action: {
                                startEditing()
                            }) {
                                Text("Edit")
                                    .font(.subheadline)
                                    .foregroundColor(Color("PrimaryColor"))
                            }
                        }
                    }
                }
                .padding()
                .background(Color("CardBackgroundColor"))
                .cornerRadius(12)
                
                // Vendor Details
                VStack(alignment: .leading, spacing: 16) {
                    Text("Vendor Details")
                        .font(.headline)
                        .foregroundColor(Color("TextColor"))
                    
                    if isEditing {
                        // Editable Fields
                        VStack(spacing: 12) {
                            // Name
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Name")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                TextField("Name", text: $editedName)
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                            
                            // Email
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Email")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                TextField("Email", text: $editedEmail)
                                    .keyboardType(.emailAddress)
                                    .autocapitalization(.none)
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                            
                            // Phone
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Phone")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                TextField("Phone", text: $editedPhone)
                                    .keyboardType(.phonePad)
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                            
                            // Address
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Address")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                TextField("Address", text: $editedAddress)
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                            
                            // Website
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Website")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                TextField("Website", text: $editedWebsite)
                                    .keyboardType(.URL)
                                    .autocapitalization(.none)
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                            
                            // Notes
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Notes")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                TextEditor(text: $editedNotes)
                                    .frame(minHeight: 100)
                                    .padding(4)
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                            
                            // Active Status
                            Toggle("Active", isOn: $editedIsActive)
                                .padding(.vertical, 8)
                            
                            // Buttons
                            HStack {
                                Button(action: cancelEditing) {
                                    Text("Cancel")
                                        .font(.subheadline)
                                        .foregroundColor(Color("SecondaryColor"))
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(Color("FieldBackgroundColor"))
                                        .cornerRadius(8)
                                }
                                
                                Spacer()
                                
                                Button(action: saveChanges) {
                                    Text("Save")
                                        .font(.subheadline)
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(Color("PrimaryColor"))
                                        .cornerRadius(8)
                                }
                                .disabled(isUpdating || !isFormValid)
                            }
                        }
                    } else {
                        // Display Vendor Details
                        if let email = vendor.email, !email.isEmpty {
                            DetailRow(title: "Email", value: email)
                        }
                        
                        if let phone = vendor.phone, !phone.isEmpty {
                            DetailRow(title: "Phone", value: phone)
                        }
                        
                        if let address = vendor.address, !address.isEmpty {
                            DetailRow(title: "Address", value: address)
                        }
                        
                        if let website = vendor.website, !website.isEmpty {
                            DetailRow(title: "Website", value: website, isLink: true)
                        }
                        
                        if let notes = vendor.notes, !notes.isEmpty {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Notes")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                Text(notes)
                                    .font(.body)
                                    .foregroundColor(Color("TextColor"))
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                        }
                        
                        DetailRow(title: "Created", value: vendor.formattedCreatedDate)
                        DetailRow(title: "Updated", value: vendor.formattedUpdatedDate)
                    }
                }
                .padding()
                .background(Color("CardBackgroundColor"))
                .cornerRadius(12)
                
                // Vendor Statistics
                if !isEditing {
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Order Statistics")
                            .font(.headline)
                            .foregroundColor(Color("TextColor"))
                        
                        let vendorStats = getVendorStatistics()
                        
                        DetailRow(title: "Orders", value: "\(vendorStats.ordersCount)")
                        DetailRow(title: "Total Spent", value: "$\(vendorStats.totalSpent, specifier: "%.2f")")
                        
                        if let lastOrderDate = vendorStats.formattedLastOrderDate {
                            DetailRow(title: "Last Order", value: lastOrderDate)
                        }
                        
                        Button(action: {
                            showingVendorOrders = true
                        }) {
                            Text("View Orders")
                                .font(.subheadline)
                                .foregroundColor(Color("PrimaryColor"))
                                .padding(.top, 8)
                        }
                    }
                    .padding()
                    .background(Color("CardBackgroundColor"))
                    .cornerRadius(12)
                }
                
                // Action Buttons
                if !isEditing {
                    Button(action: {
                        showingDeleteAlert = true
                    }) {
                        HStack {
                            Spacer()
                            
                            Image(systemName: "trash")
                            Text("Delete Vendor")
                            
                            Spacer()
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.red)
                        .cornerRadius(12)
                    }
                    .disabled(isDeleting)
                    .padding(.top, 8)
                }
            }
            .padding()
        }
        .navigationTitle(vendor.name)
        .navigationBarTitleDisplayMode(.inline)
        .alert(isPresented: $showingDeleteAlert) {
            Alert(
                title: Text("Delete Vendor"),
                message: Text("Are you sure you want to delete this vendor? This action cannot be undone."),
                primaryButton: .destructive(Text("Delete")) {
                    deleteVendor()
                },
                secondaryButton: .cancel()
            )
        }
        .sheet(isPresented: $showingVendorOrders) {
            VendorOrdersView(vendorName: vendor.name)
        }
    }
    
    // Start editing
    private func startEditing() {
        editedName = vendor.name
        editedEmail = vendor.email ?? ""
        editedPhone = vendor.phone ?? ""
        editedAddress = vendor.address ?? ""
        editedWebsite = vendor.website ?? ""
        editedNotes = vendor.notes ?? ""
        editedIsActive = vendor.isActive
        
        isEditing = true
    }
    
    // Cancel editing
    private func cancelEditing() {
        isEditing = false
    }
    
    // Save changes
    private func saveChanges() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        isUpdating = true
        
        let updateRequest = UpdateVendorRequest(
            id: vendor.id,
            adminId: authService.selectedUser?.id ?? user.id,
            name: editedName,
            email: editedEmail.isEmpty ? nil : editedEmail,
            phone: editedPhone.isEmpty ? nil : editedPhone,
            address: editedAddress.isEmpty ? nil : editedAddress,
            website: editedWebsite.isEmpty ? nil : editedWebsite,
            notes: editedNotes.isEmpty ? nil : editedNotes,
            isActive: editedIsActive
        )
        
        vendorService.updateVendor(vendor: updateRequest, token: token) { success in
            isUpdating = false
            
            if success {
                isEditing = false
            }
        }
    }
    
    // Delete vendor
    private func deleteVendor() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        isDeleting = true
        
        let deleteRequest = DeleteVendorRequest(
            id: vendor.id,
            adminId: authService.selectedUser?.id ?? user.id
        )
        
        vendorService.deleteVendor(deleteRequest: deleteRequest, token: token) { success in
            isDeleting = false
            
            if success {
                // Navigate back to vendor list
                // In a real app, we would use a navigation coordinator or similar
            }
        }
    }
    
    // Get vendor statistics
    private func getVendorStatistics() -> VendorStatistic {
        // Filter orders for this vendor
        let vendorOrders = orderService.orders.filter { order in
            guard let orderVendor = order.vendor else { return false }
            return orderVendor.id == vendor.id
        }
        
        // Calculate statistics
        let ordersCount = vendorOrders.count
        let totalSpent = vendorOrders.reduce(0) { $0 + $1.totalPrice }
        
        // Find the most recent order date
        let sortedOrders = vendorOrders.sorted { $0.createdAt > $1.createdAt }
        let lastOrderDate = sortedOrders.first?.createdAt
        
        return VendorStatistic(
            name: vendor.name,
            ordersCount: ordersCount,
            totalSpent: totalSpent,
            lastOrderDate: lastOrderDate
        )
    }
    
    // Validate form
    private var isFormValid: Bool {
        return !editedName.isEmpty
    }
}

struct VendorOrdersView: View {
    @EnvironmentObject var orderService: OrderService
    @Environment(\.presentationMode) var presentationMode
    
    let vendorName: String
    
    var body: some View {
        NavigationView {
            List {
                ForEach(vendorOrders) { order in
                    VStack(alignment: .leading, spacing: 8) {
                        Text(order.product.name)
                            .font(.headline)
                            .foregroundColor(Color("TextColor"))
                        
                        HStack {
                            Text("Order #\(order.orderNumber)")
                                .font(.subheadline)
                                .foregroundColor(Color("SecondaryColor"))
                            
                            Spacer()
                            
                            Text(order.formattedDate)
                                .font(.caption)
                                .foregroundColor(Color("SecondaryColor"))
                        }
                        
                        HStack {
                            Text(order.statusText)
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(Color(order.statusColor).opacity(0.2))
                                .foregroundColor(Color(order.statusColor))
                                .cornerRadius(4)
                            
                            Spacer()
                            
                            Text("$\(order.totalPrice, specifier: "%.2f")")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(Color("PrimaryColor"))
                        }
                    }
                    .padding(.vertical, 8)
                }
            }
            .listStyle(PlainListStyle())
            .navigationTitle("\(vendorName) Orders")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
    
    private var vendorOrders: [Order] {
        return orderService.orders.filter { order in
            guard let orderVendor = order.vendor else { return false }
            return orderVendor.name == vendorName
        }
        .sorted { $0.createdAt > $1.createdAt }
    }
}
