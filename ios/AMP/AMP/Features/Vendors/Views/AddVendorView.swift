import SwiftUI

struct AddVendorView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var vendorService: VendorService
    
    // Form fields
    @State private var name = ""
    @State private var email = ""
    @State private var phone = ""
    @State private var address = ""
    @State private var website = ""
    @State private var notes = ""
    
    // UI state
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isSubmitting = false
    
    var body: some View {
        NavigationView {
            Form {
                // Basic Information Section
                Section(header: Text("Basic Information")) {
                    TextField("Name", text: $name)
                    TextField("Email", text: $email)
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                    TextField("Phone", text: $phone)
                        .keyboardType(.phonePad)
                }
                
                // Additional Information Section
                Section(header: Text("Additional Information")) {
                    TextField("Address", text: $address)
                    TextField("Website", text: $website)
                        .keyboardType(.URL)
                        .autocapitalization(.none)
                }
                
                // Notes Section
                Section(header: Text("Notes")) {
                    TextEditor(text: $notes)
                        .frame(minHeight: 100)
                }
                
                // Submit Button
                Section {
                    Button(action: submitVendor) {
                        HStack {
                            Spacer()
                            
                            if isSubmitting {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                            } else {
                                Text("Add Vendor")
                                    .fontWeight(.semibold)
                            }
                            
                            Spacer()
                        }
                    }
                    .disabled(isSubmitting || !isFormValid)
                }
            }
            .navigationTitle("New Vendor")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Error"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }
    
    // Validate form
    private var isFormValid: Bool {
        return !name.isEmpty
    }
    
    // Submit vendor
    private func submitVendor() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        // Validate form
        guard isFormValid else {
            alertMessage = "Please enter a vendor name."
            showingAlert = true
            return
        }
        
        isSubmitting = true
        
        // Create vendor request
        let vendorRequest = CreateVendorRequest(
            adminId: authService.selectedUser?.id ?? user.id,
            name: name,
            email: email.isEmpty ? nil : email,
            phone: phone.isEmpty ? nil : phone,
            address: address.isEmpty ? nil : address,
            website: website.isEmpty ? nil : website,
            notes: notes.isEmpty ? nil : notes
        )
        
        // Submit vendor
        vendorService.createVendor(vendor: vendorRequest, token: token) { success in
            isSubmitting = false
            
            if success {
                presentationMode.wrappedValue.dismiss()
            } else {
                alertMessage = vendorService.error ?? "Failed to create vendor. Please try again."
                showingAlert = true
            }
        }
    }
}

struct AddVendorView_Previews: PreviewProvider {
    static var previews: some View {
        AddVendorView()
            .environmentObject(AuthService())
            .environmentObject(VendorService())
    }
}
