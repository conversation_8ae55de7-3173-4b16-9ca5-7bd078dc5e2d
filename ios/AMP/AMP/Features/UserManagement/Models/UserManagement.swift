import Foundation

/// Represents a user in the system for management purposes
struct UserManagement: Codable, Identifiable, Equatable {
    let id: Int
    let email: String
    let firstName: String
    let lastName: String
    let role: String
    let isActive: Bool
    let createdAt: String
    let updatedAt: String
    let lastLogin: String?
    
    /// Returns the user's full name
    var fullName: String {
        return "\(firstName) \(lastName)"
    }
    
    /// Returns the user's initials
    var initials: String {
        let firstInitial = firstName.first?.uppercased() ?? ""
        let lastInitial = lastName.first?.uppercased() ?? ""
        return "\(firstInitial)\(lastInitial)"
    }
    
    /// Returns the user's avatar URL
    var avatarURL: URL? {
        return URL(string: "https://ui-avatars.com/api/?name=\(firstName)+\(lastName)&background=random")
    }
    
    /// Returns the user's role as an enum
    var userRole: UserRole {
        switch role {
        case "admin":
            return .admin
        case "manager":
            return .manager
        case "user":
            return .user
        default:
            return .user
        }
    }
    
    /// Returns the formatted created date
    var formattedCreatedDate: String {
        return formatDate(createdAt)
    }
    
    /// Returns the formatted updated date
    var formattedUpdatedDate: String {
        return formatDate(updatedAt)
    }
    
    /// Returns the formatted last login date
    var formattedLastLoginDate: String? {
        guard let lastLogin = lastLogin else { return nil }
        return formatDate(lastLogin)
    }
    
    /// Formats a date string
    private func formatDate(_ dateString: String) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        
        if let date = dateFormatter.date(from: dateString) {
            dateFormatter.dateFormat = "MMM d, yyyy h:mm a"
            return dateFormatter.string(from: date)
        }
        
        return dateString
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case email
        case firstName = "first_name"
        case lastName = "last_name"
        case role
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case lastLogin = "last_login"
    }
    
    static func == (lhs: UserManagement, rhs: UserManagement) -> Bool {
        return lhs.id == rhs.id
    }
}

/// Request to create a new user
struct CreateUserRequest: Codable {
    let email: String
    let firstName: String
    let lastName: String
    let password: String
    let role: String
    
    enum CodingKeys: String, CodingKey {
        case email
        case firstName = "first_name"
        case lastName = "last_name"
        case password
        case role
    }
}

/// Request to update an existing user
struct UpdateUserRequest: Codable {
    let id: Int
    let email: String
    let firstName: String
    let lastName: String
    let role: String
    let isActive: Bool
    
    enum CodingKeys: String, CodingKey {
        case id
        case email
        case firstName = "first_name"
        case lastName = "last_name"
        case role
        case isActive = "is_active"
    }
}

/// Request to update a user's password
struct UpdatePasswordRequest: Codable {
    let id: Int
    let password: String
    
    enum CodingKeys: String, CodingKey {
        case id
        case password
    }
}

/// Request to delete a user
struct DeleteUserRequest: Codable {
    let id: Int
    
    enum CodingKeys: String, CodingKey {
        case id
    }
}

/// User activity statistics
struct UserActivity: Codable {
    let userId: Int
    let ordersCreated: Int
    let lastActive: String?
    let totalSpent: Double
    
    enum CodingKeys: String, CodingKey {
        case userId = "user_id"
        case ordersCreated = "orders_created"
        case lastActive = "last_active"
        case totalSpent = "total_spent"
    }
    
    /// Returns the formatted last active date
    var formattedLastActiveDate: String? {
        guard let lastActive = lastActive else { return nil }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        
        if let date = dateFormatter.date(from: lastActive) {
            dateFormatter.dateFormat = "MMM d, yyyy h:mm a"
            return dateFormatter.string(from: date)
        }
        
        return lastActive
    }
}
