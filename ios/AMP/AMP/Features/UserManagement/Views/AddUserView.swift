import SwiftUI

struct AddUserView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var userManagementService: UserManagementService
    
    // Form fields
    @State private var email = ""
    @State private var firstName = ""
    @State private var lastName = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var role = "user"
    
    // UI state
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isSubmitting = false
    @State private var showPassword = false
    
    var body: some View {
        NavigationView {
            Form {
                // Basic Information Section
                Section(header: Text("Basic Information")) {
                    TextField("Email", text: $email)
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                    
                    TextField("First Name", text: $firstName)
                    
                    TextField("Last Name", text: $lastName)
                }
                
                // Password Section
                Section(header: Text("Password")) {
                    HStack {
                        if showPassword {
                            TextField("Password", text: $password)
                        } else {
                            SecureField("Password", text: $password)
                        }
                        
                        Button(action: {
                            showPassword.toggle()
                        }) {
                            Image(systemName: showPassword ? "eye.slash.fill" : "eye.fill")
                                .foregroundColor(Color("SecondaryColor"))
                        }
                    }
                    
                    HStack {
                        if showPassword {
                            TextField("Confirm Password", text: $confirmPassword)
                        } else {
                            SecureField("Confirm Password", text: $confirmPassword)
                        }
                        
                        Button(action: {
                            showPassword.toggle()
                        }) {
                            Image(systemName: showPassword ? "eye.slash.fill" : "eye.fill")
                                .foregroundColor(Color("SecondaryColor"))
                        }
                    }
                    
                    if !password.isEmpty && !confirmPassword.isEmpty && password != confirmPassword {
                        Text("Passwords do not match")
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                }
                
                // Role Section
                Section(header: Text("Role")) {
                    Picker("Role", selection: $role) {
                        Text("Admin").tag("admin")
                        Text("Manager").tag("manager")
                        Text("User").tag("user")
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
                
                // Submit Button
                Section {
                    Button(action: submitUser) {
                        HStack {
                            Spacer()
                            
                            if isSubmitting {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                            } else {
                                Text("Create User")
                                    .fontWeight(.semibold)
                            }
                            
                            Spacer()
                        }
                    }
                    .disabled(isSubmitting || !isFormValid)
                }
            }
            .navigationTitle("New User")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Error"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }
    
    // Validate form
    private var isFormValid: Bool {
        return !email.isEmpty &&
               !firstName.isEmpty &&
               !lastName.isEmpty &&
               !password.isEmpty &&
               password == confirmPassword &&
               isValidEmail(email)
    }
    
    // Validate email
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegEx = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPred = NSPredicate(format:"SELF MATCHES %@", emailRegEx)
        return emailPred.evaluate(with: email)
    }
    
    // Submit user
    private func submitUser() {
        guard let token = authService.token else { return }
        
        // Validate form
        guard isFormValid else {
            if !isValidEmail(email) {
                alertMessage = "Please enter a valid email address."
            } else if password != confirmPassword {
                alertMessage = "Passwords do not match."
            } else {
                alertMessage = "Please fill in all fields."
            }
            showingAlert = true
            return
        }
        
        isSubmitting = true
        
        // Create user request
        let userRequest = CreateUserRequest(
            email: email,
            firstName: firstName,
            lastName: lastName,
            password: password,
            role: role
        )
        
        // Submit user
        userManagementService.createUser(user: userRequest, token: token) { success in
            isSubmitting = false
            
            if success {
                presentationMode.wrappedValue.dismiss()
            } else {
                alertMessage = userManagementService.error ?? "Failed to create user. Please try again."
                showingAlert = true
            }
        }
    }
}

struct AddUserView_Previews: PreviewProvider {
    static var previews: some View {
        AddUserView()
            .environmentObject(AuthService())
            .environmentObject(UserManagementService())
    }
}
