import SwiftUI

struct UserDetailView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var userManagementService: UserManagementService
    @Environment(\.presentationMode) var presentationMode
    
    let user: UserManagement
    
    @State private var isEditing = false
    @State private var showingDeleteAlert = false
    @State private var showingResetPasswordAlert = false
    @State private var isUpdating = false
    @State private var isDeleting = false
    @State private var isResettingPassword = false
    @State private var userActivity: UserActivity?
    
    // Editable fields
    @State private var editedEmail = ""
    @State private var editedFirstName = ""
    @State private var editedLastName = ""
    @State private var editedRole = ""
    @State private var editedIsActive = true
    
    // Reset password fields
    @State private var newPassword = ""
    @State private var confirmPassword = ""
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // User Header
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        // User Avatar
                        if let avatarURL = user.avatarURL {
                            AsyncImage(url: avatarURL) { image in
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                            } placeholder: {
                                ProgressView()
                            }
                            .frame(width: 60, height: 60)
                            .clipShape(Circle())
                        } else {
                            Circle()
                                .fill(Color("PrimaryColor"))
                                .frame(width: 60, height: 60)
                                .overlay(
                                    Text(user.initials)
                                        .font(.title2)
                                        .fontWeight(.bold)
                                        .foregroundColor(.white)
                                )
                        }
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(user.fullName)
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(Color("TextColor"))
                            
                            Text(user.email)
                                .font(.subheadline)
                                .foregroundColor(Color("SecondaryColor"))
                        }
                        .padding(.leading, 8)
                        
                        Spacer()
                        
                        if !isEditing {
                            Button(action: {
                                startEditing()
                            }) {
                                Text("Edit")
                                    .font(.subheadline)
                                    .foregroundColor(Color("PrimaryColor"))
                            }
                        }
                    }
                    
                    HStack {
                        Text(user.role.capitalized)
                            .font(.subheadline)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(roleColor(user.role).opacity(0.2))
                            .foregroundColor(roleColor(user.role))
                            .cornerRadius(8)
                        
                        if !user.isActive {
                            Text("Inactive")
                                .font(.subheadline)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color("SecondaryColor").opacity(0.2))
                                .foregroundColor(Color("SecondaryColor"))
                                .cornerRadius(8)
                        }
                    }
                }
                .padding()
                .background(Color("CardBackgroundColor"))
                .cornerRadius(12)
                
                // User Details
                VStack(alignment: .leading, spacing: 16) {
                    Text("User Details")
                        .font(.headline)
                        .foregroundColor(Color("TextColor"))
                    
                    if isEditing {
                        // Editable Fields
                        VStack(spacing: 12) {
                            // Email
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Email")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                TextField("Email", text: $editedEmail)
                                    .keyboardType(.emailAddress)
                                    .autocapitalization(.none)
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                            
                            // First Name
                            VStack(alignment: .leading, spacing: 4) {
                                Text("First Name")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                TextField("First Name", text: $editedFirstName)
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                            
                            // Last Name
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Last Name")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                TextField("Last Name", text: $editedLastName)
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                            
                            // Role
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Role")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                Picker("Role", selection: $editedRole) {
                                    Text("Admin").tag("admin")
                                    Text("Manager").tag("manager")
                                    Text("User").tag("user")
                                }
                                .pickerStyle(SegmentedPickerStyle())
                            }
                            
                            // Active Status
                            Toggle("Active", isOn: $editedIsActive)
                                .padding(.vertical, 8)
                            
                            // Buttons
                            HStack {
                                Button(action: cancelEditing) {
                                    Text("Cancel")
                                        .font(.subheadline)
                                        .foregroundColor(Color("SecondaryColor"))
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(Color("FieldBackgroundColor"))
                                        .cornerRadius(8)
                                }
                                
                                Spacer()
                                
                                Button(action: saveChanges) {
                                    if isUpdating {
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle())
                                    } else {
                                        Text("Save")
                                            .font(.subheadline)
                                            .foregroundColor(.white)
                                    }
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(Color("PrimaryColor"))
                                .cornerRadius(8)
                                .disabled(isUpdating || !isFormValid)
                            }
                        }
                    } else {
                        // Display User Details
                        DetailRow(title: "Created", value: user.formattedCreatedDate)
                        DetailRow(title: "Updated", value: user.formattedUpdatedDate)
                        
                        if let lastLogin = user.formattedLastLoginDate {
                            DetailRow(title: "Last Login", value: lastLogin)
                        }
                        
                        // User Activity
                        if let activity = userActivity {
                            Divider()
                                .padding(.vertical, 8)
                            
                            Text("Activity")
                                .font(.headline)
                                .foregroundColor(Color("TextColor"))
                                .padding(.top, 8)
                            
                            DetailRow(title: "Orders Created", value: "\(activity.ordersCreated)")
                            DetailRow(title: "Total Spent", value: "$\(activity.totalSpent, specifier: "%.2f")")
                            
                            if let lastActive = activity.formattedLastActiveDate {
                                DetailRow(title: "Last Active", value: lastActive)
                            }
                        }
                    }
                }
                .padding()
                .background(Color("CardBackgroundColor"))
                .cornerRadius(12)
                
                // Action Buttons
                if !isEditing {
                    VStack(spacing: 12) {
                        Button(action: {
                            showingResetPasswordAlert = true
                        }) {
                            HStack {
                                Spacer()
                                
                                Image(systemName: "lock.rotation")
                                Text("Reset Password")
                                
                                Spacer()
                            }
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding()
                            .background(Color("PrimaryColor"))
                            .cornerRadius(12)
                        }
                        
                        Button(action: {
                            showingDeleteAlert = true
                        }) {
                            HStack {
                                Spacer()
                                
                                Image(systemName: "trash")
                                Text("Delete User")
                                
                                Spacer()
                            }
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.red)
                            .cornerRadius(12)
                        }
                        .disabled(isDeleting)
                    }
                    .padding(.top, 8)
                }
            }
            .padding()
        }
        .navigationTitle("User Details")
        .navigationBarTitleDisplayMode(.inline)
        .alert("Delete User", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                deleteUser()
            }
        } message: {
            Text("Are you sure you want to delete this user? This action cannot be undone.")
        }
        .alert("Reset Password", isPresented: $showingResetPasswordAlert) {
            SecureField("New Password", text: $newPassword)
            SecureField("Confirm Password", text: $confirmPassword)
            Button("Cancel", role: .cancel) { 
                newPassword = ""
                confirmPassword = ""
            }
            Button("Reset", role: .destructive) {
                resetPassword()
            }
            .disabled(newPassword.isEmpty || newPassword != confirmPassword)
        } message: {
            Text("Enter a new password for this user.")
        }
        .onAppear {
            loadUserActivity()
        }
    }
    
    // Start editing
    private func startEditing() {
        editedEmail = user.email
        editedFirstName = user.firstName
        editedLastName = user.lastName
        editedRole = user.role
        editedIsActive = user.isActive
        
        isEditing = true
    }
    
    // Cancel editing
    private func cancelEditing() {
        isEditing = false
    }
    
    // Save changes
    private func saveChanges() {
        guard let token = authService.token else { return }
        
        isUpdating = true
        
        let updateRequest = UpdateUserRequest(
            id: user.id,
            email: editedEmail,
            firstName: editedFirstName,
            lastName: editedLastName,
            role: editedRole,
            isActive: editedIsActive
        )
        
        userManagementService.updateUser(user: updateRequest, token: token) { success in
            isUpdating = false
            
            if success {
                isEditing = false
            }
        }
    }
    
    // Delete user
    private func deleteUser() {
        guard let token = authService.token else { return }
        
        isDeleting = true
        
        let deleteRequest = DeleteUserRequest(id: user.id)
        
        userManagementService.deleteUser(request: deleteRequest, token: token) { success in
            isDeleting = false
            
            if success {
                presentationMode.wrappedValue.dismiss()
            }
        }
    }
    
    // Reset password
    private func resetPassword() {
        guard let token = authService.token else { return }
        guard newPassword == confirmPassword else { return }
        
        isResettingPassword = true
        
        let resetRequest = UpdatePasswordRequest(id: user.id, password: newPassword)
        
        userManagementService.updatePassword(request: resetRequest, token: token) { success in
            isResettingPassword = false
            
            if success {
                newPassword = ""
                confirmPassword = ""
            }
        }
    }
    
    // Load user activity
    private func loadUserActivity() {
        guard let token = authService.token else { return }
        
        userManagementService.getUserActivity(userId: user.id, token: token) { activity in
            self.userActivity = activity
        }
    }
    
    // Validate form
    private var isFormValid: Bool {
        return !editedEmail.isEmpty && !editedFirstName.isEmpty && !editedLastName.isEmpty
    }
    
    private func roleColor(_ role: String) -> Color {
        switch role {
        case "admin":
            return Color("PrimaryColor")
        case "manager":
            return Color("SuccessColor")
        default:
            return Color("InfoColor")
        }
    }
}
