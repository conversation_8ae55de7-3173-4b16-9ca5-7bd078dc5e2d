import SwiftUI

struct UserListView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var userManagementService: UserManagementService
    
    @State private var searchText = ""
    @State private var roleFilter: UserRole? = nil
    @State private var activeOnly = false
    @State private var showingAddUser = false
    @State private var isRefreshing = false
    
    var body: some View {
        NavigationView {
            ZStack {
                Color("BackgroundColor")
                    .ignoresSafeArea()
                
                VStack {
                    // Search and Filter Bar
                    HStack {
                        // Search Field
                        HStack {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(Color("SecondaryColor"))
                            
                            TextField("Search users", text: $searchText)
                                .foregroundColor(Color("TextColor"))
                        }
                        .padding(10)
                        .background(Color("FieldBackgroundColor"))
                        .cornerRadius(10)
                        
                        // Filter Button
                        Menu {
                            // Role Filter
                            Menu {
                                Button(action: {
                                    roleFilter = nil
                                }) {
                                    Label("All Roles", systemImage: "person.3")
                                }
                                
                                Button(action: {
                                    roleFilter = .admin
                                }) {
                                    Label("Admin", systemImage: "person.fill.checkmark")
                                }
                                
                                Button(action: {
                                    roleFilter = .manager
                                }) {
                                    Label("Manager", systemImage: "person.fill.badge.plus")
                                }
                                
                                Button(action: {
                                    roleFilter = .user
                                }) {
                                    Label("User", systemImage: "person")
                                }
                            } label: {
                                Label("Role", systemImage: "person.text.rectangle")
                            }
                            
                            // Active Only Filter
                            Button(action: {
                                activeOnly.toggle()
                            }) {
                                Label(activeOnly ? "Show All" : "Active Only", systemImage: activeOnly ? "checkmark.circle.fill" : "checkmark.circle")
                            }
                        } label: {
                            Image(systemName: "line.3.horizontal.decrease.circle")
                                .font(.title2)
                                .foregroundColor(Color("PrimaryColor"))
                        }
                        .padding(.leading, 8)
                    }
                    .padding(.horizontal)
                    .padding(.top, 8)
                    
                    // User List
                    if userManagementService.isLoading && userManagementService.users.isEmpty {
                        Spacer()
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(1.5)
                        Spacer()
                    } else if userManagementService.users.isEmpty {
                        Spacer()
                        VStack(spacing: 16) {
                            Image(systemName: "person.3")
                                .font(.system(size: 60))
                                .foregroundColor(Color("SecondaryColor").opacity(0.5))
                            
                            Text("No Users Found")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(Color("TextColor"))
                            
                            Text("Try adjusting your filters or add a new user")
                                .font(.subheadline)
                                .foregroundColor(Color("TextColor").opacity(0.7))
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 32)
                            
                            Button(action: {
                                showingAddUser = true
                            }) {
                                HStack {
                                    Image(systemName: "plus.circle.fill")
                                    Text("Add New User")
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(Color("PrimaryColor"))
                                .foregroundColor(.white)
                                .cornerRadius(8)
                            }
                            .padding(.top, 8)
                        }
                        Spacer()
                    } else {
                        List {
                            ForEach(filteredUsers) { user in
                                NavigationLink(destination: UserDetailView(user: user)) {
                                    UserRowView(user: user)
                                }
                                .listRowBackground(Color("CardBackgroundColor"))
                            }
                        }
                        .listStyle(PlainListStyle())
                        .refreshable {
                            await refreshUsers()
                        }
                    }
                }
                .navigationTitle("User Management")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(action: {
                            showingAddUser = true
                        }) {
                            Image(systemName: "plus")
                        }
                    }
                }
                .sheet(isPresented: $showingAddUser) {
                    AddUserView()
                }
            }
        }
        .onAppear {
            loadUsers()
        }
    }
    
    private var filteredUsers: [UserManagement] {
        return userManagementService.filteredUsers(
            searchText: searchText,
            roleFilter: roleFilter,
            activeOnly: activeOnly
        )
    }
    
    private func loadUsers() {
        guard let token = authService.token else { return }
        
        userManagementService.getUsers(token: token) { _ in }
    }
    
    private func refreshUsers() async {
        isRefreshing = true
        
        guard let token = authService.token else {
            isRefreshing = false
            return
        }
        
        await withCheckedContinuation { continuation in
            userManagementService.getUsers(token: token) { _ in
                isRefreshing = false
                continuation.resume()
            }
        }
    }
}

struct UserRowView: View {
    let user: UserManagement
    
    var body: some View {
        HStack(spacing: 16) {
            // User Avatar
            if let avatarURL = user.avatarURL {
                AsyncImage(url: avatarURL) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    ProgressView()
                }
                .frame(width: 40, height: 40)
                .clipShape(Circle())
            } else {
                Circle()
                    .fill(Color("PrimaryColor"))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Text(user.initials)
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    )
            }
            
            VStack(alignment: .leading, spacing: 4) {
                // User Name
                Text(user.fullName)
                    .font(.headline)
                    .foregroundColor(Color("TextColor"))
                
                // User Email
                Text(user.email)
                    .font(.subheadline)
                    .foregroundColor(Color("SecondaryColor"))
            }
            
            Spacer()
            
            // Role and Status
            VStack(alignment: .trailing, spacing: 4) {
                Text(user.role.capitalized)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(roleColor(user.role).opacity(0.2))
                    .foregroundColor(roleColor(user.role))
                    .cornerRadius(4)
                
                if !user.isActive {
                    Text("Inactive")
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color("SecondaryColor").opacity(0.2))
                        .foregroundColor(Color("SecondaryColor"))
                        .cornerRadius(4)
                }
            }
        }
        .padding(.vertical, 8)
    }
    
    private func roleColor(_ role: String) -> Color {
        switch role {
        case "admin":
            return Color("PrimaryColor")
        case "manager":
            return Color("SuccessColor")
        default:
            return Color("InfoColor")
        }
    }
}

struct UserListView_Previews: PreviewProvider {
    static var previews: some View {
        UserListView()
            .environmentObject(AuthService())
            .environmentObject(UserManagementService())
    }
}
