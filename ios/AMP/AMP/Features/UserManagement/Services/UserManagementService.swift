import Foundation
import Combine
import SwiftUI

/// Service for managing users
class UserManagementService: ObservableObject {
    @Published var users: [UserManagement] = []
    @Published var userActivities: [Int: UserActivity] = [:]
    @Published var isLoading = false
    @Published var error: String?
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Get Users
    func getUsers(token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.get(endpoint: "/user/list_users/", token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (users: [UserManagement]) in
                    self?.users = users
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Create User
    func createUser(user: CreateUserRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.post(endpoint: "/user/create_user/", body: user, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (users: [UserManagement]) in
                    self?.users = users
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Update User
    func updateUser(user: UpdateUserRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.put(endpoint: "/user/update_user/", body: user, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (users: [UserManagement]) in
                    self?.users = users
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Update Password
    func updatePassword(request: UpdatePasswordRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.put(endpoint: "/user/update_password/", body: request, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { (_: EmptyResponse) in
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Delete User
    func deleteUser(request: DeleteUserRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.put(endpoint: "/user/delete_user/", body: request, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (users: [UserManagement]) in
                    self?.users = users
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Get User Activity
    func getUserActivity(userId: Int, token: String, onComplete: @escaping (UserActivity?) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.get(endpoint: "/user/activity/\(userId)/", token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(nil)
                        }
                    }
                },
                receiveValue: { [weak self] (activity: UserActivity) in
                    self?.userActivities[userId] = activity
                    DispatchQueue.main.async {
                        onComplete(activity)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Filter Users
    func filteredUsers(searchText: String, roleFilter: UserRole? = nil, activeOnly: Bool = false) -> [UserManagement] {
        return users.filter { user in
            let matchesSearch = searchText.isEmpty || 
                user.fullName.localizedCaseInsensitiveContains(searchText) ||
                user.email.localizedCaseInsensitiveContains(searchText)
            
            let matchesRole = roleFilter == nil || user.userRole == roleFilter
            
            let matchesActive = !activeOnly || user.isActive
            
            return matchesSearch && matchesRole && matchesActive
        }
    }
    
    // MARK: - Get User by ID
    func getUser(by id: Int) -> UserManagement? {
        return users.first { $0.id == id }
    }
    
    // MARK: - Get User Activity by ID
    func getUserActivity(by id: Int) -> UserActivity? {
        return userActivities[id]
    }
}
