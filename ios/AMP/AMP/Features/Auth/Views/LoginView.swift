import SwiftUI

struct LoginView: View {
    @EnvironmentObject var authService: AuthService
    @State private var email = ""
    @State private var password = ""
    @State private var showPassword = false
    @State private var rememberMe = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        ZStack {
            // Background
            Color("BackgroundColor")
                .ignoresSafeArea()
            
            VStack {
                // Logo
                Image("AppLogo")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 120, height: 120)
                    .padding(.top, 60)
                
                Text("AMP")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(Color("PrimaryColor"))
                
                Text("A Medical Partner")
                    .font(.headline)
                    .foregroundColor(Color("SecondaryColor"))
                    .padding(.bottom, 40)
                
                // Login Form
                VStack(spacing: 20) {
                    // Email Field
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Email")
                            .font(.headline)
                            .foregroundColor(Color("TextColor"))
                        
                        TextField("Enter your email", text: $email)
                            .padding()
                            .background(Color("FieldBackgroundColor"))
                            .cornerRadius(10)
                            .keyboardType(.emailAddress)
                            .autocapitalization(.none)
                            .disableAutocorrection(true)
                    }
                    
                    // Password Field
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Password")
                            .font(.headline)
                            .foregroundColor(Color("TextColor"))
                        
                        HStack {
                            if showPassword {
                                TextField("Enter your password", text: $password)
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(10)
                            } else {
                                SecureField("Enter your password", text: $password)
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(10)
                            }
                            
                            Button(action: {
                                showPassword.toggle()
                            }) {
                                Image(systemName: showPassword ? "eye.slash.fill" : "eye.fill")
                                    .foregroundColor(Color("SecondaryColor"))
                            }
                            .padding(.trailing, 16)
                        }
                        .background(Color("FieldBackgroundColor"))
                        .cornerRadius(10)
                    }
                    
                    // Remember Me
                    HStack {
                        Button(action: {
                            rememberMe.toggle()
                        }) {
                            HStack {
                                Image(systemName: rememberMe ? "checkmark.square.fill" : "square")
                                    .foregroundColor(Color("PrimaryColor"))
                                
                                Text("Remember me")
                                    .font(.subheadline)
                                    .foregroundColor(Color("TextColor"))
                            }
                        }
                        
                        Spacer()
                        
                        Button(action: {
                            // Forgot password action
                        }) {
                            Text("Forgot password?")
                                .font(.subheadline)
                                .foregroundColor(Color("PrimaryColor"))
                        }
                    }
                    
                    // Login Button
                    Button(action: {
                        login()
                    }) {
                        ZStack {
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color("PrimaryColor"))
                                .frame(height: 50)
                            
                            if authService.isLoading {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            } else {
                                Text("Sign In")
                                    .font(.headline)
                                    .foregroundColor(.white)
                            }
                        }
                    }
                    .disabled(authService.isLoading || email.isEmpty || password.isEmpty)
                    .opacity(authService.isLoading || email.isEmpty || password.isEmpty ? 0.7 : 1)
                }
                .padding(.horizontal, 24)
                
                Spacer()
                
                // Footer
                VStack {
                    Text("© \(Calendar.current.component(.year, from: Date())) A Medical Partner. All rights reserved.")
                        .font(.caption)
                        .foregroundColor(Color("TextColor").opacity(0.7))
                        .padding(.bottom, 16)
                }
            }
        }
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text("Error"),
                message: Text(alertMessage),
                dismissButton: .default(Text("OK"))
            )
        }
    }
    
    private func login() {
        authService.signIn(email: email, password: password) { success in
            if !success {
                alertMessage = authService.error ?? "Failed to sign in. Please try again."
                showAlert = true
            }
        }
    }
}

struct LoginView_Previews: PreviewProvider {
    static var previews: some View {
        LoginView()
            .environmentObject(AuthService())
    }
}
