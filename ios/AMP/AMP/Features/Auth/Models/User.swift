import Foundation

/// Represents a user in the system
struct User: Codable, Identifiable, Equatable {
    let id: Int
    let email: String
    let firstName: String
    let lastName: String
    let role: String
    let isActive: Bool
    let createdAt: String
    let updatedAt: String
    
    /// Returns the user's full name
    var fullName: String {
        return "\(firstName) \(lastName)"
    }
    
    /// Returns the user's initials
    var initials: String {
        let firstInitial = firstName.first?.uppercased() ?? ""
        let lastInitial = lastName.first?.uppercased() ?? ""
        return "\(firstInitial)\(lastInitial)"
    }
    
    /// Returns the user's avatar URL
    var avatarURL: URL? {
        return URL(string: "https://ui-avatars.com/api/?name=\(firstName)+\(lastName)&background=random")
    }
    
    /// Returns the user's role as an enum
    var userRole: UserRole {
        switch role {
        case "admin":
            return .admin
        case "manager":
            return .manager
        case "user":
            return .user
        default:
            return .user
        }
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case email
        case firstName = "first_name"
        case lastName = "last_name"
        case role
        case isActive = "is_active"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
    
    static func == (lhs: User, rhs: User) -> Bool {
        return lhs.id == rhs.id
    }
}

/// User roles
enum UserRole: String {
    case admin
    case manager
    case user
}
