import Foundation
import Combine
import SwiftUI

/// Service for authentication
class AuthService: ObservableObject {
    @Published var isAuthenticated = false
    @Published var isLoading = false
    @Published var user: User?
    @Published var token: String?
    @Published var error: String?
    @Published var selectedUser: User?
    @Published var userList: [User] = []
    
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        // Check if user is already authenticated
        checkAuthState()
    }
    
    // MARK: - Sign In
    func signIn(email: String, password: String, completion: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        // Create form data
        let formData = [
            "username": email,
            "password": password
        ]
        
        // Make request
        APIClient.shared.post(endpoint: "/auth/login/", body: formData)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        completion(false)
                    }
                },
                receiveValue: { [weak self] (response: LoginResponse) in
                    self?.token = response.accessToken
                    self?.isAuthenticated = true
                    
                    // Save token to keychain
                    KeychainService.shared.save(key: Config.KeychainKeys.authToken, value: response.accessToken)
                    
                    // Fetch user details after successful login
                    self?.getCurrentUser { success in
                        completion(success)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Get Current User
    func getCurrentUser(completion: @escaping (Bool) -> Void) {
        guard let token = token else {
            completion(false)
            return
        }
        
        isLoading = true
        
        APIClient.shared.post(endpoint: "/user/", token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        
                        // If we get an unauthorized error, sign out
                        if error.isAuthError {
                            self?.signOut()
                        }
                        
                        completion(false)
                    }
                },
                receiveValue: { [weak self] (user: User) in
                    self?.user = user
                    self?.saveUserToUserDefaults(user)
                    
                    // If user is admin, fetch user list
                    if user.userRole == .admin || user.userRole == .manager {
                        self?.getUserList { _ in
                            completion(true)
                        }
                    } else {
                        completion(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Get User List
    func getUserList(completion: @escaping (Bool) -> Void) {
        guard let token = token else {
            completion(false)
            return
        }
        
        isLoading = true
        
        APIClient.shared.get(endpoint: "/user/user_list", token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        completion(false)
                    }
                },
                receiveValue: { [weak self] (users: [User]) in
                    self?.userList = users.sorted { $0.firstName < $1.firstName }
                    completion(true)
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Sign Out
    func signOut() {
        // Clear auth state
        self.token = nil
        self.user = nil
        self.isAuthenticated = false
        self.selectedUser = nil
        self.userList = []
        
        // Clear keychain and user defaults
        KeychainService.shared.delete(key: Config.KeychainKeys.authToken)
        UserDefaults.standard.removeObject(forKey: Config.UserDefaultsKeys.user)
    }
    
    // MARK: - Auth State Management
    func checkAuthState() {
        // Check if token is stored in keychain
        if let token = KeychainService.shared.get(key: Config.KeychainKeys.authToken),
           let userData = UserDefaults.standard.data(forKey: Config.UserDefaultsKeys.user),
           let user = try? JSONDecoder().decode(User.self, from: userData) {
            self.token = token
            self.user = user
            self.isAuthenticated = true
            
            // Verify the token is still valid by fetching the current user
            getCurrentUser { _ in }
        }
    }
    
    // MARK: - Select User
    func selectUser(_ user: User) {
        self.selectedUser = user
        
        // Save selected user to user defaults
        if let userData = try? JSONEncoder().encode(user) {
            UserDefaults.standard.set(userData, forKey: "selectedUser")
        }
    }
    
    // MARK: - Helper Methods
    private func saveUserToUserDefaults(_ user: User) {
        if let userData = try? JSONEncoder().encode(user) {
            UserDefaults.standard.set(userData, forKey: Config.UserDefaultsKeys.user)
        }
    }
}
