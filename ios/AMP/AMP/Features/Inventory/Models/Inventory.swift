import Foundation

/// Represents an inventory item
struct InventoryItem: Codable, Identifiable, Equatable {
    let id: Int
    let product: Product
    let qty: Int
    let minQty: Int
    let expirationDate: String?
    let notes: String?
    let admin: Int
    let createdAt: String
    let updatedAt: String
    
    /// Returns the formatted expiration date
    var formattedExpirationDate: String? {
        guard let expirationDate = expirationDate else { return nil }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        if let date = dateFormatter.date(from: expirationDate) {
            dateFormatter.dateFormat = "MMM d, yyyy"
            return dateFormatter.string(from: date)
        }
        
        return expirationDate
    }
    
    /// Returns the stock status
    var stockStatus: StockStatus {
        if qty <= 0 {
            return .outOfStock
        } else if qty < minQty {
            return .lowStock
        } else {
            return .inStock
        }
    }
    
    /// Returns true if the item is expiring soon (within 6 months)
    var isExpiringSoon: Bool {
        guard let expirationDate = expirationDate else { return false }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        guard let expDate = dateFormatter.date(from: expirationDate) else { return false }
        
        let calendar = Calendar.current
        let sixMonthsFromNow = calendar.date(byAdding: .month, value: 6, to: Date())!
        
        return expDate <= sixMonthsFromNow
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case product
        case qty
        case minQty = "min_qty"
        case expirationDate = "expiration_date"
        case notes
        case admin
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
    
    static func == (lhs: InventoryItem, rhs: InventoryItem) -> Bool {
        return lhs.id == rhs.id
    }
}

/// Stock status
enum StockStatus: String, CaseIterable {
    case inStock = "In Stock"
    case lowStock = "Low Stock"
    case outOfStock = "Out of Stock"
    
    /// Returns the color for the status
    var color: String {
        switch self {
        case .inStock:
            return "SuccessColor"
        case .lowStock:
            return "WarningColor"
        case .outOfStock:
            return "ErrorColor"
        }
    }
}

/// Request to create a new inventory item
struct CreateInventoryRequest: Codable {
    let adminId: Int
    let productId: Int
    let qty: Int
    let minQty: Int
    let expirationDate: String?
    let notes: String?
    
    enum CodingKeys: String, CodingKey {
        case adminId = "admin_id"
        case productId = "product_id"
        case qty
        case minQty = "min_qty"
        case expirationDate = "expiration_date"
        case notes
    }
}

/// Request to update an existing inventory item
struct UpdateInventoryRequest: Codable {
    let id: Int
    let adminId: Int
    let productId: Int
    let qty: Int
    let minQty: Int
    let expirationDate: String?
    let notes: String?
    
    enum CodingKeys: String, CodingKey {
        case id
        case adminId = "admin_id"
        case productId = "product_id"
        case qty
        case minQty = "min_qty"
        case expirationDate = "expiration_date"
        case notes
    }
}

/// Request to delete an inventory item
struct DeleteInventoryRequest: Codable {
    let id: Int
    let adminId: Int
    
    enum CodingKeys: String, CodingKey {
        case id
        case adminId = "admin_id"
    }
}
