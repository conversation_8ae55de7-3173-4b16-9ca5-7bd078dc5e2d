import Foundation
import Combine
import SwiftUI

/// Service for managing inventory
class InventoryService: ObservableObject {
    @Published var inventoryItems: [InventoryItem] = []
    @Published var isLoading = false
    @Published var error: String?
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Get User Inventory
    func getUserInventory(userId: Int, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.post(endpoint: "/inventory/list_user_inventory/\(userId)/", token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (items: [InventoryItem]) in
                    self?.inventoryItems = items
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Create Inventory Item
    func createInventoryItem(item: CreateInventoryRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.post(endpoint: "/inventory/create_inventory/", body: item, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (items: [InventoryItem]) in
                    self?.inventoryItems = items
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Update Inventory Item
    func updateInventoryItem(item: UpdateInventoryRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.post(endpoint: "/inventory/update_inventory/", body: item, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (items: [InventoryItem]) in
                    self?.inventoryItems = items
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Delete Inventory Item
    func deleteInventoryItem(deleteRequest: DeleteInventoryRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.post(endpoint: "/inventory/delete_inventory/", body: deleteRequest, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (items: [InventoryItem]) in
                    self?.inventoryItems = items
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Filter Inventory Items
    func filteredInventoryItems(searchText: String, statusFilter: StockStatus? = nil, expiringOnly: Bool = false) -> [InventoryItem] {
        return inventoryItems.filter { item in
            let matchesSearch = searchText.isEmpty || 
                item.product.name.localizedCaseInsensitiveContains(searchText) ||
                item.product.sku.localizedCaseInsensitiveContains(searchText) ||
                (item.notes?.localizedCaseInsensitiveContains(searchText) ?? false)
            
            let matchesStatus = statusFilter == nil || item.stockStatus == statusFilter
            
            let matchesExpiring = !expiringOnly || item.isExpiringSoon
            
            return matchesSearch && matchesStatus && matchesExpiring
        }
    }
    
    // MARK: - Get Low Stock Items
    var lowStockItems: [InventoryItem] {
        return inventoryItems.filter { $0.stockStatus == .lowStock || $0.stockStatus == .outOfStock }
    }
    
    // MARK: - Get Expiring Items
    var expiringItems: [InventoryItem] {
        return inventoryItems.filter { $0.isExpiringSoon }
    }
    
    // MARK: - Get Items Sorted By Quantity
    var itemsSortedByQuantity: [InventoryItem] {
        return inventoryItems.sorted { $0.qty < $1.qty }
    }
    
    // MARK: - Get Items Sorted By Expiration
    var itemsSortedByExpiration: [InventoryItem] {
        let itemsWithExpiration = inventoryItems.filter { $0.expirationDate != nil }
        let itemsWithoutExpiration = inventoryItems.filter { $0.expirationDate == nil }
        
        let sortedItemsWithExpiration = itemsWithExpiration.sorted { item1, item2 in
            guard let date1 = item1.expirationDate, let date2 = item2.expirationDate else {
                return false
            }
            
            return date1 < date2
        }
        
        return sortedItemsWithExpiration + itemsWithoutExpiration
    }
    
    // MARK: - Get Items Sorted By Priority
    var itemsSortedByPriority: [InventoryItem] {
        // First, get items that are both low stock and expiring soon
        let criticalItems = inventoryItems.filter { $0.stockStatus != .inStock && $0.isExpiringSoon }
        
        // Next, get items that are expiring soon but not low stock
        let expiringItems = inventoryItems.filter { $0.stockStatus == .inStock && $0.isExpiringSoon }
        
        // Next, get items that are low stock but not expiring soon
        let lowStockItems = inventoryItems.filter { $0.stockStatus != .inStock && !$0.isExpiringSoon }
        
        // Finally, get the rest of the items
        let normalItems = inventoryItems.filter { $0.stockStatus == .inStock && !$0.isExpiringSoon }
        
        return criticalItems + expiringItems + lowStockItems + normalItems
    }
}
