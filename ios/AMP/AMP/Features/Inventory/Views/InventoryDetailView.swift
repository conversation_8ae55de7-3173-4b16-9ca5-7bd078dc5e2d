import SwiftUI

struct InventoryDetailView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var inventoryService: InventoryService
    
    let item: InventoryItem
    
    @State private var isEditing = false
    @State private var showingDeleteAlert = false
    @State private var isUpdating = false
    @State private var isDeleting = false
    
    // Editable fields
    @State private var editedQuantity = ""
    @State private var editedMinQuantity = ""
    @State private var editedExpirationDate = Date()
    @State private var hasExpirationDate = false
    @State private var editedNotes = ""
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Inventory Header
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text(item.product.name)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(Color("TextColor"))
                        
                        Spacer()
                        
                        Text(item.stockStatus.rawValue)
                            .font(.subheadline)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(Color(item.stockStatus.color).opacity(0.2))
                            .foregroundColor(Color(item.stockStatus.color))
                            .cornerRadius(8)
                    }
                    
                    Text("SKU: \(item.product.sku)")
                        .font(.subheadline)
                        .foregroundColor(Color("SecondaryColor"))
                }
                .padding()
                .background(Color("CardBackgroundColor"))
                .cornerRadius(12)
                
                // Inventory Details
                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        Text("Inventory Details")
                            .font(.headline)
                            .foregroundColor(Color("TextColor"))
                        
                        Spacer()
                        
                        if !isEditing {
                            Button(action: {
                                startEditing()
                            }) {
                                Text("Edit")
                                    .font(.subheadline)
                                    .foregroundColor(Color("PrimaryColor"))
                            }
                        }
                    }
                    
                    if isEditing {
                        // Editable Fields
                        VStack(spacing: 12) {
                            // Quantity
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Quantity")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                TextField("Quantity", text: $editedQuantity)
                                    .keyboardType(.numberPad)
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                            
                            // Minimum Quantity
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Minimum Quantity")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                TextField("Minimum Quantity", text: $editedMinQuantity)
                                    .keyboardType(.numberPad)
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                            
                            // Expiration Date
                            VStack(alignment: .leading, spacing: 4) {
                                Toggle("Has Expiration Date", isOn: $hasExpirationDate)
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                if hasExpirationDate {
                                    DatePicker(
                                        "Expiration Date",
                                        selection: $editedExpirationDate,
                                        displayedComponents: .date
                                    )
                                    .datePickerStyle(DefaultDatePickerStyle())
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                                }
                            }
                            
                            // Notes
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Notes")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                TextEditor(text: $editedNotes)
                                    .frame(minHeight: 100)
                                    .padding(4)
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                            
                            // Buttons
                            HStack {
                                Button(action: cancelEditing) {
                                    Text("Cancel")
                                        .font(.subheadline)
                                        .foregroundColor(Color("SecondaryColor"))
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(Color("FieldBackgroundColor"))
                                        .cornerRadius(8)
                                }
                                
                                Spacer()
                                
                                Button(action: saveChanges) {
                                    Text("Save")
                                        .font(.subheadline)
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(Color("PrimaryColor"))
                                        .cornerRadius(8)
                                }
                                .disabled(isUpdating || !isFormValid)
                            }
                        }
                    } else {
                        // Display Inventory Details
                        DetailRow(title: "Quantity", value: "\(item.qty)")
                        DetailRow(title: "Minimum Quantity", value: "\(item.minQty)")
                        
                        if let expirationDate = item.formattedExpirationDate {
                            DetailRow(title: "Expiration Date", value: expirationDate)
                            
                            if item.isExpiringSoon {
                                HStack {
                                    Spacer()
                                    
                                    Label("Expiring Soon", systemImage: "exclamationmark.triangle")
                                        .font(.caption)
                                        .foregroundColor(Color("WarningColor"))
                                }
                            }
                        }
                        
                        if let notes = item.notes, !notes.isEmpty {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Notes")
                                    .font(.subheadline)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                Text(notes)
                                    .font(.body)
                                    .foregroundColor(Color("TextColor"))
                                    .padding()
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                        }
                        
                        DetailRow(title: "Created", value: formatDate(item.createdAt))
                        DetailRow(title: "Updated", value: formatDate(item.updatedAt))
                    }
                }
                .padding()
                .background(Color("CardBackgroundColor"))
                .cornerRadius(12)
                
                // Action Buttons
                if !isEditing {
                    Button(action: {
                        showingDeleteAlert = true
                    }) {
                        HStack {
                            Spacer()
                            
                            Image(systemName: "trash")
                            Text("Delete Item")
                            
                            Spacer()
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.red)
                        .cornerRadius(12)
                    }
                    .disabled(isDeleting)
                    .padding(.top, 8)
                }
            }
            .padding()
        }
        .navigationTitle("Inventory Details")
        .navigationBarTitleDisplayMode(.inline)
        .alert(isPresented: $showingDeleteAlert) {
            Alert(
                title: Text("Delete Inventory Item"),
                message: Text("Are you sure you want to delete this inventory item? This action cannot be undone."),
                primaryButton: .destructive(Text("Delete")) {
                    deleteInventoryItem()
                },
                secondaryButton: .cancel()
            )
        }
    }
    
    // Start editing
    private func startEditing() {
        editedQuantity = "\(item.qty)"
        editedMinQuantity = "\(item.minQty)"
        
        if let expirationDateString = item.expirationDate {
            hasExpirationDate = true
            
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            
            if let date = dateFormatter.date(from: expirationDateString) {
                editedExpirationDate = date
            } else {
                editedExpirationDate = Date()
            }
        } else {
            hasExpirationDate = false
            editedExpirationDate = Date()
        }
        
        editedNotes = item.notes ?? ""
        
        isEditing = true
    }
    
    // Cancel editing
    private func cancelEditing() {
        isEditing = false
    }
    
    // Save changes
    private func saveChanges() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        isUpdating = true
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        let updateRequest = UpdateInventoryRequest(
            id: item.id,
            adminId: authService.selectedUser?.id ?? user.id,
            productId: item.product.id,
            qty: Int(editedQuantity) ?? item.qty,
            minQty: Int(editedMinQuantity) ?? item.minQty,
            expirationDate: hasExpirationDate ? dateFormatter.string(from: editedExpirationDate) : nil,
            notes: editedNotes.isEmpty ? nil : editedNotes
        )
        
        inventoryService.updateInventoryItem(item: updateRequest, token: token) { success in
            isUpdating = false
            
            if success {
                isEditing = false
            }
        }
    }
    
    // Delete inventory item
    private func deleteInventoryItem() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        isDeleting = true
        
        let deleteRequest = DeleteInventoryRequest(
            id: item.id,
            adminId: authService.selectedUser?.id ?? user.id
        )
        
        inventoryService.deleteInventoryItem(deleteRequest: deleteRequest, token: token) { success in
            isDeleting = false
            
            if success {
                // Navigate back to inventory list
                // In a real app, we would use a navigation coordinator or similar
            }
        }
    }
    
    // Format date
    private func formatDate(_ dateString: String) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        
        if let date = dateFormatter.date(from: dateString) {
            dateFormatter.dateFormat = "MMM d, yyyy h:mm a"
            return dateFormatter.string(from: date)
        }
        
        return dateString
    }
    
    // Validate form
    private var isFormValid: Bool {
        guard let quantity = Int(editedQuantity), quantity >= 0 else { return false }
        guard let minQuantity = Int(editedMinQuantity), minQuantity >= 0 else { return false }
        
        return true
    }
}
