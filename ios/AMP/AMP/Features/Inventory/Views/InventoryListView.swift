import SwiftUI

struct InventoryListView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var inventoryService: InventoryService
    
    @State private var searchText = ""
    @State private var statusFilter: StockStatus? = nil
    @State private var expiringOnly = false
    @State private var sortOption = SortOption.priority
    @State private var showingAddInventory = false
    @State private var isRefreshing = false
    
    enum SortOption: String, CaseIterable {
        case priority = "Priority"
        case quantity = "Quantity"
        case expiration = "Expiration"
        case name = "Name"
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color("BackgroundColor")
                    .ignoresSafeArea()
                
                VStack {
                    // Search and Filter Bar
                    HStack {
                        // Search Field
                        HStack {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(Color("SecondaryColor"))
                            
                            TextField("Search inventory", text: $searchText)
                                .foregroundColor(Color("TextColor"))
                        }
                        .padding(10)
                        .background(Color("FieldBackgroundColor"))
                        .cornerRadius(10)
                        
                        // Filter Button
                        Menu {
                            // Status Filter
                            Menu {
                                Button(action: {
                                    statusFilter = nil
                                }) {
                                    Label("All", systemImage: "list.bullet")
                                }
                                
                                ForEach(StockStatus.allCases, id: \.self) { status in
                                    Button(action: {
                                        statusFilter = status
                                    }) {
                                        Label(status.rawValue, systemImage: statusIcon(for: status))
                                    }
                                }
                            } label: {
                                Label("Status", systemImage: "tag")
                            }
                            
                            // Expiring Filter
                            Button(action: {
                                expiringOnly.toggle()
                            }) {
                                Label(expiringOnly ? "Show All" : "Expiring Only", systemImage: expiringOnly ? "calendar.badge.exclamationmark" : "calendar")
                            }
                            
                            Divider()
                            
                            // Sort Options
                            Menu {
                                ForEach(SortOption.allCases, id: \.self) { option in
                                    Button(action: {
                                        sortOption = option
                                    }) {
                                        Label(option.rawValue, systemImage: sortIcon(for: option))
                                    }
                                }
                            } label: {
                                Label("Sort By", systemImage: "arrow.up.arrow.down")
                            }
                        } label: {
                            Image(systemName: "line.3.horizontal.decrease.circle")
                                .font(.title2)
                                .foregroundColor(Color("PrimaryColor"))
                        }
                        .padding(.leading, 8)
                    }
                    .padding(.horizontal)
                    .padding(.top, 8)
                    
                    // Inventory List
                    if inventoryService.isLoading && inventoryService.inventoryItems.isEmpty {
                        Spacer()
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(1.5)
                        Spacer()
                    } else if inventoryService.inventoryItems.isEmpty {
                        Spacer()
                        VStack(spacing: 16) {
                            Image(systemName: "cube.box")
                                .font(.system(size: 60))
                                .foregroundColor(Color("SecondaryColor").opacity(0.5))
                            
                            Text("No Inventory Items Found")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(Color("TextColor"))
                            
                            Text("Try adjusting your filters or add a new inventory item")
                                .font(.subheadline)
                                .foregroundColor(Color("TextColor").opacity(0.7))
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 32)
                            
                            Button(action: {
                                showingAddInventory = true
                            }) {
                                HStack {
                                    Image(systemName: "plus.circle.fill")
                                    Text("Add New Item")
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(Color("PrimaryColor"))
                                .foregroundColor(.white)
                                .cornerRadius(8)
                            }
                            .padding(.top, 8)
                        }
                        Spacer()
                    } else {
                        List {
                            ForEach(sortedFilteredItems) { item in
                                NavigationLink(destination: InventoryDetailView(item: item)) {
                                    InventoryRowView(item: item)
                                }
                                .listRowBackground(Color("CardBackgroundColor"))
                            }
                        }
                        .listStyle(PlainListStyle())
                        .refreshable {
                            await refreshInventory()
                        }
                    }
                }
                .navigationTitle("Inventory")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(action: {
                            showingAddInventory = true
                        }) {
                            Image(systemName: "plus")
                        }
                    }
                }
                .sheet(isPresented: $showingAddInventory) {
                    AddInventoryView()
                }
            }
        }
        .onAppear {
            loadInventory()
        }
    }
    
    private var sortedFilteredItems: [InventoryItem] {
        let filteredItems = inventoryService.filteredInventoryItems(
            searchText: searchText,
            statusFilter: statusFilter,
            expiringOnly: expiringOnly
        )
        
        switch sortOption {
        case .priority:
            // Items that are both low stock and expiring first, then low stock, then expiring, then normal
            let criticalItems = filteredItems.filter { $0.stockStatus != .inStock && $0.isExpiringSoon }
            let expiringItems = filteredItems.filter { $0.stockStatus == .inStock && $0.isExpiringSoon }
            let lowStockItems = filteredItems.filter { $0.stockStatus != .inStock && !$0.isExpiringSoon }
            let normalItems = filteredItems.filter { $0.stockStatus == .inStock && !$0.isExpiringSoon }
            
            return criticalItems + expiringItems + lowStockItems + normalItems
            
        case .quantity:
            // Sort by quantity (ascending)
            return filteredItems.sorted { $0.qty < $1.qty }
            
        case .expiration:
            // Items with expiration dates first, sorted by date, then items without expiration dates
            let itemsWithExpiration = filteredItems.filter { $0.expirationDate != nil }
            let itemsWithoutExpiration = filteredItems.filter { $0.expirationDate == nil }
            
            let sortedItemsWithExpiration = itemsWithExpiration.sorted { item1, item2 in
                guard let date1 = item1.expirationDate, let date2 = item2.expirationDate else {
                    return false
                }
                
                return date1 < date2
            }
            
            return sortedItemsWithExpiration + itemsWithoutExpiration
            
        case .name:
            // Sort by product name
            return filteredItems.sorted { $0.product.name < $1.product.name }
        }
    }
    
    private func statusIcon(for status: StockStatus) -> String {
        switch status {
        case .inStock:
            return "checkmark.circle"
        case .lowStock:
            return "exclamationmark.triangle"
        case .outOfStock:
            return "xmark.circle"
        }
    }
    
    private func sortIcon(for option: SortOption) -> String {
        switch option {
        case .priority:
            return "exclamationmark.triangle"
        case .quantity:
            return "number"
        case .expiration:
            return "calendar"
        case .name:
            return "textformat"
        }
    }
    
    private func loadInventory() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        let userId = authService.selectedUser?.id ?? user.id
        
        inventoryService.getUserInventory(userId: userId, token: token) { _ in }
    }
    
    private func refreshInventory() async {
        isRefreshing = true
        
        guard let user = authService.user else {
            isRefreshing = false
            return
        }
        guard let token = authService.token else {
            isRefreshing = false
            return
        }
        
        let userId = authService.selectedUser?.id ?? user.id
        
        await withCheckedContinuation { continuation in
            inventoryService.getUserInventory(userId: userId, token: token) { _ in
                isRefreshing = false
                continuation.resume()
            }
        }
    }
}

struct InventoryRowView: View {
    let item: InventoryItem
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // Status Indicator
            Circle()
                .fill(Color(item.stockStatus.color))
                .frame(width: 12, height: 12)
                .padding(.top, 4)
            
            VStack(alignment: .leading, spacing: 4) {
                // Product Name
                HStack {
                    Text(item.product.name)
                        .font(.headline)
                        .foregroundColor(Color("TextColor"))
                    
                    Spacer()
                    
                    if item.isExpiringSoon, let expirationDate = item.formattedExpirationDate {
                        HStack(spacing: 4) {
                            Image(systemName: "calendar.badge.exclamationmark")
                                .foregroundColor(Color("WarningColor"))
                            
                            Text(expirationDate)
                                .font(.caption)
                                .foregroundColor(Color("WarningColor"))
                        }
                    }
                }
                
                // SKU
                Text(item.product.sku)
                    .font(.subheadline)
                    .foregroundColor(Color("SecondaryColor"))
                
                // Quantity and Status
                HStack {
                    Text("Qty: \(item.qty)")
                        .font(.subheadline)
                        .foregroundColor(Color("TextColor"))
                    
                    if item.minQty > 0 {
                        Text("Min: \(item.minQty)")
                            .font(.caption)
                            .foregroundColor(Color("SecondaryColor"))
                    }
                    
                    Spacer()
                    
                    Text(item.stockStatus.rawValue)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color(item.stockStatus.color).opacity(0.2))
                        .foregroundColor(Color(item.stockStatus.color))
                        .cornerRadius(4)
                }
            }
        }
        .padding(.vertical, 8)
    }
}

struct InventoryDetailView: View {
    let item: InventoryItem
    
    var body: some View {
        Text("Inventory Detail View - Coming Soon")
            .navigationTitle("Inventory Details")
    }
}

struct AddInventoryView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        Text("Add Inventory View - Coming Soon")
            .navigationTitle("New Inventory Item")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
    }
}

struct InventoryListView_Previews: PreviewProvider {
    static var previews: some View {
        InventoryListView()
            .environmentObject(AuthService())
            .environmentObject(InventoryService())
    }
}
