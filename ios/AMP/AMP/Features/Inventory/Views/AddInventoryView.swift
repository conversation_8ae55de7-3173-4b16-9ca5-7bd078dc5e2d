import SwiftUI

struct AddInventoryView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var inventoryService: InventoryService
    @EnvironmentObject var orderService: OrderService
    
    // Form fields
    @State private var selectedProduct: Product?
    @State private var quantity = ""
    @State private var minQuantity = ""
    @State private var hasExpirationDate = false
    @State private var expirationDate = Date()
    @State private var notes = ""
    @State private var searchText = ""
    
    // UI state
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isSubmitting = false
    @State private var showingProductPicker = false
    
    var body: some View {
        NavigationView {
            Form {
                // Product Section
                Section(header: Text("Product Information")) {
                    if let product = selectedProduct {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(product.name)
                                    .font(.headline)
                                    .foregroundColor(Color("TextColor"))
                                
                                Text("SKU: \(product.sku)")
                                    .font(.caption)
                                    .foregroundColor(Color("SecondaryColor"))
                            }
                            
                            Spacer()
                            
                            Button(action: {
                                selectedProduct = nil
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(Color("SecondaryColor"))
                            }
                        }
                    } else {
                        Button(action: {
                            showingProductPicker = true
                        }) {
                            HStack {
                                Text("Select Product")
                                    .foregroundColor(Color("PrimaryColor"))
                                
                                Spacer()
                                
                                Image(systemName: "chevron.right")
                                    .foregroundColor(Color("SecondaryColor"))
                            }
                        }
                    }
                }
                
                // Quantity Section
                Section(header: Text("Quantity Information")) {
                    TextField("Quantity", text: $quantity)
                        .keyboardType(.numberPad)
                    
                    TextField("Minimum Quantity", text: $minQuantity)
                        .keyboardType(.numberPad)
                }
                
                // Expiration Section
                Section(header: Text("Expiration Information")) {
                    Toggle("Has Expiration Date", isOn: $hasExpirationDate)
                    
                    if hasExpirationDate {
                        DatePicker("Expiration Date", selection: $expirationDate, displayedComponents: .date)
                    }
                }
                
                // Notes Section
                Section(header: Text("Notes")) {
                    TextEditor(text: $notes)
                        .frame(minHeight: 100)
                }
                
                // Submit Button
                Section {
                    Button(action: submitInventoryItem) {
                        HStack {
                            Spacer()
                            
                            if isSubmitting {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                            } else {
                                Text("Add Inventory Item")
                                    .fontWeight(.semibold)
                            }
                            
                            Spacer()
                        }
                    }
                    .disabled(isSubmitting || !isFormValid)
                }
            }
            .navigationTitle("New Inventory Item")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Error"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
            .sheet(isPresented: $showingProductPicker) {
                ProductPickerView(selectedProduct: $selectedProduct, searchText: $searchText)
            }
        }
    }
    
    // Validate form
    private var isFormValid: Bool {
        guard selectedProduct != nil else { return false }
        guard let qty = Int(quantity), qty > 0 else { return false }
        guard let minQty = Int(minQuantity), minQty >= 0 else { return false }
        
        return true
    }
    
    // Submit inventory item
    private func submitInventoryItem() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        guard let product = selectedProduct else { return }
        
        // Validate form
        guard isFormValid else {
            alertMessage = "Please fill in all required fields with valid values."
            showingAlert = true
            return
        }
        
        isSubmitting = true
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        // Create inventory request
        let inventoryRequest = CreateInventoryRequest(
            adminId: authService.selectedUser?.id ?? user.id,
            productId: product.id,
            qty: Int(quantity) ?? 0,
            minQty: Int(minQuantity) ?? 0,
            expirationDate: hasExpirationDate ? dateFormatter.string(from: expirationDate) : nil,
            notes: notes.isEmpty ? nil : notes
        )
        
        // Submit inventory item
        inventoryService.createInventoryItem(item: inventoryRequest, token: token) { success in
            isSubmitting = false
            
            if success {
                presentationMode.wrappedValue.dismiss()
            } else {
                alertMessage = inventoryService.error ?? "Failed to create inventory item. Please try again."
                showingAlert = true
            }
        }
    }
}

struct ProductPickerView: View {
    @EnvironmentObject var orderService: OrderService
    @Binding var selectedProduct: Product?
    @Binding var searchText: String
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            VStack {
                // Search Bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(Color("SecondaryColor"))
                    
                    TextField("Search products", text: $searchText)
                        .foregroundColor(Color("TextColor"))
                }
                .padding(10)
                .background(Color("FieldBackgroundColor"))
                .cornerRadius(10)
                .padding(.horizontal)
                .padding(.top, 8)
                
                // Product List
                List {
                    ForEach(filteredProducts) { product in
                        Button(action: {
                            selectedProduct = product
                            presentationMode.wrappedValue.dismiss()
                        }) {
                            HStack {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(product.name)
                                        .font(.headline)
                                        .foregroundColor(Color("TextColor"))
                                    
                                    Text("SKU: \(product.sku)")
                                        .font(.caption)
                                        .foregroundColor(Color("SecondaryColor"))
                                }
                                
                                Spacer()
                                
                                Image(systemName: "checkmark")
                                    .foregroundColor(Color("PrimaryColor"))
                                    .opacity(product.id == selectedProduct?.id ? 1 : 0)
                            }
                        }
                    }
                }
                .listStyle(PlainListStyle())
            }
            .navigationTitle("Select Product")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
    
    // Get unique products from orders
    private var uniqueProducts: [Product] {
        var productDict = [Int: Product]()
        
        for order in orderService.orders {
            productDict[order.product.id] = order.product
        }
        
        return Array(productDict.values).sorted { $0.name < $1.name }
    }
    
    // Filter products by search text
    private var filteredProducts: [Product] {
        if searchText.isEmpty {
            return uniqueProducts
        } else {
            return uniqueProducts.filter {
                $0.name.localizedCaseInsensitiveContains(searchText) ||
                $0.sku.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
}

struct AddInventoryView_Previews: PreviewProvider {
    static var previews: some View {
        AddInventoryView()
            .environmentObject(AuthService())
            .environmentObject(InventoryService())
            .environmentObject(OrderService())
    }
}
