import Foundation

/// Represents a note in the system
struct Note: Codable, Identifiable, Equatable {
    let id: Int
    let title: String
    let content: String
    let adminId: Int
    let createdAt: String
    let updatedAt: String
    
    /// Returns the formatted created date
    var formattedCreatedDate: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        
        if let date = dateFormatter.date(from: createdAt) {
            dateFormatter.dateFormat = "MMM d, yyyy h:mm a"
            return dateFormatter.string(from: date)
        }
        
        return createdAt
    }
    
    /// Returns the formatted updated date
    var formattedUpdatedDate: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        
        if let date = dateFormatter.date(from: updatedAt) {
            dateFormatter.dateFormat = "MMM d, yyyy h:mm a"
            return dateFormatter.string(from: date)
        }
        
        return updatedAt
    }
    
    /// Returns a preview of the content (first 100 characters)
    var contentPreview: String {
        if content.count > 100 {
            return content.prefix(100) + "..."
        }
        return content
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case title
        case content
        case adminId = "admin_id"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
    
    static func == (lhs: Note, rhs: Note) -> Bool {
        return lhs.id == rhs.id
    }
}

/// Request to create a new note
struct CreateNoteRequest: Codable {
    let adminId: Int
    let title: String
    let content: String
    
    enum CodingKeys: String, CodingKey {
        case adminId = "admin_id"
        case title
        case content
    }
}

/// Request to update an existing note
struct UpdateNoteRequest: Codable {
    let id: Int
    let adminId: Int
    let title: String
    let content: String
    
    enum CodingKeys: String, CodingKey {
        case id
        case adminId = "admin_id"
        case title
        case content
    }
}

/// Request to delete a note
struct DeleteNoteRequest: Codable {
    let id: Int
    let adminId: Int
    
    enum CodingKeys: String, CodingKey {
        case id
        case adminId = "admin_id"
    }
}
