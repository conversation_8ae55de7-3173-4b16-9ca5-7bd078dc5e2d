import SwiftUI

struct NoteDetailView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var noteService: NoteService
    @Environment(\.presentationMode) var presentationMode
    
    let note: Note
    
    @State private var isEditing = false
    @State private var showingDeleteAlert = false
    @State private var isUpdating = false
    @State private var isDeleting = false
    
    // Editable fields
    @State private var editedTitle = ""
    @State private var editedContent = ""
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                if isEditing {
                    // Edit Mode
                    VStack(alignment: .leading, spacing: 16) {
                        // Title Field
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Title")
                                .font(.headline)
                                .foregroundColor(Color("SecondaryColor"))
                            
                            TextField("Title", text: $editedTitle)
                                .font(.title3)
                                .padding()
                                .background(Color("FieldBackgroundColor"))
                                .cornerRadius(8)
                        }
                        
                        // Content Field
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Content")
                                .font(.headline)
                                .foregroundColor(Color("SecondaryColor"))
                            
                            TextEditor(text: $editedContent)
                                .frame(minHeight: 300)
                                .padding(4)
                                .background(Color("FieldBackgroundColor"))
                                .cornerRadius(8)
                        }
                        
                        // Buttons
                        HStack {
                            Button(action: cancelEditing) {
                                Text("Cancel")
                                    .font(.headline)
                                    .foregroundColor(Color("SecondaryColor"))
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 8)
                                    .background(Color("FieldBackgroundColor"))
                                    .cornerRadius(8)
                            }
                            
                            Spacer()
                            
                            Button(action: saveChanges) {
                                if isUpdating {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle())
                                } else {
                                    Text("Save")
                                        .font(.headline)
                                        .foregroundColor(.white)
                                }
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color("PrimaryColor"))
                            .cornerRadius(8)
                            .disabled(isUpdating || !isFormValid)
                        }
                    }
                    .padding()
                } else {
                    // View Mode
                    VStack(alignment: .leading, spacing: 16) {
                        // Title
                        Text(note.title)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(Color("TextColor"))
                        
                        // Dates
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Created")
                                    .font(.caption)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                Text(note.formattedCreatedDate)
                                    .font(.caption)
                                    .foregroundColor(Color("TextColor"))
                            }
                            
                            Spacer()
                            
                            VStack(alignment: .trailing, spacing: 4) {
                                Text("Updated")
                                    .font(.caption)
                                    .foregroundColor(Color("SecondaryColor"))
                                
                                Text(note.formattedUpdatedDate)
                                    .font(.caption)
                                    .foregroundColor(Color("TextColor"))
                            }
                        }
                        .padding(.vertical, 8)
                        
                        Divider()
                        
                        // Content
                        Text(note.content)
                            .font(.body)
                            .foregroundColor(Color("TextColor"))
                            .fixedSize(horizontal: false, vertical: true)
                    }
                    .padding()
                    .background(Color("CardBackgroundColor"))
                    .cornerRadius(12)
                    .padding(.horizontal)
                }
                
                // Action Buttons (only in view mode)
                if !isEditing {
                    HStack {
                        Button(action: {
                            startEditing()
                        }) {
                            HStack {
                                Image(systemName: "pencil")
                                Text("Edit")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color("PrimaryColor"))
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }
                        
                        Button(action: {
                            showingDeleteAlert = true
                        }) {
                            HStack {
                                Image(systemName: "trash")
                                Text("Delete")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.red)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }
                        .disabled(isDeleting)
                    }
                    .padding(.horizontal)
                }
            }
            .padding(.vertical)
        }
        .navigationTitle(isEditing ? "Edit Note" : "Note Details")
        .navigationBarTitleDisplayMode(.inline)
        .alert(isPresented: $showingDeleteAlert) {
            Alert(
                title: Text("Delete Note"),
                message: Text("Are you sure you want to delete this note? This action cannot be undone."),
                primaryButton: .destructive(Text("Delete")) {
                    deleteNote()
                },
                secondaryButton: .cancel()
            )
        }
    }
    
    // Start editing
    private func startEditing() {
        editedTitle = note.title
        editedContent = note.content
        isEditing = true
    }
    
    // Cancel editing
    private func cancelEditing() {
        isEditing = false
    }
    
    // Save changes
    private func saveChanges() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        isUpdating = true
        
        let updateRequest = UpdateNoteRequest(
            id: note.id,
            adminId: authService.selectedUser?.id ?? user.id,
            title: editedTitle,
            content: editedContent
        )
        
        noteService.updateNote(note: updateRequest, token: token) { success in
            isUpdating = false
            
            if success {
                isEditing = false
            }
        }
    }
    
    // Delete note
    private func deleteNote() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        isDeleting = true
        
        let deleteRequest = DeleteNoteRequest(
            id: note.id,
            adminId: authService.selectedUser?.id ?? user.id
        )
        
        noteService.deleteNote(deleteRequest: deleteRequest, token: token) { success in
            isDeleting = false
            
            if success {
                presentationMode.wrappedValue.dismiss()
            }
        }
    }
    
    // Validate form
    private var isFormValid: Bool {
        return !editedTitle.isEmpty && !editedContent.isEmpty
    }
}

struct NoteDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            NoteDetailView(note: Note(
                id: 1,
                title: "Sample Note",
                content: "This is a sample note content for preview purposes.",
                adminId: 1,
                createdAt: "2023-01-01T12:00:00.000Z",
                updatedAt: "2023-01-01T12:00:00.000Z"
            ))
        }
        .environmentObject(AuthService())
        .environmentObject(NoteService())
    }
}
