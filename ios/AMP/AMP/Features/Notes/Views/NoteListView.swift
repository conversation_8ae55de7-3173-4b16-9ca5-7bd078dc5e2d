import SwiftUI

struct NoteListView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var noteService: NoteService
    
    @State private var searchText = ""
    @State private var showingAddNote = false
    @State private var isRefreshing = false
    
    var body: some View {
        NavigationView {
            ZStack {
                Color("BackgroundColor")
                    .ignoresSafeArea()
                
                VStack {
                    // Search Bar
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(Color("SecondaryColor"))
                        
                        TextField("Search notes", text: $searchText)
                            .foregroundColor(Color("TextColor"))
                    }
                    .padding(10)
                    .background(Color("FieldBackgroundColor"))
                    .cornerRadius(10)
                    .padding(.horizontal)
                    .padding(.top, 8)
                    
                    // Notes List
                    if noteService.isLoading && noteService.notes.isEmpty {
                        Spacer()
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(1.5)
                        Spacer()
                    } else if noteService.notes.isEmpty {
                        Spacer()
                        VStack(spacing: 16) {
                            Image(systemName: "note.text")
                                .font(.system(size: 60))
                                .foregroundColor(Color("SecondaryColor").opacity(0.5))
                            
                            Text("No Notes Found")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(Color("TextColor"))
                            
                            Text("Add a new note to get started")
                                .font(.subheadline)
                                .foregroundColor(Color("TextColor").opacity(0.7))
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 32)
                            
                            Button(action: {
                                showingAddNote = true
                            }) {
                                HStack {
                                    Image(systemName: "plus.circle.fill")
                                    Text("Add New Note")
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(Color("PrimaryColor"))
                                .foregroundColor(.white)
                                .cornerRadius(8)
                            }
                            .padding(.top, 8)
                        }
                        Spacer()
                    } else {
                        List {
                            ForEach(filteredNotes) { note in
                                NavigationLink(destination: NoteDetailView(note: note)) {
                                    NoteRowView(note: note)
                                }
                                .listRowBackground(Color("CardBackgroundColor"))
                            }
                        }
                        .listStyle(PlainListStyle())
                        .refreshable {
                            await refreshNotes()
                        }
                    }
                }
                .navigationTitle("Notes")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(action: {
                            showingAddNote = true
                        }) {
                            Image(systemName: "plus")
                        }
                    }
                }
                .sheet(isPresented: $showingAddNote) {
                    AddNoteView()
                }
            }
        }
        .onAppear {
            loadNotes()
        }
    }
    
    private var filteredNotes: [Note] {
        return noteService.filteredNotes(searchText: searchText)
    }
    
    private func loadNotes() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        let userId = authService.selectedUser?.id ?? user.id
        
        noteService.getUserNotes(userId: userId, token: token) { _ in }
    }
    
    private func refreshNotes() async {
        isRefreshing = true
        
        guard let user = authService.user else {
            isRefreshing = false
            return
        }
        guard let token = authService.token else {
            isRefreshing = false
            return
        }
        
        let userId = authService.selectedUser?.id ?? user.id
        
        await withCheckedContinuation { continuation in
            noteService.getUserNotes(userId: userId, token: token) { _ in
                isRefreshing = false
                continuation.resume()
            }
        }
    }
}

struct NoteRowView: View {
    let note: Note
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Title
            Text(note.title)
                .font(.headline)
                .foregroundColor(Color("TextColor"))
            
            // Content Preview
            Text(note.contentPreview)
                .font(.subheadline)
                .foregroundColor(Color("SecondaryColor"))
                .lineLimit(2)
            
            // Date
            HStack {
                Spacer()
                
                Text(note.formattedUpdatedDate)
                    .font(.caption)
                    .foregroundColor(Color("SecondaryColor"))
            }
        }
        .padding(.vertical, 8)
    }
}

struct NoteListView_Previews: PreviewProvider {
    static var previews: some View {
        NoteListView()
            .environmentObject(AuthService())
            .environmentObject(NoteService())
    }
}
