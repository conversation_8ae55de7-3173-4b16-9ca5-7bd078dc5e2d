import SwiftUI

struct AddNoteView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var noteService: NoteService
    
    // Form fields
    @State private var title = ""
    @State private var content = ""
    
    // UI state
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isSubmitting = false
    
    var body: some View {
        NavigationView {
            Form {
                // Title Section
                Section(header: Text("Title")) {
                    TextField("Enter a title", text: $title)
                }
                
                // Content Section
                Section(header: Text("Content")) {
                    TextEditor(text: $content)
                        .frame(minHeight: 200)
                }
                
                // Submit Button
                Section {
                    Button(action: submitNote) {
                        HStack {
                            Spacer()
                            
                            if isSubmitting {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                            } else {
                                Text("Save Note")
                                    .fontWeight(.semibold)
                            }
                            
                            Spacer()
                        }
                    }
                    .disabled(isSubmitting || !isFormValid)
                }
            }
            .navigationTitle("New Note")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Error"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }
    
    // Validate form
    private var isFormValid: Bool {
        return !title.isEmpty && !content.isEmpty
    }
    
    // Submit note
    private func submitNote() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }
        
        // Validate form
        guard isFormValid else {
            alertMessage = "Please fill in all fields."
            showingAlert = true
            return
        }
        
        isSubmitting = true
        
        // Create note request
        let noteRequest = CreateNoteRequest(
            adminId: authService.selectedUser?.id ?? user.id,
            title: title,
            content: content
        )
        
        // Submit note
        noteService.createNote(note: noteRequest, token: token) { success in
            isSubmitting = false
            
            if success {
                presentationMode.wrappedValue.dismiss()
            } else {
                alertMessage = noteService.error ?? "Failed to create note. Please try again."
                showingAlert = true
            }
        }
    }
}

struct AddNoteView_Previews: PreviewProvider {
    static var previews: some View {
        AddNoteView()
            .environmentObject(AuthService())
            .environmentObject(NoteService())
    }
}
