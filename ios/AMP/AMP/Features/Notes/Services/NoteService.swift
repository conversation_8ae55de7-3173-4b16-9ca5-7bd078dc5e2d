import Foundation
import Combine
import SwiftUI

/// Service for managing notes
class NoteService: ObservableObject {
    @Published var notes: [Note] = []
    @Published var isLoading = false
    @Published var error: String?
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Get User Notes
    func getUserNotes(userId: Int, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.get(endpoint: "/notes/get_notes/\(userId)/", token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (notes: [Note]) in
                    self?.notes = notes
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Create Note
    func createNote(note: CreateNoteRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.post(endpoint: "/notes/create_notes/", body: note, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (notes: [Note]) in
                    self?.notes = notes
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Update Note
    func updateNote(note: UpdateNoteRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.put(endpoint: "/notes/update_notes/", body: note, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (notes: [Note]) in
                    self?.notes = notes
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Delete Note
    func deleteNote(deleteRequest: DeleteNoteRequest, token: String, onComplete: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.put(endpoint: "/notes/delete_notes/", body: deleteRequest, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            onComplete(false)
                        }
                    }
                },
                receiveValue: { [weak self] (notes: [Note]) in
                    self?.notes = notes
                    DispatchQueue.main.async {
                        onComplete(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Filter Notes
    func filteredNotes(searchText: String) -> [Note] {
        if searchText.isEmpty {
            return notes.sorted { $0.updatedAt > $1.updatedAt }
        } else {
            return notes.filter { note in
                note.title.localizedCaseInsensitiveContains(searchText) ||
                note.content.localizedCaseInsensitiveContains(searchText)
            }
            .sorted { $0.updatedAt > $1.updatedAt }
        }
    }
    
    // MARK: - Get Note by ID
    func getNote(by id: Int) -> Note? {
        return notes.first { $0.id == id }
    }
    
    // MARK: - Get Recent Notes
    func getRecentNotes(limit: Int = 5) -> [Note] {
        return Array(notes.sorted { $0.updatedAt > $1.updatedAt }.prefix(limit))
    }
}
