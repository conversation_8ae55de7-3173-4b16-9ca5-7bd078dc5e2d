import Foundation

/// Represents a dashboard summary
struct DashboardSummary: Codable {
    let totalSales: Double
    let totalOrders: Int
    let averageOrderValue: Double
    let lowStockItems: Int
    
    enum CodingKeys: String, CodingKey {
        case totalSales = "total_sales"
        case totalOrders = "total_orders"
        case averageOrderValue = "average_order_value"
        case lowStockItems = "low_stock_items"
    }
}

/// Represents a sales report
struct SalesReport: Codable {
    let totalSales: Double
    let totalOrders: Int
    let averageOrderValue: Double
    let dailySales: [DailySales]
    
    enum CodingKeys: String, CodingKey {
        case totalSales = "total_sales"
        case totalOrders = "total_orders"
        case averageOrderValue = "average_order_value"
        case dailySales = "daily_sales"
    }
}

/// Represents daily sales
struct DailySales: Codable {
    let date: String
    let sales: Double
    let orders: Int
    
    var formattedDate: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        if let date = dateFormatter.date(from: date) {
            dateFormatter.dateFormat = "MMM d"
            return dateFormatter.string(from: date)
        }
        
        return date
    }
}

/// Represents monthly sales
struct MonthlySales: Codable {
    let month: String
    let sales: Double
    let orders: Int
    
    var formattedMonth: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM"
        
        if let date = dateFormatter.date(from: month) {
            dateFormatter.dateFormat = "MMM yyyy"
            return dateFormatter.string(from: date)
        }
        
        return month
    }
}

/// Represents a top selling product
struct TopSellingProduct: Codable, Identifiable {
    let id: Int
    let name: String
    let sku: String
    let quantity: Int
    let revenue: Double
    
    enum CodingKeys: String, CodingKey {
        case id
        case name
        case sku = "s_k_u"
        case quantity
        case revenue
    }
}

/// Represents an inventory item
struct InventoryItem: Codable, Identifiable {
    let id: Int
    let name: String
    let sku: String
    let currentStock: Int
    let minStockLevel: Int
    
    enum CodingKeys: String, CodingKey {
        case id
        case name
        case sku = "s_k_u"
        case currentStock = "current_stock"
        case minStockLevel = "min_stock_level"
    }
    
    var stockStatus: StockStatus {
        if currentStock <= 0 {
            return .outOfStock
        } else if currentStock < minStockLevel {
            return .lowStock
        } else {
            return .inStock
        }
    }
}

/// Stock status
enum StockStatus {
    case inStock
    case lowStock
    case outOfStock
    
    var color: String {
        switch self {
        case .inStock:
            return "green"
        case .lowStock:
            return "orange"
        case .outOfStock:
            return "red"
        }
    }
    
    var text: String {
        switch self {
        case .inStock:
            return "In Stock"
        case .lowStock:
            return "Low Stock"
        case .outOfStock:
            return "Out of Stock"
        }
    }
}

/// Represents a dashboard order
struct DashboardOrder: Codable, Identifiable {
    let id: Int
    let orderNumber: String
    let date: String
    let status: Int
    let total: Double
    
    enum CodingKeys: String, CodingKey {
        case id
        case orderNumber = "order_number"
        case date
        case status
        case total
    }
    
    var statusText: String {
        switch status {
        case 1: return "Pending"
        case 2: return "Processing"
        case 3: return "Shipped"
        case 4: return "Delivered"
        case 5: return "Cancelled"
        default: return "Unknown"
        }
    }
    
    var statusColor: String {
        switch status {
        case 1: return "PendingColor"
        case 2: return "ProcessingColor"
        case 3: return "ShippedColor"
        case 4: return "DeliveredColor"
        case 5: return "CancelledColor"
        default: return "DefaultColor"
        }
    }
    
    var formattedDate: String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        
        if let date = dateFormatter.date(from: date) {
            dateFormatter.dateFormat = "MMM d, yyyy"
            return dateFormatter.string(from: date)
        }
        
        return date
    }
}

/// Represents category sales
struct CategorySales: Codable {
    let category: String
    let sales: Double
    let percentage: Double
}
