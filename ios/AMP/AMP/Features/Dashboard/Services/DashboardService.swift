import Foundation
import Combine
import SwiftUI

/// Service for dashboard
class DashboardService: ObservableObject {
    @Published var dashboardSummary: DashboardSummary?
    @Published var salesReport: SalesReport?
    @Published var monthlySales: [MonthlySales] = []
    @Published var topSellingProducts: [TopSellingProduct] = []
    @Published var lowStockItems: [InventoryItem] = []
    @Published var recentOrders: [DashboardOrder] = []
    @Published var categorySales: [CategorySales] = []
    @Published var isLoading = false
    @Published var error: String?
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Get Dashboard Summary
    func getDashboardSummary(token: String, completion: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.get(endpoint: "/dashboard/summary", token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            completion(false)
                        }
                    }
                },
                receiveValue: { [weak self] (summary: DashboardSummary) in
                    self?.dashboardSummary = summary
                    DispatchQueue.main.async {
                        completion(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Get Sales Report
    func getSalesReport(startDate: String, endDate: String, token: String, completion: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        let endpoint = "/dashboard/sales-report?start_date=\(startDate)&end_date=\(endDate)"
        
        APIClient.shared.get(endpoint: endpoint, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            completion(false)
                        }
                    }
                },
                receiveValue: { [weak self] (report: SalesReport) in
                    self?.salesReport = report
                    DispatchQueue.main.async {
                        completion(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Get Monthly Sales
    func getMonthlySales(year: Int, token: String, completion: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        let endpoint = "/dashboard/monthly-sales?year=\(year)"
        
        APIClient.shared.get(endpoint: endpoint, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            completion(false)
                        }
                    }
                },
                receiveValue: { [weak self] (monthlySales: [MonthlySales]) in
                    self?.monthlySales = monthlySales
                    DispatchQueue.main.async {
                        completion(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Get Top Selling Products
    func getTopSellingProducts(limit: Int = 5, token: String, completion: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        let endpoint = "/dashboard/top-products?limit=\(limit)"
        
        APIClient.shared.get(endpoint: endpoint, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            completion(false)
                        }
                    }
                },
                receiveValue: { [weak self] (products: [TopSellingProduct]) in
                    self?.topSellingProducts = products
                    DispatchQueue.main.async {
                        completion(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Get Low Stock Items
    func getLowStockItems(token: String, completion: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.get(endpoint: "/dashboard/low-stock", token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            completion(false)
                        }
                    }
                },
                receiveValue: { [weak self] (items: [InventoryItem]) in
                    self?.lowStockItems = items
                    DispatchQueue.main.async {
                        completion(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Get Recent Orders
    func getRecentOrders(limit: Int = 5, token: String, completion: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        let endpoint = "/dashboard/recent-orders?limit=\(limit)"
        
        APIClient.shared.get(endpoint: endpoint, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            completion(false)
                        }
                    }
                },
                receiveValue: { [weak self] (orders: [DashboardOrder]) in
                    self?.recentOrders = orders
                    DispatchQueue.main.async {
                        completion(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Get Sales By Category
    func getSalesByCategory(token: String, completion: @escaping (Bool) -> Void) {
        isLoading = true
        error = nil
        
        APIClient.shared.get(endpoint: "/dashboard/sales-by-category", token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionStatus in
                    self?.isLoading = false
                    
                    if case .failure(let error) = completionStatus {
                        self?.error = error.message
                        DispatchQueue.main.async {
                            completion(false)
                        }
                    }
                },
                receiveValue: { [weak self] (categorySales: [CategorySales]) in
                    self?.categorySales = categorySales
                    DispatchQueue.main.async {
                        completion(true)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // MARK: - Load All Dashboard Data
    func loadAllDashboardData(token: String, completion: @escaping (Bool) -> Void) {
        let group = DispatchGroup()
        var success = true
        
        // Get dashboard summary
        group.enter()
        getDashboardSummary(token: token) { result in
            if !result { success = false }
            group.leave()
        }
        
        // Get sales report for last 30 days
        group.enter()
        let calendar = Calendar.current
        let endDate = Date()
        let startDate = calendar.date(byAdding: .day, value: -30, to: endDate)!
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        getSalesReport(
            startDate: dateFormatter.string(from: startDate),
            endDate: dateFormatter.string(from: endDate),
            token: token
        ) { result in
            if !result { success = false }
            group.leave()
        }
        
        // Get monthly sales for current year
        group.enter()
        let currentYear = calendar.component(.year, from: Date())
        getMonthlySales(year: currentYear, token: token) { result in
            if !result { success = false }
            group.leave()
        }
        
        // Get top selling products
        group.enter()
        getTopSellingProducts(token: token) { result in
            if !result { success = false }
            group.leave()
        }
        
        // Get low stock items
        group.enter()
        getLowStockItems(token: token) { result in
            if !result { success = false }
            group.leave()
        }
        
        // Get recent orders
        group.enter()
        getRecentOrders(token: token) { result in
            if !result { success = false }
            group.leave()
        }
        
        // Get sales by category
        group.enter()
        getSalesByCategory(token: token) { result in
            if !result { success = false }
            group.leave()
        }
        
        // Notify when all requests are complete
        group.notify(queue: .main) {
            completion(success)
        }
    }
}
