import SwiftUI

struct DashboardView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var orderService: OrderService
    @EnvironmentObject var dashboardService: DashboardService

    @State private var isRefreshing = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Summary Cards
                    LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 16) {
                        // Total Orders
                        SummaryCardView(
                            title: "Total Orders",
                            value: "\(orderService.orders.count)",
                            icon: "shippingbox",
                            color: Color("PrimaryColor")
                        )

                        // Total Spent
                        SummaryCardView(
                            title: "Total Spent",
                            value: "$\(orderService.totalSpent, specifier: "%.2f")",
                            icon: "dollarsign.circle",
                            color: Color("SuccessColor")
                        )

                        // Pending Orders
                        SummaryCardView(
                            title: "Pending Orders",
                            value: "\(orderService.pendingOrders.count)",
                            icon: "clock",
                            color: Color("WarningColor")
                        )

                        // Total Saved
                        SummaryCardView(
                            title: "Total Saved",
                            value: "$\(orderService.totalSaved, specifier: "%.2f")",
                            icon: "tag",
                            color: Color("InfoColor")
                        )
                    }
                    .padding(.horizontal)

                    // Recent Orders
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text("Recent Orders")
                                .font(.headline)
                                .foregroundColor(Color("TextColor"))

                            Spacer()

                            NavigationLink(destination: OrderListView()) {
                                Text("View All")
                                    .font(.subheadline)
                                    .foregroundColor(Color("PrimaryColor"))
                            }
                        }

                        if orderService.isLoading {
                            HStack {
                                Spacer()
                                ProgressView()
                                Spacer()
                            }
                            .padding()
                        } else if orderService.orders.isEmpty {
                            HStack {
                                Spacer()
                                VStack(spacing: 8) {
                                    Image(systemName: "doc.text")
                                        .font(.largeTitle)
                                        .foregroundColor(Color("SecondaryColor").opacity(0.5))

                                    Text("No orders found")
                                        .font(.subheadline)
                                        .foregroundColor(Color("SecondaryColor"))
                                }
                                Spacer()
                            }
                            .padding()
                        } else {
                            ForEach(orderService.orders.prefix(5)) { order in
                                NavigationLink(destination: OrderDetailView(order: order)) {
                                    RecentOrderRowView(order: order)
                                }
                            }
                        }
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(Color("CardBackgroundColor"))
                    .cornerRadius(12)
                    .padding(.horizontal)

                    // Monthly Summary
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Monthly Summary")
                            .font(.headline)
                            .foregroundColor(Color("TextColor"))

                        if orderService.isLoading {
                            HStack {
                                Spacer()
                                ProgressView()
                                Spacer()
                            }
                            .padding()
                        } else if orderService.yearSummaryReports.isEmpty {
                            HStack {
                                Spacer()
                                VStack(spacing: 8) {
                                    Image(systemName: "chart.bar")
                                        .font(.largeTitle)
                                        .foregroundColor(Color("SecondaryColor").opacity(0.5))

                                    Text("No summary data available")
                                        .font(.subheadline)
                                        .foregroundColor(Color("SecondaryColor"))
                                }
                                Spacer()
                            }
                            .padding()
                        } else if let currentYear = orderService.yearSummaryReports.first {
                            VStack(spacing: 16) {
                                // Year Summary
                                HStack {
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text("\(currentYear.year)")
                                            .font(.title3)
                                            .fontWeight(.bold)
                                            .foregroundColor(Color("TextColor"))

                                        Text("Total Spent: $\(currentYear.totalPaid, specifier: "%.2f")")
                                            .font(.subheadline)
                                            .foregroundColor(Color("SecondaryColor"))
                                    }

                                    Spacer()

                                    VStack(alignment: .trailing, spacing: 4) {
                                        Text("Total Saved")
                                            .font(.subheadline)
                                            .foregroundColor(Color("SecondaryColor"))

                                        Text("$\(currentYear.totalSaved, specifier: "%.2f")")
                                            .font(.headline)
                                            .foregroundColor(Color("SuccessColor"))
                                    }
                                }

                                // Month Bars
                                VStack(spacing: 12) {
                                    ForEach(currentYear.months.prefix(6)) { month in
                                        MonthSummaryRowView(month: month, maxValue: currentYear.months.map { $0.paid }.max() ?? 1)
                                    }
                                }
                            }
                            .padding()
                        }
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(Color("CardBackgroundColor"))
                    .cornerRadius(12)
                    .padding(.horizontal)
                }
                .padding(.vertical)
            }
            .background(Color("BackgroundColor").ignoresSafeArea())
            .navigationTitle("Dashboard")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    NavigationLink(destination: DashboardDetailView()) {
                        Image(systemName: "chart.bar.xaxis")
                    }
                }
            }
            .refreshable {
                await refreshData()
            }
            .onAppear {
                loadData()
            }
        }
    }

    private func loadData() {
        guard let user = authService.user else { return }
        guard let token = authService.token else { return }

        let userId = authService.selectedUser?.id ?? user.id

        // Load orders
        orderService.getUserOrders(userId: userId, token: token) { _ in }

        // Load summary reports
        orderService.getSummaryReports(userId: userId, token: token) { _ in }
    }

    private func refreshData() async {
        isRefreshing = true

        guard let user = authService.user else {
            isRefreshing = false
            return
        }
        guard let token = authService.token else {
            isRefreshing = false
            return
        }

        let userId = authService.selectedUser?.id ?? user.id

        // Refresh orders
        await withCheckedContinuation { continuation in
            orderService.getUserOrders(userId: userId, token: token) { _ in
                continuation.resume()
            }
        }

        // Refresh summary reports
        await withCheckedContinuation { continuation in
            orderService.getSummaryReports(userId: userId, token: token) { _ in
                continuation.resume()
            }
        }

        isRefreshing = false
    }
}

struct SummaryCardView: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)

                Spacer()
            }

            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(Color("TextColor"))

            Text(title)
                .font(.caption)
                .foregroundColor(Color("SecondaryColor"))
        }
        .padding()
        .background(Color("CardBackgroundColor"))
        .cornerRadius(12)
    }
}

struct RecentOrderRowView: View {
    let order: Order

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(order.product.name)
                    .font(.subheadline)
                    .foregroundColor(Color("TextColor"))

                Text(order.formattedDate)
                    .font(.caption)
                    .foregroundColor(Color("SecondaryColor"))
            }

            Spacer()

            VStack(alignment: .trailing, spacing: 4) {
                Text("$\(order.price, specifier: "%.2f")")
                    .font(.subheadline)
                    .foregroundColor(Color("TextColor"))

                Text(order.statusText)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(Color(order.statusColor).opacity(0.2))
                    .foregroundColor(Color(order.statusColor))
                    .cornerRadius(4)
            }
        }
        .padding(.vertical, 8)
    }
}

struct MonthSummaryRowView: View {
    let month: MonthSummaryReport
    let maxValue: Double

    var body: some View {
        HStack {
            Text(month.name)
                .font(.caption)
                .foregroundColor(Color("TextColor"))
                .frame(width: 60, alignment: .leading)

            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color("FieldBackgroundColor"))
                        .frame(height: 12)

                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color("PrimaryColor"))
                        .frame(width: calculateWidth(geometry: geometry), height: 12)
                }
            }
            .frame(height: 12)

            Text("$\(month.paid, specifier: "%.2f")")
                .font(.caption)
                .foregroundColor(Color("TextColor"))
                .frame(width: 60, alignment: .trailing)
        }
    }

    private func calculateWidth(geometry: GeometryProxy) -> CGFloat {
        let percentage = month.paid / maxValue
        return geometry.size.width * CGFloat(percentage)
    }
}

struct DashboardView_Previews: PreviewProvider {
    static var previews: some View {
        DashboardView()
            .environmentObject(AuthService())
            .environmentObject(OrderService())
            .environmentObject(DashboardService())
    }
}
