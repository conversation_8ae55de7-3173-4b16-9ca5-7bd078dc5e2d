import SwiftUI

struct DashboardDetailView: View {
    @EnvironmentObject var authService: AuthService
    @EnvironmentObject var orderService: OrderService
    @EnvironmentObject var vendorService: VendorService
    
    @State private var selectedTimeRange = TimeRange.month
    @State private var selectedYear = Calendar.current.component(.year, from: Date())
    @State private var isRefreshing = false
    
    enum TimeRange: String, CaseIterable {
        case week = "Week"
        case month = "Month"
        case quarter = "Quarter"
        case year = "Year"
        case all = "All Time"
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Time Range Picker
                Picker("Time Range", selection: $selectedTimeRange) {
                    ForEach(TimeRange.allCases, id: \.self) { range in
                        Text(range.rawValue).tag(range)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal)
                
                // Year Picker (if applicable)
                if selectedTimeRange == .year {
                    HStack {
                        Button(action: {
                            selectedYear -= 1
                        }) {
                            Image(systemName: "chevron.left")
                                .foregroundColor(Color("PrimaryColor"))
                        }
                        
                        Spacer()
                        
                        Text("\(selectedYear)")
                            .font(.headline)
                            .foregroundColor(Color("TextColor"))
                        
                        Spacer()
                        
                        Button(action: {
                            selectedYear += 1
                        }) {
                            Image(systemName: "chevron.right")
                                .foregroundColor(Color("PrimaryColor"))
                        }
                    }
                    .padding(.horizontal)
                }
                
                // Monthly Spending Chart
                ChartCard(title: "Monthly Spending") {
                    LineChartView(
                        data: monthlySpendingData,
                        valueFormatter: { value in
                            return "$\(value, specifier: "%.0f")"
                        }
                    )
                    .padding(.top, 8)
                }
                
                // Order Status Chart
                ChartCard(title: "Order Status") {
                    PieChartView(
                        data: orderStatusData,
                        valueFormatter: { value in
                            return "\(Int(value))"
                        }
                    )
                    .padding(.top, 8)
                }
                
                // Top Vendors Chart
                ChartCard(title: "Top Vendors") {
                    BarChartView(
                        data: topVendorsData,
                        valueFormatter: { value in
                            return "$\(value, specifier: "%.0f")"
                        }
                    )
                    .padding(.top, 8)
                }
                
                // Savings Chart
                ChartCard(title: "Savings Over Time") {
                    LineChartView(
                        data: savingsData,
                        lineColor: Color("SuccessColor"),
                        valueFormatter: { value in
                            return "$\(value, specifier: "%.0f")"
                        }
                    )
                    .padding(.top, 8)
                }
            }
            .padding(.vertical)
        }
        .navigationTitle("Dashboard Analytics")
        .navigationBarTitleDisplayMode(.inline)
        .refreshable {
            await refreshData()
        }
        .onAppear {
            // Calculate vendor statistics from orders
            vendorService.calculateVendorStatistics(orders: orderService.orders)
        }
    }
    
    // Monthly spending data
    private var monthlySpendingData: [LineChartData] {
        let filteredReports: [YearSummaryReport]
        
        if selectedTimeRange == .year {
            filteredReports = orderService.yearSummaryReports.filter { $0.year == selectedYear }
        } else {
            filteredReports = orderService.yearSummaryReports
        }
        
        guard let yearReport = filteredReports.first else {
            return []
        }
        
        var months = yearReport.months
        
        // Filter based on time range
        switch selectedTimeRange {
        case .week:
            // Get the last 7 days (not implemented in this example)
            break
        case .month:
            // Get the last month
            months = months.suffix(1)
        case .quarter:
            // Get the last 3 months
            months = months.suffix(3)
        case .year, .all:
            // Use all months
            break
        }
        
        return months.map { month in
            LineChartData(label: month.name, value: month.paid)
        }
    }
    
    // Order status data
    private var orderStatusData: [PieChartData] {
        let pendingCount = orderService.orders.filter { $0.status == 1 }.count
        let processingCount = orderService.orders.filter { $0.status == 2 }.count
        let shippedCount = orderService.orders.filter { $0.status == 3 }.count
        let deliveredCount = orderService.orders.filter { $0.status == 4 }.count
        let cancelledCount = orderService.orders.filter { $0.status == 5 }.count
        
        let total = Double(pendingCount + processingCount + shippedCount + deliveredCount + cancelledCount)
        
        return [
            PieChartData(label: "Pending", value: Double(pendingCount), color: Color("PendingColor"), total: total),
            PieChartData(label: "Processing", value: Double(processingCount), color: Color("ProcessingColor"), total: total),
            PieChartData(label: "Shipped", value: Double(shippedCount), color: Color("ShippedColor"), total: total),
            PieChartData(label: "Delivered", value: Double(deliveredCount), color: Color("DeliveredColor"), total: total),
            PieChartData(label: "Cancelled", value: Double(cancelledCount), color: Color("CancelledColor"), total: total)
        ]
    }
    
    // Top vendors data
    private var topVendorsData: [BarChartData] {
        return vendorService.getTopVendors().map { vendor in
            BarChartData(label: vendor.name, value: vendor.totalSpent)
        }
    }
    
    // Savings data
    private var savingsData: [LineChartData] {
        let filteredReports: [YearSummaryReport]
        
        if selectedTimeRange == .year {
            filteredReports = orderService.yearSummaryReports.filter { $0.year == selectedYear }
        } else {
            filteredReports = orderService.yearSummaryReports
        }
        
        guard let yearReport = filteredReports.first else {
            return []
        }
        
        var months = yearReport.months
        
        // Filter based on time range
        switch selectedTimeRange {
        case .week:
            // Get the last 7 days (not implemented in this example)
            break
        case .month:
            // Get the last month
            months = months.suffix(1)
        case .quarter:
            // Get the last 3 months
            months = months.suffix(3)
        case .year, .all:
            // Use all months
            break
        }
        
        return months.map { month in
            LineChartData(label: month.name, value: month.saved)
        }
    }
    
    private func refreshData() async {
        isRefreshing = true
        
        guard let user = authService.user else {
            isRefreshing = false
            return
        }
        guard let token = authService.token else {
            isRefreshing = false
            return
        }
        
        let userId = authService.selectedUser?.id ?? user.id
        
        // Refresh orders
        await withCheckedContinuation { continuation in
            orderService.getUserOrders(userId: userId, token: token) { _ in
                continuation.resume()
            }
        }
        
        // Refresh summary reports
        await withCheckedContinuation { continuation in
            orderService.getSummaryReports(userId: userId, token: token) { _ in
                continuation.resume()
            }
        }
        
        // Recalculate vendor statistics
        vendorService.calculateVendorStatistics(orders: orderService.orders)
        
        isRefreshing = false
    }
}

struct ChartCard<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .foregroundColor(Color("TextColor"))
            
            content
        }
        .padding()
        .background(Color("CardBackgroundColor"))
        .cornerRadius(12)
        .padding(.horizontal)
    }
}

struct DashboardDetailView_Previews: PreviewProvider {
    static var previews: some View {
        DashboardDetailView()
            .environmentObject(AuthService())
            .environmentObject(OrderService())
            .environmentObject(VendorService())
    }
}
